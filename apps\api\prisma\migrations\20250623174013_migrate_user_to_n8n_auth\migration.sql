/*
  Warnings:

  - You are about to drop the column `clerk_org_id` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `clerk_user_id` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `org_name` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `org_role` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[airtableUserId]` on the table `users` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "users_clerk_org_id_idx";

-- DropIndex
DROP INDEX "users_clerk_user_id_idx";

-- DropIndex
DROP INDEX "users_clerk_user_id_key";

-- DropIndex
DROP INDEX "users_email_key";

-- DropIndex
DROP INDEX "users_org_name_idx";

-- AlterTable
ALTER TABLE "users" DROP COLUMN "clerk_org_id",
DROP COLUMN "clerk_user_id",
DROP COLUMN "name",
DROP COLUMN "org_name",
DROP COLUMN "org_role",
ADD COLUMN     "airtableUserId" TEXT,
ADD COLUMN     "firstName" TEXT,
ADD COLUMN     "lastName" TEXT,
ADD COLUMN     "mcNumber" TEXT,
ALTER COLUMN "role" SET DEFAULT 'CARRIER';

-- CreateIndex
CREATE UNIQUE INDEX "users_airtableUserId_key" ON "users"("airtableUserId");

-- CreateIndex
CREATE INDEX "users_airtableUserId_idx" ON "users"("airtableUserId");

-- CreateIndex
CREATE INDEX "users_mcNumber_idx" ON "users"("mcNumber");
