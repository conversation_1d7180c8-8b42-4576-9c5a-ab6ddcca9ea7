// This file will contain the logic for fetching data from Airtable
import { Injectable, Logger, NotFoundException, InternalServerErrorException, BadRequestException, ForbiddenException, HttpException, HttpStatus, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import Airtable, { Base, Record, FieldSet } from 'airtable';
import { PrismaService } from '../prisma/prisma.service'; // Import PrismaService
import { CircuitBreakerService } from '../common/services/circuit-breaker.service'; // Import Circuit Breaker
import { AirtableLoadWebhookDto } from './dto/airtable-load.dto'; // Import the new DTO
import { AdvancedFiltersDto, SortField, SortOrder } from './dto/advanced-filters.dto'; // Import advanced filtering DTOs
import { SavedSearchDto } from './dto/saved-search.dto';
import { GeographicUtils, Coordinates } from './utils/geographic.utils'; // Import geographic utilities
import { Load, LoadStatus, User, CarrierProfile, Role, SavedSearch, InvStatus, AdminResponse, NegotiationStatus, BidResponseType } from '@repo/db'; // Import Prisma types from @repo/db
import { RequestBookingDetailsDto } from './dto/request-booking-details.dto'; // Import the new DTO for booking details
import { NotificationsService, LoadNotificationData, LoadStatusChangeData, BidNotificationData } from '../notifications/notifications.service';

// Define the confirmation interface
export interface LoadBookingConfirmation {
  message: string;
  loadId: string;
  carrierUserId: string;
}

@Injectable()
export class AirtableOrdersService {
  private readonly logger = new Logger(AirtableOrdersService.name);
  private base: Base;
  private tableName: string;

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService, // Inject PrismaService
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private notificationsService: NotificationsService,
    private circuitBreakerService: CircuitBreakerService, // Inject Circuit Breaker
  ) {
    const apiKey = this.configService.get<string>('AIRTABLE_API_KEY');
    const baseId = this.configService.get<string>('AIRTABLE_BASE_ID');
    this.tableName = this.configService.get<string>('AIRTABLE_TABLE_NAME', 'Orders'); // Default to 'Orders' if not set

    if (!apiKey || !baseId) {
      this.logger.error('Airtable API Key or Base ID is missing in environment variables.');
      throw new Error('Airtable configuration is incomplete.');
    }

    // Initialize Airtable connection with new API
    const airtable = new Airtable({ apiKey });
    this.base = airtable.base(baseId);
    this.logger.log(`Airtable configured for base ID: ${baseId}, table: ${this.tableName}`);
  }

  // Helper to fetch a single record from Airtable by its ID with circuit breaker protection
  private async getAirtableRecordById(recordId: string): Promise<Record<FieldSet>> {
    this.logger.debug(`Fetching record ${recordId} directly from Airtable table: ${this.tableName}`);
    
    return this.circuitBreakerService.execute(
      'airtable-get-record',
      async () => {
        const record = await this.base(this.tableName).find(recordId);
        if (!record) {
          this.logger.warn(`Record with ID ${recordId} not found in Airtable table ${this.tableName}.`);
          throw new NotFoundException(`Load with ID ${recordId} not found in Airtable.`);
        }
        return record;
      },
      async () => {
        // Fallback: Try to get from database cache first
        this.logger.warn(`Airtable unavailable, attempting database fallback for record ${recordId}`);
        const cachedLoad = await this.prisma.load.findFirst({
          where: { airtableRecordId: recordId }
        });
        
        if (cachedLoad) {
          // Convert database record to Airtable-like format for compatibility
          const fallbackRecord: Record<FieldSet> = {
            id: recordId,
            fields: {
              'Order ID.': cachedLoad.proNumber,
              'Status': cachedLoad.status,
              'Rate to Carrier': cachedLoad.rate,
              'Pickup City Lookup': [cachedLoad.originCity],
              'Pickup State Lookup': [cachedLoad.originState],
              'Delivery City Lookup': [cachedLoad.destinationCity],
              'Delivery State Lookup': [cachedLoad.destinationState],
              'Pickup Date & Time': cachedLoad.pickupDateUtc?.toISOString(),
              'Delivery Date & Time': cachedLoad.deliveryDateUtc?.toISOString(),
              'Weight (lbs)': cachedLoad.weightLbs,
              'Equipment Required': cachedLoad.equipmentRequired,
              'Temperature': cachedLoad.temperature
            }
          } as any;
          
          this.logger.log(`Using database fallback for record ${recordId}`);
          return fallbackRecord;
        }
        
        throw new NotFoundException(`Load with ID ${recordId} not found in Airtable or local cache.`);
      },
      {
        failureThreshold: 3,
        recoveryTimeout: 30000, // 30 seconds for Airtable
        monitoringPeriod: 300000 // 5 minutes
      }
    );
  }

  // Helper function to map Airtable status string to LoadStatus enum
  private mapAirtableStatusToEnum(airtableStatus?: string): LoadStatus | undefined {
    if (!airtableStatus) return undefined;

    // Convert to uppercase and replace spaces with underscores to match enum keys roughly
    // Or use a more explicit mapping if names differ significantly
    const key = airtableStatus.toUpperCase().replace(/\s+\/\s+|\s+/g, '_') as keyof typeof LoadStatus;
    if (LoadStatus[key]) {
      return LoadStatus[key];
    }
    this.logger.warn(`Unknown Airtable status: "${airtableStatus}". Cannot map to LoadStatus enum.`);
    return undefined; // Or a default status, or throw error, depending on desired behavior
  }

  private parseTargetOrganizations(targetOrganizations: any): string[] | undefined {
    if (!targetOrganizations) return undefined;
    
    if (Array.isArray(targetOrganizations)) {
      // Handle JsonArray - filter out non-string values
      return targetOrganizations.filter(org => typeof org === 'string') as string[];
    }
    
    if (typeof targetOrganizations === 'string') {
      return targetOrganizations.split(',').map(org => org.trim());
    }
    
    // For any other type, return undefined
    return undefined;
  }

  /**
   * Calculate miles for a load using cached distance data with Radar API integration
   * FIXED: Handles multiple destinations separated by commas and uses case-insensitive matching
   */
  private async calculateMilesForLoad(originCity: string, originState: string, destinationCity: string, destinationState: string): Promise<number> {
    const logPrefix = `MILES_CALC[${originCity}, ${originState} → ${destinationCity}, ${destinationState}]`;

    try {
      // Validate input parameters are strings
      if (typeof destinationCity !== 'string' || typeof destinationState !== 'string') {
        throw new Error(`Invalid destination data types: city=${typeof destinationCity}, state=${typeof destinationState}`);
      }
      if (typeof originCity !== 'string' || typeof originState !== 'string') {
        throw new Error(`Invalid origin data types: city=${typeof originCity}, state=${typeof originState}`);
      }

      // Handle multiple destinations separated by commas
      const destinations = destinationCity.split(',').map(city => city.trim()).filter(city => city.length > 0);
      const states = destinationState.split(',').map(state => state.trim()).filter(state => state.length > 0);
      
      if (destinations.length === 0) {
        throw new Error('No valid destination cities found');
      }
      
      // If multiple destinations, calculate total distance
      if (destinations.length > 1) {
        this.logger.log(`${logPrefix} Multiple destinations detected: ${destinations.length} cities`);
        let totalDistance = 0;
        let currentOriginCity = originCity;
        let currentOriginState = originState;
        
        for (let i = 0; i < destinations.length; i++) {
          const destCity = destinations[i];
          const destState = states[i] || destinationState.split(',')[0]?.trim() || destinationState; // Use corresponding state or fallback
          
          const segmentDistance = await this.calculateSingleRouteDistance(
            currentOriginCity, 
            currentOriginState, 
            destCity, 
            destState
          );
          
          totalDistance += segmentDistance;
          this.logger.debug(`${logPrefix} Segment ${i + 1}: ${currentOriginCity}, ${currentOriginState} → ${destCity}, ${destState} = ${segmentDistance} miles`);
          
          // Next segment starts from current destination
          currentOriginCity = destCity;
          currentOriginState = destState;
        }
        
        this.logger.log(`${logPrefix} Multi-destination total distance: ${totalDistance} miles`);
        return Math.round(totalDistance);
      } else {
        // Single destination - use existing logic
        return await this.calculateSingleRouteDistance(originCity, originState, destinations[0], states[0] || destinationState);
      }
      
    } catch (error) {
      this.logger.error(`${logPrefix} Critical error during calculation: ${error.message}`);
      // Re-throw all errors - no fallback logic as per task requirements
      throw error;
    }
  }

  /**
   * Calculate distance for a single route segment
   */
  private async calculateSingleRouteDistance(originCity: string, originState: string, destinationCity: string, destinationState: string): Promise<number> {
    const logPrefix = `MILES_CALC[${originCity}, ${originState} → ${destinationCity}, ${destinationState}]`;
    
    try {
      // Normalize addresses for consistent caching
      const originAddress = `${originCity}, ${originState}`;
      const destinationAddress = `${destinationCity}, ${destinationState}`;
      
      this.logger.debug(`${logPrefix} Starting distance calculation...`);
      
      // 1. Try exact match with case-insensitive comparison
      let cachedDistance = await this.prisma.distanceCache.findFirst({
        where: {
          AND: [
            { originAddress: { equals: originAddress, mode: 'insensitive' } },
            { destinationAddress: { equals: destinationAddress, mode: 'insensitive' } }
          ]
        }
      });

      if (cachedDistance) {
        this.logger.log(`${logPrefix} Found exact case-insensitive match: ${cachedDistance.distanceMiles} miles`);
        return Math.round(cachedDistance.distanceMiles);
      }

      // 2. Try reverse direction with case-insensitive comparison
      cachedDistance = await this.prisma.distanceCache.findFirst({
        where: {
          AND: [
            { originAddress: { equals: destinationAddress, mode: 'insensitive' } },
            { destinationAddress: { equals: originAddress, mode: 'insensitive' } }
          ]
        }
      });

      if (cachedDistance) {
        this.logger.log(`${logPrefix} Found reverse case-insensitive match: ${cachedDistance.distanceMiles} miles`);
        return Math.round(cachedDistance.distanceMiles);
      }

      // 3. Try partial matches on city names - FIXED to handle single city strings only
      cachedDistance = await this.prisma.distanceCache.findFirst({
        where: {
          AND: [
            { originAddress: { contains: originCity, mode: 'insensitive' } },
            { destinationAddress: { contains: destinationCity, mode: 'insensitive' } }
          ]
        }
      });

      if (cachedDistance) {
        this.logger.log(`${logPrefix} Found partial match: ${cachedDistance.originAddress} → ${cachedDistance.destinationAddress} = ${cachedDistance.distanceMiles} miles`);
        return Math.round(cachedDistance.distanceMiles);
      }

      // 4. Try reverse partial matches
      cachedDistance = await this.prisma.distanceCache.findFirst({
        where: {
          AND: [
            { originAddress: { contains: destinationCity, mode: 'insensitive' } },
            { destinationAddress: { contains: originCity, mode: 'insensitive' } }
          ]
        }
      });

      if (cachedDistance) {
        this.logger.log(`${logPrefix} Found reverse partial match: ${cachedDistance.originAddress} → ${cachedDistance.destinationAddress} = ${cachedDistance.distanceMiles} miles`);
        return Math.round(cachedDistance.distanceMiles);
      }

      // 5. No cached data found - Use Radar API to get real distance
      this.logger.log(`${logPrefix} No cached distance found, attempting Radar API calculation...`);
      
      try {
        // Import and use RadarDistanceService for real-time calculation
        const { RadarDistanceService } = await import('../operations/services/radar-distance.service');
        const radarService = new (RadarDistanceService as any)(this.configService, this.prisma);
        
        const distanceResult = await radarService.calculateDistanceByAddress(originAddress, destinationAddress);
        
        if (distanceResult.success) {
          this.logger.log(`${logPrefix} Radar API successful: ${distanceResult.distanceMiles} miles`);
          return Math.round(distanceResult.distanceMiles);
        } else {
          this.logger.warn(`${logPrefix} Radar API failed, using fallback calculation`);
        }
      } catch (radarError) {
        this.logger.error(`${logPrefix} Radar API error: ${radarError.message}`);
      }

      // 6. Fallback to estimated distance calculation
      this.logger.warn(`${logPrefix} No cached or API data available, using fallback estimation`);
      const fallbackDistance = this.calculateFallbackDistance(originCity, originState, destinationCity, destinationState);
      this.logger.log(`${logPrefix} Fallback estimation: ${fallbackDistance} miles`);
      return fallbackDistance;
      
    } catch (error) {
      this.logger.error(`${logPrefix} Critical error during calculation: ${error.message}`);
      // Use fallback calculation instead of throwing
      this.logger.warn(`${logPrefix} Using fallback calculation due to error`);
      return this.calculateFallbackDistance(originCity, originState, destinationCity, destinationState);
    }
  }

  /**
   * Simple fallback distance calculation using approximate state-to-state distances
   */
  private calculateFallbackDistance(originCity: string, originState: string, destinationCity: string, destinationState: string): number {
    // If same state, estimate 200 miles as average intrastate distance
    if (originState.toLowerCase().trim() === destinationState.toLowerCase().trim()) {
      return 200;
    }

    // Basic interstate distance estimation based on common routes
    const stateDistances: { [key: string]: number } = {
      'TX-CA': 1200, 'CA-TX': 1200,
      'TX-FL': 1000, 'FL-TX': 1000,
      'CA-FL': 2400, 'FL-CA': 2400,
      'NY-CA': 2800, 'CA-NY': 2800,
      'TX-NY': 1600, 'NY-TX': 1600,
      'FL-NY': 1100, 'NY-FL': 1100,
    };

    const routeKey = `${originState.toUpperCase()}-${destinationState.toUpperCase()}`;
    const reverseKey = `${destinationState.toUpperCase()}-${originState.toUpperCase()}`;
    
    return stateDistances[routeKey] || stateDistances[reverseKey] || 800; // 800 miles as default fallback
  }

  private mapAirtableInvStatusToEnum(airtableInvStatus?: string): InvStatus | undefined {
    if (!airtableInvStatus) return undefined;
    
    const invStatusMap: { [key: string]: InvStatus } = {
      'Not Sent': InvStatus.NOT_SENT,
      'Sent': InvStatus.SENT,
      'Paid': InvStatus.PAID,
    };
    
    return invStatusMap[airtableInvStatus];
  }

  async processLoadWebhook(
    payload: AirtableLoadWebhookDto, // Accept the new DTO directly
  ): Promise<Load> {
    this.logger.log(
      `SERVICE: processLoadWebhook called with payload for Airtable ID: ${payload.airtable_record_id}`,
    );

    // The payload from the script already has field names somewhat mapped.
    // We will use these directly and then the raw_airtable_data for anything else or as a fallback.
    const mappedData: any = {
      airtableRecordId: payload.airtable_record_id, // Use the correct field from payload
      originCity: payload.origin_city,
      originState: payload.origin_state,
      destinationCity: payload.destination_city,
      destinationState: payload.destination_state,
      equipmentRequired: payload.equipment_required,
      weightLbs: payload.weight_lbs,
      rate: payload.rate,
      status: this.mapAirtableStatusToEnum(payload.status),
      temperature: payload.temperature,
      rawAirtableData: payload.raw_airtable_data || (payload as any), // Store raw_data or the whole payload if raw_data is not explicitly separated by script in all cases
    };

    // Safely parse dates from the direct payload fields
    if (payload.pickup_date_utc) {
      const pickupDate = new Date(payload.pickup_date_utc);
      if (!isNaN(pickupDate.getTime())) {
        mappedData.pickupDateUtc = pickupDate;
      } else {
        this.logger.warn(`Invalid pickup_date_utc format: ${payload.pickup_date_utc}`);
      }
    }

    if (payload.delivery_date_utc) {
      const deliveryDate = new Date(payload.delivery_date_utc);
      if (!isNaN(deliveryDate.getTime())) {
        mappedData.deliveryDateUtc = deliveryDate;
      } else {
        this.logger.warn(`Invalid delivery_date_utc format: ${payload.delivery_date_utc}`);
      }
    }

    // Get full Airtable record for targeting fields and assignment information
    try {
      const airtableRecord = await this.getAirtableRecordById(payload.airtable_record_id);
      const fields = airtableRecord.fields;
      
      // Note: "Synced to API" control is now handled at the API query level
      // The webhook processes all loads, but getAvailableLoads() filters by "Synced to API"
      this.logger.log(`SERVICE: Processing load ${payload.airtable_record_id} - visibility controlled by "Synced to API" at query time`);
      
      // Handle organization targeting fields from both payload and Airtable record
      const targetOrganizations = payload.target_organizations || fields['Target Organizations'];
      const isPublic = payload.is_public !== undefined ? payload.is_public : fields['Is Public'];
      
      this.logger.log(`SERVICE: Processing targeting fields - Target Organizations: ${JSON.stringify(targetOrganizations)}, Is Public: ${isPublic}`);
      
      // Set targeting fields in mapped data
      if (targetOrganizations !== undefined) {
        // Convert to array format for database storage
        let targetOrgArray: string[] = [];
        if (Array.isArray(targetOrganizations)) {
          targetOrgArray = targetOrganizations;
        } else if (typeof targetOrganizations === 'string') {
          targetOrgArray = [targetOrganizations];
        }
        
        mappedData.targetOrganizations = targetOrgArray.length > 0 ? targetOrgArray : null;
        mappedData.isTargeted = targetOrgArray.length > 0;
        this.logger.log(`SERVICE: Set targetOrganizations: ${JSON.stringify(targetOrgArray)}, isTargeted: ${mappedData.isTargeted}`);
      }
      
      if (isPublic !== undefined) {
        mappedData.isPublic = !!isPublic; // Convert to boolean, defaulting to false if falsy
        this.logger.log(`SERVICE: Set isPublic: ${mappedData.isPublic}`);
      }
      
      // Special handling for "Assigned" status - we need to assign the load to the carrier who booked it
      if (payload.status === 'Assigned') {
        // Look for booking request information
        const carrierCompanyName = fields['Booking Request - User ID'] as string;
        
        if (carrierCompanyName) {
          this.logger.log(`SERVICE: Load ${payload.airtable_record_id} assigned to carrier: ${carrierCompanyName}`);
          
          // Find the carrier profile by company name
          const carrierProfile = await this.prisma.carrierProfile.findFirst({
            where: {
              companyName: carrierCompanyName
            }
          });
          
          if (carrierProfile) {
            mappedData.awardedToCarrierProfileId = carrierProfile.id;
            this.logger.log(`SERVICE: Successfully linked load to carrier profile ID: ${carrierProfile.id}`);
          } else {
            this.logger.warn(`SERVICE: Could not find carrier profile for company: ${carrierCompanyName}`);
          }
        } else {
          this.logger.warn(`SERVICE: Load status is "Assigned" but no booking request information found for ${payload.airtable_record_id}`);
        }
      }
    } catch (airtableError) {
      this.logger.error(`SERVICE: Error fetching Airtable record for targeting fields: ${airtableError.message}`, airtableError.stack);
      // Continue with webhook processing even if we can't fetch targeting fields
      // Set defaults for targeting fields
      mappedData.isPublic = true; // Default to public if we can't determine
      mappedData.isTargeted = false;
      mappedData.targetOrganizations = null;
    }
    
    // Remove undefined fields to prevent Prisma from trying to set them to null explicitly
    // if the schema doesn't have a default and they are optional.
    Object.keys(mappedData).forEach(key => {
      if (mappedData[key] === undefined) {
        delete mappedData[key];
      }
    });

    try {
      const existingLoad = await this.prisma.load.findUnique({
        where: { airtableRecordId: payload.airtable_record_id }, // Use correct field for query
      });

      let result: Load;
      let isNewLoad = false;
      let oldStatus: LoadStatus | null | undefined;

      if (existingLoad) {
        this.logger.log(
          `SERVICE: Found existing load (ID: ${existingLoad.id}). Updating...`,
        );
        oldStatus = existingLoad.status;
        // Ensure updatedAt is managed by Prisma or set explicitly if needed
        result = await this.prisma.load.update({
          where: { id: existingLoad.id },
          data: mappedData,
        });
      } else {
        this.logger.log(`SERVICE: No existing load found. Creating new load...`);
        isNewLoad = true;
        // Ensure createdAt and updatedAt are managed by Prisma or set explicitly if needed
        result = await this.prisma.load.create({
          data: mappedData,
        });
      }

      // Send real-time notifications
      try {
        await this.sendLoadNotifications(result, isNewLoad, oldStatus, payload);
      } catch (notificationError) {
        this.logger.error(`Failed to send notifications for load ${result.id}: ${notificationError.message}`, notificationError.stack);
        // Don't fail the webhook if notifications fail - log and continue
      }

      return result;
    } catch (error) {
      this.logger.error(
        `SERVICE: Database error processing webhook for Airtable ID ${payload.airtable_record_id}: ${error.message}`,
        error.stack,
      );
      if (error.code === 'P2002') { // Prisma unique constraint violation (though airtableRecordId check should prevent this for create)
        throw new BadRequestException('Error due to duplicate entry, potentially race condition.');
      }
      throw new InternalServerErrorException(
        `DATABASE: Database operation failed: ${error.message}`,
      );
    }
  }

  /**
   * Send real-time notifications for load changes
   */
  private async sendLoadNotifications(
    load: Load, 
    isNewLoad: boolean, 
    oldStatus: LoadStatus | null | undefined, 
    payload: AirtableLoadWebhookDto
  ): Promise<void> {
    this.logger.log(`Sending notifications for load ${load.id}, isNew: ${isNewLoad}, oldStatus: ${oldStatus}, newStatus: ${load.status}`);

    if (isNewLoad) {
      // Broadcast new load notification
      const notificationData: LoadNotificationData = {
        loadId: load.id,
        airtableRecordId: load.airtableRecordId,
        originCity: load.originCity || undefined,
        originState: load.originState || undefined,
        destinationCity: load.destinationCity || undefined,
        destinationState: load.destinationState || undefined,
        rate: load.rate || undefined,
        equipmentRequired: load.equipmentRequired || undefined,
        pickupDate: load.pickupDateUtc || undefined,
        deliveryDate: load.deliveryDateUtc || undefined,
        status: load.status || undefined,
        targetOrganizations: this.parseTargetOrganizations(load.targetOrganizations),
        isPublic: load.isPublic || false,
      };

      await this.notificationsService.broadcastNewLoad(notificationData);
    } else if (oldStatus && oldStatus !== load.status) {
      // Broadcast status change notification
      const statusChangeData: LoadStatusChangeData = {
        loadId: load.id,
        airtableRecordId: load.airtableRecordId,
        oldStatus: oldStatus || 'Unknown',
        newStatus: load.status || 'Unknown',
      };

      // If load is assigned, get carrier information
      if (load.awardedToCarrierProfileId) {
        try {
          const carrierProfile = await this.prisma.carrierProfile.findUnique({
            where: { id: load.awardedToCarrierProfileId },
            include: { user: true }
          });
          
          if (carrierProfile) {
            statusChangeData.assignedCarrierUserId = carrierProfile.user.airtableUserId || undefined;
            statusChangeData.assignedCarrierCompanyName = carrierProfile.companyName || undefined;
          }
        } catch (error) {
          this.logger.warn(`Failed to fetch carrier details for load assignment notification: ${error.message}`);
        }
      }

      await this.notificationsService.broadcastLoadStatusChange(statusChangeData);
    }
  }

  async getAvailableLoads(userAirtableId?: string): Promise<any[]> {
    const startTime = Date.now();
    this.logger.log('SERVICE: getAvailableLoads called (N8N JWT - MC Number Based Targeting)');
    
    // 🔧 DATABASE CONNECTION MONITORING
    try {
      // Check database health before making queries
      await this.prisma.$queryRaw`SELECT 1`;
    } catch (dbError) {
      this.logger.error('DATABASE: Connection health check failed:', dbError);
      throw new HttpException(
        'Database connection unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
    
    // CRITICAL: Use Airtable filtering to respect "Synced to API" control
    // Now with MC Number-based targeting instead of organization names
    
    // Create cache key based on user MC Number (for targeted loads)
    const baseCacheKey = 'available_loads_mc_number_v1';
    let cacheKey = baseCacheKey;
    
    // Get user's MC Number for filtering
    let userMcNumber: string | null = null;
    let isAdmin = false;
    
    if (userAirtableId) {
      try {
        this.logger.log(`SERVICE: Fetching user profile for ${userAirtableId}`);
        
        // Try to get user profile from cache first
        const userCacheKey = `user_profile_${userAirtableId}`;
        let userProfile = await this.cacheManager.get(userCacheKey) as { mcNumber: string | null; role: string; airtableUserId: string } | null;
        
        if (!userProfile) {
          this.logger.log(`SERVICE: User profile cache miss, fetching from database`);
          try {
            userProfile = await (this.prisma as any).userProfile.findUnique({
              where: { airtableUserId: userAirtableId },
              select: {
                mcNumber: true,
                role: true,
                airtableUserId: true
              }
            });
          
            if (userProfile) {
              // Cache user lookup for 5 minutes (service-level performance cache)
              // Note: This is different from the persistent user profile cache in AuthService
              await this.cacheManager.set(userCacheKey, userProfile, 300000);
            }
          } catch (dbError) {
            this.logger.error('DATABASE: Failed to fetch user profile:', dbError);
            throw new HttpException(
              `Database error while fetching user profile: ${dbError.message}`,
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        } else {
          this.logger.debug(`SERVICE: User profile cache hit for ${userAirtableId}`);
        }
        
        if (userProfile) {
          userMcNumber = userProfile.mcNumber;
          isAdmin = userProfile.role?.toLowerCase() === 'admin';
          // Include MC Number in cache key for targeted load filtering
          cacheKey = `${baseCacheKey}_${isAdmin ? 'admin' : (userMcNumber || 'no_mc')}`;
          this.logger.log(`SERVICE: User ${userAirtableId} MC Number: "${userMcNumber}", isAdmin: ${isAdmin}, role: ${userProfile.role}`);
        } else {
          this.logger.warn(`SERVICE: User profile ${userAirtableId} not found in database`);
        }
      } catch (error) {
        this.logger.error(`SERVICE: Failed to fetch user profile: ${error.message}`, error.stack);
        throw new HttpException(
          `Database error while fetching user profile: ${error.message}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      this.logger.warn('SERVICE: No userAirtableId provided for MC Number filtering');
    }
    
    // Check cache first
    try {
      const cachedLoads = await this.cacheManager.get(cacheKey);
      if (cachedLoads) {
        const cacheTime = Date.now() - startTime;
        this.logger.log(`SERVICE: Cache hit for ${cacheKey}, returning cached loads in ${cacheTime}ms`);
        return cachedLoads as any[];
      }
    } catch (cacheError) {
      this.logger.warn(`SERVICE: Cache retrieval failed: ${cacheError.message}`);
    }

    const recordsData: any[] = [];
    try {
      // CRITICAL: Use Airtable filtering to respect "Synced to API" checkbox control
      // This is the ONLY way to ensure carriers only see loads you've approved
      this.logger.log(`SERVICE: Querying Airtable for loads with "Synced to API" = true`);
      
      // Define the fields to fetch based on CSV mapping - now using Lookup fields
      const fieldsToFetch = [
        'Order ID.',
        'Rate to Carrier',
        'Pickup Date & Time',
        'Pickup Appt.',
        'Delivery Date & Time',
        'Delivery Appt.',
        'Weight (lbs)',
        'Status',
        'Equipment Required',
        'Pickup City Lookup',
        'Pickup State Lookup',
        'Delivery City Lookup',
        'Delivery State Lookup',
        // Add MC Number-based targeting fields
        'Target MC Numbers', // Primary field for MC Number targeting (multiple select)
        'Target Organizations' // Legacy field for backward compatibility
      ];

      this.logger.log(`SERVICE: Fetching records from Airtable with "Synced to API" filter`);
      const records = await this.base(this.tableName)
        .select({
          view: 'Grid view',
          fields: fieldsToFetch,
          filterByFormula: `AND({Synced to API}, {Status} = "Available")`
        })
        .all();

      this.logger.log(`SERVICE: Fetched ${records.length} records from Airtable with "Synced to API" = true`);

      for (const record of records) {
        try {
          // Extract city and state data, handling both string and array formats from Airtable
          const pickupCityRaw = record.get('Pickup City Lookup');
          const pickupStateRaw = record.get('Pickup State Lookup');
          const deliveryCityRaw = record.get('Delivery City Lookup');
          const deliveryStateRaw = record.get('Delivery State Lookup');

          // Convert to strings, taking first element if array
          const pickupCity = Array.isArray(pickupCityRaw) ? pickupCityRaw[0] : pickupCityRaw;
          const pickupState = Array.isArray(pickupStateRaw) ? pickupStateRaw[0] : pickupStateRaw;
          const deliveryCity = Array.isArray(deliveryCityRaw) ? deliveryCityRaw[0] : deliveryCityRaw;
          const deliveryState = Array.isArray(deliveryStateRaw) ? deliveryStateRaw[0] : deliveryStateRaw;
          
          // SIMPLIFIED MC Number-based targeting logic - SECURITY CRITICAL  
          const targetMcNumbers = record.get('Target MC Numbers') as string | string[] | undefined;
          // Legacy support: also check Target Organizations field but map to MC Numbers
          const targetOrganizations = record.get('Target Organizations') as string | string[] | undefined;
          let isTargetedToUser = false;
          let canUserSeeLoad = false; // 🔒 SECURITY: Default to deny access
          let isPublic = false; // Will be determined by targeting logic
          
          this.logger.debug(`SERVICE: Processing record ${record.id}, targetMcNumbers: ${JSON.stringify(targetMcNumbers)}, targetOrganizations: ${JSON.stringify(targetOrganizations)}`);
          
          // Check if this load has MC Number targeting
          const hasTargeting = targetMcNumbers && (
            Array.isArray(targetMcNumbers) ? targetMcNumbers.length > 0 : targetMcNumbers.trim() !== ''
          );
          
          if (hasTargeting) {
            // Load is targeted - only specific MC Numbers can see it
            isPublic = false;
            
            if (userMcNumber) {
              const mcArray = Array.isArray(targetMcNumbers) 
                ? targetMcNumbers 
                : [targetMcNumbers];
              
              isTargetedToUser = mcArray.some(mc => 
                mc.toLowerCase().trim() === userMcNumber.toLowerCase().trim()
              );
              
              this.logger.debug(`SERVICE: Load ${record.id} is TARGETED - user MC: "${userMcNumber}", target MCs: ${JSON.stringify(mcArray)}, isTargeted: ${isTargetedToUser}`);
              canUserSeeLoad = isTargetedToUser;
            } else {
              this.logger.debug(`SERVICE: Load ${record.id} is TARGETED but user has no MC Number - denying access`);
              canUserSeeLoad = false;
            }
          } else {
            // No MC Number targeting - load is public to everyone
            isPublic = true;
            canUserSeeLoad = true;
            this.logger.debug(`SERVICE: Load ${record.id} is PUBLIC (no MC targeting) - allowing access to all users`);
          }
          
          // Legacy support: Organization-based targeting (fallback for old data)
          if (!isTargetedToUser && !hasTargeting && targetOrganizations && userMcNumber) {
            this.logger.debug(`SERVICE: Load ${record.id} falling back to legacy organization targeting`);
            this.logger.warn(`SERVICE: Load ${record.id} uses legacy organization targeting - recommend migrating to MC Number targeting`);
            // For now, treat organization targeting as private (not public)
            isPublic = false;
          }
          
          // Admins can always see all loads
          if (isAdmin) {
            const wasBlocked = !canUserSeeLoad;
            canUserSeeLoad = true;
            this.logger.log(`SERVICE: Admin override - user can see load ${record.id}${wasBlocked ? ' (was blocked)' : ''}`);
          }
          
          // 🔒 SECURITY AUDIT: Log access decisions for monitoring
          if (!canUserSeeLoad) {
            this.logger.warn(`SECURITY: Access DENIED to load ${record.id} for user ${userAirtableId} (MC: "${userMcNumber}") - isPublic: ${isPublic}, targets: ${JSON.stringify(targetMcNumbers)}`);
            continue;
          } else {
            this.logger.log(`SECURITY: Access GRANTED to load ${record.id} for user ${userAirtableId} (MC: "${userMcNumber}") - isPublic: ${isPublic}, targeted: ${isTargetedToUser}`);
          }

          // Calculate miles using cached distance data or Radar API
          let miles: number;
          try {
            // Validate that we have valid string data for miles calculation
            if (!pickupCity || !pickupState || !deliveryCity || !deliveryState) {
              this.logger.warn(`SERVICE: Missing location data for load ${record.id} - pickup: ${pickupCity}, ${pickupState} → delivery: ${deliveryCity}, ${deliveryState}`);
              // Skip this load if we don't have complete location data
              continue;
            }

            miles = await this.calculateMilesForLoad(
              pickupCity as string,
              pickupState as string,
              deliveryCity as string,
              deliveryState as string
            );
          } catch (milesError) {
            this.logger.error(`SERVICE: Failed to calculate miles for load ${record.id}: ${milesError.message}`);
            // Skip this load if we can't calculate accurate miles
            continue;
          }
          
          // Set default values as requested
          const equipmentValue = record.get('Equipment Required');
          const equipment = (typeof equipmentValue === 'string' ? equipmentValue : 'Dry Van');
          const temp = equipment?.toUpperCase().includes('REEFER') ? '-10°F' : null; // Only show temp for reefer loads
          const commodity = 'Frozen Fruit'; // Default commodity as requested

          const loadData = {
            id: record.id,
            proNumber: record.get('Order ID.'),
            rate: record.get('Rate to Carrier'),
            origin: `${pickupCity}, ${pickupState}`,
            destination: `${deliveryCity}, ${deliveryState}`,
            pickupDateTime: record.get('Pickup Date & Time'),
            pickupAppointmentTime: record.get('Pickup Appt.'),
            deliveryDateTime: record.get('Delivery Date & Time'),
            deliveryAppointmentTime: record.get('Delivery Appt.'),
            weight: record.get('Weight (lbs)'),
            status: record.get('Status'),
            equipment: equipment,
            miles: miles, // Add calculated miles
            temp: temp, // Add default temp for reefer loads
            commodity: commodity, // Add default commodity
            // Add organization targeting metadata for frontend
            isPublic: isPublic,
            isTargeted: isTargetedToUser,
            targetOrganizations: targetOrganizations
          };

          this.logger.debug(`SERVICE: Load ${record.id} final result - isPublic: ${isPublic}, isTargeted: ${isTargetedToUser}, visible: ${canUserSeeLoad}`);
          recordsData.push(loadData);
        } catch (recordError) {
          this.logger.error(`SERVICE: Error processing record ${record.id}:`, recordError);
          // Continue processing other records instead of failing completely
        }
      }

      this.logger.log(`SERVICE: Filtered to ${recordsData.length} loads visible to user (ONLY loads with "Synced to API" = true)`);
      
      // Sort loads to prioritize targeted loads at the top
      const sortedLoads = recordsData.sort((a, b) => {
        // First priority: Targeted loads for this user (isTargeted = true)
        if (a.isTargeted && !b.isTargeted) return -1;
        if (!a.isTargeted && b.isTargeted) return 1;
        
        // Second priority: Within each group, sort by pickup date (earliest first)
        const aPickupDate = a.pickupDateTime ? new Date(a.pickupDateTime).getTime() : 0;
        const bPickupDate = b.pickupDateTime ? new Date(b.pickupDateTime).getTime() : 0;
        
        return aPickupDate - bPickupDate;
      });
      
      // Log sorting results for debugging
      const targetedCount = sortedLoads.filter(load => load.isTargeted).length;
      const publicCount = sortedLoads.length - targetedCount;
      this.logger.log(`SERVICE: Load sorting completed - ${targetedCount} targeted loads (top), ${publicCount} public loads (bottom)`);
      
      // Cache the sorted result for 2 minutes (load board data changes frequently)
      try {
        await this.cacheManager.set(cacheKey, sortedLoads, 120000);
        this.logger.debug(`SERVICE: Cached sorted loads under key ${cacheKey}`);
      } catch (cacheError) {
        this.logger.warn(`SERVICE: Failed to cache loads: ${cacheError.message}`);
      }
      
      const totalTime = Date.now() - startTime;
      this.logger.log(`SERVICE: getAvailableLoads (EMERGENCY FIX - Airtable Filtered) completed in ${totalTime}ms`);
      return sortedLoads;

    } catch (error) {
      this.logger.error('SERVICE: Error fetching data from Airtable:', error);
      throw new HttpException(
        `Failed to fetch loads from Airtable: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enhanced getAvailableLoads with advanced filtering capabilities
   */
  async getAvailableLoadsWithFilters(
    userAirtableId?: string, 
    filters?: AdvancedFiltersDto
  ): Promise<{ loads: any[]; totalCount: number; page: number; pageSize: number }> {
    const startTime = Date.now();
    this.logger.log(`SERVICE: getAvailableLoadsWithFilters called for user ${userAirtableId} with filters:`, filters);

    // Get base loads using existing method
    const baseLoads = await this.getAvailableLoads(userAirtableId);
    let filteredLoads = [...baseLoads];

    // Apply text search across multiple fields
    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredLoads = filteredLoads.filter(load => 
        load.proNumber?.toLowerCase().includes(searchTerm) ||
        load.origin?.toLowerCase().includes(searchTerm) ||
        load.destination?.toLowerCase().includes(searchTerm) ||
        load.equipment?.toLowerCase().includes(searchTerm) ||
        load.status?.toLowerCase().includes(searchTerm)
      );
      this.logger.debug(`SERVICE: Text search "${searchTerm}" filtered to ${filteredLoads.length} loads`);
    }

    // Apply origin filter
    if (filters?.origin) {
      const originTerm = filters.origin.toLowerCase();
      filteredLoads = filteredLoads.filter(load => 
        load.origin?.toLowerCase().includes(originTerm)
      );
      this.logger.debug(`SERVICE: Origin filter "${originTerm}" filtered to ${filteredLoads.length} loads`);
    }

    // Apply destination filter  
    if (filters?.destination) {
      const destTerm = filters.destination.toLowerCase();
      filteredLoads = filteredLoads.filter(load => 
        load.destination?.toLowerCase().includes(destTerm)
      );
      this.logger.debug(`SERVICE: Destination filter "${destTerm}" filtered to ${filteredLoads.length} loads`);
    }

    // Apply equipment filter
    if (filters?.equipment) {
      filteredLoads = filteredLoads.filter(load => 
        load.equipment === filters.equipment
      );
      this.logger.debug(`SERVICE: Equipment filter "${filters.equipment}" filtered to ${filteredLoads.length} loads`);
    }

    // Apply equipment types filter (multiple selection)
    if (filters?.equipmentTypes && filters.equipmentTypes.length > 0) {
      filteredLoads = filteredLoads.filter(load => 
        filters.equipmentTypes!.includes(load.equipment)
      );
      this.logger.debug(`SERVICE: Equipment types filter [${filters.equipmentTypes.join(', ')}] filtered to ${filteredLoads.length} loads`);
    }

    // Apply rate range filters
    if (filters?.minRate !== undefined) {
      filteredLoads = filteredLoads.filter(load => {
        const rate = typeof load.rate === 'string' ? parseFloat(load.rate) : load.rate;
        return rate >= filters.minRate!;
      });
      this.logger.debug(`SERVICE: Min rate filter ${filters.minRate} filtered to ${filteredLoads.length} loads`);
    }

    if (filters?.maxRate !== undefined) {
      filteredLoads = filteredLoads.filter(load => {
        const rate = typeof load.rate === 'string' ? parseFloat(load.rate) : load.rate;
        return rate <= filters.maxRate!;
      });
      this.logger.debug(`SERVICE: Max rate filter ${filters.maxRate} filtered to ${filteredLoads.length} loads`);
    }

    // Apply weight range filters
    if (filters?.minWeight !== undefined) {
      filteredLoads = filteredLoads.filter(load => 
        load.weight >= filters.minWeight!
      );
      this.logger.debug(`SERVICE: Min weight filter ${filters.minWeight} filtered to ${filteredLoads.length} loads`);
    }

    if (filters?.maxWeight !== undefined) {
      filteredLoads = filteredLoads.filter(load => 
        load.weight <= filters.maxWeight!
      );
      this.logger.debug(`SERVICE: Max weight filter ${filters.maxWeight} filtered to ${filteredLoads.length} loads`);
    }

    // Apply date range filters
    if (filters?.pickupDateStart) {
      filteredLoads = filteredLoads.filter(load => {
        if (!load.pickupDateTime) return false;
        const pickupDate = new Date(load.pickupDateTime);
        return pickupDate >= filters.pickupDateStart!;
      });
      this.logger.debug(`SERVICE: Pickup date start filter ${filters.pickupDateStart.toISOString()} filtered to ${filteredLoads.length} loads`);
    }

    if (filters?.pickupDateEnd) {
      filteredLoads = filteredLoads.filter(load => {
        if (!load.pickupDateTime) return false;
        const pickupDate = new Date(load.pickupDateTime);
        return pickupDate <= filters.pickupDateEnd!;
      });
      this.logger.debug(`SERVICE: Pickup date end filter ${filters.pickupDateEnd.toISOString()} filtered to ${filteredLoads.length} loads`);
    }

    if (filters?.deliveryDateStart) {
      filteredLoads = filteredLoads.filter(load => {
        if (!load.deliveryDateTime) return false;
        const deliveryDate = new Date(load.deliveryDateTime);
        return deliveryDate >= filters.deliveryDateStart!;
      });
      this.logger.debug(`SERVICE: Delivery date start filter ${filters.deliveryDateStart.toISOString()} filtered to ${filteredLoads.length} loads`);
    }

    if (filters?.deliveryDateEnd) {
      filteredLoads = filteredLoads.filter(load => {
        if (!load.deliveryDateTime) return false;
        const deliveryDate = new Date(load.deliveryDateTime);
        return deliveryDate <= filters.deliveryDateEnd!;
      });
      this.logger.debug(`SERVICE: Delivery date end filter ${filters.deliveryDateEnd.toISOString()} filtered to ${filteredLoads.length} loads`);
    }

    // Apply geographic filtering
    if (filters?.geoCenter && filters?.geoRadius) {
      const centerCoords = GeographicUtils.parseCoordinates(filters.geoCenter);
      if (centerCoords) {
        filteredLoads = await this.applyGeographicFilter(
          filteredLoads, 
          centerCoords, 
          filters.geoRadius, 
          filters.geoFilterOrigin ?? true
        );
        this.logger.debug(`SERVICE: Geographic filter (center: ${filters.geoCenter}, radius: ${filters.geoRadius} miles) filtered to ${filteredLoads.length} loads`);
      } else {
        this.logger.warn(`SERVICE: Invalid geographic center coordinates: ${filters.geoCenter}`);
      }
    }

    // Store total count before pagination
    const totalCount = filteredLoads.length;

    // Apply sorting
    if (filters?.sortBy) {
      filteredLoads = this.applySorting(filteredLoads, filters.sortBy, filters.sortOrder);
      this.logger.debug(`SERVICE: Applied sorting by ${filters.sortBy} ${filters.sortOrder}`);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const pageSize = filters?.pageSize || 25;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedLoads = filteredLoads.slice(startIndex, endIndex);

    const totalTime = Date.now() - startTime;
    this.logger.log(`SERVICE: getAvailableLoadsWithFilters completed in ${totalTime}ms - ${totalCount} total, ${paginatedLoads.length} on page ${page}`);

    return {
      loads: paginatedLoads,
      totalCount,
      page,
      pageSize
    };
  }

  /**
   * Apply geographic filtering to loads
   */
  private async applyGeographicFilter(
    loads: any[], 
    centerCoords: Coordinates, 
    radiusMiles: number, 
    filterOrigin: boolean = true
  ): Promise<any[]> {
    const filteredLoads: any[] = [];

    for (const load of loads) {
      try {
        const locationToCheck = filterOrigin ? load.origin : load.destination;
        if (!locationToCheck) continue;

        // Parse location string to get city and state
        const locationParts = GeographicUtils.parseLocation(locationToCheck);
        if (!locationParts) continue;

        // Try to get coordinates for the location
        let locationCoords = GeographicUtils.getCityCoordinates(locationParts.city, locationParts.state);
        
        // If we don't have coordinates in our lookup table, skip this load
        // In production, you would integrate with a geocoding service here
        if (!locationCoords) {
          this.logger.debug(`SERVICE: No coordinates found for ${locationToCheck}, skipping geographic filter`);
          continue;
        }

        // Check if location is within radius
        if (GeographicUtils.isWithinRadius(centerCoords, locationCoords, radiusMiles)) {
          // Add distance information to the load for display
          const distance = GeographicUtils.calculateDistance(centerCoords, locationCoords);
          filteredLoads.push({
            ...load,
            distance: Math.round(distance * 10) / 10 // Round to 1 decimal place
          });
        }
      } catch (error) {
        this.logger.warn(`SERVICE: Error applying geographic filter to load ${load.id}: ${error.message}`);
        // Continue processing other loads
      }
    }

    return filteredLoads;
  }

  /**
   * Apply sorting to loads
   */
  private applySorting(loads: any[], sortBy: SortField, sortOrder: SortOrder = SortOrder.ASC): any[] {
    return loads.sort((a, b) => {
      let valueA: any;
      let valueB: any;

      switch (sortBy) {
        case SortField.PICKUP_DATE:
          valueA = a.pickupDateTime ? new Date(a.pickupDateTime).getTime() : 0;
          valueB = b.pickupDateTime ? new Date(b.pickupDateTime).getTime() : 0;
          break;
        case SortField.DELIVERY_DATE:
          valueA = a.deliveryDateTime ? new Date(a.deliveryDateTime).getTime() : 0;
          valueB = b.deliveryDateTime ? new Date(b.deliveryDateTime).getTime() : 0;
          break;
        case SortField.RATE:
          valueA = typeof a.rate === 'string' ? parseFloat(a.rate) || 0 : a.rate || 0;
          valueB = typeof b.rate === 'string' ? parseFloat(b.rate) || 0 : b.rate || 0;
          break;
        case SortField.DISTANCE:
          valueA = a.distance || 0;
          valueB = b.distance || 0;
          break;
        case SortField.WEIGHT:
          valueA = a.weight || 0;
          valueB = b.weight || 0;
          break;
        case SortField.EQUIPMENT:
          valueA = a.equipment || '';
          valueB = b.equipment || '';
          break;
        case SortField.CREATED_AT:
        default:
          valueA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          valueB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          break;
      }

      // Handle string comparisons
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        valueA = valueA.toLowerCase();
        valueB = valueB.toLowerCase();
      }

      let comparison = 0;
      if (valueA < valueB) comparison = -1;
      else if (valueA > valueB) comparison = 1;

      return sortOrder === SortOrder.DESC ? -comparison : comparison;
    });
  }

  async syncAllLoadsFromAirtable(syncAll: boolean = false): Promise<{ syncedCount: number; errors: any[] }> {
    this.logger.log(`SERVICE: syncAllLoadsFromAirtable called with syncAll=${syncAll}`);
    this.logger.log(`Starting ${syncAll ? 'full' : 'optimized'} batch sync from Airtable table: ${this.tableName}`);
    let syncedCount = 0;
    const errors: any[] = [];

    try {
      const selectOptions: any = {
        view: 'Grid view',
        fields: [
          'Order ID.',
          'Status',
          'Pickup City Lookup',
          'Pickup State Lookup', 
          'Delivery City Lookup',
          'Delivery State Lookup',
          'Equipment Required',
          'Weight (lbs)',
          'Rate to Carrier',
          'Pickup Date & Time',
          'Delivery Date & Time',
          'Temperature',
          'Synced to API',
          // Additional fields (not visible on loadboard)
          'PO Number',
          'SO Number',
          'Pickup Number',
          'Delivery No.',
          'Shipper Address',
          'Receiver Name',
          'Receiver Address',
          'Inv Status',
          'Carrier',
          'Cases',
          'Pallets'
        ],
        maxRecords: syncAll ? 5000 : 500 // 5000 for all sync, 500 for visible only
      };

      if (!syncAll) {
        // Only sync loads with "Synced to API" = true to match the loadboard filter
        selectOptions.filterByFormula = `AND({Synced to API}, OR({Status} = "Available", {Status} = "Booking Requested"))`;
        this.logger.log('SERVICE: Using filtered sync (Synced to API = true only)');
      } else {
        // Sync all loads but filter out obviously incomplete ones
        selectOptions.filterByFormula = `NOT({Order ID.} = BLANK())`;
        this.logger.log('SERVICE: Using full sync (all loads with Order ID)');
      }

      const records = await this.base(this.tableName).select(selectOptions).all();

      this.logger.log(`SERVICE: Fetched ${records.length} records from Airtable (${syncAll ? 'all loads' : 'Synced to API = true only'}).`);

      // Process in batches to avoid timeout
      const BATCH_SIZE = syncAll ? 20 : 10; // Larger batches for full sync
      for (let i = 0; i < records.length; i += BATCH_SIZE) {
        const batch = records.slice(i, i + BATCH_SIZE);
        this.logger.log(`SERVICE: Processing batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(records.length/BATCH_SIZE)} (${batch.length} records)`);
        
        // Process batch in parallel with timeout protection
        const batchPromises = batch.map(async (record) => {
          return Promise.race([
            this.processSingleRecord(record),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Record processing timeout')), 8000) // Longer timeout for full sync
            )
          ]);
        });
        
        const batchResults = await Promise.allSettled(batchPromises);
        
        // Process results
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            syncedCount++;
          } else {
            const record = batch[index];
            this.logger.error(`SERVICE: Error processing record ${record.id}: ${result.reason}`);
            errors.push({ airtableRecordId: record.id, error: result.reason });
          }
        });
        
        // Small delay between batches to prevent overwhelming the database
        if (i + BATCH_SIZE < records.length) {
          await new Promise(resolve => setTimeout(resolve, syncAll ? 50 : 100)); // Shorter delay for full sync
        }
      }

      this.logger.log(`SERVICE: Optimized sync completed. Synced ${syncedCount} records. Encountered ${errors.length} errors.`);
      return { syncedCount, errors };

    } catch (error) {
      this.logger.error('SERVICE: Major error during optimized sync from Airtable:', error);
      throw new InternalServerErrorException(`Failed to sync loads from Airtable: ${error.message}`);
    }
  }

  private async processSingleRecord(record: any): Promise<void> {
    const airtableRecordId = record.id;
    
    const mappedData: any = {
      airtableRecordId: airtableRecordId,
      proNumber: record.fields['Order ID.'] as string || undefined,
      originCity: record.fields['Pickup City Lookup'] ? (record.fields['Pickup City Lookup'] as string[])[0] : undefined,
      originState: record.fields['Pickup State Lookup'] ? (record.fields['Pickup State Lookup'] as string[])[0] : undefined,
      destinationCity: record.fields['Delivery City Lookup'] ? (record.fields['Delivery City Lookup'] as string[])[0] : undefined,
      destinationState: record.fields['Delivery State Lookup'] ? (record.fields['Delivery State Lookup'] as string[])[0] : undefined,
      equipmentRequired: record.fields['Equipment Required'] as string || undefined,
      weightLbs: record.fields['Weight (lbs)'] as number || undefined,
      rate: record.fields['Rate to Carrier'] as number || undefined,
      status: this.mapAirtableStatusToEnum(record.fields['Status'] as string),
      temperature: record.fields['Temperature'] as string || undefined,
      rawAirtableData: record.fields,
      // Additional fields (not visible on loadboard)
      poNumber: record.fields['PO Number'] as string || undefined,
      soNumber: record.fields['SO Number'] as string || undefined,
      pickupNumber: record.fields['Pickup Number'] as string || undefined,
      deliveryNumber: record.fields['Delivery No.'] as string || undefined,
      shipperAddress: record.fields['Shipper Address'] as string || undefined,
      receiverName: record.fields['Receiver Name'] as string || undefined,
      receiverAddress: record.fields['Receiver Address'] as string || undefined,
      invStatus: this.mapAirtableInvStatusToEnum(record.fields['Inv Status'] as string),
      carrier: record.fields['Carrier'] as string || undefined,
      cases: record.fields['Cases'] as number || undefined,
      pallets: record.fields['Pallets'] as number || undefined,
    };

    if (record.fields['Pickup Date & Time']) {
      const pickupDate = new Date(record.fields['Pickup Date & Time'] as string);
      if (!isNaN(pickupDate.getTime())) {
        mappedData.pickupDateUtc = pickupDate;
      }
    }

    if (record.fields['Delivery Date & Time']) {
      const deliveryDate = new Date(record.fields['Delivery Date & Time'] as string);
      if (!isNaN(deliveryDate.getTime())) {
        mappedData.deliveryDateUtc = deliveryDate;
      }
    }
    
    // Remove undefined fields before upsert
    Object.keys(mappedData).forEach(key => {
      if (mappedData[key] === undefined) {
        delete mappedData[key];
      }
    });

    await this.prisma.load.upsert({
      where: { airtableRecordId: airtableRecordId },
      update: mappedData,
      create: mappedData,
    });
  }

  async requestLoadBooking(
    loadId: string, // This is expected to be the Airtable Record ID
    requestingUserClerkId: string,
    bookingDetailsDto: RequestBookingDetailsDto,
  ): Promise<LoadBookingConfirmation> {
    this.logger.log(
      `SERVICE: User ${requestingUserClerkId} requesting to book load ${loadId} with details: ${JSON.stringify(bookingDetailsDto)}`,
    );

    // 1. Fetch the load details directly from Airtable using the provided loadId (airtableRecordId)
    const airtableRecord = await this.getAirtableRecordById(loadId);
    const loadFields = airtableRecord.fields;

    // Check if the load is in a bookable status, e.g., 'Available'
    const airtableStatus = loadFields['Status'] as string;
    if (airtableStatus !== 'Available') {
      this.logger.warn(`SERVICE: Load ${loadId} is not available for booking. Current Airtable status: ${airtableStatus}`);
      throw new BadRequestException(`Load is not available for booking. Status: ${airtableStatus}`);
    }

    const requestingUser = await this.prisma.user.findUnique({
      where: { airtableUserId: requestingUserClerkId },
      include: { carrierProfile: true },
    });

    if (!requestingUser) {
      this.logger.error(`SERVICE: User with Clerk ID ${requestingUserClerkId} not found.`);
      throw new NotFoundException('Requesting user not found.');
    }

    if (requestingUser.role !== Role.CARRIER && requestingUser.role !== Role.ADMIN) {
      this.logger.warn(
        `SERVICE: User ${requestingUserClerkId} is not a CARRIER or ADMIN. Role: ${requestingUser.role}. Cannot book load.`,
      );
      throw new ForbiddenException('Only users with the CARRIER or ADMIN role can book loads.');
    }

    if (!requestingUser.carrierProfile) {
      this.logger.warn(
        `SERVICE: User ${requestingUserClerkId} (Role: CARRIER) has no carrier profile. Cannot book load.`,
      );
      throw new ForbiddenException(
        'Your carrier profile is incomplete. Please complete your carrier profile to book loads.',
      );
    }

    // Prepare fields for Airtable update
    // Ensure these field names EXACTLY match your Airtable field names
    const fieldsToUpdate: FieldSet = {
      'Status': 'Booking Requested', // First step: mark as booking requested for admin review
      'Booking Request - User ID': requestingUser.carrierProfile.companyName ?? 'Unknown Company', // Use carrier company name instead of Clerk ID
      'Booking Request - Timestamp': new Date().toISOString(),
      // Carrier Details - removing redundant company name field since it's now in User ID
      'Booking Request - Carrier MC Number': requestingUser.carrierProfile.mcNumber ?? undefined,
      'Booking Request - Carrier Contact Name': requestingUser.carrierProfile.contact_name ?? 
        (`${requestingUser.firstName || ''} ${requestingUser.lastName || ''}`.trim() || 
        undefined),
      'Booking Request - Carrier Contact Email': requestingUser.carrierProfile.contact_email ?? requestingUser.email ?? undefined,
      'Booking Request - Carrier Contact Phone': requestingUser.carrierProfile.contact_phone ?? undefined,
      // Booking Submission Details
      'Booking Request - Is Truck Empty': bookingDetailsDto.isTruckEmpty,
      'Booking Request - Truck Empty Location': bookingDetailsDto.truckEmptyLocation,
      'Booking Request - ETA to Shipper': bookingDetailsDto.etaToShipper,
      'Booking Request - Notes': bookingDetailsDto.notes ?? undefined,
      // Add the accepted rate
      'Booking Request - Rate Accepted': (loadFields['Rate to Carrier'] as number) ?? undefined,
      // Original Load Details for reference in Airtable (optional, but can be useful)
      // These would be copied to new fields or handled by Airtable automation if needed in a separate "Booking Requests" table
      // For now, we're updating the existing load record.
    };
    
    // Remove any undefined fields to avoid issues with Airtable API
    Object.keys(fieldsToUpdate).forEach(key => {
        if (fieldsToUpdate[key] === undefined) {
          delete fieldsToUpdate[key];
        }
      });


    try {
      // Update Airtable record
      await this.base(this.tableName).update([
        {
          id: loadId, // Airtable Record ID
          fields: fieldsToUpdate,
        },
      ], { typecast: true });
      this.logger.log(`SERVICE: Successfully updated Airtable record ${loadId} for booking request by user ${requestingUserClerkId}.`);

      // Send real-time notification for booking confirmation
      try {
        const loadDetails = {
          airtableRecordId: airtableRecord.id,
          proNumber: airtableRecord.fields['Order ID.'],
          rate: airtableRecord.fields['Rate to Carrier'],
          origin: `${airtableRecord.fields['Pickup City Lookup']?.[0] || ''}, ${airtableRecord.fields['Pickup State Lookup']?.[0] || ''}`,
          destination: `${airtableRecord.fields['Delivery City Lookup']?.[0] || ''}, ${airtableRecord.fields['Delivery State Lookup']?.[0] || ''}`,
        };
        
        await this.notificationsService.broadcastBookingConfirmation(
          loadId, 
          requestingUserClerkId, 
          loadDetails
        );
      } catch (notificationError) {
        this.logger.error(`Failed to send booking confirmation notification: ${notificationError.message}`, notificationError.stack);
        // Don't fail the booking if notifications fail
      }

      return {
        message: 'Booking request submitted successfully! Awaiting admin approval.',
        loadId: airtableRecord.id, 
        carrierUserId: requestingUserClerkId,
      };
    } catch (error) {
      this.logger.error(`SERVICE: Failed to update Airtable record ${loadId} for booking request: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to log booking request. Please try again or contact support.');
    }
  }

  async getAssignedLoadsForCarrier(airtableUserId: string): Promise<any[]> {
    this.logger.log(`SERVICE: getAssignedLoadsForCarrier called for user ${airtableUserId}`);

    // First, get the user and their carrier profile
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId: airtableUserId },
      include: { carrierProfile: true },
    });

    if (!user) {
      this.logger.error(`SERVICE: User with Airtable ID ${airtableUserId} not found.`);
      throw new NotFoundException('User not found.');
    }

    // Allow both CARRIER and ADMIN users to view assigned loads
    if (user.role !== Role.CARRIER && user.role !== Role.ADMIN) {
      this.logger.warn(`SERVICE: User ${airtableUserId} is not a CARRIER or ADMIN. Role: ${user.role}`);
      throw new ForbiddenException('Only users with the CARRIER or ADMIN role can view assigned loads.');
    }

    // If ADMIN user, show all assigned loads instead of just their own
    if (user.role === Role.ADMIN) {
      this.logger.log(`SERVICE: Admin user ${airtableUserId} requesting all assigned loads`);
      
      const allAssignedLoads = await this.prisma.load.findMany({
        where: {
          status: {
            in: [LoadStatus.ASSIGNED, LoadStatus.DELIVERED_EMPTY],
          },
          awardedToCarrierProfileId: { not: null }, // Only loads that are actually assigned
        },
        include: {
          awardedToCarrierProfile: {
            select: {
              companyName: true,
              contact_name: true,
              contact_email: true,
              contact_phone: true,
            },
          },
        },
        orderBy: {
          pickupDateUtc: 'asc',
        },
      });

      this.logger.log(`SERVICE: Found ${allAssignedLoads.length} assigned loads for admin user`);
      return allAssignedLoads;
    }

    // For CARRIER users, check they have a carrier profile
    if (!user.carrierProfile) {
      this.logger.warn(`SERVICE: User ${airtableUserId} (Role: CARRIER) has no carrier profile.`);
      throw new ForbiddenException('Carrier profile not found.');
    }

    // Get loads assigned to this carrier from our database
    const assignedLoads = await this.prisma.load.findMany({
      where: {
        awardedToCarrierProfileId: user.carrierProfile.id,
        status: {
          in: [LoadStatus.ASSIGNED, LoadStatus.DELIVERED_EMPTY], // Include loads that are assigned or completed
        },
      },
      include: {
        awardedToCarrierProfile: {
          select: {
            companyName: true,
            contact_name: true,
            contact_email: true,
            contact_phone: true,
          },
        },
      },
      orderBy: {
        pickupDateUtc: 'asc',
      },
    });

    // For each assigned load, also fetch the latest data from Airtable to ensure we have up-to-date information
    // Use Promise.allSettled to handle individual failures gracefully
    const loadsWithAirtableData = await Promise.allSettled(
      assignedLoads.map(async (load) => {
        try {
          // Add timeout to Airtable requests
          const airtableRecord = await Promise.race([
            this.getAirtableRecordById(load.airtableRecordId),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Airtable timeout')), 5000)
            )
          ]) as any;
          const fields = airtableRecord.fields;

          return {
            id: load.id,
            airtableRecordId: load.airtableRecordId,
            proNumber: fields['Order ID.'] || load.proNumber,
            status: fields['Status'] || load.status,
            rate: fields['Rate to Carrier'] || load.rate,
            origin: `${fields['Pickup City Lookup']?.[0] || load.originCity}, ${fields['Pickup State Lookup']?.[0] || load.originState}`,
            destination: `${fields['Delivery City Lookup']?.[0] || load.destinationCity}, ${fields['Delivery State Lookup']?.[0] || load.destinationState}`,
            pickupDateTime: fields['Pickup Date & Time'] || load.pickupDateUtc?.toISOString(),
            deliveryDateTime: fields['Delivery Date & Time'] || load.deliveryDateUtc?.toISOString(),
            pickupAppointmentTime: fields['Pickup Appt.'],
            deliveryAppointmentTime: fields['Delivery Appt.'],
            weight: fields['Weight (lbs)'] || load.weightLbs,
            equipment: fields['Equipment Required'] || load.equipmentRequired,
            temperature: fields['Temperature'] || load.temperature,
            // Additional fields that might be useful for carriers
            distance: fields['Distance (Miles)'],
            brokerCompany: fields['Broker Company'],
            shipperCompany: fields['Shipper Company'],
            receiverCompany: fields['Receiver Company'],
            specialInstructions: fields['Special Instructions'],
            // Carrier information
            awardedToCarrier: load.awardedToCarrierProfile,
            // Document tracking
            bolUploaded: !!load.bolFileUrl,
            podUploaded: !!load.podFileUrl,
            invoiceUploaded: !!load.invoiceFileUrl,
            bolUploadedAt: load.bolUploadedAt?.toISOString(),
            podUploadedAt: load.podUploadedAt?.toISOString(),
            invoiceUploadedAt: load.invoiceUploadedAt?.toISOString(),
            // Payment tracking
            paymentStatus: load.paymentStatus?.toLowerCase(),
            paymentDueDate: load.paymentDueDate?.toISOString(),
            paidAt: load.paidAt?.toISOString(),
            paymentAmount: load.paymentAmount,
            paymentNotes: load.paymentNotes,
          };
        } catch (error) {
          this.logger.warn(`SERVICE: Could not fetch Airtable data for load ${load.airtableRecordId}: ${error.message}`);
          // Fallback to database data if Airtable is unavailable
          return {
            id: load.id,
            airtableRecordId: load.airtableRecordId,
            proNumber: load.proNumber,
            status: load.status,
            rate: load.rate,
            origin: `${load.originCity}, ${load.originState}`,
            destination: `${load.destinationCity}, ${load.destinationState}`,
            pickupDateTime: load.pickupDateUtc?.toISOString(),
            deliveryDateTime: load.deliveryDateUtc?.toISOString(),
            weight: load.weightLbs,
            equipment: load.equipmentRequired,
            temperature: load.temperature,
            awardedToCarrier: load.awardedToCarrierProfile,
            // Document tracking
            bolUploaded: !!load.bolFileUrl,
            podUploaded: !!load.podFileUrl,
            invoiceUploaded: !!load.invoiceFileUrl,
            bolUploadedAt: load.bolUploadedAt?.toISOString(),
            podUploadedAt: load.podUploadedAt?.toISOString(),
            invoiceUploadedAt: load.invoiceUploadedAt?.toISOString(),
            // Payment tracking
            paymentStatus: load.paymentStatus?.toLowerCase(),
            paymentDueDate: load.paymentDueDate?.toISOString(),
            paidAt: load.paidAt?.toISOString(),
            paymentAmount: load.paymentAmount,
            paymentNotes: load.paymentNotes,
          };
        }
      })
    );

    // Extract successful results and log failures
    const successfulLoads = loadsWithAirtableData
      .filter((result) => result.status === 'fulfilled')
      .map((result) => (result as PromiseFulfilledResult<any>).value);
    
    const failures = loadsWithAirtableData.filter((result) => result.status === 'rejected');
    if (failures.length > 0) {
      this.logger.warn(`SERVICE: ${failures.length} loads failed to fetch Airtable data, using database fallback`);
    }

    this.logger.log(`SERVICE: Found ${successfulLoads.length} assigned loads for carrier ${user.carrierProfile.companyName}`);
    return successfulLoads;
  }

  async uploadLoadDocument(
    loadId: string,
    documentType: 'bol' | 'pod' | 'invoice',
    fileUrl: string,
    airtableUserId: string
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`SERVICE: uploadLoadDocument called for load ${loadId}, type ${documentType} by user ${airtableUserId}`);

    // Verify the user has access to this load
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId },
      include: { carrierProfile: true },
    });

    if (!user || (user.role !== Role.CARRIER && user.role !== Role.ADMIN)) {
      throw new ForbiddenException('Access denied');
    }

    // For ADMIN users, allow uploading documents for any load
    if (user.role === Role.ADMIN) {
      this.logger.log(`SERVICE: Admin user ${airtableUserId} uploading ${documentType} for load ${loadId}`);
    } else {
      // For CARRIER users, verify they have a carrier profile and access to this load
      if (!user.carrierProfile) {
        throw new ForbiddenException('Carrier profile not found');
      }

      // Verify this load is assigned to the carrier
      const load = await this.prisma.load.findFirst({
        where: {
          id: loadId,
          awardedToCarrierProfileId: user.carrierProfile.id,
        },
      });

      if (!load) {
        throw new ForbiddenException('Load not found or not assigned to you');
      }
    }

    // Update the appropriate document field
    const updateData: any = {
      updatedAt: new Date(),
    };

    switch (documentType) {
      case 'bol':
        updateData.bolFileUrl = fileUrl;
        updateData.bolUploadedAt = new Date();
        break;
      case 'pod':
        updateData.podFileUrl = fileUrl;
        updateData.podUploadedAt = new Date();
        break;
      case 'invoice':
        updateData.invoiceFileUrl = fileUrl;
        updateData.invoiceUploadedAt = new Date();
        break;
    }

    try {
      await this.prisma.load.update({
        where: { id: loadId },
        data: updateData,
      });

      this.logger.log(`SERVICE: Successfully uploaded ${documentType} for load ${loadId}`);
      return {
        success: true,
        message: `${documentType.toUpperCase()} uploaded successfully`,
      };
    } catch (error) {
      this.logger.error(`SERVICE: Failed to update load ${loadId} with ${documentType}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update document information');
    }
  }

  async assignLoadToCarrier(
    airtableRecordId: string,
    clerkUserId: string
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`SERVICE: assignLoadToCarrier called for load ${airtableRecordId} to user ${clerkUserId}`);

    // Find the user and their carrier profile
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId: clerkUserId },
      include: { carrierProfile: true },
    });

    if (!user || (user.role !== Role.CARRIER && user.role !== Role.ADMIN)) {
      throw new ForbiddenException('User not found or not a valid carrier or admin');
    }

    // For ADMIN users, allow assigning loads (for now, they need a carrier profile to assign to)
    // For CARRIER users, verify they have a carrier profile
    if (!user.carrierProfile) {
      throw new ForbiddenException('Carrier profile not found. Cannot assign load without a carrier profile.');
    }

    // Find the load
    const load = await this.prisma.load.findUnique({
      where: { airtableRecordId },
    });

    if (!load) {
      throw new NotFoundException('Load not found');
    }

    // Check if load is in a state that can be assigned
    if (load.status !== LoadStatus.ASSIGNED) {
      throw new BadRequestException(`Load status must be "Assigned" to assign to carrier. Current status: ${load.status}`);
    }

    try {
      // Update the load to assign it to the carrier
      const updatedLoad = await this.prisma.load.update({
        where: { airtableRecordId },
        data: {
          awardedToCarrierProfileId: user.carrierProfile.id,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`SERVICE: Successfully assigned load ${airtableRecordId} to carrier ${user.carrierProfile.companyName}`);
      
      return {
        success: true,
        message: `Load successfully assigned to ${user.carrierProfile.companyName}`,
      };
    } catch (error) {
      this.logger.error(`SERVICE: Failed to assign load ${airtableRecordId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to assign load to carrier');
    }
  }

  async unassignLoadFromCarrier(
    airtableRecordId: string,
    adminClerkUserId: string
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`SERVICE: unassignLoadFromCarrier called for load ${airtableRecordId} by admin ${adminClerkUserId}`);

    // Verify the user is an admin
    const adminUser = await this.prisma.user.findUnique({
      where: { airtableUserId: adminClerkUserId },
    });

    if (!adminUser || adminUser.role !== Role.ADMIN) {
      throw new ForbiddenException('Only admins can unassign loads');
    }

    // Find the load
    const load = await this.prisma.load.findUnique({
      where: { airtableRecordId },
      include: {
        awardedToCarrierProfile: {
          select: {
            companyName: true,
          },
        },
      },
    });

    if (!load) {
      throw new NotFoundException('Load not found');
    }

    if (!load.awardedToCarrierProfileId) {
      throw new BadRequestException('Load is not currently assigned to any carrier');
    }

    const previousCarrier = load.awardedToCarrierProfile?.companyName;

    try {
      // Update the load to remove carrier assignment
      const updatedLoad = await this.prisma.load.update({
        where: { airtableRecordId },
        data: {
          awardedToCarrierProfileId: null,
          updatedAt: new Date(),
        },
      });

      // Also update Airtable to change status back to Available
      try {
        await this.base(this.tableName).update([
          {
            id: airtableRecordId,
            fields: {
              'Status': 'Available',
              'Booking Request - User ID': '', // Clear the booking request info
              'Booking Request - Timestamp': '', 
              'Booking Request - Carrier MC Number': '',
              'Booking Request - Carrier Contact Name': '',
              'Booking Request - Carrier Contact Email': '',
              'Booking Request - Carrier Contact Phone': '',
              'Booking Request - Is Truck Empty': '',
              'Booking Request - Truck Empty Location': '',
              'Booking Request - ETA to Shipper': '',
              'Booking Request - Notes': '',
              'Booking Request - Rate Accepted': '',
            },
          },
        ], { typecast: true });
        this.logger.log(`SERVICE: Successfully updated Airtable status to Available for load ${airtableRecordId}`);
      } catch (airtableError) {
        this.logger.warn(`SERVICE: Failed to update Airtable status: ${airtableError.message}`);
        // Continue even if Airtable update fails - database update is more important
      }

      this.logger.log(`SERVICE: Successfully unassigned load ${airtableRecordId} from carrier ${previousCarrier}`);
      
      return {
        success: true,
        message: `Load successfully unassigned from ${previousCarrier} and made available again`,
      };
    } catch (error) {
      this.logger.error(`SERVICE: Failed to unassign load ${airtableRecordId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to unassign load from carrier');
    }
  }

  async carrierCancelLoad(
    airtableRecordId: string,
    airtableUserId: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`SERVICE: carrierCancelLoad called for load ${airtableRecordId} by user ${airtableUserId}`);

    // Verify user exists and is either a carrier or admin
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId },
      include: { carrierProfile: true },
    });

    if (!user || (user.role !== Role.CARRIER && user.role !== Role.ADMIN)) {
      throw new ForbiddenException('Only carriers and admins can cancel loads');
    }

    // Verify the load exists
    const existingLoad = await this.prisma.load.findUnique({
      where: { airtableRecordId },
      include: { awardedToCarrierProfile: true },
    });

    if (!existingLoad) {
      throw new NotFoundException(`Load with Airtable ID ${airtableRecordId} not found`);
    }

    // For ADMIN users, allow canceling any load
    if (user.role === Role.ADMIN) {
      this.logger.log(`SERVICE: Admin user ${airtableUserId} canceling load ${airtableRecordId}`);
    } else {
      // For CARRIER users, verify they have a carrier profile and the load is assigned to them
      if (!user.carrierProfile) {
        throw new ForbiddenException('Carrier profile not found');
      }

      if (existingLoad.awardedToCarrierProfileId !== user.carrierProfile.id) {
        throw new ForbiddenException('You can only cancel loads assigned to you');
      }
    }

    try {
      // Update the load to remove carrier assignment
      const updatedLoad = await this.prisma.load.update({
        where: { airtableRecordId },
        data: {
          awardedToCarrierProfileId: null,
          updatedAt: new Date(),
        },
      });

      // Update Airtable to change status back to Available and add cancellation info
      try {
        await this.base(this.tableName).update([
          {
            id: airtableRecordId,
            fields: {
              'Status': 'Available',
              'Booking Request - User ID': '', // Clear the booking request info
              'Booking Request - Timestamp': '', 
              'Booking Request - Carrier MC Number': '',
              'Booking Request - Carrier Contact Name': '',
              'Booking Request - Carrier Contact Email': '',
              'Booking Request - Carrier Contact Phone': '',
              'Booking Request - Is Truck Empty': '',
              'Booking Request - Truck Empty Location': '',
              'Booking Request - ETA to Shipper': '',
              'Booking Request - Notes': reason ? `CANCELLED BY CARRIER: ${reason}` : 'CANCELLED BY CARRIER',
              'Booking Request - Rate Accepted': '',
            },
          },
        ], { typecast: true });
        this.logger.log(`SERVICE: Successfully updated Airtable status to Available for cancelled load ${airtableRecordId}`);
      } catch (airtableError) {
        this.logger.warn(`SERVICE: Failed to update Airtable status: ${airtableError.message}`);
        // Continue even if Airtable update fails - database update is more important
      }

      this.logger.log(`SERVICE: ${user.role === Role.ADMIN ? 'Admin' : 'Carrier'} ${user.carrierProfile?.companyName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || airtableUserId} successfully cancelled load ${airtableRecordId}`);
      
      return {
        success: true,
        message: 'Load successfully cancelled and made available for other carriers',
      };
    } catch (error) {
      this.logger.error(`SERVICE: Error cancelling load: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to cancel load: ${error.message}`);
    }
  }

  // ===== BID MANAGEMENT METHODS =====

  async createBid(
    loadAirtableRecordId: string,
    airtableUserId: string,
    bidAmount: number,
    carrierNotes?: string
  ): Promise<{ success: boolean; bid: any; message: string }> {
    this.logger.log(`SERVICE: createBid called for load ${loadAirtableRecordId} by user ${airtableUserId} with amount $${bidAmount}`);

    // Verify user exists and is a carrier or admin
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId },
      include: { carrierProfile: true },
    });

    if (!user || (user.role !== Role.CARRIER && user.role !== Role.ADMIN)) {
      throw new ForbiddenException('Only carriers and admins can submit bids');
    }

    // For ADMIN users, they need to have or specify a carrier profile to bid with
    if (user.role === Role.ADMIN) {
      this.logger.log(`SERVICE: Admin user ${airtableUserId} creating bid for load ${loadAirtableRecordId}`);
      // For now, require admin to have a carrier profile to submit bids
      if (!user.carrierProfile) {
        throw new ForbiddenException('Admin users must have a carrier profile to submit bids');
      }
    } else {
      // For CARRIER users, verify they have a carrier profile
      if (!user.carrierProfile) {
        throw new ForbiddenException('Carrier profile not found');
      }
    }

    // Verify the load exists and is available for bidding
    let load = await this.prisma.load.findUnique({
      where: { airtableRecordId: loadAirtableRecordId },
    });

    if (!load) {
      this.logger.warn(`SERVICE: Load ${loadAirtableRecordId} not found in database, attempting to sync from Airtable...`);
      
      try {
        // Try to fetch and sync the record from Airtable
        const airtableRecord = await this.getAirtableRecordById(loadAirtableRecordId);
        
        // Check if it meets sync criteria
        const syncedToApi = airtableRecord.get('Synced to API');
        const status = airtableRecord.get('Status');
        
        if (!syncedToApi) {
          throw new NotFoundException(
            `Load ${loadAirtableRecordId} exists in Airtable but "Synced to API" is not checked. Please check the "Synced to API" checkbox in Airtable.`
          );
        }
        
        if (status !== 'Available' && status !== 'Booking Requested') {
          throw new NotFoundException(
            `Load ${loadAirtableRecordId} exists but status is "${status}". Only loads with status "Available" or "Booking Requested" can receive bids.`
          );
        }
        
        // Sync this specific record to the database
        this.logger.log(`SERVICE: Syncing load ${loadAirtableRecordId} from Airtable to database...`);
        await this.processSingleRecord(airtableRecord);
        
        // Try to find the load again after syncing
        load = await this.prisma.load.findUnique({
          where: { airtableRecordId: loadAirtableRecordId },
        });
        
        if (!load) {
          throw new Error('Failed to sync load to database');
        }
        
        this.logger.log(`SERVICE: Successfully synced load ${loadAirtableRecordId} to database for bidding`);
        
      } catch (syncError) {
        this.logger.error(`SERVICE: Failed to sync load ${loadAirtableRecordId}: ${syncError.message}`);
        
        if (syncError instanceof NotFoundException) {
          throw syncError; // Re-throw with specific message
        }
        
        throw new NotFoundException(
          `Load ${loadAirtableRecordId} not found or could not be synced. ` +
          `Error: ${syncError.message}. Please try running a manual sync or contact support.`
        );
      }
    }

    // Check if load is available for bidding
    if (load.status !== LoadStatus.AVAILABLE) {
      throw new BadRequestException(`Load is not available for bidding. Current status: ${load.status}`);
    }

    try {
      // Calculate bid expiration (24 hours from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Create or update bid (upsert)
      const bid = await this.prisma.bid.upsert({
        where: {
          load_carrier_unique_bid: {
            loadId: load.id,
            carrierProfileId: user.carrierProfile.id,
          },
        },
        update: {
          bidAmount,
          carrierNotes,
          status: 'ACTIVE',
          updatedAt: new Date(),
          expires_at: expiresAt,
          negotiation_status: 'OPEN',
          admin_response: 'PENDING'
        },
        create: {
          loadId: load.id,
          carrierProfileId: user.carrierProfile.id,
          bidAmount,
          carrierNotes,
          status: 'ACTIVE',
          expires_at: expiresAt,
          negotiation_status: 'OPEN',
          admin_response: 'PENDING'
        },
        include: {
          carrierProfile: true,
          load: true,
        },
      });

          // Update Airtable with bid information - use graceful error handling
      // CRITICAL FIX: Prevent Airtable sync failures from breaking bid creation (P2-T0A)
      // Bid creation should succeed even if Airtable field is missing or permissions are insufficient
      try {
        await this.updateAirtableBids(loadAirtableRecordId);
        this.logger.log(`SERVICE: Successfully synced bid to Airtable for load ${loadAirtableRecordId}`);
      } catch (airtableError) {
        // Log the Airtable sync error but don't fail the bid creation
        this.logger.warn(`SERVICE: Airtable sync failed for bid on load ${loadAirtableRecordId}: ${airtableError.message}`);
        this.logger.warn(`SERVICE: Bid was saved successfully to database, but Airtable sync failed - this is non-critical`);
        this.logger.warn(`SERVICE: Airtable sync error details:`, airtableError.stack);
      }

      this.logger.log(`SERVICE: Successfully created/updated bid for load ${loadAirtableRecordId} by carrier ${user.carrierProfile.companyName}`);
      
      // Send real-time notification for the bid
      try {
        const bidNotificationData: BidNotificationData = {
          bidId: bid.id,
          loadId: load.id,
          loadAirtableRecordId: loadAirtableRecordId,
          carrierUserId: airtableUserId,
          carrierCompanyName: user.carrierProfile.companyName || undefined,
          bidAmount,
          status: 'pending',
          carrierNotes,
        };
        
        await this.notificationsService.broadcastBidUpdate(bidNotificationData);
      } catch (notificationError) {
        this.logger.error(`Failed to send bid notification: ${notificationError.message}`, notificationError.stack);
        // Don't fail the bid creation if notifications fail
      }
      
      return {
        success: true,
        bid: {
          id: bid.id,
          bidAmount: bid.bidAmount,
          carrierNotes: bid.carrierNotes,
          status: bid.status,
          createdAt: bid.createdAt,
          updatedAt: bid.updatedAt,
          carrierName: bid.carrierProfile.companyName,
        },
        message: 'Bid submitted successfully',
      };
    } catch (error) {
      this.logger.error(`SERVICE: Error creating bid: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to submit bid: ${error.message}`);
    }
  }

  async getCarrierBids(airtableUserId: string): Promise<any[]> {
    this.logger.log(`SERVICE: getCarrierBids called for user ${airtableUserId}`);

    // Verify user exists and is either a carrier or admin
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId },
      include: { carrierProfile: true },
    });

    if (!user || (user.role !== Role.CARRIER && user.role !== Role.ADMIN)) {
      throw new ForbiddenException('Only carriers and admins can view bids');
    }

    try {
      // For ADMIN users, show all bids
      if (user.role === Role.ADMIN) {
        this.logger.log(`SERVICE: Admin user ${airtableUserId} requesting all bids`);
        
        const allBids = await this.prisma.bid.findMany({
          where: {
            status: 'ACTIVE', // Only show active bids, exclude withdrawn bids
          },
          include: {
            load: {
              include: {
                awardedToCarrierProfile: true,
                bids: {
                  include: {
                    carrierProfile: true,
                  },
                  where: {
                    status: 'ACTIVE', // Only active bids for comparison
                  },
                  orderBy: {
                    bidAmount: 'desc', // Highest bids first
                  },
                },
              },
            },
            carrierProfile: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        // Transform admin bids with additional context (same as carrier bids)
        const transformedAdminBids = await Promise.allSettled(
          allBids.map(async (bid) => {
            // Get load details from Airtable for the most up-to-date info
            let airtableData: FieldSet | null = null;
            try {
              const airtableRecord = await Promise.race([
                this.getAirtableRecordById(bid.load.airtableRecordId),
                new Promise((_, reject) => 
                  setTimeout(() => reject(new Error('Airtable timeout')), 3000)
                )
              ]) as any;
              airtableData = airtableRecord.fields;
            } catch (error) {
              this.logger.warn(`Could not fetch Airtable data for load ${bid.load.airtableRecordId}: ${error.message}`);
            }

            // Determine bid status
            const allLoadBids = bid.load.bids;
            const isHighestBid = allLoadBids.length > 0 && allLoadBids[0].id === bid.id;
            const isOutbid = !isHighestBid && allLoadBids.length > 1;
            const loadIsAssigned = !!bid.load.awardedToCarrierProfileId;
            const wonLoad = loadIsAssigned && bid.load.awardedToCarrierProfileId === bid.carrierProfileId;

            let bidStatus = 'ACTIVE';
            if (wonLoad) {
              bidStatus = 'WON';
            } else if (loadIsAssigned) {
              bidStatus = 'LOST';
            } else if (isOutbid) {
              bidStatus = 'OUTBID';
            } else if (isHighestBid) {
              bidStatus = 'LEADING';
            }

            return {
              id: bid.id,
              bidAmount: bid.bidAmount,
              carrierNotes: bid.carrierNotes,
              status: bidStatus,
              createdAt: bid.createdAt,
              updatedAt: bid.updatedAt,
              // Enhanced carrier information for admin view
              carrier: {
                id: bid.carrierProfile.id,
                companyName: bid.carrierProfile.companyName,
                mcNumber: bid.carrierProfile.mcNumber,
                contactName: bid.carrierProfile.contact_name,
                contactEmail: bid.carrierProfile.contact_email,
                contactPhone: bid.carrierProfile.contact_phone,
              },
              carrierName: bid.carrierProfile.companyName, // Keep for backward compatibility
              load: {
                id: bid.load.id,
                airtableRecordId: bid.load.airtableRecordId,
                proNumber: airtableData?.['Order ID.'] || bid.load.proNumber,
                status: airtableData?.['Status'] || bid.load.status,
                rate: airtableData?.['Rate to Carrier'] || bid.load.rate,
                origin: airtableData ? 
                  `${airtableData['Pickup City Lookup']?.[0] || ''}, ${airtableData['Pickup State Lookup']?.[0] || ''}` :
                  `${bid.load.originCity}, ${bid.load.originState}`,
                destination: airtableData ?
                  `${airtableData['Delivery City Lookup']?.[0] || ''}, ${airtableData['Delivery State Lookup']?.[0] || ''}` :
                  `${bid.load.destinationCity}, ${bid.load.destinationState}`,
                pickupDateTime: airtableData?.['Pickup Date & Time'] || bid.load.pickupDateUtc?.toISOString(),
                deliveryDateTime: airtableData?.['Delivery Date & Time'] || bid.load.deliveryDateUtc?.toISOString(),
                weight: airtableData?.['Weight (lbs)'] || bid.load.weightLbs,
                equipment: airtableData?.['Equipment Required'] || bid.load.equipmentRequired,
              },
              competingBids: {
                total: allLoadBids.length,
                highest: allLoadBids.length > 0 ? allLoadBids[0].bidAmount : null,
                myRank: allLoadBids.findIndex(b => b.id === bid.id) + 1,
              },
            };
          })
        );

        // Extract successful results
        const successfulAdminBids = transformedAdminBids
          .filter((result) => result.status === 'fulfilled')
          .map((result) => (result as PromiseFulfilledResult<any>).value);
        
        const adminFailures = transformedAdminBids.filter((result) => result.status === 'rejected');
        if (adminFailures.length > 0) {
          this.logger.warn(`SERVICE: ${adminFailures.length} admin bids failed to fetch Airtable data`);
        }

        this.logger.log(`SERVICE: Found ${successfulAdminBids.length} total bids from all carriers for admin user ${airtableUserId}`);
        return successfulAdminBids;
      }

      // For CARRIER users, verify they have a carrier profile
      if (!user.carrierProfile) {
        throw new ForbiddenException('Carrier profile not found');
      }

      const bids = await this.prisma.bid.findMany({
        where: {
          carrierProfileId: user.carrierProfile.id,
          status: 'ACTIVE', // Only show active bids, exclude withdrawn bids
        },
        include: {
          load: {
            include: {
              awardedToCarrierProfile: true,
              bids: {
                include: {
                  carrierProfile: true,
                },
                orderBy: {
                  bidAmount: 'desc', // Highest bids first
                },
              },
            },
          },
          carrierProfile: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Transform bids with additional context
      const transformedBids = await Promise.allSettled(
        bids.map(async (bid) => {
          // Get load details from Airtable for the most up-to-date info
          let airtableData: FieldSet | null = null;
          try {
            const airtableRecord = await Promise.race([
              this.getAirtableRecordById(bid.load.airtableRecordId),
              new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Airtable timeout')), 3000)
              )
            ]) as any;
            airtableData = airtableRecord.fields;
          } catch (error) {
            this.logger.warn(`Could not fetch Airtable data for load ${bid.load.airtableRecordId}: ${error.message}`);
          }

          // Determine bid status
          const allBids = bid.load.bids;
          const isHighestBid = allBids.length > 0 && allBids[0].id === bid.id;
          const isOutbid = !isHighestBid && allBids.length > 1;
          const loadIsAssigned = !!bid.load.awardedToCarrierProfileId;
          const wonLoad = loadIsAssigned && user.carrierProfile && bid.load.awardedToCarrierProfileId === user.carrierProfile.id;

          let bidStatus = 'ACTIVE';
          if (wonLoad) {
            bidStatus = 'WON';
          } else if (loadIsAssigned) {
            bidStatus = 'LOST';
          } else if (isOutbid) {
            bidStatus = 'OUTBID';
          } else if (isHighestBid) {
            bidStatus = 'LEADING';
          }

          return {
            id: bid.id,
            bidAmount: bid.bidAmount,
            carrierNotes: bid.carrierNotes,
            status: bidStatus,
            createdAt: bid.createdAt,
            updatedAt: bid.updatedAt,
            load: {
              id: bid.load.id,
              airtableRecordId: bid.load.airtableRecordId,
              proNumber: airtableData?.['Order ID.'] || bid.load.proNumber,
              status: airtableData?.['Status'] || bid.load.status,
              rate: airtableData?.['Rate to Carrier'] || bid.load.rate,
              origin: airtableData ? 
                `${airtableData['Pickup City Lookup']?.[0] || ''}, ${airtableData['Pickup State Lookup']?.[0] || ''}` :
                `${bid.load.originCity}, ${bid.load.originState}`,
              destination: airtableData ?
                `${airtableData['Delivery City Lookup']?.[0] || ''}, ${airtableData['Delivery State Lookup']?.[0] || ''}` :
                `${bid.load.destinationCity}, ${bid.load.destinationState}`,
              pickupDateTime: airtableData?.['Pickup Date & Time'] || bid.load.pickupDateUtc?.toISOString(),
              deliveryDateTime: airtableData?.['Delivery Date & Time'] || bid.load.deliveryDateUtc?.toISOString(),
              weight: airtableData?.['Weight (lbs)'] || bid.load.weightLbs,
              equipment: airtableData?.['Equipment Required'] || bid.load.equipmentRequired,
            },
            competingBids: {
              total: allBids.length,
              highest: allBids.length > 0 ? allBids[0].bidAmount : null,
              myRank: allBids.findIndex(b => b.id === bid.id) + 1,
            },
          };
        })
      );

      // Extract successful results
      const successfulBids = transformedBids
        .filter((result) => result.status === 'fulfilled')
        .map((result) => (result as PromiseFulfilledResult<any>).value);
      
      const bidFailures = transformedBids.filter((result) => result.status === 'rejected');
      if (bidFailures.length > 0) {
        this.logger.warn(`SERVICE: ${bidFailures.length} bids failed to fetch Airtable data`);
      }

      this.logger.log(`SERVICE: Found ${successfulBids.length} bids for carrier ${user.carrierProfile?.companyName || 'Unknown'}`);
      return successfulBids;
    } catch (error) {
      this.logger.error(`SERVICE: Error fetching carrier bids: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to fetch bids: ${error.message}`);
    }
  }

  // Helper method to ensure the Bids field exists in Airtable
  private async ensureBidsFieldExists(loadAirtableRecordId: string): Promise<string> {
    this.logger.log(`SERVICE: ensureBidsFieldExists called for load ${loadAirtableRecordId}`);
    
    try {
      // Get the current record to check available fields
      const currentRecord = await this.getAirtableRecordById(loadAirtableRecordId);
      const availableFields = Object.keys(currentRecord.fields);
      
      // ENHANCED DEBUGGING: Log all field information with more detail
      this.logger.log(`SERVICE: Total available fields count: ${availableFields.length}`);
      this.logger.log(`SERVICE: Complete Airtable fields list: ${JSON.stringify(availableFields, null, 2)}`);
      
      // Try different possible field names for bids (in order of preference)
      const possibleBidFieldNames = ['Bids', 'Carrier Bids', 'Bid', 'bids', 'carrier_bids', 'CarrierBids', 'BIDS'];
      
      this.logger.log(`SERVICE: Searching for bid field names: ${JSON.stringify(possibleBidFieldNames)}`);
      
      // Enhanced field matching with case-insensitive search
      for (const fieldName of possibleBidFieldNames) {
        this.logger.debug(`SERVICE: Checking for exact match: "${fieldName}"`);
        if (availableFields.includes(fieldName)) {
          this.logger.log(`SERVICE: ✅ FOUND exact match for bid field: "${fieldName}"`);
          return fieldName;
        }
      }
      
      // Try case-insensitive search as fallback
      this.logger.log(`SERVICE: No exact match found, trying case-insensitive search...`);
      for (const fieldName of possibleBidFieldNames) {
        const matchedField = availableFields.find(field => 
          field.toLowerCase() === fieldName.toLowerCase()
        );
        if (matchedField) {
          this.logger.log(`SERVICE: ✅ FOUND case-insensitive match: "${matchedField}" (searched for "${fieldName}")`);
          return matchedField;
        }
      }
      
      // Try partial matching for fields containing "bid"
      this.logger.log(`SERVICE: No case-insensitive match found, trying partial matching...`);
      const bidRelatedFields = availableFields.filter(field => 
        field.toLowerCase().includes('bid')
      );
      
      if (bidRelatedFields.length > 0) {
        this.logger.log(`SERVICE: ✅ FOUND bid-related fields: ${JSON.stringify(bidRelatedFields)}`);
        this.logger.log(`SERVICE: Using first bid-related field: "${bidRelatedFields[0]}"`);
        return bidRelatedFields[0];
      }

      // If no bid field exists, log detailed information for troubleshooting
      this.logger.error(`SERVICE: ❌ NO BID FIELD FOUND in Airtable for load ${loadAirtableRecordId}`);
      this.logger.error(`SERVICE: Available fields (${availableFields.length} total):`);
      availableFields.forEach((field, index) => {
        this.logger.error(`SERVICE:   ${index + 1}. "${field}"`);
      });
      this.logger.error(`SERVICE: Searched for field names: ${possibleBidFieldNames.join(', ')}`);
      this.logger.error(`SERVICE: RECOMMENDATION: Create a field named 'Bids' (Multiple Select type) in your Airtable base`);
      this.logger.error(`SERVICE: Field settings: Type = Multiple Select, Allow users to add new options = ON`);
      
      // Return the preferred field name for the error message
      return 'Bids';
    } catch (error) {
      this.logger.error(`SERVICE: Error checking Airtable fields: ${error.message}`, error.stack);
      return 'Bids'; // Fallback to default
    }
  }

  private async updateAirtableBids(loadAirtableRecordId: string): Promise<void> {
    this.logger.log(`SERVICE: updateAirtableBids called for load ${loadAirtableRecordId}`);

    try {
      // Get the load and all its bids
      const load = await this.prisma.load.findUnique({
        where: { airtableRecordId: loadAirtableRecordId },
        include: {
          bids: {
            include: {
              carrierProfile: true,
            },
            where: {
              status: 'ACTIVE',
            },
            orderBy: {
              bidAmount: 'desc',
            },
          },
        },
      });

      if (!load) {
        throw new NotFoundException(`Load with Airtable ID ${loadAirtableRecordId} not found`);
      }

      this.logger.log(`SERVICE: Found ${load.bids.length} active bids for load ${loadAirtableRecordId}`);

      // If no bids, we'll clear the field
      if (load.bids.length === 0) {
        this.logger.log(`SERVICE: No active bids found, clearing Airtable bid field`);
      }

      // Format bids for Airtable Multiple Select field
      // Each bid becomes a separate option in the format: "Company Name - $Amount"
      const bidOptions = load.bids.map(bid => 
        `${bid.carrierProfile.companyName} - $${bid.bidAmount.toLocaleString()}`
      );

      this.logger.log(`SERVICE: Formatted bid options for Airtable: ${JSON.stringify(bidOptions)}`);

      // Ensure the bid field exists and get its name
      const bidFieldName = await this.ensureBidsFieldExists(loadAirtableRecordId);

      // For Multiple Select fields in Airtable, we need to pass an array of option names
      // Airtable will automatically create new options if they don't exist (if field allows it)
      const updateFields: any = {};
      updateFields[bidFieldName] = bidOptions; // This should be an array of strings

      this.logger.log(`SERVICE: Updating Airtable field '${bidFieldName}' with ${bidOptions.length} bid options`);
      this.logger.log(`SERVICE: Bid options array: ${JSON.stringify(bidOptions)}`);

      // ENHANCED: Update Airtable with bid information and better error handling
      this.logger.log(`SERVICE: Attempting Airtable update for record ${loadAirtableRecordId}`);
      this.logger.log(`SERVICE: Update payload: ${JSON.stringify({ 
        id: loadAirtableRecordId, 
        fields: updateFields 
      }, null, 2)}`);
      
      const updateResult = await this.base(this.tableName).update([
        {
          id: loadAirtableRecordId,
          fields: updateFields,
        },
      ], { typecast: true });

      this.logger.log(`SERVICE: ✅ Airtable update successful for load ${loadAirtableRecordId}`);
      if (updateResult && updateResult.length > 0) {
        this.logger.log(`SERVICE: Updated record ID: ${updateResult[0].id}`);
        // Log the updated field value to verify it worked
        const updatedRecord = updateResult[0];
        if (updatedRecord.fields[bidFieldName]) {
          this.logger.log(`SERVICE: ✅ Updated ${bidFieldName} field value: ${JSON.stringify(updatedRecord.fields[bidFieldName])}`);
        } else {
          this.logger.warn(`SERVICE: ⚠️ Field ${bidFieldName} was updated but value not returned in response`);
        }
      } else {
        this.logger.warn(`SERVICE: ⚠️ Airtable update returned unexpected result: ${JSON.stringify(updateResult)}`);
      }
    } catch (error: any) {
      this.logger.error(`SERVICE: ❌ ERROR updating Airtable bids for load ${loadAirtableRecordId}: ${error.message}`, error.stack);
      
      // ENHANCED ERROR DEBUGGING: Log complete error details
      this.logger.error(`SERVICE: Full error object: ${JSON.stringify({
        message: error.message,
        statusCode: error.statusCode,
        error: error.error,
        type: error.type,
        stack: error.stack?.substring(0, 500) // Truncate stack trace
      }, null, 2)}`);
      
      // Get the field name for error reporting (fallback to 'Bids' if detection fails)
      let errorReportingFieldName = 'Bids';
      try {
        errorReportingFieldName = await this.ensureBidsFieldExists(loadAirtableRecordId);
      } catch (fieldError) {
        this.logger.warn(`SERVICE: Could not determine field name for error reporting: ${fieldError.message}`);
      }
      
      // Log the error type for better debugging
      if (error.statusCode) {
        this.logger.error(`SERVICE: Airtable HTTP status code: ${error.statusCode}`);
      }

      // ENHANCED: Check for specific Airtable error types
      const errorMessage = error.message?.toLowerCase() || '';
      
      if (errorMessage.includes('invalid_multiple_choice_options') || error.type === 'INVALID_MULTIPLE_CHOICE_OPTIONS') {
        this.logger.error(`SERVICE: ❌ MULTIPLE SELECT FIELD PERMISSION ERROR`);
        this.logger.error(`SERVICE: The 'Bids' field exists but doesn't allow new options`);
        this.logger.error(`SERVICE: SOLUTION: Go to Airtable > ${errorReportingFieldName} field > Field Options > Allow users to add new options = ON`);
        this.logger.error(`SERVICE: NOTE: API now includes 'typecast: true' parameter to auto-create new options`);
      }

      if (errorMessage.includes('unknown_field_name') || errorMessage.includes('does not exist') || error.type === 'UNKNOWN_FIELD_NAME') {
        this.logger.error(`SERVICE: ❌ FIELD NOT FOUND ERROR`);
        this.logger.error(`SERVICE: Field '${errorReportingFieldName}' not found in Airtable base`);
        this.logger.error(`SERVICE: SOLUTION: Create a 'Bids' field (Multiple Select type) in your Airtable base`);
        this.logger.error(`SERVICE: DEBUG: Use endpoint POST /api/v1/airtable-orders/{loadId}/debug-bids to see available fields`);
      }
      
      if (errorMessage.includes('unauthorized') || errorMessage.includes('forbidden') || error.statusCode === 401 || error.statusCode === 403) {
        this.logger.error(`SERVICE: ❌ AIRTABLE API PERMISSION ERROR`);
        this.logger.error(`SERVICE: API key may not have write permissions to this base/table`);
        this.logger.error(`SERVICE: SOLUTION: Check Airtable API key permissions and base access`);
      }
      
      if (errorMessage.includes('not_found') || error.statusCode === 404) {
        this.logger.error(`SERVICE: ❌ AIRTABLE RECORD/BASE NOT FOUND ERROR`);
        this.logger.error(`SERVICE: Record ${loadAirtableRecordId} or base may not exist`);
        this.logger.error(`SERVICE: SOLUTION: Check Airtable base ID and record ID are correct`);
      }
      
      // Provide detailed error information for monitoring and debugging
      const detailedErrorMessage = `Failed to sync bid to Airtable: ${error.message}. ` +
        `Field: '${errorReportingFieldName}'. ` +
        `Status: ${error.statusCode || 'Unknown'}. ` +
        `Type: ${error.type || 'Unknown'}. ` +
        `Recommendation: Check Airtable 'Bids' Multiple Select field permissions and API access.`;
      
      throw new InternalServerErrorException(detailedErrorMessage);
    }
  }

  async withdrawBid(
    bidId: string,
    clerkUserId: string
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`SERVICE: withdrawBid called for bid ${bidId} by user ${clerkUserId}`);

    // Verify user exists and is a carrier or admin (consistent with createBid)
    const user = await this.prisma.user.findUnique({
      where: { airtableUserId: clerkUserId },
      include: { carrierProfile: true },
    });

    if (!user || (user.role !== Role.CARRIER && user.role !== Role.ADMIN)) {
      throw new ForbiddenException('Only carriers and admins can withdraw bids');
    }

    // For both CARRIER and ADMIN users, verify they have a carrier profile
    if (!user.carrierProfile) {
      throw new ForbiddenException('Carrier profile not found');
    }

    try {
      // Find the bid and verify ownership
      const bid = await this.prisma.bid.findUnique({
        where: { id: bidId },
        include: {
          load: true,
          carrierProfile: true,
        },
      });

      if (!bid) {
        throw new NotFoundException(`Bid with ID ${bidId} not found`);
      }

      // Allow admins to withdraw any bid, but carriers can only withdraw their own
      if (user.role !== Role.ADMIN && bid.carrierProfileId !== user.carrierProfile.id) {
        throw new ForbiddenException('You can only withdraw your own bids');
      }
      
      // Log admin actions for audit purposes
      if (user.role === Role.ADMIN && bid.carrierProfileId !== user.carrierProfile?.id) {
        this.logger.log(`SERVICE: Admin ${(`${user.firstName || ''} ${user.lastName || ''}`.trim() || clerkUserId)} withdrawing bid ${bidId} on behalf of carrier ${bid.carrierProfile.companyName}`);
      }

      // Check if load is still available for bid withdrawal
      if (bid.load.awardedToCarrierProfileId) {
        throw new BadRequestException('Cannot withdraw bid - load has already been awarded');
      }

      // Update bid status to withdrawn
      await this.prisma.bid.update({
        where: { id: bidId },
        data: {
          status: 'WITHDRAWN',
          updatedAt: new Date(),
        },
      });

      // Update Airtable with updated bid information - use graceful error handling
      try {
        await this.updateAirtableBids(bid.load.airtableRecordId);
        this.logger.log(`SERVICE: Successfully synced bid withdrawal to Airtable for load ${bid.load.airtableRecordId}`);
      } catch (airtableError) {
        // Log the Airtable sync error but don't fail the bid withdrawal
        this.logger.warn(`SERVICE: Airtable sync failed for bid withdrawal on load ${bid.load.airtableRecordId}: ${airtableError.message}`);
        this.logger.warn(`SERVICE: Bid was withdrawn successfully in database, but Airtable sync failed - this is non-critical`);
        this.logger.warn(`SERVICE: Airtable sync error details:`, airtableError.stack);
      }

      this.logger.log(`SERVICE: Successfully withdrew bid ${bidId} for carrier ${user.carrierProfile?.companyName || 'Unknown'}`);
      
      return {
        success: true,
        message: 'Bid withdrawn successfully',
      };
    } catch (error) {
      this.logger.error(`SERVICE: Error withdrawing bid: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to withdraw bid: ${error.message}`);
    }
  }

  // Debug method to test Airtable bid updates
  async debugAirtableBidUpdate(loadAirtableRecordId: string): Promise<any> {
    // Define the interface for test results
    interface TestResult {
      fieldName: string;
      success: boolean;
      message?: string;
      error?: string;
      statusCode?: string | number;
    }

    try {
      const testResults: TestResult[] = [];

      // First check if the record exists in Airtable at all
      this.logger.log(`SERVICE: Checking if Airtable record ${loadAirtableRecordId} exists...`);
      let airtableRecord;
      try {
        airtableRecord = await this.base(this.tableName).find(loadAirtableRecordId);
        testResults.push({
          fieldName: 'Record Exists in Airtable',
          success: true,
          message: `Record ${loadAirtableRecordId} found in Airtable`
        });
      } catch (error) {
        testResults.push({
          fieldName: 'Record Exists in Airtable',
          success: false,
          error: error.message,
          message: `Record ${loadAirtableRecordId} NOT found in Airtable`
        });
        return { testResults, summary: 'Record does not exist in Airtable' };
      }

      // Check if "Synced to API" is true
      const syncedToApi = airtableRecord.get('Synced to API');
      testResults.push({
        fieldName: 'Synced to API',
        success: !!syncedToApi,
        message: `Synced to API = ${syncedToApi} (needs to be true for bidding)`
      });

      // Check if record exists in database
      const dbRecord = await this.prisma.load.findUnique({
        where: { airtableRecordId: loadAirtableRecordId }
      });
      testResults.push({
        fieldName: 'Record in Database',
        success: !!dbRecord,
        message: dbRecord ? `Record found in database with ID ${dbRecord.id}` : 'Record NOT found in database'
      });

      // Check status
      const status = airtableRecord.get('Status');
      testResults.push({
        fieldName: 'Status',
        success: status === 'Available',
        message: `Status = "${status}" (needs to be "Available" for bidding)`
      });

      // Check if record should be visible based on current filters
      const isEligibleForSync = syncedToApi && (status === 'Available' || status === 'Booking Requested');
      testResults.push({
        fieldName: 'Eligible for Sync',
        success: isEligibleForSync,
        message: `Eligible for sync = ${isEligibleForSync} (needs "Synced to API" = true AND status = "Available" or "Booking Requested")`
      });

      // Summary
      const allPassed = testResults.every(test => test.success);
      const summary = allPassed ? 
        'All checks passed - record should be available for bidding' : 
        'Some checks failed - record may not be properly synced';

      const recommendedActions: string[] = [];
      if (!syncedToApi) {
        recommendedActions.push('✅ Check the "Synced to API" checkbox in Airtable');
      }
      if (!dbRecord && syncedToApi) {
        recommendedActions.push('🔄 Run manual sync to sync this record to database');
      }
      if (status !== 'Available') {
        recommendedActions.push(`📝 Change status from "${status}" to "Available" in Airtable`);
      }

      return {
        loadAirtableRecordId,
        testResults,
        summary,
        recommendedActions,
        rawAirtableData: {
          'Synced to API': syncedToApi,
          'Status': status,
          'Order ID.': airtableRecord.get('Order ID.'),
          'Rate to Carrier': airtableRecord.get('Rate to Carrier')
        }
      };

    } catch (error) {
      this.logger.error(`SERVICE: Error debugging Airtable record ${loadAirtableRecordId}: ${error.message}`);
      return {
        error: error.message,
        loadAirtableRecordId,
        summary: 'Error occurred during diagnostics'
      };
    }
  }

  /**
   * Diagnostic method to check why a specific load cannot be found for bidding
   */
  async diagnoseLoadForBidding(loadAirtableRecordId: string): Promise<any> {
    this.logger.log(`SERVICE: Diagnosing load ${loadAirtableRecordId} for bidding eligibility...`);
    
    try {
      const diagnostics = await this.debugAirtableBidUpdate(loadAirtableRecordId);
      
      // Add specific bidding context
      const biddingDiagnostics = {
        ...diagnostics,
        biddingRequirements: {
          'Record exists in Airtable': true,
          'Synced to API checkbox checked': true,
          'Status is "Available"': true,
          'Record synced to database': true
        },
        nextSteps: [
          '1. Verify "Synced to API" is checked in Airtable',
          '2. Verify Status is "Available"',
          '3. Run manual sync if record exists but not in database',
          '4. Wait a few minutes for automatic sync, then try again'
        ]
      };
      
      return biddingDiagnostics;
    } catch (error) {
      this.logger.error(`SERVICE: Error diagnosing load for bidding: ${error.message}`);
      throw error;
    }
  }

  // Saved Search Methods
  async getSavedSearches(userAirtableId: string): Promise<any[]> {
    this.logger.log(`SERVICE: getSavedSearches called for user ${userAirtableId}`);
    try {
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: userAirtableId },
        include: {
          savedSearches: {
            orderBy: [
              { isDefault: 'desc' }, // Default searches first
              { createdAt: 'desc' }   // Then by creation date
            ]
          }
        }
      });

      if (!user) {
        throw new NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
      }

      return user.savedSearches.map(search => ({
        id: search.id,
        name: search.name,
        criteria: search.criteria,
        isDefault: search.isDefault,
        createdAt: search.createdAt,
        updatedAt: search.updatedAt
      }));
    } catch (error) {
      this.logger.error(`SERVICE: Error fetching saved searches for user ${userAirtableId}:`, error);
      throw error;
    }
  }

  async saveSearch(userAirtableId: string, savedSearchDto: SavedSearchDto): Promise<SavedSearch> {
    this.logger.log(`SERVICE: saveSearch called for user ${userAirtableId} with name "${savedSearchDto.name}"`);
    try {
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: userAirtableId }
      });

      if (!user) {
        throw new NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
      }

      // If this search is set as default, remove default from other searches
      if (savedSearchDto.isDefault) {
        await this.prisma.savedSearch.updateMany({
          where: { 
            userId: user.id,
            isDefault: true
          },
          data: { isDefault: false }
        });
      }

      const savedSearch = await this.prisma.savedSearch.create({
        data: {
          userId: user.id,
          name: savedSearchDto.name,
          criteria: savedSearchDto.criteria || {},
          isDefault: savedSearchDto.isDefault || false
        }
      });

      this.logger.log(`SERVICE: Saved search "${savedSearchDto.name}" created successfully for user ${userAirtableId}`);
      return savedSearch;
    } catch (error) {
      this.logger.error(`SERVICE: Error saving search for user ${userAirtableId}:`, error);
      throw error;
    }
  }

  async updateSavedSearch(userAirtableId: string, searchId: string, savedSearchDto: SavedSearchDto): Promise<SavedSearch> {
    this.logger.log(`SERVICE: updateSavedSearch called for user ${userAirtableId}, search ${searchId}`);
    try {
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: userAirtableId }
      });

      if (!user) {
        throw new NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
      }

      // Verify the search belongs to the user
      const existingSearch = await this.prisma.savedSearch.findFirst({
        where: {
          id: searchId,
          userId: user.id
        }
      });

      if (!existingSearch) {
        throw new NotFoundException(`Saved search ${searchId} not found for user ${userAirtableId}`);
      }

      // If this search is set as default, remove default from other searches
      if (savedSearchDto.isDefault) {
        await this.prisma.savedSearch.updateMany({
          where: { 
            userId: user.id,
            isDefault: true,
            id: { not: searchId } // Don't update the current search
          },
          data: { isDefault: false }
        });
      }

      const updatedSearch = await this.prisma.savedSearch.update({
        where: { id: searchId },
        data: {
          name: savedSearchDto.name,
          criteria: savedSearchDto.criteria || {},
          isDefault: savedSearchDto.isDefault || false
        }
      });

      this.logger.log(`SERVICE: Saved search "${savedSearchDto.name}" updated successfully for user ${userAirtableId}`);
      return updatedSearch;
    } catch (error) {
      this.logger.error(`SERVICE: Error updating saved search for user ${userAirtableId}:`, error);
      throw error;
    }
  }

  async deleteSavedSearch(userAirtableId: string, searchId: string): Promise<void> {
    this.logger.log(`SERVICE: deleteSavedSearch called for user ${userAirtableId}, search ${searchId}`);
    try {
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: userAirtableId }
      });

      if (!user) {
        throw new NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
      }

      // Verify the search belongs to the user and delete it
      const result = await this.prisma.savedSearch.deleteMany({
        where: {
          id: searchId,
          userId: user.id
        }
      });

      if (result.count === 0) {
        throw new NotFoundException(`Saved search ${searchId} not found for user ${userAirtableId}`);
      }

      this.logger.log(`SERVICE: Saved search ${searchId} deleted successfully for user ${userAirtableId}`);
    } catch (error) {
      this.logger.error(`SERVICE: Error deleting saved search for user ${userAirtableId}:`, error);
      throw error;
    }
  }

  /**
   * Carrier responds to admin counter-offer
   */
  async respondToCounterOffer(
    bidId: string,
    carrierUserId: string,
    response: 'accepted' | 'declined',
    notes?: string
  ): Promise<{ success: boolean; message: string; bid?: any }> {
    this.logger.log(`SERVICE: Carrier ${carrierUserId} responding to counter-offer on bid ${bidId}: ${response}`);

    try {
      // Verify user exists and is a carrier
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: carrierUserId },
        include: { carrierProfile: true },
      });

      if (!user || user.role !== Role.CARRIER) {
        throw new ForbiddenException('Only carriers can respond to counter-offers');
      }

      if (!user.carrierProfile) {
        throw new ForbiddenException('Carrier profile not found');
      }

      // Verify the bid exists and belongs to this carrier
      const existingBid = await this.prisma.bid.findUnique({
        where: { id: bidId },
        include: {
          load: true,
          carrierProfile: true
        }
      });

      if (!existingBid) {
        throw new NotFoundException(`Bid ${bidId} not found`);
      }

      if (existingBid.carrierProfileId !== user.carrierProfile.id) {
        throw new ForbiddenException('You can only respond to your own bids');
      }

      // Verify the bid is in a countered state
      if (existingBid.admin_response !== AdminResponse.COUNTERED) {
        throw new BadRequestException(`Bid is not in countered state. Current status: ${existingBid.admin_response}`);
      }

      if (existingBid.negotiation_status !== NegotiationStatus.OPEN) {
        throw new BadRequestException(`Bid negotiation is no longer open. Current status: ${existingBid.negotiation_status}`);
      }

      // Determine new status based on carrier response
      let newNegotiationStatus: NegotiationStatus;
      let newAdminResponse: AdminResponse;
      let finalAmount: number | null = null;

      if (response === 'accepted') {
        newNegotiationStatus = NegotiationStatus.CLOSED;
        newAdminResponse = AdminResponse.ACCEPTED;
        finalAmount = existingBid.counter_offer_amount;
      } else {
        newNegotiationStatus = NegotiationStatus.CLOSED;
        newAdminResponse = AdminResponse.DECLINED;
      }

      // Use transaction to ensure data consistency
      const result = await this.prisma.$transaction(async (tx) => {
        // Update the bid
        const updatedBid = await tx.bid.update({
          where: { id: bidId },
          data: {
            negotiation_status: newNegotiationStatus,
            response_timestamp: new Date(),
            carrierNotes: notes || existingBid.carrierNotes,
            // If accepted, update the bid amount to the counter-offer amount
            ...(response === 'accepted' && finalAmount && { bidAmount: finalAmount })
          },
          include: {
            load: true,
            carrierProfile: true
          }
        });

        // Create bid response record for history
        await tx.bid_responses.create({
          data: {
            id: `carrier_response_${bidId}_${Date.now()}`,
            bid_id: bidId,
            response_type: BidResponseType.CARRIER_RESPONSE,
            amount: finalAmount,
            notes: notes || null,
            created_by: carrierUserId
          }
        });

        // If carrier accepted counter-offer, assign the load
        if (response === 'accepted') {
          await tx.load.update({
            where: { id: existingBid.loadId },
            data: {
              awardedToCarrierProfileId: existingBid.carrierProfileId,
              status: 'ASSIGNED'
            }
          });

          // Close any other pending bids on this load
          await tx.bid.updateMany({
            where: {
              loadId: existingBid.loadId,
              id: { not: bidId },
              negotiation_status: NegotiationStatus.OPEN
            },
            data: {
              negotiation_status: NegotiationStatus.CLOSED,
              admin_response: AdminResponse.DECLINED,
              response_timestamp: new Date(),
              admin_notes: 'Load awarded to another carrier'
            }
          });
        }

        return updatedBid;
      });

      // Send real-time notifications
      await this.sendCounterOfferResponseNotifications(result, response, carrierUserId);

      const message = response === 'accepted' 
        ? `Counter-offer accepted! You have been assigned to load ${result.load.airtableRecordId} for $${finalAmount}`
        : `Counter-offer declined. The negotiation has been closed.`;

      this.logger.log(`SERVICE: Carrier ${carrierUserId} ${response} counter-offer on bid ${bidId}`);

      return {
        success: true,
        message,
        bid: {
          id: result.id,
          loadId: result.loadId,
          bidAmount: result.bidAmount,
          negotiationStatus: result.negotiation_status,
          adminResponse: result.admin_response,
          finalAmount: finalAmount,
          assignedLoad: response === 'accepted' ? result.load : null
        }
      };

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`SERVICE: Error responding to counter-offer ${bidId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to respond to counter-offer');
    }
  }

  /**
   * Send notifications for carrier counter-offer responses
   */
  private async sendCounterOfferResponseNotifications(bid: any, response: string, carrierUserId: string): Promise<void> {
    try {
      // Notify admins about the carrier's response
      const adminNotification = {
        type: 'counter_offer_response',
        title: `Counter-offer ${response}`,
        message: `${bid.carrierProfile.companyName} has ${response} your counter-offer for load ${bid.load.airtableRecordId}`,
        data: {
          bidId: bid.id,
          loadId: bid.loadId,
          carrierResponse: response,
          carrierName: bid.carrierProfile.companyName,
          finalAmount: bid.bidAmount,
          loadDetails: bid.load
        }
      };

      // Broadcast to all admin users
      await this.notificationsService.broadcastToAdmins(adminNotification);

      // If load was assigned, send load assignment notification to carrier
      if (response === 'accepted') {
        await this.notificationsService.broadcastToUser(carrierUserId, {
          type: 'load_assigned',
          title: 'Load Assigned!',
          message: `Congratulations! You have been assigned to load ${bid.load.airtableRecordId}`,
          data: {
            loadId: bid.loadId,
            bidId: bid.id,
            finalAmount: bid.bidAmount,
            loadDetails: bid.load
          }
        });
      }

    } catch (error) {
      this.logger.error(`Failed to send counter-offer response notifications: ${error.message}`, error.stack);
      // Don't throw error as notification failure shouldn't block the response
    }
  }

  /**
   * Get dashboard metrics for the authenticated carrier
   */
  async getDashboardMetrics(airtableUserId: string): Promise<any> {
    this.logger.log(`Fetching dashboard metrics for user ${airtableUserId}`);
    
    try {
      // Get carrier profile
      const carrierProfile = await this.prisma.carrierProfile.findFirst({
        where: { 
          user: { airtableUserId } 
        },
        include: { user: true }
      });

      if (!carrierProfile) {
        throw new NotFoundException('Carrier profile not found');
      }

      // Get assigned loads count (using the correct field name)
      const assignedLoads = await this.prisma.load.findMany({
        where: {
          status: 'ASSIGNED',
          awardedToCarrierProfileId: carrierProfile.id
        }
      });

      // Get active bids count
      const activeBids = await this.prisma.bid.findMany({
        where: {
          carrierProfileId: carrierProfile.id,
          admin_response: 'PENDING'
        }
      });

      // Calculate total earnings from completed loads (using correct enum value)
      const completedLoads = await this.prisma.load.findMany({
        where: {
          status: 'DELIVERED_EMPTY',
          awardedToCarrierProfileId: carrierProfile.id
        }
      });

      const totalEarnings = completedLoads.reduce((sum, load) => sum + (load.rate || 0), 0);

      // Get recent loads (last 10)
      const recentLoads = await this.prisma.load.findMany({
        where: {
          awardedToCarrierProfileId: carrierProfile.id
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          airtableRecordId: true,
          proNumber: true,
          originCity: true,
          originState: true,
          destinationCity: true,
          destinationState: true,
          rate: true,
          status: true,
          pickupDateUtc: true,
          deliveryDateUtc: true
        }
      });

      // Get recent bids (last 10)
      const recentBids = await this.prisma.bid.findMany({
        where: {
          carrierProfileId: carrierProfile.id
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          load: {
            select: {
              airtableRecordId: true,
              proNumber: true,
              originCity: true,
              originState: true,
              destinationCity: true,
              destinationState: true,
              rate: true
            }
          }
        }
      });

      // Get loads by status breakdown
      const loadsByStatus = await this.prisma.load.groupBy({
        by: ['status'],
        where: {
          awardedToCarrierProfileId: carrierProfile.id
        },
        _count: {
          _all: true
        }
      });

      const statusCounts: { [key: string]: number } = {};
      loadsByStatus.forEach(group => {
        if (group._count && group.status) {
          statusCounts[group.status] = group._count._all;
        }
      });

      // Get this week vs last week comparison
      const now = new Date();
      const thisWeekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
      const lastWeekStart = new Date(thisWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastWeekEnd = new Date(thisWeekStart.getTime() - 1);

      const thisWeekLoads = await this.prisma.load.count({
        where: {
          awardedToCarrierProfileId: carrierProfile.id,
          createdAt: {
            gte: thisWeekStart
          }
        }
      });

      const lastWeekLoads = await this.prisma.load.count({
        where: {
          awardedToCarrierProfileId: carrierProfile.id,
          createdAt: {
            gte: lastWeekStart,
            lte: lastWeekEnd
          }
        }
      });

      const metrics = {
        totalAssignedLoads: assignedLoads.length,
        totalActiveBids: activeBids.length,
        totalEarnings: totalEarnings,
        recentLoads: recentLoads,
        recentBids: recentBids.map(bid => ({
          id: bid.id,
          bidAmount: bid.bidAmount,
          status: bid.admin_response,
          createdAt: bid.createdAt,
          load: bid.load
        })),
        loadsByStatus: statusCounts,
        thisWeekLoads: thisWeekLoads,
        lastWeekLoads: lastWeekLoads
      };

      this.logger.log(`Dashboard metrics fetched successfully for user ${airtableUserId}`);
      return metrics;

    } catch (error) {
      this.logger.error(`Error fetching dashboard metrics for user ${airtableUserId}: ${error.message}`, error.stack);
      throw error;
    }
  }
} 