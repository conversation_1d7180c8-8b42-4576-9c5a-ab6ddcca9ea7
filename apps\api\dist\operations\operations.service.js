"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OperationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const pattern_analysis_service_1 = require("./services/pattern-analysis.service");
const smart_suggestions_service_1 = require("./services/smart-suggestions.service");
const radar_distance_service_1 = require("./services/radar-distance.service");
let OperationsService = OperationsService_1 = class OperationsService {
    prisma;
    patternAnalysis;
    smartSuggestions;
    radarDistance;
    logger = new common_1.Logger(OperationsService_1.name);
    constructor(prisma, patternAnalysis, smartSuggestions, radarDistance) {
        this.prisma = prisma;
        this.patternAnalysis = patternAnalysis;
        this.smartSuggestions = smartSuggestions;
        this.radarDistance = radarDistance;
    }
    async getLanes() {
        this.logger.log('Getting lanes for operations - checking cached lanes first');
        try {
            const cachedLanes = await this.getCachedLanes();
            if (cachedLanes && cachedLanes.length > 0) {
                this.logger.log(`✅ Retrieved ${cachedLanes.length} pre-calculated lanes from cache`);
                return cachedLanes;
            }
            this.logger.log('No cached lanes found, generating basic lanes with fast calculations');
            return await this.generateBasicLanes();
        }
        catch (error) {
            this.logger.error('Error retrieving lanes:', error);
            return await this.generateBasicLanes();
        }
    }
    async getCachedLanes() {
        try {
            const cacheCount = await this.prisma.distanceCache.count();
            if (cacheCount < 10) {
                this.logger.debug('Insufficient cached distance data, will generate basic lanes');
                return null;
            }
            const rawLanes = await this.prisma.load.groupBy({
                by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
                where: {
                    AND: [
                        { originCity: { not: null } },
                        { originState: { not: null } },
                        { destinationCity: { not: null } },
                        { destinationState: { not: null } }
                    ]
                },
                _count: { id: true },
                _max: { createdAt: true }
            });
            const lanes = [];
            for (let index = 0; index < rawLanes.length; index++) {
                const rawLane = rawLanes[index];
                const frequency = rawLane._count.id;
                const lastUsed = rawLane._max.createdAt?.toISOString();
                const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
                const originAddr = `${rawLane.originCity}, ${rawLane.originState}`;
                const destAddr = `${rawLane.destinationCity}, ${rawLane.destinationState}`;
                const cachedDistance = await this.prisma.distanceCache.findFirst({
                    where: {
                        OR: [
                            {
                                originAddress: { contains: rawLane.originCity, mode: 'insensitive' },
                                destinationAddress: { contains: rawLane.destinationCity, mode: 'insensitive' }
                            },
                            {
                                originAddress: originAddr,
                                destinationAddress: destAddr
                            }
                        ]
                    }
                });
                const estimatedMiles = cachedDistance ?
                    Math.round(cachedDistance.distanceMiles) :
                    this.calculateQuickDistance(rawLane.originState, rawLane.destinationState);
                const estimatedDuration = this.calculateEstimatedDuration(estimatedMiles);
                const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);
                lanes.push({
                    id: laneId,
                    originCity: rawLane.originCity,
                    originState: rawLane.originState,
                    destinationCity: rawLane.destinationCity,
                    destinationState: rawLane.destinationState,
                    estimatedMiles,
                    estimatedDuration,
                    frequencyRank,
                    lastUsed
                });
            }
            return lanes.sort((a, b) => {
                if (b.frequencyRank !== a.frequencyRank) {
                    return b.frequencyRank - a.frequencyRank;
                }
                return a.originCity.localeCompare(b.originCity);
            });
        }
        catch (error) {
            this.logger.error('Error getting cached lanes:', error);
            return null;
        }
    }
    async generateBasicLanes() {
        this.logger.log('Generating basic lanes with fast state-to-state distance calculations');
        const rawLanes = await this.prisma.load.groupBy({
            by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
            where: {
                AND: [
                    { originCity: { not: null } },
                    { originState: { not: null } },
                    { destinationCity: { not: null } },
                    { destinationState: { not: null } }
                ]
            },
            _count: { id: true },
            _max: { createdAt: true }
        });
        this.logger.log(`Found ${rawLanes.length} unique lane combinations from historical data`);
        const lanes = rawLanes.map((rawLane, index) => {
            const frequency = rawLane._count.id;
            const lastUsed = rawLane._max.createdAt?.toISOString();
            const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
            const estimatedMiles = this.calculateQuickDistance(rawLane.originState, rawLane.destinationState);
            const estimatedDuration = this.calculateEstimatedDuration(estimatedMiles);
            const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);
            return {
                id: laneId,
                originCity: rawLane.originCity,
                originState: rawLane.originState,
                destinationCity: rawLane.destinationCity,
                destinationState: rawLane.destinationState,
                estimatedMiles,
                estimatedDuration,
                frequencyRank,
                lastUsed
            };
        });
        const sortedLanes = lanes.sort((a, b) => {
            if (b.frequencyRank !== a.frequencyRank) {
                return b.frequencyRank - a.frequencyRank;
            }
            return a.originCity.localeCompare(b.originCity);
        });
        this.logger.log(`✅ Generated ${sortedLanes.length} basic lanes quickly using state-to-state distances`);
        this.logger.log('💡 For more accurate distances, run the background lane calculation job');
        return sortedLanes;
    }
    calculateQuickDistance(originState, destinationState) {
        const stateCoords = {
            'AL': { lat: 32.318, lng: -86.902 }, 'AK': { lat: 64.068, lng: -152.2782 },
            'AZ': { lat: 34.292, lng: -111.666 }, 'AR': { lat: 34.799, lng: -92.199 },
            'CA': { lat: 36.778, lng: -119.417 }, 'CO': { lat: 39.113, lng: -105.358 },
            'CT': { lat: 41.767, lng: -72.677 }, 'DE': { lat: 38.910, lng: -75.527 },
            'FL': { lat: 27.766, lng: -81.686 }, 'GA': { lat: 32.157, lng: -82.907 },
            'HI': { lat: 19.741, lng: -155.844 }, 'ID': { lat: 44.068, lng: -114.742 },
            'IL': { lat: 40.003, lng: -89.000 }, 'IN': { lat: 39.790, lng: -86.147 },
            'IA': { lat: 41.590, lng: -93.620 }, 'KS': { lat: 38.500, lng: -98.000 },
            'KY': { lat: 37.839, lng: -84.270 }, 'LA': { lat: 30.391, lng: -92.329 },
            'ME': { lat: 45.367, lng: -68.972 }, 'MD': { lat: 39.045, lng: -76.641 },
            'MA': { lat: 42.407, lng: -71.382 }, 'MI': { lat: 44.182, lng: -84.506 },
            'MN': { lat: 46.392, lng: -94.636 }, 'MS': { lat: 32.354, lng: -89.398 },
            'MO': { lat: 38.573, lng: -92.603 }, 'MT': { lat: 46.965, lng: -109.533 },
            'NE': { lat: 41.492, lng: -99.901 }, 'NV': { lat: 38.502, lng: -116.654 },
            'NH': { lat: 43.193, lng: -71.549 }, 'NJ': { lat: 40.221, lng: -74.756 },
            'NM': { lat: 34.307, lng: -106.018 }, 'NY': { lat: 42.659, lng: -73.781 },
            'NC': { lat: 35.771, lng: -78.638 }, 'ND': { lat: 47.650, lng: -100.437 },
            'OH': { lat: 40.367, lng: -82.996 }, 'OK': { lat: 35.482, lng: -97.534 },
            'OR': { lat: 44.931, lng: -120.767 }, 'PA': { lat: 40.269, lng: -76.875 },
            'RI': { lat: 41.342, lng: -71.422 }, 'SC': { lat: 33.830, lng: -81.163 },
            'SD': { lat: 44.045, lng: -99.793 }, 'TN': { lat: 35.860, lng: -86.660 },
            'TX': { lat: 31.106, lng: -97.563 }, 'UT': { lat: 39.421, lng: -111.950 },
            'VT': { lat: 44.558, lng: -72.580 }, 'VA': { lat: 37.768, lng: -78.169 },
            'WA': { lat: 47.042, lng: -120.806 }, 'WV': { lat: 38.349, lng: -81.633 },
            'WI': { lat: 44.745, lng: -89.626 }, 'WY': { lat: 42.755, lng: -107.302 }
        };
        const origin = stateCoords[originState];
        const destination = stateCoords[destinationState];
        if (!origin || !destination) {
            return 500;
        }
        if (originState === destinationState) {
            return 150;
        }
        const toRad = (degree) => degree * (Math.PI / 180);
        const earthRadius = 3959;
        const dLat = toRad(destination.lat - origin.lat);
        const dLng = toRad(destination.lng - origin.lng);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(toRad(origin.lat)) * Math.cos(toRad(destination.lat)) *
                Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const straightLineDistance = earthRadius * c;
        return Math.round(straightLineDistance * 1.3);
    }
    async calculateAccurateLanes() {
        this.logger.log('Starting accurate lane calculation with Radar.com API (background job)');
        try {
            const rawLanes = await this.prisma.load.groupBy({
                by: ['shipperAddress', 'receiverAddress', 'originCity', 'originState', 'destinationCity', 'destinationState'],
                where: {
                    AND: [
                        { originCity: { not: null } },
                        { originState: { not: null } },
                        { destinationCity: { not: null } },
                        { destinationState: { not: null } }
                    ]
                },
                _count: {
                    id: true
                },
                _max: {
                    createdAt: true
                }
            });
            this.logger.log(`Found ${rawLanes.length} unique lane combinations from historical data`);
            const distanceRequests = rawLanes.map(rawLane => {
                const originAddress = rawLane.shipperAddress || `${rawLane.originCity}, ${rawLane.originState}`;
                const destinationAddress = rawLane.receiverAddress || `${rawLane.destinationCity}, ${rawLane.destinationState}`;
                return {
                    originAddress: originAddress.trim(),
                    destinationAddress: destinationAddress.trim()
                };
            });
            this.logger.log('Starting batch distance calculations with Radar.com API, caching, and rate limiting...');
            this.logger.log('Using full shipper/receiver addresses for maximum accuracy');
            const distanceResults = await this.radarDistance.calculateDistancesBatchByAddress(distanceRequests);
            const lanes = [];
            for (let index = 0; index < rawLanes.length; index++) {
                const rawLane = rawLanes[index];
                const distanceResult = distanceResults[index];
                const frequency = rawLane._count.id;
                const lastUsed = rawLane._max.createdAt?.toISOString();
                const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
                const estimatedMiles = Math.round(distanceResult.distanceMiles);
                const estimatedDuration = this.calculateEstimatedDuration(estimatedMiles);
                const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);
                lanes.push({
                    id: laneId,
                    originCity: rawLane.originCity,
                    originState: rawLane.originState,
                    destinationCity: rawLane.destinationCity,
                    destinationState: rawLane.destinationState,
                    estimatedMiles,
                    estimatedDuration,
                    frequencyRank,
                    lastUsed
                });
                const originUsed = rawLane.shipperAddress ? 'full address' : 'city, state';
                const destUsed = rawLane.receiverAddress ? 'full address' : 'city, state';
                this.logger.debug(`Lane ${index + 1}: ${rawLane.originCity}, ${rawLane.originState} → ${rawLane.destinationCity}, ${rawLane.destinationState} = ${estimatedMiles} miles (${distanceResult.source}, origin: ${originUsed}, dest: ${destUsed})`);
            }
            const sortedLanes = lanes.sort((a, b) => {
                if (b.frequencyRank !== a.frequencyRank) {
                    return b.frequencyRank - a.frequencyRank;
                }
                return a.originCity.localeCompare(b.originCity);
            });
            const radarCount = distanceResults.filter(r => r.source === 'radar').length;
            const cacheCount = distanceResults.filter(r => r.source === 'cache').length;
            const fallbackCount = distanceResults.filter(r => r.source === 'fallback').length;
            const fullAddressCount = rawLanes.filter(lane => lane.shipperAddress && lane.receiverAddress).length;
            const successRate = (((radarCount + cacheCount) / distanceResults.length) * 100).toFixed(1);
            this.logger.log(`✅ Generated ${sortedLanes.length} lanes with accurate distances:`);
            this.logger.log(`🏠 Full addresses used: ${fullAddressCount}/${rawLanes.length} lanes (${((fullAddressCount / rawLanes.length) * 100).toFixed(1)}%)`);
            this.logger.log(`💾 Cache hits: ${cacheCount} lanes`);
            this.logger.log(`📡 Radar.com API: ${radarCount} lanes`);
            this.logger.log(`🔄 Fallback calculations: ${fallbackCount} lanes`);
            this.logger.log(`📊 Overall accuracy rate: ${successRate}%`);
            this.logger.log(`🎯 All distances rounded to nearest whole mile as requested`);
            return sortedLanes;
        }
        catch (error) {
            this.logger.error('Error generating accurate lanes from historical data:', error);
            throw new common_1.InternalServerErrorException('Failed to calculate accurate lanes');
        }
    }
    async createOrder(createOrderDto, clerkUserId) {
        this.logger.log(`Creating order for user ${clerkUserId}:`, createOrderDto);
        try {
            const existingOrder = await this.prisma.load.findFirst({
                where: { proNumber: createOrderDto.poNumber }
            });
            if (existingOrder) {
                throw new common_1.BadRequestException(`PO Number "${createOrderDto.poNumber}" already exists`);
            }
            const pickupDate = new Date(createOrderDto.pickupDate);
            let deliveryDate;
            if (createOrderDto.deliveryDate) {
                deliveryDate = new Date(createOrderDto.deliveryDate);
                if (deliveryDate <= pickupDate) {
                    throw new common_1.BadRequestException('Delivery date must be after pickup date');
                }
            }
            const newLoad = await this.prisma.load.create({
                data: {
                    proNumber: createOrderDto.poNumber,
                    originCity: createOrderDto.originCity,
                    originState: createOrderDto.originState,
                    destinationCity: createOrderDto.destinationCity,
                    destinationState: createOrderDto.destinationState,
                    pickupDateUtc: pickupDate,
                    deliveryDateUtc: deliveryDate,
                    equipmentRequired: createOrderDto.equipmentRequired,
                    weightLbs: createOrderDto.weightLbs,
                    rate: createOrderDto.rate,
                    temperature: createOrderDto.temperature,
                    status: 'AVAILABLE',
                    targetOrganizations: ['First Cut Produce'],
                    isPublic: false,
                    isTargeted: true,
                    rawAirtableData: {
                        createdBy: clerkUserId,
                        createdVia: 'operations-interface',
                        laneId: createOrderDto.laneId,
                        soNumber: createOrderDto.soNumber,
                        priority: createOrderDto.priority || 'normal',
                        notes: createOrderDto.notes,
                        estimatedMiles: createOrderDto.estimatedMiles
                    },
                    airtableRecordId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                }
            });
            this.logger.log(`Created order in database with ID: ${newLoad.id}`);
            this.logger.log(`Order ${newLoad.id} created successfully - Airtable sync will be implemented in Phase 2`);
            return {
                id: newLoad.id,
                airtableRecordId: newLoad.airtableRecordId
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Error creating order for user ${clerkUserId}:`, error);
            throw new common_1.InternalServerErrorException('Failed to create order');
        }
    }
    calculateEstimatedDuration(miles) {
        const days = Math.ceil(miles / 500);
        if (days === 1) {
            return '1 day';
        }
        return `${days} days`;
    }
    async getSmartSuggestions(originCity, originState, destinationCity, destinationState, userId, currentValues) {
        this.logger.log(`Getting smart suggestions for lane: ${originCity}, ${originState} → ${destinationCity}, ${destinationState}`);
        try {
            const suggestions = await this.smartSuggestions.getOrderSuggestions({
                originCity,
                originState,
                destinationCity,
                destinationState,
                userId,
                currentValues
            });
            this.logger.log(`Generated ${suggestions.suggestions.length} smart suggestions with ${(suggestions.confidence * 100).toFixed(1)}% confidence`);
            return suggestions;
        }
        catch (error) {
            this.logger.error('Error getting smart suggestions:', error);
            throw new common_1.InternalServerErrorException('Failed to generate smart suggestions');
        }
    }
    async validateOrderWithAI(orderData, context) {
        this.logger.log('Validating order with AI-powered rules');
        try {
            const validation = await this.smartSuggestions.validateOrderData(orderData, context);
            this.logger.log(`Validation complete: ${validation.warnings.length} warnings, ${validation.criticalIssues.length} critical issues`);
            return validation;
        }
        catch (error) {
            this.logger.error('Error in AI validation:', error);
            return {
                isValid: true,
                warnings: [],
                suggestions: [],
                criticalIssues: []
            };
        }
    }
    async recordSuggestionFeedback(orderId, feedbackData) {
        this.logger.log(`Recording suggestion feedback for order: ${orderId}`);
        try {
            await this.smartSuggestions.recordSuggestionFeedback({
                orderId,
                ...feedbackData
            });
            this.logger.log('Suggestion feedback recorded successfully');
        }
        catch (error) {
            this.logger.error('Error recording suggestion feedback:', error);
        }
    }
    async getAutoCompleteSuggestions(field, partialValue, context) {
        this.logger.log(`Getting auto-complete suggestions for ${field}: "${partialValue}"`);
        try {
            const suggestions = await this.smartSuggestions.getAutoCompleteSuggestions(field, partialValue, context);
            return suggestions;
        }
        catch (error) {
            this.logger.error('Error getting auto-complete suggestions:', error);
            return [];
        }
    }
};
exports.OperationsService = OperationsService;
exports.OperationsService = OperationsService = OperationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        pattern_analysis_service_1.PatternAnalysisService,
        smart_suggestions_service_1.SmartSuggestionsService,
        radar_distance_service_1.RadarDistanceService])
], OperationsService);
//# sourceMappingURL=operations.service.js.map