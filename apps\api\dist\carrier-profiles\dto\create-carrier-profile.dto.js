"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCarrierProfileDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class CreateCarrierProfileDto {
    companyName;
    dotNumber;
    mcNumber;
    phoneNumber;
    contact_name;
    contact_email;
    contact_phone;
    equipmentTypes;
    serviceableRegions;
}
exports.CreateCarrierProfileDto = CreateCarrierProfileDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Big Rigz Inc.', description: 'Company name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '1234567', description: 'DOT number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "dotNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'MC987654', description: 'MC number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "mcNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '+14155552671', description: 'Contact phone number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'John Smith', description: 'Contact person name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "contact_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '<EMAIL>', description: 'Contact email' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "contact_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '+14155552671', description: 'Contact phone number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateCarrierProfileDto.prototype, "contact_phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        example: ['Dry Van 53ft', 'Reefer'],
        description: 'List of equipment types',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            try {
                return JSON.parse(value);
            }
            catch (e) {
                return [value];
            }
        }
        return value;
    }),
    __metadata("design:type", Array)
], CreateCarrierProfileDto.prototype, "equipmentTypes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        example: ['Northeast', 'CA', 'Midwest'],
        description: 'List of serviceable regions or states',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            try {
                return JSON.parse(value);
            }
            catch (e) {
                return [value];
            }
        }
        return value;
    }),
    __metadata("design:type", Array)
], CreateCarrierProfileDto.prototype, "serviceableRegions", void 0);
//# sourceMappingURL=create-carrier-profile.dto.js.map