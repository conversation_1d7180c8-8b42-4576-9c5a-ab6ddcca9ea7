import { Module } from '@nestjs/common';
import { AirtableOrdersController } from './airtable-orders.controller';
import { AirtableOrdersService } from './airtable-orders.service';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { CircuitBreakerService } from '../common/services/circuit-breaker.service';

@Module({
  imports: [PrismaModule, AuthModule, NotificationsModule],
  controllers: [AirtableOrdersController],
  providers: [AirtableOrdersService, CircuitBreakerService],
  exports: [AirtableOrdersService],
})
export class AirtableOrdersModule {}