# APM Task Assignment: Phase 2.7-T2 Backend Authentication Migration

## 1. Agent Role & APM Context

**Introduction:** You are activated as an Implementation Agent within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As an Implementation Agent, you will execute the assigned task diligently, make necessary technical decisions within the defined scope, and meticulously log all work to the Memory Bank for project continuity and knowledge transfer.

**Workflow:** You will interact with the Manager Agent via the User interface and contribute to the centralized Memory Bank system that tracks all project progress and decisions.

## 2. Onboarding / Context from Prior Work

**Previous Work Completed:**
- **P2.7-T1: N8N Authentication Workflow Setup** has been completed successfully (Agent_N8N_Specialist)
- **Authentication Infrastructure:** The new N8N JWT authentication system is operational with:
  - Working N8N authentication workflow with Airtable backend
  - JWT token generation and validation capabilities  
  - Frontend authentication context (`apps/web/src/contexts/auth-context.tsx`)
  - Login/register forms (`apps/web/src/components/auth/N8NLoginForm.tsx`, `N8NRegisterForm.tsx`)
  - New user interface and authentication flow fully functional

**Current System State:**
- The new N8N JWT authentication system is implemented and working
- However, extensive Clerk remnants exist throughout the codebase causing conflicts
- Both old Clerk authentication and new N8N authentication coexist, creating system instability
- The Memory Bank shows this migration as critical to completing the "clean break from Clerk" architectural decision

**Critical Dependencies:**
- N8N authentication endpoints are functional
- Frontend auth context provides: `useAuth()`, `useUser()`, `getToken()` methods
- JWT validation is working for new authentication flows

## 3. Task Assignment

**Reference Implementation Plan:** This assignment corresponds to `Phase 2.7, Task P2.7-T2: Backend Authentication Migration` in the Implementation_Plan.md.

**Objective:** Replace all Clerk authentication dependencies with the N8N/Airtable JWT system throughout the API backend and systematically purge Clerk remnants from the entire codebase to achieve a "clean break from Clerk" as specified in the strategic architecture decisions.

**Detailed Action Steps (Incorporating Plan Guidance):**

### Step 1: Backend API Authentication System Replacement
1. **Remove Clerk Dependencies from API:**
   - Uninstall all Clerk packages from `apps/api/package.json`
   - Remove all Clerk imports from API source files
   - Delete Clerk environment variables from API configuration
   - Remove `apps/api/src/auth/clerk.guard.ts` and `clerk-admin.guard.ts`

2. **Implement New JWT Authentication Guard:**
   - Create new `JwtAuthGuard` replacing `ClerkGuard` functionality
   - Implement JWT token validation middleware using N8N token format
   - Update JWT strategy (`apps/api/src/auth/jwt.strategy.ts`) to validate N8N tokens
   - Create new authentication interfaces matching N8N JWT payload structure

3. **Update All Protected Routes:**
   - Replace all `@UseGuards(ClerkGuard)` with new `@UseGuards(JwtAuthGuard)`
   - Update authentication decorators throughout controllers
   - Modify user context extraction in authenticated endpoints
   - Ensure proper role-based authorization (CARRIER vs ADMIN)

### Step 2: Frontend Component Migration  
1. **Systematic Clerk Hook Replacement:**
   - Replace all `useAuth()` imports from `@clerk/nextjs` with `useAuth()` from `@/contexts/auth-context`
   - Remove all `useUser()`, `useOrganization()`, `useClerk()` imports and usage
   - Update all components that use Clerk authentication state
   - Focus on components in `/org/[orgId]/` directory structure

2. **Component-by-Component Migration:**
   - **Priority Components (High Usage):**
     - `apps/web/src/app/org/[orgId]/loadboard/page.tsx`
     - `apps/web/src/app/org/[orgId]/page.tsx` (dashboard)
     - `apps/web/src/app/org/[orgId]/my-loads/page.tsx`
     - All loadboard components (`bid-dialog.tsx`, `columns.tsx`, etc.)
   - **Admin Components:**
     - `apps/web/src/app/org/[orgId]/admin/page.tsx`
     - All admin panel components
   - **Debug/Test Components:**
     - Remove or migrate debug components as appropriate

### Step 3: URL Structure Simplification
1. **Implement Direct Routing:**
   - Create new pages at root level: `/loadboard`, `/dashboard`, `/my-loads`
   - Copy functionality from `/org/[orgId]/` pages to new direct routes
   - Remove `orgId` parameter dependencies from page logic
   - Update navigation components to use simplified routes

2. **Update Navigation and Redirects:**
   - Modify all internal links to use direct routes
   - Update middleware routing logic
   - Remove organization-based route protection
   - Implement user-based route protection using new auth system

### Step 4: Database and User Management Updates
1. **User Context Migration:**
   - Update user profile endpoints to use new JWT payload
   - Modify user identification from Clerk user IDs to N8N/Airtable user IDs
   - Update user context providers throughout application
   - Ensure carrier profile associations work with new user identification

2. **API Integration Points:**
   - Update all user-related database queries
   - Modify load assignment logic for new user identification
   - Update bidding system user associations
   - Ensure admin verification workflows use new auth system

### Step 5: Cleanup and Validation
1. **Remove Unused Components:**
   - Delete Clerk debug components (`ClerkDebugger.tsx`, `SafeOrgSelector.tsx`, etc.)
   - Remove unused Clerk webhook endpoints and controllers
   - Clean up Clerk-related type definitions and interfaces
   - Remove Clerk configuration from middleware and layouts

2. **Testing and Validation:**
   - Test all authentication flows (login, logout, protected routes)
   - Verify all pages work with new authentication system
   - Ensure API endpoints properly validate N8N JWT tokens
   - Test role-based access control for admin vs carrier functions

**Guidance from Implementation Plan:**
- **Enhanced JWT Payload:** Use minimal payload structure (id, email, mcNumber, role) as specified
- **Simplified URL Structure:** Implement direct routes (/loadboard vs /org/{orgId}/loadboard)
- **MC Number-based Targeting:** Replace organization-based targeting with MC Number-based targeting
- **Local Database Caching:** Maintain user profile caching in local database for performance

## 4. Expected Output & Deliverables

**Define Success:** 
- Complete elimination of all Clerk dependencies from the codebase
- All authentication flows using N8N JWT system exclusively
- Simplified URL structure implemented (/loadboard, /dashboard, /my-loads)
- All existing functionality preserved while using new authentication
- Clean build with no Clerk-related errors or imports

**Specify Deliverables:**
1. **Modified Files:**
   - Updated authentication guards and strategies in API
   - Migrated frontend components with new auth hooks
   - New simplified routing structure pages
   - Updated navigation and middleware components

2. **Removed Files:**
   - Deleted Clerk guard files
   - Removed Clerk debug components
   - Cleaned up unused authentication components

3. **Validation Results:**
   - Successful build of both API and web applications
   - Working authentication flows tested on all major pages
   - Documentation of any breaking changes or migration notes

## 5. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Adhere strictly to the established logging format. Ensure your log includes:
- A reference to "Phase 2.7-T2: Backend Authentication Migration" 
- A clear description of all actions taken with file-by-file migration details
- Code snippets for critical authentication changes
- Any key decisions made during Clerk removal process
- Any challenges encountered and how they were resolved
- Confirmation of successful execution with build and test results
- Specific validation that all authentication flows work with new system

## 6. Clarification Instruction

If any part of this task assignment is unclear, please state your specific questions before proceeding. Given the complexity of this authentication migration, clarification is encouraged to ensure successful completion without breaking existing functionality. 