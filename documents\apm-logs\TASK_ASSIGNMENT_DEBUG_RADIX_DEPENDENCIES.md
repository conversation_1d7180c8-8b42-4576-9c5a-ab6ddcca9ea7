# APM Task Assignment: Fix Deployment Build Error - Missing Radix UI Dependencies

## 1. Agent Role & APM Context

**Introduction:** You are activated as a **Debug Agent** within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As a Debug Agent, you are responsible for quickly identifying and resolving build errors, deployment issues, and critical system failures. You must provide immediate solutions to unblock deployment and development workflows.

**Workflow:** You will work directly with the Manager Agent (via the User) to resolve the current deployment blocker and ensure the system can be deployed successfully.

## 2. Context from Prior Work

**UI/Design Agent (P2.5-T1) Context:** The UI/Design Agent recently completed comprehensive UI/UX improvements including:
- ✅ Created new UI components using Radix UI primitives
- ✅ Implemented `collapsible.tsx` and `tooltip.tsx` components  
- ✅ Added help modal, onboarding tour, and contextual help systems
- ❌ **MISSING:** Failed to add required Radix UI dependencies to package.json

**Current Issue:** The deployment build is failing due to missing Radix UI package dependencies.

## 3. Task Assignment

**Reference Implementation Plan:** This is an urgent debug task to resolve deployment blocking error from Phase 2.5-T1 implementation.

**Objective:** Immediately fix the deployment build error by adding missing Radix UI dependencies to the web application's package.json file.

### Critical Error Analysis:

**Build Error Log:**
```
Failed to compile.

./src/components/ui/collapsible.tsx
Module not found: Can't resolve '@radix-ui/react-collapsible'

./src/components/ui/tooltip.tsx
Module not found: Can't resolve '@radix-ui/react-tooltip'
```

**Root Cause:** Missing dependencies in `apps/web/package.json` for Radix UI components used by UI/Design Agent.

### Detailed Action Steps:

#### A. Immediate Dependency Resolution
1. **Add Missing Radix UI Dependencies:**
   - **File to Modify:** `apps/web/package.json`
   - **Required Dependencies:**
     ```json
     "@radix-ui/react-collapsible": "^1.0.3",
     "@radix-ui/react-tooltip": "^1.0.7"
     ```
   - **Installation Location:** Add to `dependencies` section (not devDependencies)
   - **Version Strategy:** Use latest stable versions compatible with React 18+

2. **Verify Package.json Structure:**
   - Ensure proper JSON formatting after addition
   - Confirm dependencies are in correct section
   - Check for any duplicate entries
   - Validate alphabetical ordering if maintained

#### B. Complete Dependency Audit
1. **Review UI Components for Additional Dependencies:**
   - **Check Files:**
     - `apps/web/src/components/ui/help-modal.tsx`
     - `apps/web/src/components/ui/onboarding-tour.tsx`
     - `apps/web/src/components/ui/help-tooltip.tsx`
     - `apps/web/src/components/ui/profile-completion.tsx`
     - `apps/web/src/components/ui/empty-state.tsx`
   - **Look for:** Additional `@radix-ui/*` imports that might be missing
   - **Common Radix Packages:** `@radix-ui/react-dialog`, `@radix-ui/react-portal`, `@radix-ui/react-slot`

2. **Verify Existing Compatible Dependencies:**
   - Check if other Radix UI packages are already installed
   - Ensure version compatibility across Radix packages
   - Verify React compatibility (React 18+ required for Radix)

#### C. Build Verification
1. **Local Build Test (if possible):**
   - Run `pnpm install` in apps/web directory
   - Execute `pnpm build` to verify compilation
   - Check for any additional missing dependencies

2. **Import Resolution Verification:**
   - Confirm all Radix imports resolve correctly
   - Verify TypeScript types are available
   - Check for peer dependency warnings

## 4. Technical Implementation Guidelines

**Package.json Location:** `apps/web/package.json`

**Required Dependencies (Minimum):**
```json
{
  "dependencies": {
    "@radix-ui/react-collapsible": "^1.0.3",
    "@radix-ui/react-tooltip": "^1.0.7"
  }
}
```

**Additional Dependencies (if found in component files):**
- `@radix-ui/react-dialog` - for modal implementations
- `@radix-ui/react-portal` - for portal-based components  
- `@radix-ui/react-slot` - for component composition
- `@radix-ui/react-visually-hidden` - for accessibility

**Version Considerations:**
- Use latest stable versions (1.x.x)
- Ensure React 18+ compatibility
- Check for peer dependency requirements

## 5. Expected Output & Deliverables

**Define Success:** Successful completion means:
- ✅ Deployment build completes without module resolution errors
- ✅ All Radix UI imports resolve correctly
- ✅ No additional missing dependency errors
- ✅ Build artifacts generated successfully

**Specific Deliverables:**
1. **Updated package.json:** `apps/web/package.json` with all required Radix UI dependencies
2. **Verification Report:** Confirmation that build completes successfully
3. **Dependency List:** Complete list of all Radix UI packages added
4. **Build Test Results:** Evidence of successful compilation

**Critical Success Criteria:**
- **Deployment Unblocked:** Vercel build must complete successfully
- **No Breaking Changes:** Existing functionality must remain intact
- **Performance Impact:** Minimal bundle size increase
- **Type Safety:** TypeScript compilation without errors

## 6. Emergency Priority Instructions

**⚡ IMMEDIATE ACTION REQUIRED:**

This is a **P0 CRITICAL** deployment blocker. The web application cannot be deployed until this is resolved.

**Time Sensitivity:** This task should be completed within 15-30 minutes maximum.

**Deployment Impact:** 
- Current deployment is blocked
- New UI/UX improvements cannot be tested in production
- User experience improvements are not available to carriers

**Business Impact:**
- Deployment pipeline blocked
- Development workflow interrupted  
- UI improvements cannot be delivered to users

## 7. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Ensure your log includes:
- Reference to **Debug Task: Radix UI Dependencies** 
- **Root Cause:** Missing dependencies from P2.5-T1 implementation
- **Resolution:** Exact dependencies added to package.json
- **Verification:** Build success confirmation
- **Prevention:** Recommendations for future dependency management
- **Timeline:** Duration from problem identification to resolution

**Special Instructions:**
- Mark this as a **CRITICAL DEPLOYMENT FIX**
- Include the exact package.json changes made
- Document any additional dependencies discovered
- Provide recommendations for preventing similar issues

## 8. Clarification Instruction

**If any part of this task assignment is unclear, please state your specific questions before proceeding.**

**However, given the critical nature of this deployment blocker, you should:**
1. **Start immediately** with adding the two known missing dependencies
2. **Verify build success** 
3. **Ask clarifying questions** only if additional issues arise

---

**Priority:** 🔴 **P0 CRITICAL - DEPLOYMENT BLOCKER**

**Estimated Duration:** 15-30 minutes

**Success Metric:** Vercel deployment build completes successfully without module resolution errors.

**Dependencies:** None - can begin immediately

**Impact:** Unblocks deployment pipeline and enables testing of Phase 2.5-T1 UI improvements in production. 