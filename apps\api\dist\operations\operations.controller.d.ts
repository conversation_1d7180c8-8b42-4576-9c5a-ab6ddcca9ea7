import { AuthenticatedRequest } from '../auth/authenticated-request.interface';
import { OperationsService, Lane } from './operations.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { AuthService } from '../auth/auth.service';
export declare class OperationsController {
    private readonly operationsService;
    private readonly authService;
    private readonly logger;
    private requestMetrics;
    private readonly CIRCUIT_BREAKER_CONFIG;
    constructor(operationsService: OperationsService, authService: AuthService);
    private checkCircuitBreaker;
    private recordMetrics;
    private withTimeout;
    getLanes(req: AuthenticatedRequest): Promise<{
        lanes: Lane[];
        total: number;
    }>;
    calculateAccurateLanes(req: AuthenticatedRequest): Promise<{
        message: string;
        lanesCalculated: number;
        summary: string;
    }>;
    createOrder(req: AuthenticatedRequest, createOrderDto: CreateOrderDto): Promise<{
        id: string;
        airtableRecordId: string | undefined;
        message: string;
        success: boolean;
    }>;
    private verifyOperationsAccess;
    getSmartSuggestions(req: AuthenticatedRequest, originCity: string, originState: string, destinationCity: string, destinationState: string, currentValues?: string): Promise<{
        suggestions: never[];
        smartDefaults: {};
        confidence: number;
        metadata: {
            error: string;
            generatedAt: Date;
            dataPoints: number;
            learningActive: boolean;
        };
    } | {
        suggestions: any[];
        smartDefaults: any;
        confidence: number;
        metadata: {
            generatedAt: Date;
            dataPoints: number;
            learningActive: boolean;
            databaseHealthy: boolean;
        };
    }>;
    validateOrderWithAI(req: AuthenticatedRequest, validationData: {
        orderData: any;
        context: any;
    }): Promise<{
        isValid: boolean;
        warnings: any[];
        suggestions: any[];
        criticalIssues: any[];
    }>;
    recordFeedback(req: AuthenticatedRequest, orderId: string, feedbackData: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getAutoComplete(req: AuthenticatedRequest, field: string, partialValue: string, context?: string): Promise<{
        suggestions: string[] | number[];
    }>;
}
