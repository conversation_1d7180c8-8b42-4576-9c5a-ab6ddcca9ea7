Partitioned cookie or storage access was provided to “https://vercel.live/_next-live/feedback/feedback.html?dpl=dpl_9TRL6UezXE6PBQcSpZv6yL7YpH8s” because it is loaded in the third-party context and dynamic state partitioning is enabled. feedback.html:9:10574
[PROFILE_COMPLETION] Received profileData: null page-2d88438fe0aa38ae.js:1:2490
[PROFILE_COMPLETION] Field completion status: 
Array(7) [ {…}, {…}, {…}, {…}, {…}, {…}, {…} ]
page-2d88438fe0aa38ae.js:1:4601
[PROFILE_COMPLETION] Completion stats: 
Object { completedFields: 0, totalFields: 7, requiredFields: 7, incompleteRequiredFields: 7, completionPercentage: 0, requiredCompletionPercentage: 0, isProfileComplete: false }
page-2d88438fe0aa38ae.js:1:4777
Making API request to: https://api.fcp-portal.com/api/v1/auth/profile page-6b93e54eef3e19b5.js:1:16280
XHRGET
https://api.fcp-portal.com/api/v1/auth/profile

XHROPTIONS
https://api.fcp-portal.com/api/v1/auth/profile
CORS Missing Allow Origin

GET
https://www.fcp-portal.com/favicon.ico
[HTTP/2 200  0ms]

Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/auth/profile. (Reason: CORS header ‘Access-Control-Allow-Origin’ missing). Status code: 500.
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/auth/profile. (Reason: CORS request did not succeed). Status code: (null).
Error fetching from API: 
Object { endpoint: "/auth/profile", url: "https://api.fcp-portal.com/api/v1/auth/profile", attempt: 1, error: "NetworkError when attempting to fetch resource." }
273-125b2abb733999b5.js:1:54926
User profile not found, using current user data page-6b93e54eef3e19b5.js:1:2639
Making API request to: https://api.fcp-portal.com/api/v1/carrier-profiles/me page-6b93e54eef3e19b5.js:1:16280
XHRGET
https://api.fcp-portal.com/api/v1/carrier-profiles/me

XHROPTIONS
https://api.fcp-portal.com/api/v1/carrier-profiles/me
CORS Missing Allow Origin

Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/carrier-profiles/me. (Reason: CORS header ‘Access-Control-Allow-Origin’ missing). Status code: 500.
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/carrier-profiles/me. (Reason: CORS request did not succeed). Status code: (null).
Error fetching from API: 
Object { endpoint: "/carrier-profiles/me", url: "https://api.fcp-portal.com/api/v1/carrier-profiles/me", attempt: 1, error: "NetworkError when attempting to fetch resource." }
273-125b2abb733999b5.js:1:54926
Carrier profile not found page-6b93e54eef3e19b5.js:1:3158
Making API request to: https://api.fcp-portal.com/api/v1/auth/profile page-6b93e54eef3e19b5.js:1:16280
XHRPATCH
https://api.fcp-portal.com/api/v1/auth/profile

XHROPTIONS
https://api.fcp-portal.com/api/v1/auth/profile
CORS Missing Allow Origin

Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/auth/profile. (Reason: CORS header ‘Access-Control-Allow-Origin’ missing). Status code: 500.
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/auth/profile. (Reason: CORS request did not succeed). Status code: (null).
Error fetching from API: 
Object { endpoint: "/auth/profile", url: "https://api.fcp-portal.com/api/v1/auth/profile", attempt: 1, error: "NetworkError when attempting to fetch resource." }
273-125b2abb733999b5.js:1:54926
Error updating user profile: ApiError: Network error. Please check your connection and try again.
    NextJS 11
273-125b2abb733999b5.js:1:54926
Making API request to: https://api.fcp-portal.com/api/v1/carrier-profiles page-6b93e54eef3e19b5.js:1:16280
XHRPOST
https://api.fcp-portal.com/api/v1/carrier-profiles

XHROPTIONS
https://api.fcp-portal.com/api/v1/carrier-profiles
CORS Missing Allow Origin

Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/carrier-profiles. (Reason: CORS header ‘Access-Control-Allow-Origin’ missing). Status code: 500.
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://api.fcp-portal.com/api/v1/carrier-profiles. (Reason: CORS request did not succeed). Status code: (null).
Error fetching from API: 
Object { endpoint: "/carrier-profiles", url: "https://api.fcp-portal.com/api/v1/carrier-profiles", attempt: 1, error: "NetworkError when attempting to fetch resource." }
273-125b2abb733999b5.js:1:54926
Error updating carrier profile: ApiError: Network error. Please check your connection and try again.
    NextJS 11
273-125b2abb733999b5.js:1:54926
