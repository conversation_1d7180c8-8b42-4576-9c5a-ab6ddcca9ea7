import { PrismaService } from '../../prisma/prisma.service';
import { PatternAnalysisService } from './pattern-analysis.service';
export declare class SmartSuggestionsService {
    private readonly prisma;
    private readonly patternAnalysis;
    private readonly logger;
    private healthCheckCache;
    private readonly HEALTH_CHECK_INTERVAL;
    private readonly DB_TIMEOUT;
    constructor(prisma: PrismaService, patternAnalysis: PatternAnalysisService);
    private checkDatabaseHealth;
    private safeDbOperation;
    getOrderSuggestions(orderContext: {
        originCity: string;
        originState: string;
        destinationCity: string;
        destinationState: string;
        userId: string;
        currentValues?: any;
    }): Promise<{
        suggestions: never[];
        smartDefaults: {};
        confidence: number;
        metadata: {
            error: string;
            generatedAt: Date;
            dataPoints: number;
            learningActive: boolean;
        };
    } | {
        suggestions: any[];
        smartDefaults: any;
        confidence: number;
        metadata: {
            generatedAt: Date;
            dataPoints: number;
            learningActive: boolean;
            databaseHealthy: boolean;
        };
    }>;
    recordSuggestionFeedback(feedback: {
        userId: string;
        orderId: string;
        suggestions: any[];
        actualValues: any;
        orderContext: any;
    }): Promise<void>;
    getAutoCompleteSuggestions(field: string, partialValue: string, context: any): Promise<string[] | number[]>;
    validateOrderData(orderData: any, context: any): Promise<{
        isValid: boolean;
        warnings: any[];
        suggestions: any[];
        criticalIssues: any[];
    }>;
    private enhanceWithLearnings;
    private applyMarketAdjustments;
    private generateSmartDefaults;
    private calculateOverallConfidence;
    private calculateDaysDifference;
    private storeFeedbackData;
    private updateLearnedInsights;
    private getUserLearnedInsights;
    private adjustSuggestionWithInsight;
    private suggestPONumbers;
    private suggestRates;
    private suggestWeights;
    private suggestNotes;
    private generateSmartPONumber;
    private validateBusinessRules;
    private validateAgainstPatterns;
    private validateMarketConditions;
}
