# Loadboard & Bidding Web Application Architecture (v2)

**Version:** 2.0
**Last Updated:** \[Current Date]

## 1. Overview
This document outlines the architecture for the Carrier Loadboard & Bidding web application. The system enables motor carriers to manage profiles, view, and bid on freight loads sourced from Airtable. It includes robust user authentication, an admin interface, and a clear separation of frontend and backend concerns.

## 2. Core Tenets & Decisions
*   **Frontend:** Next.js (React) with Tailwind CSS for a modern, responsive UI.
*   **Backend:** NestJS (Node.js, TypeScript) for a scalable and maintainable API.
*   **Database:** PostgreSQL for reliable relational data storage.
*   **Authentication:** Clerk for secure and feature-rich user identity management, including organization and role handling.
*   **Airtable Sync:** Push-based via Airtable Automations (webhooks) for near real-time load updates.
*   **Deployment:** Vercel for frontend hosting, Render.com for backend API and database hosting.

## 3. Components & Interactions

### 3.1. Frontend Application (Next.js)
*   **Description:** The client-side application rendered in the user's browser, providing the user interface.
*   **Technology:** Next.js (React), Tailwind CSS.
*   **Deployment:** Vercel.
*   **Key Responsibilities:**
    *   Rendering UI for carrier registration, login, profile management, loadboard, bidding, and admin panels.
    *   Handling user input and interactions.
    *   Initiating authentication flows with Clerk.
    *   Making authenticated API calls (using JWTs obtained from Clerk) to the NestJS Backend API.
    *   (Post-MVP) Connecting to the Real-time Service (Socket.IO) for live updates.
    *   State management (React Context, Zustand, or similar).

### 3.2. Backend API (NestJS)
*   **Description:** The server-side application handling business logic, data persistence, and secure API endpoints.
*   **Technology:** NestJS (Node.js, TypeScript).
*   **Deployment:** Render.com (as a Web Service).
*   **Key Responsibilities:**
    *   Providing a RESTful API for the Next.js frontend.
    *   Validating JWTs from Clerk to secure API endpoints.
    *   Synchronizing user data with a local `Users` table upon Clerk signup/login events (via Clerk rules/hooks or backend checks).
    *   Implementing business logic for:
        *   Carrier profile management (equipment types, service regions, verification).
        *   Receiving and processing load data from Airtable webhooks.
        *   Managing bids on loads.
        *   Admin functions (carrier verification, load/bid oversight).
    *   Interacting with the PostgreSQL database via an ORM (TypeORM or Prisma).
    *   (Post-MVP) Managing WebSocket connections for real-time communication (Socket.IO).
    *   (Post-MVP) Integrating with an Email Service (e.g., SendGrid) for notifications.

### 3.3. Database (PostgreSQL)
*   **Description:** The primary relational database for storing application data.
*   **Technology:** PostgreSQL.
*   **Deployment:** Render.com (as a Managed Database).
*   **Key Data Stored:**
    *   `Users`: Local references to Clerk users, including roles (Carrier, Admin).
    *   `CarrierProfiles`: Detailed carrier information, equipment, service regions, verification status.
    *   `Loads`: Synchronized freight load data from Airtable, including status and awarded carrier.
    *   `Bids`: Bids submitted by carriers for specific loads.
    *   (Schema details as previously defined).

### 3.4. Authentication Service (Clerk)
*   **Description:** Managed external service for user identity, session management, and access control (including organization-level roles).
*   **Technology:** Clerk Platform.
*   **Key Responsibilities:**
    *   Handling user registration, login, and password management flows (via pre-built UI components and backend SDKs).
    *   Issuing JWTs upon successful authentication.
    *   Managing user profiles, organization memberships, and organization roles (e.g., `org_role`).
    *   Providing social logins and Multi-Factor Authentication (MFA) capabilities.
    *   Enabling secure token validation by the NestJS Backend API using Clerk's SDKs/libraries.

### 3.5. Airtable & Sync Mechanism
*   **Description:** External service acting as the source of truth for freight load data.
*   **Technology:** Airtable cloud platform.
*   **Sync Method:**
    1.  **Airtable Automations:** When a record in the designated "Loads" table is created or updated in Airtable.
    2.  **Webhook Trigger:** The automation triggers an HTTP POST request (webhook) to a specific, secure endpoint on the NestJS Backend API.
    3.  **Data Processing:** The NestJS API receives the load data payload, validates it, and then upserts (updates or inserts) it into the `Loads` table in the PostgreSQL database, using `airtable_record_id` as the unique key for matching.

### 3.6. (Post-MVP) Email Service
*   **Description:** Managed external service for sending transactional emails.
*   **Technology:** e.g., SendGrid, Mailgun, AWS SES.
*   **Key Responsibilities:** Sending notifications for events like bid awarded, account verification, password resets (if not handled by Clerk).

### 3.7. (Post-MVP) Real-time Service (Socket.IO)
*   **Description:** Enables bi-directional, real-time communication between the frontend and backend.
*   **Technology:** Socket.IO, integrated within the NestJS application.
*   **Key Responsibilities:** Pushing live updates to connected clients (e.g., new loads on the board, bid status changes) without requiring page reloads.

## 4. Data Flow Examples

### 4.1. User Login:
1.  User navigates to the login page/triggers login action on the Next.js Frontend.
2.  Frontend utilizes Clerk's SDK and UI components (e.g., `<SignInButton>`, `<ClerkProvider>`) to present login options.
3.  User authenticates with Clerk (credentials, social login, etc.).
4.  Clerk's SDK on the frontend handles the session and makes JWTs available (e.g., via `useAuth().getToken()`).
5.  Subsequent API calls from Frontend to the NestJS Backend include the Clerk-issued JWT in the `Authorization` header.
6.  NestJS Backend validates the JWT using Clerk's backend SDK and configured keys.

### 4.2. Load Creation/Update & Sync:
1.  Admin or process creates/updates a load record in Airtable.
2.  Airtable Automation detects the change and triggers a webhook.
3.  Webhook sends an HTTP POST request with the load data to a designated NestJS API endpoint.
4.  NestJS API endpoint receives the data, validates it, and upserts the record into the `Loads` table in PostgreSQL.
5.  (Post-MVP) If Socket.IO is active, NestJS API can broadcast a message about the new/updated load to connected frontend clients.

### 4.3. Carrier Bidding on a Load:
1.  Carrier views the Loadboard on the Next.js Frontend (loads fetched via API call to NestJS).
2.  Carrier selects a load and submits a bid.
3.  Frontend sends an authenticated API request (POST `/api/v1/bids`) with bid details to the NestJS Backend.
4.  NestJS Backend validates the request (user is a verified carrier, load is open for bidding, etc.), creates a new `Bids` record in PostgreSQL, and links it to the load and carrier.
5.  Backend returns a success response.
6.  (Post-MVP) NestJS API can send real-time notifications to the Admin and potentially other relevant parties.

## 5. Deployment Strategy
*   **Frontend (Next.js):** Deployed to **Vercel**. Vercel offers seamless integration with Next.js, CI/CD, global CDN, and serverless functions.
*   **Backend API (NestJS):** Deployed as a Web Service on **Render.com**. Render provides easy deployment for Dockerized applications or Node.js services.
*   **Database (PostgreSQL):** Hosted as a Managed Database on **Render.com**, ensuring backups, scalability, and easy connectivity with the backend service.
*   **CI/CD:**
    *   Vercel handles CI/CD for the frontend automatically from Git commits.
    *   Render.com can be configured for CI/CD from Git commits for the backend.
    *   Separate workflows can be set up for testing and linting on each commit/PR.