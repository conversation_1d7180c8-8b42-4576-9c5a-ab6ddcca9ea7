# Vercel Functions

Vercel Functions lets you run server-side code without managing servers. They adapt automatically to user demand, handle connections to APIs and databases, and offer enhanced concurrency through [fluid compute](/docs/functions/fluid-compute), which is useful for AI workloads or any I/O-bound tasks that require efficient scaling

When you deploy your application, Vercel automatically sets up the tools and optimizations for your chosen [framework](/docs/frameworks). It ensures low latency by routing traffic through Vercel's [Edge Network](/docs/edge-network), and placing your functions in a specific region when you need more control over [data locality](/docs/functions#functions-and-your-data-source).

![Functions location within Vercel's managed infrastructure](/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1737717848%2Ffront%2Fdocs%2Fvercel-functions%2Ffirst_image_light.png&w=3840&q=75)![Functions location within Vercel's managed infrastructure](/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1737717848%2Ffront%2Fdocs%2Fvercel-functions%2Ffirst_image_dark.png&w=3840&q=75)

Functions location within Vercel's managed infrastructure

## [Getting started](#getting-started)

To get started with creating your first function, copy the code below:

Next.js (/app)Next.js (/pages)Other frameworks

```
export function GET(request: Request) {
  return new Response('Hello from Vercel!');
}
```

To learn more, see the [quickstart](/docs/functions/quickstart) or [deploy a template](/templates).

## [Functions lifecycle](#functions-lifecycle)

Vercel Functions run in a single [region](/docs/functions/configuring-functions/region) by default, although you can configure them to run in multiple regions if you have globally replicated data. These functions let you add extra capabilities to your application, such as handling authentication, streaming data, or querying databases.

When a user sends a request to your site, Vercel can automatically run a function based on your application code. You do not need to manage servers, or handle scaling.

Vercel creates a new function invocation for each incoming request. If another request arrives soon after the previous one, Vercel [reuses the same function](/docs/functions/fluid-compute#optimized-concurrency) instance to optimize performance and cost efficiency. Over time, Vercel only keeps as many active functions as needed to handle your traffic. Vercel scales your functions down to zero when there are no incoming requests.

By allowing concurrent execution within the same instance (and so using idle time for compute), fluid compute reduces cold starts, lowers latency, and saves on compute costs. It also prevents the need to spin up multiple isolated instances when tasks spend most of their time waiting for external operations.

### [Functions and your data source](#functions-and-your-data-source)

Functions should always execute close to where your data source is to reduce latency. By default, functions using the Node.js runtime execute in Washington, D.C., USA (`iad1`), a common location for external data sources. You can set a new region through your [project's settings on Vercel](/docs/functions/configuring-functions/region#setting-your-default-region).

## [Viewing Vercel Function metrics](#viewing-vercel-function-metrics)

You can view various performance and cost efficiency metrics using Vercel Observability:

1.  Choose your project from the [dashboard](https://vercel.com/d?to=%2F%5Bteam%5D%2F%5Bproject%5D&title=Go+to+dashboard).
2.  Click on the Observability tab and select the Vercel Functions section.
3.  Click on the chevron icon to expand and see all charts.

From here, you'll be able to see total consumed and saved GB-Hours, and the ratio of the saved usage. When you have [fluid](/docs/functions/fluid-compute) enabled, you will also see the amount of cost savings from the [optimized concurrency model](/docs/functions/fluid-compute#optimized-concurrency).

## [Related](#related)

*   [What is compute?](/docs/fundamentals/what-is-compute)
*   [What is streaming?](/docs/fundamentals/what-is-streaming)
*   [Fluid compute](/docs/functions/fluid-compute)
*   [Runtimes](/docs/functions/runtimes)
*   [Configuring functions](/docs/functions/configuring-functions)
*   [Streaming](/docs/functions/streaming-functions)
*   [Limits](/docs/functions/limitations)
*   [Functions logs](/docs/functions/logs)

Last updated on March 12, 2025