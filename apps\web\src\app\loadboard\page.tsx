"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { BookLoadDialog } from "@/components/ui/book-load-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search,
  MapPin,
  Calendar,
  Truck,
  DollarSign,
  Filter,
  RefreshCw,
  Loader2,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { apiClient } from '@/lib/api-client';

interface Load {
  id: string;
  proNumber?: string;
  origin?: string;
  destination?: string;
  pickupDateTime?: string;
  deliveryDateTime?: string;
  equipment?: string;
  weight?: number;
  rate?: number;
  status?: string;
  miles?: number;
  temp?: string;
  commodity?: string;
  isPublic?: boolean;
  isTargeted?: boolean;
}

interface LoadFilters {
  originState: string;
  destinationState: string;
  equipmentType: string;
  minRate: string;
  maxRate: string;
  searchQuery: string;
}

export default function LoadboardPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const { getToken } = useAuth();
  
  const [loads, setLoads] = useState<Load[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LoadFilters>({
    originState: 'all',
    destinationState: 'all',
    equipmentType: 'all',
    minRate: '',
    maxRate: '',
    searchQuery: ''
  });
  
  // Dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedLoad, setSelectedLoad] = useState<Load | null>(null);
  const [isBookingSubmitting, setIsBookingSubmitting] = useState(false);
  const [dialogMode, setDialogMode] = useState<'book' | 'bid'>('book');

  const fetchLoads = useCallback(async () => {
    if (!isUserLoaded || !user) return;
    
    setIsLoading(true);
    setError(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (filters.originState && filters.originState !== 'all') params.append('originState', filters.originState);
      if (filters.destinationState && filters.destinationState !== 'all') params.append('destinationState', filters.destinationState);
      if (filters.equipmentType && filters.equipmentType !== 'all') params.append('equipmentType', filters.equipmentType);
      if (filters.minRate) params.append('minRate', filters.minRate);
      if (filters.maxRate) params.append('maxRate', filters.maxRate);
      if (filters.searchQuery) params.append('search', filters.searchQuery);

      const data = await apiClient.get<{ loads: Load[] }>(`airtable-orders/available?${params.toString()}`);

      setLoads(data.loads || []);

    } catch (error) {
      console.error('Error fetching loads:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
      toast.error('Failed to load loads');
    } finally {
      setIsLoading(false);
    }
  }, [isUserLoaded, user, getToken, filters]);

  useEffect(() => {
    fetchLoads();
  }, [fetchLoads]);

  const handleFilterChange = (key: keyof LoadFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleBidSubmit = async (loadId: string, bidAmount: number, notes?: string) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const result = await apiClient.post(`airtable-orders/${loadId}/bid`, {
        bidAmount,
        carrierNotes: notes,
      });

      toast.success('Bid submitted successfully!');
      // Refresh loads to update status
      fetchLoads();

    } catch (error) {
      console.error('Error submitting bid:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit bid');
    }
  };

  const handleBookLoad = (load: Load) => {
    setSelectedLoad(load);
    setDialogMode('book');
    setDialogOpen(true);
  };

  const handlePlaceBid = (load: Load) => {
    setSelectedLoad(load);
    setDialogMode('bid');
    setDialogOpen(true);
  };

  const handleBookingSubmit = async (loadId: string, bookingDetails: any) => {
    setIsBookingSubmitting(true);
    
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const result = await apiClient.post(`airtable-orders/${loadId}/book`, bookingDetails);

      toast.success('Booking request submitted successfully! Awaiting admin approval.');
      
      // Refresh loads to update status
      fetchLoads();

    } catch (error: any) {
      console.error('Error submitting booking request:', error);
      
      // Handle specific error messages from the API
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('Failed to submit booking request');
      }
      
      throw error; // Re-throw so the dialog can handle it
    } finally {
      setIsBookingSubmitting(false);
    }
  };

  // Show loading state
  if (!isUserLoaded || isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading loads...</span>
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Unable to Load Loadboard</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchLoads} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loadboard</h1>
            <p className="text-muted-foreground">
              Find and bid on available loads
            </p>
          </div>
          
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Button onClick={fetchLoads} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <div>
                <label htmlFor="search" className="text-sm font-medium text-foreground">Search</label>
                <Input
                  id="search"
                  placeholder="Search loads..."
                  value={filters.searchQuery}
                  onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                  className="bg-background text-foreground border-border"
                />
              </div>
              
              <div>
                <label htmlFor="originState" className="text-sm font-medium text-foreground">Origin State</label>
                <Select value={filters.originState} onValueChange={(value) => handleFilterChange('originState', value === 'all' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any state" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any state</SelectItem>
                    <SelectItem value="TX">Texas</SelectItem>
                    <SelectItem value="CA">California</SelectItem>
                    <SelectItem value="FL">Florida</SelectItem>
                    <SelectItem value="NY">New York</SelectItem>
                    {/* Add more states as needed */}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="destinationState" className="text-sm font-medium text-foreground">Destination State</label>
                <Select value={filters.destinationState} onValueChange={(value) => handleFilterChange('destinationState', value === 'all' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any state" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any state</SelectItem>
                    <SelectItem value="TX">Texas</SelectItem>
                    <SelectItem value="CA">California</SelectItem>
                    <SelectItem value="FL">Florida</SelectItem>
                    <SelectItem value="NY">New York</SelectItem>
                    {/* Add more states as needed */}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="equipmentType" className="text-sm font-medium text-foreground">Equipment</label>
                <Select value={filters.equipmentType} onValueChange={(value) => handleFilterChange('equipmentType', value === 'all' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any equipment" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any equipment</SelectItem>
                    <SelectItem value="Dry Van">Dry Van</SelectItem>
                    <SelectItem value="Reefer">Reefer</SelectItem>
                    <SelectItem value="Flatbed">Flatbed</SelectItem>
                    <SelectItem value="Step Deck">Step Deck</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="minRate" className="text-sm font-medium text-foreground">Min Rate</label>
                <Input
                  id="minRate"
                  type="number"
                  placeholder="Min rate"
                  value={filters.minRate}
                  onChange={(e) => handleFilterChange('minRate', e.target.value)}
                  className="bg-background text-foreground border-border"
                />
              </div>

              <div>
                <label htmlFor="maxRate" className="text-sm font-medium text-foreground">Max Rate</label>
                <Input
                  id="maxRate"
                  type="number"
                  placeholder="Max rate"
                  value={filters.maxRate}
                  onChange={(e) => handleFilterChange('maxRate', e.target.value)}
                  className="bg-background text-foreground border-border"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                  <Search className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Loads</p>
                  <p className="text-2xl font-bold text-foreground">{loads.length}</p>
                  <p className="text-xs text-muted-foreground">In system</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                  <Truck className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Available Now</p>
                  <p className="text-2xl font-bold text-foreground">{loads.length}</p>
                  <p className="text-xs text-muted-foreground">Ready for booking</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                  <DollarSign className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Avg Rate</p>
                  <p className="text-2xl font-bold text-foreground">
                    ${loads.length > 0 ? Math.round(loads.reduce((sum, load) => sum + (load.rate || 0), 0) / loads.length).toLocaleString() : 0}
                  </p>
                  <p className="text-xs text-muted-foreground">Average rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Load Results Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Search className="mr-2 h-5 w-5" />
                Load Results
              </CardTitle>
              <p className="text-sm text-muted-foreground">{loads.length} of {loads.length} loads</p>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="border-b bg-muted/50">
                  <tr>
                    <th className="text-left p-3 font-medium text-foreground">Pro #</th>
                    <th className="text-left p-3 font-medium text-foreground">Origin</th>
                    <th className="text-left p-3 font-medium text-foreground">Destination</th>
                    <th className="text-left p-3 font-medium text-foreground">Miles</th>
                    <th className="text-left p-3 font-medium text-foreground">DH-O</th>
                    <th className="text-left p-3 font-medium text-foreground">Pickup</th>
                    <th className="text-left p-3 font-medium text-foreground">Delivery</th>
                    <th className="text-left p-3 font-medium text-foreground">Equipment</th>
                    <th className="text-left p-3 font-medium text-foreground">Temp</th>
                    <th className="text-left p-3 font-medium text-foreground">Weight</th>
                    <th className="text-left p-3 font-medium text-foreground">Commodity</th>
                    <th className="text-left p-3 font-medium text-foreground">Status</th>
                    <th className="text-left p-3 font-medium text-foreground">Rate</th>
                    <th className="text-left p-3 font-medium text-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loads.length > 0 ? (
                    loads.map((load) => (
                      <tr 
                        key={load.id} 
                        className={`border-b hover:bg-muted/30 transition-colors ${
                          load.isTargeted ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                      >
                        <td className="p-3">
                          <div className="flex flex-col">
                            <span className="font-mono text-sm border rounded px-2 py-1 bg-muted/30 inline-block w-fit text-foreground">
                              {load.proNumber || 'TBD'}
                            </span>
                            {load.isTargeted && (
                              <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 text-xs mt-1 w-fit">
                                Targeted
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center space-x-1">
                            <span className="w-2 h-2 rounded-full bg-green-500"></span>
                            <span className="text-foreground">{load.origin || 'TBD'}</span>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center space-x-1">
                            <span className="w-2 h-2 rounded-full bg-red-500"></span>
                            <span className="text-foreground">{load.destination || 'TBD'}</span>
                          </div>
                        </td>
                        <td className="p-3 text-foreground">{load.miles || 'TBD'}</td>
                        <td className="p-3 text-foreground">-</td>
                        <td className="p-3">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-blue-500" />
                            <span className="text-xs text-foreground">
                              {load.pickupDateTime 
                                ? new Date(load.pickupDateTime).toLocaleDateString('en-US', { 
                                    month: 'short', 
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                  })
                                : 'TBD'
                              }
                            </span>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-orange-500" />
                            <span className="text-xs text-foreground">
                              {load.deliveryDateTime 
                                ? new Date(load.deliveryDateTime).toLocaleDateString('en-US', { 
                                    month: 'short', 
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                  })
                                : 'TBD'
                              }
                            </span>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center space-x-1">
                            <Truck className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-foreground">{load.equipment || 'TBD'}</span>
                          </div>
                        </td>
                        <td className="p-3 text-foreground">{load.temp || '—'}</td>
                        <td className="p-3 text-foreground">
                          {load.weight ? `${load.weight.toLocaleString()} lbs` : 'TBD'}
                        </td>
                        <td className="p-3 text-foreground">{load.commodity || 'General'}</td>
                        <td className="p-3">
                          <Badge variant="outline" className="text-xs bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700">
                            Available
                          </Badge>
                        </td>
                        <td className="p-3">
                          <div className="text-right">
                            <div className="font-bold text-green-600 dark:text-green-400">
                              ${load.rate?.toLocaleString() || '0'}
                            </div>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex space-x-1">
                            <Button 
                              size="sm" 
                              variant="outline"
                              className="text-xs h-7"
                              onClick={() => handleBookLoad(load)}
                            >
                              Book Now
                            </Button>
                            <Button 
                              size="sm" 
                              variant="default"
                              className="text-xs h-7"
                              onClick={() => handlePlaceBid(load)}
                            >
                              Place Bid
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={14} className="p-8 text-center">
                        <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No loads found</h3>
                        <p className="text-muted-foreground">Try adjusting your search filters to find more loads.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Booking/Bidding Dialog */}
      <BookLoadDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        load={selectedLoad}
        onSubmit={handleBookingSubmit}
        onBidSubmit={async (loadId, biddingDetails) => {
          // Legacy compatibility - the component now handles N8N webhook directly
          console.log('Bid submitted for load:', loadId, biddingDetails);
        }}
        isSubmitting={isBookingSubmitting}
        mode={dialogMode}
      />
    </PageLayout>
  );
} 