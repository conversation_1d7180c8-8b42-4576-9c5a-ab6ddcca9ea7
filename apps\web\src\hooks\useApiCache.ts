import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../contexts/auth-context';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  loading: boolean;
}

// Type definitions for API responses
interface AuthMeResponse {
  id: string;
  clerkUserId: string;
  role: 'ADMIN' | 'CARRIER' | 'SHIPPER';
  orgName?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  carrierProfile?: any;
  createdAt: string;
  updatedAt: string;
}

interface AssignedLoad {
  id: string;
  airtableRecordId: string;
  proNumber: string;
  status: string;
  rate: number;
  origin: string;
  destination: string;
  pickupDateTime: string;
  deliveryDateTime: string;
  weight: number;
  equipment: string;
}

interface CarrierBid {
  id: string;
  bidAmount: number;
  carrierNotes?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  load: {
    id: string;
    airtableRecordId: string;
    proNumber: string;
    status: string;
    rate: number;
    origin: string;
    destination: string;
    pickupDateTime: string;
    deliveryDateTime: string;
    weight: number;
    equipment: string;
  };
  competingBids: {
    total: number;
    highest: number | null;
    myRank: number;
  };
}

// Global cache to share data across components
const globalCache = new Map<string, CacheEntry<any>>();

interface UseApiCacheOptions {
  cacheTime?: number; // Cache duration in milliseconds (default: 30 seconds)
  staleTime?: number; // Time before refetch (default: 10 seconds)
}

export function useApiCache<T>(
  endpoint: string, 
  options: UseApiCacheOptions = {}
) {
  const { getToken } = useAuth();
  const { cacheTime = 30000, staleTime = 10000 } = options;
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchData = useCallback(async (force = false) => {
    const cacheKey = endpoint;
    const cached = globalCache.get(cacheKey);
    const now = Date.now();

    // Return cached data if it's fresh and not forced
    if (!force && cached && !cached.loading && (now - cached.timestamp < cacheTime)) {
      setData(cached.data);
      setLoading(false);
      setError(null);
      return cached.data;
    }

    // If another request is in progress, wait for it
    if (cached?.loading) {
      return new Promise((resolve) => {
        const checkCache = () => {
          const current = globalCache.get(cacheKey);
          if (current && !current.loading) {
            setData(current.data);
            setLoading(false);
            resolve(current.data);
          } else {
            setTimeout(checkCache, 100);
          }
        };
        checkCache();
      });
    }

    // Mark as loading in cache
    globalCache.set(cacheKey, {
      data: cached?.data || null,
      timestamp: cached?.timestamp || 0,
      loading: true
    });

    setLoading(true);
    setError(null);

    try {
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();
      
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Update cache with successful result
      globalCache.set(cacheKey, {
        data: result,
        timestamp: now,
        loading: false
      });

      setData(result);
      setError(null);
      return result;

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return; // Request was cancelled, don't update state
      }

      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      
      // Mark as not loading in cache but keep old data
      const current = globalCache.get(cacheKey);
      if (current) {
        globalCache.set(cacheKey, {
          ...current,
          loading: false
        });
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [endpoint, getToken, cacheTime]);

  const refresh = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Initial fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Auto-refresh stale data
  useEffect(() => {
    const interval = setInterval(() => {
      const cached = globalCache.get(endpoint);
      if (cached && !cached.loading && (Date.now() - cached.timestamp > staleTime)) {
        fetchData();
      }
    }, staleTime);

    return () => clearInterval(interval);
  }, [endpoint, fetchData, staleTime]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    refresh,
    // Utility to check if data is stale
    isStale: () => {
      const cached = globalCache.get(endpoint);
      return !cached || (Date.now() - cached.timestamp > staleTime);
    }
  };
}

// Hook for common endpoints
export function useAuthMe() {
  return useApiCache<AuthMeResponse>('/api/v1/auth/me', { cacheTime: 60000, staleTime: 30000 });
}

export function useAssignedLoads() {
  return useApiCache<AssignedLoad[]>('/api/v1/airtable-orders/assigned', { cacheTime: 30000, staleTime: 15000 });
}

export function useCarrierBids() {
  return useApiCache<CarrierBid[]>('/api/v1/airtable-orders/bids', { cacheTime: 30000, staleTime: 15000 });
} 