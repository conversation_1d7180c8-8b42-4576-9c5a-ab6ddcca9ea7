'use client';

import { useEffect, useState } from 'react';

export default function DebugEnvPage() {
  const [buildTimeEnv, setBuildTimeEnv] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    try {
      // Log all environment variables that we can access
      const envInfo = {
        publicVars: Object.keys(process.env)
          .filter(key => key.startsWith('NEXT_PUBLIC_'))
          .reduce((acc, key) => {
            acc[key] = process.env[key];
            return acc;
          }, {} as Record<string, string | undefined>),
        specificVars: {
          NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
          NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
          NODE_ENV: process.env.NODE_ENV,
          VERCEL_ENV: process.env.NEXT_PUBLIC_VERCEL_ENV,
        },
        processEnvKeys: Object.keys(process.env),
      };
      
      setBuildTimeEnv(JSON.stringify(envInfo, null, 2));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    }
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Debug Info</h1>
      
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded">
          Error: {error}
        </div>
      )}

      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Environment Information:</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-[500px]">
          {buildTimeEnv || 'Loading...'}
        </pre>
      </div>

      <div className="mt-8 p-4 bg-yellow-50 rounded">
        <h3 className="font-semibold">Troubleshooting Notes:</h3>
        <ul className="list-disc pl-4 mt-2">
          <li>Environment variables must be set in Vercel project settings</li>
          <li>Only NEXT_PUBLIC_* variables are available in the browser</li>
          <li>Changes to environment variables require a new deployment</li>
          <li>Build cache should be cleared when changing environment variables</li>
        </ul>
      </div>
    </div>
  );
} 