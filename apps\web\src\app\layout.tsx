import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";


const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  preload: false, // Let Next.js handle preloading automatically
  display: 'swap', // Improve loading performance
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  preload: false, // Let Next.js handle preloading automatically
  display: 'swap', // Improve loading performance
});

export const metadata: Metadata = {
  title: "FCP Carrier Portal",
  description: "Carrier Portal for First Cut Produce",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // CSP and nonce handling is done in middleware.ts
  
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Security and performance meta tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#0F172A" />
        {/* Performance optimization */}
        <link rel="dns-prefetch" href="//vercel.live" />
        <link rel="preconnect" href="https://fonts.googleapis.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} font-sans antialiased`}>
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem={true}
            disableTransitionOnChange
          >
            {children}
            <Toaster />
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
