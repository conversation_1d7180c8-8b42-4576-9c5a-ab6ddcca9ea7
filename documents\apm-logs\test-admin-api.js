// Simple test script to validate admin settings API
const API_BASE = 'https://api.fcp-portal.com';

async function testAdminAPI() {
  console.log('Testing Admin Settings API...');
  
  // Test basic connectivity
  try {
    const response = await fetch(`${API_BASE}/api/v1/health`);
    console.log(`Health check: ${response.status}`);
  } catch (error) {
    console.log('Health check failed:', error.message);
  }
  
  // Test admin settings endpoint (without auth - should return 401)
  try {
    const response = await fetch(`${API_BASE}/api/v1/admin/settings`);
    console.log(`Admin settings (no auth): ${response.status}`);
    const data = await response.json();
    console.log('Response:', data);
  } catch (error) {
    console.log('Admin settings test failed:', error.message);
  }
}

testAdminAPI(); 