export declare enum SortField {
    PICKUP_DATE = "pickupDate",
    DELIVERY_DATE = "deliveryDate",
    RATE = "rate",
    DISTANCE = "distance",
    CREATED_AT = "createdAt",
    WEIGHT = "weight",
    EQUIPMENT = "equipment"
}
export declare enum SortOrder {
    ASC = "asc",
    DESC = "desc"
}
export declare class AdvancedFiltersDto {
    origin?: string;
    destination?: string;
    equipment?: string;
    equipmentTypes?: string[];
    minRate?: number;
    maxRate?: number;
    minWeight?: number;
    maxWeight?: number;
    pickupDateStart?: Date;
    pickupDateEnd?: Date;
    deliveryDateStart?: Date;
    deliveryDateEnd?: Date;
    geoCenter?: string;
    geoRadius?: number;
    geoFilterOrigin?: boolean;
    search?: string;
    sortBy?: SortField;
    sortOrder?: SortOrder;
    page?: number;
    pageSize?: number;
}
export declare class SavedSearchDto {
    name: string;
    criteria?: AdvancedFiltersDto;
    isDefault?: boolean;
}
