"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var NotificationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let NotificationsService = NotificationsService_1 = class NotificationsService {
    prisma;
    logger = new common_1.Logger(NotificationsService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async broadcastNewLoad(loadData) {
        this.logger.log(`New load posted: ${loadData.loadId} - N8N handles notifications`);
    }
    async broadcastLoadStatusChange(statusData) {
        this.logger.log(`Load status change: ${statusData.loadId} from ${statusData.oldStatus} to ${statusData.newStatus} - N8N handles notifications`);
    }
    async broadcastBidUpdate(bidData) {
        this.logger.log(`Bid update: ${bidData.bidId} status ${bidData.status} - N8N handles notifications`);
    }
    async broadcastToUser(userId, notification) {
        this.logger.log(`User notification for ${userId}: ${notification.type} - N8N handles delivery`);
    }
    async broadcastToOrganization(organizationId, notification) {
        this.logger.log(`Organization notification for ${organizationId}: ${notification.type} - N8N handles delivery`);
    }
    async broadcastBookingConfirmation(loadId, carrierUserId, loadDetails) {
        this.logger.log(`Booking confirmation for load ${loadId} by carrier ${carrierUserId} - N8N handles notifications`);
    }
    async broadcastLoadAssignment(loadId, carrierUserId, loadDetails) {
        this.logger.log(`Load assignment: ${loadId} to carrier ${carrierUserId} - N8N handles notifications`);
    }
    async broadcastSystemAnnouncement(title, message, data) {
        this.logger.log(`System announcement: ${title} - N8N handles delivery`);
    }
    getConnectionStats() {
        return { connectedUsers: 0 };
    }
    isUserConnected(userId) {
        return false;
    }
    async broadcastToAdmins(notification) {
        this.logger.log(`Admin notification: ${notification.type} - N8N handles delivery`);
    }
    getBidStatusMessage(status, bidAmount) {
        switch (status) {
            case 'pending':
                return `Your bid of $${bidAmount.toLocaleString()} has been submitted and is pending review.`;
            case 'accepted':
                return `Congratulations! Your bid of $${bidAmount.toLocaleString()} has been accepted.`;
            case 'rejected':
                return `Your bid of $${bidAmount.toLocaleString()} was not accepted this time.`;
            case 'countered':
                return `Your bid of $${bidAmount.toLocaleString()} received a counter-offer.`;
            default:
                return `Your bid status has been updated to: ${status}`;
        }
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = NotificationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map