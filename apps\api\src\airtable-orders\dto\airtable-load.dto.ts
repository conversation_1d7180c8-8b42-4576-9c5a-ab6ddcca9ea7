// apps/api/src/airtable-orders/dto/airtable-load.dto.ts
// DTO for the "fields" part of the Airtable webhook payload

// import { IsString, IsNotEmpty, IsOptional, IsNumber, ValidateNested, IsObject } from 'class-validator';
// import { Type } from 'class-transformer';

// This DTO represents the flat payload sent by your Airtable automation script.
export class AirtableLoadWebhookDto {
  // @IsString()
  // @IsNotEmpty()
  airtable_record_id: string; // Matches the snake_case from your script

  // @IsString()
  // @IsOptional()
  status?: string;

  // @IsString()
  // @IsOptional()
  payout?: string; // Keep as string if Airtable sends it as such, parse in service

  // @IsString()
  // @IsOptional()
  origin_city?: string;

  // @IsString()
  // @IsOptional()
  origin_state?: string;

  // @IsString()
  // @IsOptional()
  destination_city?: string;

  // @IsString()
  // @IsOptional()
  destination_state?: string;

  // @IsString()
  // @IsOptional()
  distance?: string; // Keep as string, parse in service

  // @IsString()
  // @IsOptional()
  pro_number?: string;

  // @IsString()
  // @IsOptional()
  pickup_date_utc?: string;

  // @IsString()
  // @IsOptional()
  delivery_date_utc?: string;

  // @IsString()
  // @IsOptional()
  equipment_required?: string;

  // @IsNumber()
  // @IsOptional()
  weight_lbs?: number;

  // @IsNumber()
  // @IsOptional()
  rate?: number;

  // @IsString()
  // @IsOptional()
  temperature?: string;

  // Organization targeting fields
  // @IsOptional()
  target_organizations?: string | string[]; // Multiple select field from Airtable

  // @IsOptional()
  is_public?: boolean; // Checkbox field from Airtable

  // @IsObject()
  // @IsOptional()
  // This will contain the raw fields as fetched from Airtable, using Airtable's field names
  raw_airtable_data?: Record<string, any>;
}

// The old DTOs below are no longer representative of what the current Airtable script sends.
// They are kept here for reference or if you revert the Airtable script.

export class AirtableLoadDataDto {
  // Assuming Airtable sends field names as they appear in their UI
  // These are based on the fields fetched in AirtableOrdersService.getAvailableLoads()
  // @IsString()
  // @IsNotEmpty()
  // Adjust the names if they differ in the actual webhook payload from Airtable.
  'Record ID': string; // This is likely the Airtable Record ID itself

  // @IsString()
  // @IsOptional()
  'Status'?: string;

  // @IsNumber()
  // @IsOptional()
  'Payout'?: number;

  // @IsString()
  // @IsOptional()
  'Origin City'?: string;

  // @IsString()
  // @IsOptional()
  'Origin State'?: string;

  // @IsString()
  // @IsOptional()
  'Destination City'?: string;

  // @IsString()
  // @IsOptional()
  'Destination State'?: string;

  // @IsNumber()
  // @IsOptional()
  'Distance (Miles)'?: number;

  // @IsString()
  // @IsOptional()
  'Pro Number'?: string;

  // Add other fields as needed based on your Airtable base and what you want to sync
}

export class AirtableWebhookPayloadDto {
  // @IsString()
  // @IsNotEmpty()
  airtableRecordId: string;

  // @ValidateNested()
  // @Type(() => AirtableLoadDataDto)
  fields: AirtableLoadDataDto;
}

// If Airtable sends a more complex payload, e.g., for batched updates or different event types,
// you might need a more encompassing DTO like this:
export class AirtableWebhookEventDto {
  // @IsString()
  // @IsOptional()
  // eventType?: string; // e.g., 'created', 'updated', 'batched'

  // @ValidateNested({ each: true })
  // @Type(() => AirtableWebhookPayloadDto)
  // @IsOptional()
  // records?: Array<{ recordId: string; fields: AirtableLoadDataDto }>; // For batch events
  payload: AirtableWebhookPayloadDto; // Or directly embed fields if simpler
} 