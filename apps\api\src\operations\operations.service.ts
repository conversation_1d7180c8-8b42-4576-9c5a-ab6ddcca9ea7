import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { PatternAnalysisService } from './services/pattern-analysis.service';
import { SmartSuggestionsService } from './services/smart-suggestions.service';
import { RadarDistanceService } from './services/radar-distance.service';

export interface Lane {
  id: string;
  originCity: string;
  originState: string;
  destinationCity: string;
  destinationState: string;
  estimatedMiles: number;
  estimatedDuration: string;
  frequencyRank: number;
  lastUsed?: string;
}

interface CreateOrderResult {
  id: string;
  airtableRecordId?: string;
}

@Injectable()
export class OperationsService {
  private readonly logger = new Logger(OperationsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly patternAnalysis: PatternAnalysisService,
    private readonly smartSuggestions: SmartSuggestionsService,
    private readonly radarDistance: RadarDistanceService,
  ) {}

  /**
   * Get available lanes by analyzing existing load data
   * Generates lane combinations from historical pickup/delivery data
   */
  async getLanes(): Promise<Lane[]> {
    this.logger.log('Getting lanes for operations - checking cached lanes first');

    try {
      // First, try to get pre-calculated lanes from cache/database
      const cachedLanes = await this.getCachedLanes();
      if (cachedLanes && cachedLanes.length > 0) {
        this.logger.log(`✅ Retrieved ${cachedLanes.length} pre-calculated lanes from cache`);
        return cachedLanes;
      }

      // If no cached lanes, generate basic lanes without expensive distance calculations
      this.logger.log('No cached lanes found, generating basic lanes with fast calculations');
      return await this.generateBasicLanes();

    } catch (error) {
      this.logger.error('Error retrieving lanes:', error);
      // Fallback to basic lanes generation
      return await this.generateBasicLanes();
    }
  }

  /**
   * Get pre-calculated lanes from cache (could be database table or Redis in future)
   */
  private async getCachedLanes(): Promise<Lane[] | null> {
    try {
      // For now, we'll check if distance cache has enough data to work with
      const cacheCount = await this.prisma.distanceCache.count();
      
      if (cacheCount < 10) {
        // Not enough cached data, return null to trigger basic generation
        this.logger.debug('Insufficient cached distance data, will generate basic lanes');
        return null;
      }

      // Get unique lanes from existing loads and try to use cached distances
      const rawLanes = await this.prisma.load.groupBy({
        by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
        where: {
          AND: [
            { originCity: { not: null } },
            { originState: { not: null } },
            { destinationCity: { not: null } },
            { destinationState: { not: null } }
          ]
        },
        _count: { id: true },
        _max: { createdAt: true }
      });

      const lanes: Lane[] = [];
      
      for (let index = 0; index < rawLanes.length; index++) {
        const rawLane = rawLanes[index];
        const frequency = rawLane._count.id;
        const lastUsed = rawLane._max.createdAt?.toISOString();
        
        const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
        
        // Try to get cached distance
        const originAddr = `${rawLane.originCity}, ${rawLane.originState}`;
        const destAddr = `${rawLane.destinationCity}, ${rawLane.destinationState}`;
        
        const cachedDistance = await this.prisma.distanceCache.findFirst({
          where: {
            OR: [
              {
                originAddress: { contains: rawLane.originCity!, mode: 'insensitive' },
                destinationAddress: { contains: rawLane.destinationCity!, mode: 'insensitive' }
              },
              {
                originAddress: originAddr,
                destinationAddress: destAddr
              }
            ]
          }
        });

        const estimatedMiles = cachedDistance ? 
          Math.round(cachedDistance.distanceMiles) : 
          this.calculateQuickDistance(rawLane.originState!, rawLane.destinationState!);
        
        const estimatedDuration = this.calculateEstimatedDuration(estimatedMiles);
        const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);

        lanes.push({
          id: laneId,
          originCity: rawLane.originCity!,
          originState: rawLane.originState!,
          destinationCity: rawLane.destinationCity!,
          destinationState: rawLane.destinationState!,
          estimatedMiles,
          estimatedDuration,
          frequencyRank,
          lastUsed
        });
      }

      // Sort by frequency rank
      return lanes.sort((a, b) => {
        if (b.frequencyRank !== a.frequencyRank) {
          return b.frequencyRank - a.frequencyRank;
        }
        return a.originCity.localeCompare(b.originCity);
      });

    } catch (error) {
      this.logger.error('Error getting cached lanes:', error);
      return null;
    }
  }

  /**
   * Generate basic lanes quickly without expensive API calls
   */
  private async generateBasicLanes(): Promise<Lane[]> {
    this.logger.log('Generating basic lanes with fast state-to-state distance calculations');

    const rawLanes = await this.prisma.load.groupBy({
      by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
      where: {
        AND: [
          { originCity: { not: null } },
          { originState: { not: null } },
          { destinationCity: { not: null } },
          { destinationState: { not: null } }
        ]
      },
      _count: { id: true },
      _max: { createdAt: true }
    });

    this.logger.log(`Found ${rawLanes.length} unique lane combinations from historical data`);

    const lanes: Lane[] = rawLanes.map((rawLane, index) => {
      const frequency = rawLane._count.id;
      const lastUsed = rawLane._max.createdAt?.toISOString();
      const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
      
      // Use quick state-to-state distance calculation
      const estimatedMiles = this.calculateQuickDistance(rawLane.originState!, rawLane.destinationState!);
      const estimatedDuration = this.calculateEstimatedDuration(estimatedMiles);
      const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);

      return {
        id: laneId,
        originCity: rawLane.originCity!,
        originState: rawLane.originState!,
        destinationCity: rawLane.destinationCity!,
        destinationState: rawLane.destinationState!,
        estimatedMiles,
        estimatedDuration,
        frequencyRank,
        lastUsed
      };
    });

    // Sort by frequency rank
    const sortedLanes = lanes.sort((a, b) => {
      if (b.frequencyRank !== a.frequencyRank) {
        return b.frequencyRank - a.frequencyRank;
      }
      return a.originCity.localeCompare(b.originCity);
    });

    this.logger.log(`✅ Generated ${sortedLanes.length} basic lanes quickly using state-to-state distances`);
    this.logger.log('💡 For more accurate distances, run the background lane calculation job');
    
    return sortedLanes;
  }

  /**
   * Quick state-to-state distance calculation for fast lane generation
   */
  private calculateQuickDistance(originState: string, destinationState: string): number {
    // State center coordinates (approximate)
    const stateCoords: Record<string, { lat: number; lng: number }> = {
      'AL': { lat: 32.318, lng: -86.902 }, 'AK': { lat: 64.068, lng: -152.2782 },
      'AZ': { lat: 34.292, lng: -111.666 }, 'AR': { lat: 34.799, lng: -92.199 },
      'CA': { lat: 36.778, lng: -119.417 }, 'CO': { lat: 39.113, lng: -105.358 },
      'CT': { lat: 41.767, lng: -72.677 }, 'DE': { lat: 38.910, lng: -75.527 },
      'FL': { lat: 27.766, lng: -81.686 }, 'GA': { lat: 32.157, lng: -82.907 },
      'HI': { lat: 19.741, lng: -155.844 }, 'ID': { lat: 44.068, lng: -114.742 },
      'IL': { lat: 40.003, lng: -89.000 }, 'IN': { lat: 39.790, lng: -86.147 },
      'IA': { lat: 41.590, lng: -93.620 }, 'KS': { lat: 38.500, lng: -98.000 },
      'KY': { lat: 37.839, lng: -84.270 }, 'LA': { lat: 30.391, lng: -92.329 },
      'ME': { lat: 45.367, lng: -68.972 }, 'MD': { lat: 39.045, lng: -76.641 },
      'MA': { lat: 42.407, lng: -71.382 }, 'MI': { lat: 44.182, lng: -84.506 },
      'MN': { lat: 46.392, lng: -94.636 }, 'MS': { lat: 32.354, lng: -89.398 },
      'MO': { lat: 38.573, lng: -92.603 }, 'MT': { lat: 46.965, lng: -109.533 },
      'NE': { lat: 41.492, lng: -99.901 }, 'NV': { lat: 38.502, lng: -116.654 },
      'NH': { lat: 43.193, lng: -71.549 }, 'NJ': { lat: 40.221, lng: -74.756 },
      'NM': { lat: 34.307, lng: -106.018 }, 'NY': { lat: 42.659, lng: -73.781 },
      'NC': { lat: 35.771, lng: -78.638 }, 'ND': { lat: 47.650, lng: -100.437 },
      'OH': { lat: 40.367, lng: -82.996 }, 'OK': { lat: 35.482, lng: -97.534 },
      'OR': { lat: 44.931, lng: -120.767 }, 'PA': { lat: 40.269, lng: -76.875 },
      'RI': { lat: 41.342, lng: -71.422 }, 'SC': { lat: 33.830, lng: -81.163 },
      'SD': { lat: 44.045, lng: -99.793 }, 'TN': { lat: 35.860, lng: -86.660 },
      'TX': { lat: 31.106, lng: -97.563 }, 'UT': { lat: 39.421, lng: -111.950 },
      'VT': { lat: 44.558, lng: -72.580 }, 'VA': { lat: 37.768, lng: -78.169 },
      'WA': { lat: 47.042, lng: -120.806 }, 'WV': { lat: 38.349, lng: -81.633 },
      'WI': { lat: 44.745, lng: -89.626 }, 'WY': { lat: 42.755, lng: -107.302 }
    };

    const origin = stateCoords[originState];
    const destination = stateCoords[destinationState];

    if (!origin || !destination) {
      return 500; // Default fallback distance
    }

    if (originState === destinationState) {
      return 150; // Intra-state average
    }

    // Calculate straight-line distance and multiply by road factor
    const toRad = (degree: number) => degree * (Math.PI / 180);
    const earthRadius = 3959; // miles

    const dLat = toRad(destination.lat - origin.lat);
    const dLng = toRad(destination.lng - origin.lng);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(toRad(origin.lat)) * Math.cos(toRad(destination.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const straightLineDistance = earthRadius * c;

    // Apply road factor (roads are typically 1.2-1.4x straight line distance)
    return Math.round(straightLineDistance * 1.3);
  }

  /**
   * Background job to calculate accurate distances (can be run separately)
   */
  async calculateAccurateLanes(): Promise<Lane[]> {
    this.logger.log('Starting accurate lane calculation with Radar.com API (background job)');

    try {
      // Get unique address combinations from existing loads - prioritize full addresses when available
      const rawLanes = await this.prisma.load.groupBy({
        by: ['shipperAddress', 'receiverAddress', 'originCity', 'originState', 'destinationCity', 'destinationState'],
        where: {
          AND: [
            { originCity: { not: null } },
            { originState: { not: null } },
            { destinationCity: { not: null } },
            { destinationState: { not: null } }
          ]
        },
        _count: {
          id: true
        },
        _max: {
          createdAt: true
        }
      });

      this.logger.log(`Found ${rawLanes.length} unique lane combinations from historical data`);

      // Prepare batch requests for distance calculations using full addresses when available
      const distanceRequests = rawLanes.map(rawLane => {
        // Use full addresses if available, fallback to city/state
        const originAddress = rawLane.shipperAddress || `${rawLane.originCity}, ${rawLane.originState}`;
        const destinationAddress = rawLane.receiverAddress || `${rawLane.destinationCity}, ${rawLane.destinationState}`;
        
        return {
          originAddress: originAddress.trim(),
          destinationAddress: destinationAddress.trim()
        };
      });

      // Calculate distances using batch processing with rate limiting and caching
      this.logger.log('Starting batch distance calculations with Radar.com API, caching, and rate limiting...');
      this.logger.log('Using full shipper/receiver addresses for maximum accuracy');
      const distanceResults = await this.radarDistance.calculateDistancesBatchByAddress(distanceRequests);

      // Transform raw data into lane objects with calculated metrics
      const lanes: Lane[] = [];
      
      for (let index = 0; index < rawLanes.length; index++) {
        const rawLane = rawLanes[index];
        const distanceResult = distanceResults[index];
        const frequency = rawLane._count.id;
        const lastUsed = rawLane._max.createdAt?.toISOString();
        
        // Generate lane ID
        const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
        
        // Use the batch-calculated distance (rounded to nearest whole number as requested)
        const estimatedMiles = Math.round(distanceResult.distanceMiles);
        
        // Calculate estimated duration based on miles
        const estimatedDuration = this.calculateEstimatedDuration(estimatedMiles);
        
        // Calculate frequency rank (1-10 scale)
        const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);

        // Use city/state for display even if we used full addresses for calculation
        lanes.push({
          id: laneId,
          originCity: rawLane.originCity!,
          originState: rawLane.originState!,
          destinationCity: rawLane.destinationCity!,
          destinationState: rawLane.destinationState!,
          estimatedMiles,
          estimatedDuration,
          frequencyRank,
          lastUsed
        });

        // Log what addresses were used for calculation
        const originUsed = rawLane.shipperAddress ? 'full address' : 'city, state';
        const destUsed = rawLane.receiverAddress ? 'full address' : 'city, state';
        this.logger.debug(`Lane ${index + 1}: ${rawLane.originCity}, ${rawLane.originState} → ${rawLane.destinationCity}, ${rawLane.destinationState} = ${estimatedMiles} miles (${distanceResult.source}, origin: ${originUsed}, dest: ${destUsed})`);
      }

      // Sort by frequency rank (high to low) then by origin city
      const sortedLanes = lanes.sort((a, b) => {
        if (b.frequencyRank !== a.frequencyRank) {
          return b.frequencyRank - a.frequencyRank;
        }
        return a.originCity.localeCompare(b.originCity);
      });

      // Log summary statistics
      const radarCount = distanceResults.filter(r => r.source === 'radar').length;
      const cacheCount = distanceResults.filter(r => r.source === 'cache').length;
      const fallbackCount = distanceResults.filter(r => r.source === 'fallback').length;
      const fullAddressCount = rawLanes.filter(lane => lane.shipperAddress && lane.receiverAddress).length;
      const successRate = (((radarCount + cacheCount) / distanceResults.length) * 100).toFixed(1);

      this.logger.log(`✅ Generated ${sortedLanes.length} lanes with accurate distances:`);
      this.logger.log(`🏠 Full addresses used: ${fullAddressCount}/${rawLanes.length} lanes (${((fullAddressCount / rawLanes.length) * 100).toFixed(1)}%)`);
      this.logger.log(`💾 Cache hits: ${cacheCount} lanes`);
      this.logger.log(`📡 Radar.com API: ${radarCount} lanes`);
      this.logger.log(`🔄 Fallback calculations: ${fallbackCount} lanes`);
      this.logger.log(`📊 Overall accuracy rate: ${successRate}%`);
      this.logger.log(`🎯 All distances rounded to nearest whole mile as requested`);
      
      return sortedLanes;

    } catch (error) {
      this.logger.error('Error generating accurate lanes from historical data:', error);
      throw new InternalServerErrorException('Failed to calculate accurate lanes');
    }
  }

  /**
   * Create a new order in database and sync to Airtable
   */
  async createOrder(createOrderDto: CreateOrderDto, clerkUserId: string): Promise<CreateOrderResult> {
    this.logger.log(`Creating order for user ${clerkUserId}:`, createOrderDto);

    try {
      // Check for duplicate PO number
      const existingOrder = await this.prisma.load.findFirst({
        where: { proNumber: createOrderDto.poNumber }
      });

      if (existingOrder) {
        throw new BadRequestException(`PO Number "${createOrderDto.poNumber}" already exists`);
      }

      // Parse dates
      const pickupDate = new Date(createOrderDto.pickupDate);
      let deliveryDate: Date | undefined;
      
      if (createOrderDto.deliveryDate) {
        deliveryDate = new Date(createOrderDto.deliveryDate);
        
        // Validate delivery date is after pickup date
        if (deliveryDate <= pickupDate) {
          throw new BadRequestException('Delivery date must be after pickup date');
        }
      }

      // Create order in database
      const newLoad = await this.prisma.load.create({
        data: {
          proNumber: createOrderDto.poNumber,
          originCity: createOrderDto.originCity,
          originState: createOrderDto.originState,
          destinationCity: createOrderDto.destinationCity,
          destinationState: createOrderDto.destinationState,
          pickupDateUtc: pickupDate,
          deliveryDateUtc: deliveryDate,
          equipmentRequired: createOrderDto.equipmentRequired,
          weightLbs: createOrderDto.weightLbs,
          rate: createOrderDto.rate,
          temperature: createOrderDto.temperature,
          status: 'AVAILABLE', // Set initial status
          
          // Organization targeting - automatically target to First Cut Produce
          targetOrganizations: ['First Cut Produce'],
          isPublic: false,
          isTargeted: true,
          
          // Metadata for operations tracking
          rawAirtableData: {
            createdBy: clerkUserId,
            createdVia: 'operations-interface',
            laneId: createOrderDto.laneId,
            soNumber: createOrderDto.soNumber,
            priority: createOrderDto.priority || 'normal',
            notes: createOrderDto.notes,
            estimatedMiles: createOrderDto.estimatedMiles
          },
          
          // Generate a temporary airtable record ID until Airtable sync completes
          airtableRecordId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }
      });

      this.logger.log(`Created order in database with ID: ${newLoad.id}`);

      // TODO: Sync to Airtable
      // For Phase 1, we'll create in database only
      // Phase 2 will add Airtable sync functionality
      this.logger.log(`Order ${newLoad.id} created successfully - Airtable sync will be implemented in Phase 2`);

      return {
        id: newLoad.id,
        airtableRecordId: newLoad.airtableRecordId
      };

    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      this.logger.error(`Error creating order for user ${clerkUserId}:`, error);
      throw new InternalServerErrorException('Failed to create order');
    }
  }

  /**
   * Calculate estimated duration based on miles
   */
  private calculateEstimatedDuration(miles: number): string {
    // Assume average speed of 500 miles per day including stops
    const days = Math.ceil(miles / 500);
    
    if (days === 1) {
      return '1 day';
    }
    
    return `${days} days`;
  }

  /**
   * Get smart suggestions for order creation
   */
  async getSmartSuggestions(
    originCity: string,
    originState: string,
    destinationCity: string,
    destinationState: string,
    userId: string,
    currentValues?: any
  ) {
    this.logger.log(`Getting smart suggestions for lane: ${originCity}, ${originState} → ${destinationCity}, ${destinationState}`);

    try {
      const suggestions = await this.smartSuggestions.getOrderSuggestions({
        originCity,
        originState,
        destinationCity,
        destinationState,
        userId,
        currentValues
      });

      this.logger.log(`Generated ${suggestions.suggestions.length} smart suggestions with ${(suggestions.confidence * 100).toFixed(1)}% confidence`);
      return suggestions;

    } catch (error) {
      this.logger.error('Error getting smart suggestions:', error);
      throw new InternalServerErrorException('Failed to generate smart suggestions');
    }
  }

  /**
   * Validate order data with smart validation
   */
  async validateOrderWithAI(orderData: any, context: any) {
    this.logger.log('Validating order with AI-powered rules');

    try {
      const validation = await this.smartSuggestions.validateOrderData(orderData, context);
      this.logger.log(`Validation complete: ${validation.warnings.length} warnings, ${validation.criticalIssues.length} critical issues`);
      return validation;

    } catch (error) {
      this.logger.error('Error in AI validation:', error);
      return {
        isValid: true,
        warnings: [],
        suggestions: [],
        criticalIssues: []
      };
    }
  }

  /**
   * Record feedback on suggestions to improve future recommendations
   */
  async recordSuggestionFeedback(orderId: string, feedbackData: any) {
    this.logger.log(`Recording suggestion feedback for order: ${orderId}`);

    try {
      await this.smartSuggestions.recordSuggestionFeedback({
        orderId,
        ...feedbackData
      });

      this.logger.log('Suggestion feedback recorded successfully');

    } catch (error) {
      this.logger.error('Error recording suggestion feedback:', error);
      // Don't throw error - feedback is non-critical
    }
  }

  /**
   * Get auto-complete suggestions for form fields
   */
  async getAutoCompleteSuggestions(field: string, partialValue: string, context: any) {
    this.logger.log(`Getting auto-complete suggestions for ${field}: "${partialValue}"`);

    try {
      const suggestions = await this.smartSuggestions.getAutoCompleteSuggestions(field, partialValue, context);
      return suggestions;

    } catch (error) {
      this.logger.error('Error getting auto-complete suggestions:', error);
      return [];
    }
  }
} 