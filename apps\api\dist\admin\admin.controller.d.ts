import { AdminService } from './admin.service';
export declare enum UserRole {
    CARRIER = "CARRIER",
    ADMIN = "ADMIN"
}
export declare class UpdateUserRoleDto {
    role: 'CARRIER' | 'ADMIN';
}
export declare class VerifyCarrierDto {
    isVerified: boolean;
}
export interface SystemSettingsDto {
    platformName?: string;
    supportEmail?: string;
    maintenanceMode?: boolean;
    autoAssignLoads?: boolean;
    requireLoadApproval?: boolean;
    maxLoadsPerCarrier?: number;
    loadExpirationHours?: number;
    requireInsuranceVerification?: boolean;
    requireDotVerification?: boolean;
    autoApproveCarriers?: boolean;
    verificationReminderDays?: number;
    enableEmailNotifications?: boolean;
    enableSmsNotifications?: boolean;
    notificationFrequency?: 'REAL_TIME' | 'HOURLY' | 'DAILY';
    requireTwoFactor?: boolean;
    sessionTimeoutMinutes?: number;
    maxLoginAttempts?: number;
    passwordExpirationDays?: number;
    defaultPaymentTerms?: string;
    latePaymentFeePercent?: number;
    invoiceReminderDays?: number;
    maxFileUploadSize?: number;
    rateLimitPerMinute?: number;
    enableLoadTracking?: boolean;
    enableRealTimeUpdates?: boolean;
    enableAdvancedReporting?: boolean;
    enableApiAccess?: boolean;
    maintenanceWindowStart?: string;
    maintenanceWindowEnd?: string;
    backupFrequency?: 'HOURLY' | 'DAILY' | 'WEEKLY';
}
export declare class UpdateSystemSettingsDto {
    platformName?: string;
    supportEmail?: string;
    maintenanceMode?: boolean;
    autoAssignLoads?: boolean;
    requireLoadApproval?: boolean;
    maxLoadsPerCarrier?: number;
    loadExpirationHours?: number;
    requireInsuranceVerification?: boolean;
    requireDotVerification?: boolean;
    autoApproveCarriers?: boolean;
    verificationReminderDays?: number;
    enableEmailNotifications?: boolean;
    enableSmsNotifications?: boolean;
    notificationFrequency?: 'REAL_TIME' | 'HOURLY' | 'DAILY';
    requireTwoFactor?: boolean;
    sessionTimeoutMinutes?: number;
    maxLoginAttempts?: number;
    passwordExpirationDays?: number;
    defaultPaymentTerms?: string;
    latePaymentFeePercent?: number;
    invoiceReminderDays?: number;
    maxFileUploadSize?: number;
    rateLimitPerMinute?: number;
    enableLoadTracking?: boolean;
    enableRealTimeUpdates?: boolean;
    enableAdvancedReporting?: boolean;
    enableApiAccess?: boolean;
    maintenanceWindowStart?: string;
    maintenanceWindowEnd?: string;
    backupFrequency?: 'HOURLY' | 'DAILY' | 'WEEKLY';
}
export declare class AdminBidResponseDto {
    response: 'accepted' | 'countered' | 'declined';
    counterOfferAmount?: number;
    notes?: string;
}
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getCurrentUserInfo(params: any, body: any, req: any): Promise<any>;
    promoteFirstAdmin(): Promise<any>;
    getAllUsers(): Promise<any>;
    verifyCarrier(userId: string, verifyCarrierDto: VerifyCarrierDto): Promise<any>;
    updateUserRole(userId: string, updateUserRoleDto: UpdateUserRoleDto): Promise<any>;
    getSystemSettings(): Promise<SystemSettingsDto>;
    updateSystemSettings(updateData: UpdateSystemSettingsDto): Promise<{
        message: string;
        settings: SystemSettingsDto;
    }>;
    resetSystemSettings(): Promise<{
        message: string;
        settings: SystemSettingsDto;
    }>;
    getPendingBids(req: any): Promise<any[]>;
    getAllBids(req: any, status?: string, carrierId?: string, loadId?: string, dateFrom?: string, dateTo?: string, page?: string, limit?: string): Promise<{
        bids: any[];
        totalCount: number;
        page: number;
        pageSize: number;
    }>;
    respondToBid(bidId: string, responseDto: AdminBidResponseDto, req: any): Promise<any>;
    getBidHistory(bidId: string): Promise<any[]>;
    getBiddingStats(req: any): Promise<any>;
    processExpiredBids(req: any): Promise<any>;
    getBidsExpiringSoon(req: any, hours?: string): Promise<any[]>;
}
