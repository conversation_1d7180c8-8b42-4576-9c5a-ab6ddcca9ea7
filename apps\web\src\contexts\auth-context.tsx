'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// JWT payload structure from handover document
interface User {
  id: string;
  email: string;
  role: 'CARRIER' | 'ADMIN';
  mcNumber?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  getToken: () => string | null;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  companyName: string;
  mcNumber: string;
}

interface N8NAuthResponse {
  success: boolean;
  token?: string; // Optional for registration
  message?: string; // For registration success
  user?: {
    id: string;
    email: string;
    role: string;
    mcNumber?: string;
    firstName?: string;
    lastName?: string;
    companyName?: string;
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const TOKEN_KEY = 'n8n_auth_token';
const USER_KEY = 'n8n_user_data';

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!token;

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = localStorage.getItem(TOKEN_KEY);
        const storedUser = localStorage.getItem(USER_KEY);

        if (storedToken && storedUser) {
          // Validate token format and expiration
          const tokenParts = storedToken.split('.');
          if (tokenParts.length === 3 && tokenParts[1]) {
            try {
              const tokenPayload = JSON.parse(atob(tokenParts[1]));
              // Add buffer time to prevent unnecessary logouts due to clock skew
              const bufferTime = 60 * 1000; // 1 minute buffer
              const isExpired = tokenPayload.exp ? (tokenPayload.exp * 1000 < Date.now() - bufferTime) : false;

              if (!isExpired) {
                setToken(storedToken);
                setUser(JSON.parse(storedUser));
              } else {
                // Token expired, clear storage
                localStorage.removeItem(TOKEN_KEY);
                localStorage.removeItem(USER_KEY);
              }
            } catch (parseError) {
              // Invalid token format, clear storage
              console.error('Invalid token format:', parseError);
              localStorage.removeItem(TOKEN_KEY);
              localStorage.removeItem(USER_KEY);
            }
          } else {
            // Invalid JWT format, clear storage
            localStorage.removeItem(TOKEN_KEY);
            localStorage.removeItem(USER_KEY);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear potentially corrupted data
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      
      // N8N login endpoint - using production URL
      const n8nBaseUrl = 'https://firstcutproduce.app.n8n.cloud';
      
      const response = await fetch(`${n8nBaseUrl}/webhook/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Login failed' }));
        throw new Error(errorData.message || 'Login failed');
      }

      // Check if response is just a JWT token (raw string) or JSON
      const contentType = response.headers.get('content-type');
      let token: string;
      let userFromToken: any;

      if (contentType && contentType.includes('application/json')) {
        // JSON response format
        const data: N8NAuthResponse = await response.json();
        
        if (!data.token) {
          throw new Error('No token received from authentication server');
        }
        
        token = data.token;
        
        // If user data is provided in response, use it; otherwise parse from token
        if (data.user) {
          userFromToken = data.user;
        } else {
          // Parse user data from JWT payload
          try {
            const tokenParts = token.split('.');
            if (tokenParts.length !== 3 || !tokenParts[1]) {
              throw new Error('Invalid JWT format');
            }
            
            const payload = JSON.parse(atob(tokenParts[1]));
            
            userFromToken = {
              id: payload.id,
              email: payload.email,
              role: payload.role,
              mcNumber: payload.mcNumber,
              firstName: payload.firstName,
              lastName: payload.lastName,
              companyName: payload.companyName,
            };
          } catch (parseError) {
            throw new Error('Invalid JWT token received');
          }
        }
      } else {
        // Raw JWT token response
        token = await response.text();
        
        // Parse user data from JWT payload
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length !== 3 || !tokenParts[1]) {
            throw new Error('Invalid JWT format');
          }
          
          const payload = JSON.parse(atob(tokenParts[1]));
          
          userFromToken = {
            id: payload.id,
            email: payload.email,
            role: payload.role,
            mcNumber: payload.mcNumber,
            firstName: payload.firstName,
            lastName: payload.lastName,
            companyName: payload.companyName,
          };
        } catch (parseError) {
          throw new Error('Invalid JWT token received');
        }
      }

      // Store token and user data
      localStorage.setItem(TOKEN_KEY, token);
      localStorage.setItem(USER_KEY, JSON.stringify(userFromToken));
      
      // Also store token in cookies for middleware access
      document.cookie = `n8n_auth_token=${token}; path=/; secure; samesite=strict`;

      setToken(token);
      setUser({
        id: userFromToken.id,
        email: userFromToken.email,
        role: userFromToken.role as 'CARRIER' | 'ADMIN',
        mcNumber: userFromToken.mcNumber,
        firstName: userFromToken.firstName,
        lastName: userFromToken.lastName,
        companyName: userFromToken.companyName,
      });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true);

      // N8N register endpoint - using production URL
      const n8nBaseUrl = 'https://firstcutproduce.app.n8n.cloud';

      const response = await fetch(`${n8nBaseUrl}/webhook/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Registration failed' }));
        throw new Error(errorData.message || 'Registration failed');
      }

      const data: N8NAuthResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Registration failed');
      }

      // Registration successful - user needs to sign in separately
      // Don't set token/user here since registration doesn't include login
      
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    // Remove token cookie
    document.cookie = 'n8n_auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    setToken(null);
    setUser(null);
  };

  const getToken = () => {
    return token;
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    getToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Compatibility hooks for easier migration from Clerk
export function useUser() {
  const { user, isLoading } = useAuth();
  return {
    user,
    isLoaded: !isLoading,
    isSignedIn: !!user,
  };
}

export function useOrganization() {
  // For backward compatibility - N8N uses MC Number instead of organizations
  const { user } = useAuth();
  return {
    organization: user?.mcNumber ? {
      id: user.mcNumber,
      name: user.companyName || `MC ${user.mcNumber}`,
      slug: user.mcNumber,
    } : null,
    isLoaded: true,
  };
} 