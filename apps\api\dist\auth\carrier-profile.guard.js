"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CarrierProfileGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarrierProfileGuard = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CarrierProfileGuard = CarrierProfileGuard_1 = class CarrierProfileGuard {
    prisma;
    logger = new common_1.Logger(CarrierProfileGuard_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const userAirtableId = request.auth?.id;
        if (!userAirtableId) {
            this.logger.error('CarrierProfileGuard: No user ID found in request');
            throw new common_1.UnauthorizedException('Authentication required');
        }
        this.logger.log(`CarrierProfileGuard: Validating carrier profile for user ${userAirtableId}`);
        try {
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: userAirtableId },
                select: {
                    id: true,
                    airtableUserId: true,
                    role: true
                }
            });
            if (!user) {
                this.logger.error(`CarrierProfileGuard: User ${userAirtableId} not found in database`);
                throw new common_1.UnauthorizedException('User account not found. Please contact support.');
            }
            if (user.role !== 'ADMIN') {
                const carrierProfile = await this.prisma.carrierProfile.findUnique({
                    where: { userId: user.id },
                    select: {
                        id: true,
                        companyName: true,
                        mcNumber: true
                    }
                });
                if (!carrierProfile) {
                    this.logger.warn(`CarrierProfileGuard: User ${userAirtableId} has no carrier profile - allowing access to create one`);
                    return true;
                }
                const hasBasicInfo = carrierProfile.companyName || carrierProfile.mcNumber;
                if (!hasBasicInfo) {
                    this.logger.warn(`CarrierProfileGuard: User ${userAirtableId} has empty carrier profile - allowing access to complete it`);
                    return true;
                }
                this.logger.log(`CarrierProfileGuard: User ${userAirtableId} profile validation passed`);
            }
            else {
                this.logger.log(`CarrierProfileGuard: Admin user ${userAirtableId} - bypassing profile checks`);
            }
            return true;
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            this.logger.error(`CarrierProfileGuard: Database error validating user ${userAirtableId}: ${error.message}`, error.stack);
            throw new common_1.UnauthorizedException('Profile validation failed. Please try again.');
        }
    }
};
exports.CarrierProfileGuard = CarrierProfileGuard;
exports.CarrierProfileGuard = CarrierProfileGuard = CarrierProfileGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CarrierProfileGuard);
//# sourceMappingURL=carrier-profile.guard.js.map