'use client';

import { useUser } from '@/contexts/auth-context';
import { ReactNode } from 'react';

interface AdminOnlyWrapperProps {
  children: ReactNode;
  adminEmails?: string[];
  showInDevelopment?: boolean;
}

export function AdminOnlyWrapper({ 
  children, 
  adminEmails = ['<EMAIL>'], // Your email
  showInDevelopment = true 
}: AdminOnlyWrapperProps) {
  const { user, isLoaded } = useUser();
  
  // Show loading state while user data loads
  if (!isLoaded) {
    return null;
  }
  
  const isDevelopment = process.env.NODE_ENV === 'development';
  const userEmail = user?.email;
  const isAuthorizedAdmin = userEmail && adminEmails.includes(userEmail);
  const hasAdminRole = user?.role === 'ADMIN';

  // Only show if:
  // 1. In development mode (and showInDevelopment is true), OR
  // 2. User has admin role, OR
  // 3. User is in authorized admin emails list
  if ((isDevelopment && showInDevelopment) || hasAdminRole || isAuthorizedAdmin) {
    return <>{children}</>;
  }
  
  // Hide from everyone else
  return null;
} 