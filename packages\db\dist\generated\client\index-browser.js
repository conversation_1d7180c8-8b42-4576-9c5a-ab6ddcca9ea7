
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  airtableUserId: 'airtableUserId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  mcNumber: 'mcNumber',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  airtableUserId: 'airtableUserId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  companyName: 'companyName',
  mcNumber: 'mcNumber',
  dotNumber: 'dotNumber',
  role: 'role',
  verificationStatus: 'verificationStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CarrierProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyName: 'companyName',
  dotNumber: 'dotNumber',
  mcNumber: 'mcNumber',
  phoneNumber: 'phoneNumber',
  equipmentTypes: 'equipmentTypes',
  serviceableRegions: 'serviceableRegions',
  isVerifiedByAdmin: 'isVerifiedByAdmin',
  adminNotes: 'adminNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  contact_email: 'contact_email',
  contact_name: 'contact_name',
  contact_phone: 'contact_phone'
};

exports.Prisma.LoadScalarFieldEnum = {
  id: 'id',
  airtableRecordId: 'airtableRecordId',
  originCity: 'originCity',
  originState: 'originState',
  destinationCity: 'destinationCity',
  destinationState: 'destinationState',
  pickupDateUtc: 'pickupDateUtc',
  deliveryDateUtc: 'deliveryDateUtc',
  equipmentRequired: 'equipmentRequired',
  weightLbs: 'weightLbs',
  rate: 'rate',
  status: 'status',
  temperature: 'temperature',
  awardedToCarrierProfileId: 'awardedToCarrierProfileId',
  rawAirtableData: 'rawAirtableData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  proNumber: 'proNumber',
  bolFileUrl: 'bolFileUrl',
  bolUploadedAt: 'bolUploadedAt',
  invoiceFileUrl: 'invoiceFileUrl',
  invoiceUploadedAt: 'invoiceUploadedAt',
  paidAt: 'paidAt',
  paymentAmount: 'paymentAmount',
  paymentDueDate: 'paymentDueDate',
  paymentNotes: 'paymentNotes',
  paymentStatus: 'paymentStatus',
  podFileUrl: 'podFileUrl',
  podUploadedAt: 'podUploadedAt',
  isPublic: 'isPublic',
  isTargeted: 'isTargeted',
  targetOrganizations: 'targetOrganizations',
  carrier: 'carrier',
  cases: 'cases',
  deliveryNumber: 'deliveryNumber',
  invStatus: 'invStatus',
  pallets: 'pallets',
  pickupNumber: 'pickupNumber',
  poNumber: 'poNumber',
  receiverAddress: 'receiverAddress',
  receiverName: 'receiverName',
  shipperAddress: 'shipperAddress',
  soNumber: 'soNumber'
};

exports.Prisma.BidScalarFieldEnum = {
  id: 'id',
  loadId: 'loadId',
  carrierProfileId: 'carrierProfileId',
  bidAmount: 'bidAmount',
  carrierNotes: 'carrierNotes',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  admin_notes: 'admin_notes',
  admin_response: 'admin_response',
  counter_offer_amount: 'counter_offer_amount',
  expires_at: 'expires_at',
  negotiation_status: 'negotiation_status',
  response_timestamp: 'response_timestamp'
};

exports.Prisma.SavedSearchScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  criteria: 'criteria',
  isDefault: 'isDefault',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemSettingsScalarFieldEnum = {
  id: 'id',
  platformName: 'platformName',
  supportEmail: 'supportEmail',
  maintenanceMode: 'maintenanceMode',
  autoAssignLoads: 'autoAssignLoads',
  requireLoadApproval: 'requireLoadApproval',
  maxLoadsPerCarrier: 'maxLoadsPerCarrier',
  loadExpirationHours: 'loadExpirationHours',
  requireInsuranceVerification: 'requireInsuranceVerification',
  requireDotVerification: 'requireDotVerification',
  autoApproveCarriers: 'autoApproveCarriers',
  verificationReminderDays: 'verificationReminderDays',
  enableEmailNotifications: 'enableEmailNotifications',
  enableSmsNotifications: 'enableSmsNotifications',
  notificationFrequency: 'notificationFrequency',
  requireTwoFactor: 'requireTwoFactor',
  sessionTimeoutMinutes: 'sessionTimeoutMinutes',
  maxLoginAttempts: 'maxLoginAttempts',
  passwordExpirationDays: 'passwordExpirationDays',
  defaultPaymentTerms: 'defaultPaymentTerms',
  latePaymentFeePercent: 'latePaymentFeePercent',
  invoiceReminderDays: 'invoiceReminderDays',
  maxFileUploadSize: 'maxFileUploadSize',
  rateLimitPerMinute: 'rateLimitPerMinute',
  enableLoadTracking: 'enableLoadTracking',
  enableRealTimeUpdates: 'enableRealTimeUpdates',
  enableAdvancedReporting: 'enableAdvancedReporting',
  enableApiAccess: 'enableApiAccess',
  maintenanceWindowStart: 'maintenanceWindowStart',
  maintenanceWindowEnd: 'maintenanceWindowEnd',
  backupFrequency: 'backupFrequency',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  loadId: 'loadId',
  filename: 'filename',
  url: 'url',
  type: 'type',
  size: 'size',
  uploadedAt: 'uploadedAt'
};

exports.Prisma.DistanceCacheScalarFieldEnum = {
  id: 'id',
  originAddress: 'originAddress',
  destinationAddress: 'destinationAddress',
  distanceMiles: 'distanceMiles',
  durationHours: 'durationHours',
  calculatedBy: 'calculatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Bid_responsesScalarFieldEnum = {
  id: 'id',
  bid_id: 'bid_id',
  response_type: 'response_type',
  amount: 'amount',
  notes: 'notes',
  created_by: 'created_by',
  created_at: 'created_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Role = exports.$Enums.Role = {
  CARRIER: 'CARRIER',
  ADMIN: 'ADMIN'
};

exports.LoadStatus = exports.$Enums.LoadStatus = {
  NEW_ORDER: 'NEW_ORDER',
  AVAILABLE: 'AVAILABLE',
  ASSIGNED: 'ASSIGNED',
  INVOICED: 'INVOICED',
  ON_HOLD: 'ON_HOLD',
  CANCELLED: 'CANCELLED',
  DELIVERED_EMPTY: 'DELIVERED_EMPTY'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  OVERDUE: 'OVERDUE'
};

exports.InvStatus = exports.$Enums.InvStatus = {
  NOT_SENT: 'NOT_SENT',
  SENT: 'SENT',
  PAID: 'PAID'
};

exports.AdminResponse = exports.$Enums.AdminResponse = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  COUNTERED: 'COUNTERED',
  DECLINED: 'DECLINED'
};

exports.NegotiationStatus = exports.$Enums.NegotiationStatus = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED',
  EXPIRED: 'EXPIRED'
};

exports.BidResponseType = exports.$Enums.BidResponseType = {
  INITIAL_BID: 'INITIAL_BID',
  ADMIN_RESPONSE: 'ADMIN_RESPONSE',
  CARRIER_RESPONSE: 'CARRIER_RESPONSE'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserProfile: 'UserProfile',
  CarrierProfile: 'CarrierProfile',
  Load: 'Load',
  Bid: 'Bid',
  SavedSearch: 'SavedSearch',
  SystemSettings: 'SystemSettings',
  Document: 'Document',
  DistanceCache: 'DistanceCache',
  bid_responses: 'bid_responses'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
