"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AirtableOrdersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AirtableOrdersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const airtable_orders_service_1 = require("./airtable-orders.service");
const auth_guard_1 = require("../auth/auth.guard");
const airtable_load_dto_1 = require("./dto/airtable-load.dto");
const request_booking_details_dto_1 = require("./dto/request-booking-details.dto");
const create_bid_dto_1 = require("./dto/create-bid.dto");
const upload_document_dto_1 = require("./dto/upload-document.dto");
const advanced_filters_dto_1 = require("./dto/advanced-filters.dto");
let AirtableOrdersController = AirtableOrdersController_1 = class AirtableOrdersController {
    airtableOrdersService;
    logger = new common_1.Logger(AirtableOrdersController_1.name);
    constructor(airtableOrdersService) {
        this.airtableOrdersService = airtableOrdersService;
    }
    async getAvailableLoads(req, filters) {
        const userAirtableId = req.user?.airtableUserId;
        if (!userAirtableId) {
            this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/available');
            throw new common_1.UnauthorizedException('Authentication required');
        }
        this.logger.log(`GET /airtable-orders/available called by user ${userAirtableId} (MC: ${req.user?.mcNumber}) with filters:`, filters);
        try {
            let retries = 2;
            let lastError;
            while (retries >= 0) {
                try {
                    const result = await Promise.race([
                        this.airtableOrdersService.getAvailableLoadsWithFilters(userAirtableId, filters),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Service timeout')), 30000))
                    ]);
                    this.logger.log(`SECURITY: User ${userAirtableId} (MC: ${req.user?.mcNumber}) accessed ${result.loads.length} loads (total: ${result.totalCount})`);
                    return result;
                }
                catch (error) {
                    lastError = error;
                    retries--;
                    if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
                        this.logger.warn(`Retrying request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    else {
                        throw error;
                    }
                }
            }
            throw lastError;
        }
        catch (error) {
            this.logger.error(`Error fetching available loads for user ${userAirtableId}:`, error.message, error.stack);
            if (error.message === 'Service timeout') {
                throw new common_1.InternalServerErrorException('Service is currently slow. Please try again.');
            }
            if (error.message.includes('connection') || error.message.includes('closed')) {
                throw new common_1.InternalServerErrorException('Backend service temporarily unavailable. Please try again shortly.');
            }
            if (error.message.includes('AIRTABLE')) {
                throw new common_1.InternalServerErrorException('Load data service temporarily unavailable. Please try again.');
            }
            throw new common_1.InternalServerErrorException('Unable to fetch loads. Please try again.');
        }
    }
    async getMyLoads(req, status, search, dateRange) {
        const userAirtableId = req.user?.airtableUserId;
        if (!userAirtableId) {
            this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/my-loads');
            throw new common_1.UnauthorizedException('Authentication required');
        }
        this.logger.log(`GET /airtable-orders/my-loads called by user ${userAirtableId} with filters:`, { status, search, dateRange });
        try {
            const filters = { status, search, dateRange };
            let retries = 2;
            let lastError;
            while (retries >= 0) {
                try {
                    const loads = await Promise.race([
                        this.airtableOrdersService.getMyLoadsFromAirtable(userAirtableId, filters),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Service timeout')), 20000))
                    ]);
                    this.logger.log(`Successfully fetched ${loads.length} loads for user ${userAirtableId}`);
                    const companyName = loads.length > 0 ? loads[0].carrier : 'Unknown Company';
                    return {
                        loads,
                        totalCount: loads.length,
                        companyName
                    };
                }
                catch (error) {
                    lastError = error;
                    retries--;
                    if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
                        this.logger.warn(`Retrying my-loads request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    else {
                        throw error;
                    }
                }
            }
            throw lastError;
        }
        catch (error) {
            this.logger.error(`Error fetching my loads for user ${userAirtableId}:`, error.message, error.stack);
            if (error.message === 'Service timeout') {
                throw new common_1.InternalServerErrorException('Service is currently slow. Please try again.');
            }
            if (error.message.includes('Company name not found')) {
                throw new common_1.BadRequestException('Please complete your company profile to view assigned loads.');
            }
            if (error.message.includes('connection') || error.message.includes('closed')) {
                throw new common_1.InternalServerErrorException('Load data service temporarily unavailable. Please try again shortly.');
            }
            if (error.message.includes('AIRTABLE')) {
                throw new common_1.InternalServerErrorException('Load data service temporarily unavailable. Please try again.');
            }
            throw new common_1.InternalServerErrorException('Unable to fetch your loads. Please try again.');
        }
    }
    async getAssignedLoadsForCarrier(req) {
        const userAirtableId = req.user?.airtableUserId;
        if (!userAirtableId) {
            this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/assigned');
            throw new common_1.UnauthorizedException('Authentication required');
        }
        this.logger.log(`GET /airtable-orders/assigned called by user ${userAirtableId} (MC: ${req.user?.mcNumber})`);
        try {
            let retries = 2;
            let lastError;
            while (retries >= 0) {
                try {
                    const result = await Promise.race([
                        this.airtableOrdersService.getAssignedLoadsForCarrier(userAirtableId),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Service timeout')), 20000))
                    ]);
                    this.logger.log(`Successfully fetched ${result.length} assigned loads for user ${userAirtableId}`);
                    return result;
                }
                catch (error) {
                    lastError = error;
                    retries--;
                    if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
                        this.logger.warn(`Retrying assigned loads request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    else {
                        throw error;
                    }
                }
            }
            throw lastError;
        }
        catch (error) {
            this.logger.error(`Error fetching assigned loads for user ${userAirtableId}:`, error.message, error.stack);
            if (error.message === 'Service timeout') {
                throw new common_1.InternalServerErrorException('Service is currently slow. Please try again.');
            }
            if (error.message.includes('connection') || error.message.includes('closed')) {
                throw new common_1.InternalServerErrorException('Backend service temporarily unavailable. Please try again shortly.');
            }
            if (error.message.includes('not found') || error.message.includes('No assigned loads')) {
                this.logger.log(`No assigned loads found for user ${userAirtableId}, returning empty array`);
                return [];
            }
            throw new common_1.InternalServerErrorException('Unable to fetch assigned loads. Please try again.');
        }
    }
    async getDashboardMetrics(req) {
        const userAirtableId = req.user?.airtableUserId;
        if (!userAirtableId) {
            this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/dashboard-metrics');
            throw new common_1.UnauthorizedException('Authentication required');
        }
        this.logger.log(`GET /airtable-orders/dashboard-metrics called by user ${userAirtableId} (MC: ${req.user?.mcNumber})`);
        try {
            let retries = 2;
            let lastError;
            while (retries >= 0) {
                try {
                    const result = await Promise.race([
                        this.airtableOrdersService.getDashboardMetrics(userAirtableId),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Service timeout')), 20000))
                    ]);
                    this.logger.log(`Successfully fetched dashboard metrics for user ${userAirtableId}`);
                    return result;
                }
                catch (error) {
                    lastError = error;
                    retries--;
                    if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
                        this.logger.warn(`Retrying dashboard metrics request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    else {
                        throw error;
                    }
                }
            }
            throw lastError;
        }
        catch (error) {
            this.logger.error(`Error fetching dashboard metrics for user ${userAirtableId}:`, error.message, error.stack);
            if (error.message === 'Service timeout') {
                throw new common_1.InternalServerErrorException('Service is currently slow. Please try again.');
            }
            if (error.message.includes('connection') || error.message.includes('closed')) {
                throw new common_1.InternalServerErrorException('Backend service temporarily unavailable. Please try again shortly.');
            }
            this.logger.log(`Returning default dashboard metrics for user ${userAirtableId} due to error`);
            return {
                totalAssignedLoads: 0,
                totalActiveBids: 0,
                totalEarnings: 0,
                recentLoads: [],
                recentBids: [],
                loadsByStatus: {},
                thisWeekLoads: 0,
                lastWeekLoads: 0
            };
        }
    }
    async requestLoadBooking(loadId, bookingDetailsDto, req) {
        this.logger.log(`POST /airtable-orders/${loadId}/book called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.requestLoadBooking(loadId, req.user?.airtableUserId, bookingDetailsDto);
        }
        catch (error) {
            this.logger.error('Error processing booking request:', error);
            throw error;
        }
    }
    async processWebhook(payload) {
        this.logger.log('POST /airtable-orders/webhook called');
        try {
            return await this.airtableOrdersService.processLoadWebhook(payload);
        }
        catch (error) {
            this.logger.error('Error processing webhook:', error);
            throw error;
        }
    }
    async syncAllLoads(syncAll) {
        this.logger.log(`POST /airtable-orders/sync called with syncAll=${syncAll}`);
        try {
            const shouldSyncAll = syncAll === 'true';
            return await this.airtableOrdersService.syncAllLoadsFromAirtable(shouldSyncAll);
        }
        catch (error) {
            this.logger.error('Error syncing loads:', error);
            throw error;
        }
    }
    async uploadLoadDocument(loadId, uploadData, req) {
        this.logger.log(`POST /airtable-orders/${loadId}/upload-document called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        if (!/^[a-z0-9]{25}$/.test(loadId)) {
            throw new common_1.BadRequestException('Invalid load ID format');
        }
        try {
            return await this.airtableOrdersService.uploadLoadDocument(loadId, uploadData.documentType, uploadData.fileUrl, req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error uploading document:', error);
            if (process.env.NODE_ENV === 'production') {
                throw new common_1.InternalServerErrorException('Document upload failed');
            }
            throw error;
        }
    }
    async assignLoadToCarrier(airtableRecordId, req) {
        this.logger.log(`POST /airtable-orders/${airtableRecordId}/assign-to-me called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.assignLoadToCarrier(airtableRecordId, req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error assigning load to carrier:', error);
            throw error;
        }
    }
    async updateLoadStatus(loadId, body, req) {
        this.logger.log(`PATCH /airtable-orders/${loadId}/status called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber}) - Status: ${body.status}`);
        try {
            return await this.airtableOrdersService.updateLoadStatus(loadId, body.status, req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error updating load status:', error);
            throw error;
        }
    }
    async unassignLoadFromCarrier(airtableRecordId, req) {
        this.logger.log(`POST /api/v1/airtable-orders/${airtableRecordId}/unassign - Admin unassigning load by ${req.user?.airtableUserId}`);
        return this.airtableOrdersService.unassignLoadFromCarrier(airtableRecordId, req.user?.airtableUserId);
    }
    async cancelAssignedLoad(loadId, body, req) {
        this.logger.log(`POST /airtable-orders/${loadId}/cancel called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.carrierCancelLoad(loadId, req.user?.airtableUserId, body.reason);
        }
        catch (error) {
            this.logger.error('Error cancelling load:', error);
            throw error;
        }
    }
    async createBid(loadId, createBidDto, req) {
        this.logger.log(`POST /airtable-orders/${loadId}/bid called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        this.logger.log(`DEBUG: Received DTO: ${JSON.stringify(createBidDto)}`);
        this.logger.log(`DEBUG: bidAmount type: ${typeof createBidDto.bidAmount}, value: ${createBidDto.bidAmount}`);
        this.logger.log(`DEBUG: carrierNotes type: ${typeof createBidDto.carrierNotes}, value: ${createBidDto.carrierNotes}`);
        try {
            return await this.airtableOrdersService.createBid(loadId, req.user?.airtableUserId, createBidDto.bidAmount, createBidDto.carrierNotes);
        }
        catch (error) {
            this.logger.error('Error creating bid:', error);
            throw error;
        }
    }
    async respondToCounterOffer(bidId, body, req) {
        this.logger.log(`POST /airtable-orders/bids/${bidId}/respond called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        this.logger.log(`Carrier responding to counter-offer: ${body.response}`);
        try {
            return await this.airtableOrdersService.respondToCounterOffer(bidId, req.user?.airtableUserId, body.response, body.notes);
        }
        catch (error) {
            this.logger.error('Error responding to counter-offer:', error);
            throw error;
        }
    }
    async getCarrierBids(req) {
        const userAirtableId = req.user?.airtableUserId;
        if (!userAirtableId) {
            this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/bids');
            throw new common_1.UnauthorizedException('Authentication required');
        }
        this.logger.log(`GET /airtable-orders/bids called by user ${userAirtableId} (MC: ${req.user?.mcNumber})`);
        try {
            let retries = 2;
            let lastError;
            while (retries >= 0) {
                try {
                    const result = await Promise.race([
                        this.airtableOrdersService.getCarrierBids(userAirtableId),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Service timeout')), 15000))
                    ]);
                    this.logger.log(`Successfully fetched ${result.length} bids for user ${userAirtableId}`);
                    return result;
                }
                catch (error) {
                    lastError = error;
                    retries--;
                    if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
                        this.logger.warn(`Retrying bids request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    else {
                        throw error;
                    }
                }
            }
            throw lastError;
        }
        catch (error) {
            this.logger.error(`Error fetching carrier bids for user ${userAirtableId}:`, error.message, error.stack);
            if (error.message === 'Service timeout') {
                throw new common_1.InternalServerErrorException('Bid service is currently slow. Please try again.');
            }
            if (error.message.includes('connection') || error.message.includes('closed')) {
                throw new common_1.InternalServerErrorException('Bid service temporarily unavailable. Please try again shortly.');
            }
            if (error.message.includes('not found') || error.message.includes('No bids')) {
                this.logger.log(`No bids found for user ${userAirtableId}, returning empty array`);
                return [];
            }
            throw new common_1.InternalServerErrorException('Unable to fetch bids. Please try again.');
        }
    }
    async withdrawBid(bidId, req) {
        this.logger.log(`DELETE /airtable-orders/bids/${bidId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.withdrawBid(bidId, req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error withdrawing bid:', error);
            throw error;
        }
    }
    async withdrawBidPost(bidId, req) {
        this.logger.log(`POST /airtable-orders/bid-withdraw/${bidId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.withdrawBid(bidId, req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error withdrawing bid (POST route):', error);
            throw error;
        }
    }
    async withdrawBidFrontendRoute(bidId, req) {
        this.logger.log(`POST /airtable-orders/bids/${bidId}/withdraw called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber}) - FRONTEND ROUTE`);
        try {
            return await this.airtableOrdersService.withdrawBid(bidId, req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error withdrawing bid (frontend route):', error);
            throw error;
        }
    }
    async debugAirtableBids(loadId, req) {
        this.logger.log(`POST /airtable-orders/${loadId}/debug-bids called by admin ${req.user?.airtableUserId}`);
        try {
            return await this.airtableOrdersService.debugAirtableBidUpdate(loadId);
        }
        catch (error) {
            this.logger.error('Error in debug bid update:', error);
            throw error;
        }
    }
    async getSavedSearches(req) {
        this.logger.log(`GET /airtable-orders/saved-searches called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.getSavedSearches(req.user?.airtableUserId);
        }
        catch (error) {
            this.logger.error('Error fetching saved searches:', error);
            throw error;
        }
    }
    async saveSearch(req, savedSearchDto) {
        this.logger.log(`POST /airtable-orders/saved-searches called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.saveSearch(req.user?.airtableUserId, savedSearchDto);
        }
        catch (error) {
            this.logger.error('Error saving search:', error);
            throw error;
        }
    }
    async updateSavedSearch(req, searchId, savedSearchDto) {
        this.logger.log(`PUT /airtable-orders/saved-searches/${searchId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            return await this.airtableOrdersService.updateSavedSearch(req.user?.airtableUserId, searchId, savedSearchDto);
        }
        catch (error) {
            this.logger.error('Error updating saved search:', error);
            throw error;
        }
    }
    async deleteSavedSearch(req, searchId) {
        this.logger.log(`DELETE /airtable-orders/saved-searches/${searchId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            await this.airtableOrdersService.deleteSavedSearch(req.user?.airtableUserId, searchId);
            return { success: true };
        }
        catch (error) {
            this.logger.error('Error deleting saved search:', error);
            throw error;
        }
    }
    async debugLoadForBidding(loadId, req) {
        this.logger.log(`GET /airtable-orders/${loadId}/debug called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
        try {
            const diagnostics = await this.airtableOrdersService.diagnoseLoadForBidding(loadId);
            return {
                success: true,
                loadId,
                diagnostics,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`Error debugging load ${loadId}:`, error);
            return {
                success: false,
                loadId,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
};
exports.AirtableOrdersController = AirtableOrdersController;
__decorate([
    (0, common_1.Get)('available'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available loads with advanced filtering',
        description: 'Fetch available loads from Airtable with advanced filtering options including geographic radius, date ranges, equipment types, and more.'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, advanced_filters_dto_1.AdvancedFiltersDto]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "getAvailableLoads", null);
__decorate([
    (0, common_1.Get)('my-loads'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get loads assigned to carrier company using Airtable Carrier field',
        description: 'Fetch loads assigned to the authenticated carrier company from Airtable directly'
    }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by load status' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search loads by various fields' }),
    (0, swagger_1.ApiQuery)({ name: 'dateRange', required: false, description: 'Filter by date range (today, week, month)' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('dateRange')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "getMyLoads", null);
__decorate([
    (0, common_1.Get)('assigned'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '[DEPRECATED] Get assigned loads for the authenticated carrier',
        description: '[DEPRECATED] Use /my-loads instead. Fetch all loads assigned to the authenticated carrier user'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "getAssignedLoadsForCarrier", null);
__decorate([
    (0, common_1.Get)('dashboard-metrics'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get dashboard metrics for the authenticated carrier',
        description: 'Fetch dashboard statistics including assigned loads, active bids, earnings, and recent activity'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Post)(':loadId/book'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Request to book a load',
        description: 'Submit a booking request for a specific load'
    }),
    (0, swagger_1.ApiBody)({ type: request_booking_details_dto_1.RequestBookingDetailsDto }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, request_booking_details_dto_1.RequestBookingDetailsDto, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "requestLoadBooking", null);
__decorate([
    (0, common_1.Post)('webhook'),
    (0, swagger_1.ApiOperation)({
        summary: 'Process Airtable webhook',
        description: 'Endpoint for Airtable to send load updates via webhook'
    }),
    (0, swagger_1.ApiBody)({ type: airtable_load_dto_1.AirtableLoadWebhookDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [airtable_load_dto_1.AirtableLoadWebhookDto]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "processWebhook", null);
__decorate([
    (0, common_1.Post)('sync'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Sync loads from Airtable',
        description: 'Manually trigger a sync of loads from Airtable (Admin use). Use syncAll=true to sync all loads.'
    }),
    __param(0, (0, common_1.Query)('syncAll')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "syncAllLoads", null);
__decorate([
    (0, common_1.Post)(':loadId/upload-document'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Upload a document for a load',
        description: 'Upload BOL, POD, or Invoice document for a specific load. Only HTTPS URLs are accepted.'
    }),
    (0, swagger_1.ApiBody)({ type: upload_document_dto_1.UploadDocumentDto }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, upload_document_dto_1.UploadDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "uploadLoadDocument", null);
__decorate([
    (0, common_1.Post)(':airtableRecordId/assign-to-me'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Manually assign a load to the authenticated carrier',
        description: 'Assign a load with "Assigned" status to the authenticated carrier (useful for fixing assignment issues)'
    }),
    __param(0, (0, common_1.Param)('airtableRecordId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "assignLoadToCarrier", null);
__decorate([
    (0, common_1.Patch)(':loadId/status'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Update load status',
        description: 'Update the status of a load. For "assigned" status, also assigns the load to the authenticated carrier.'
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', description: 'New status for the load' }
            },
            required: ['status']
        }
    }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "updateLoadStatus", null);
__decorate([
    (0, common_1.Post)(':airtableRecordId/unassign'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    __param(0, (0, common_1.Param)('airtableRecordId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "unassignLoadFromCarrier", null);
__decorate([
    (0, common_1.Post)(':loadId/cancel'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Cancel assigned load',
        description: 'Cancel a load that was assigned to the carrier'
    }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "cancelAssignedLoad", null);
__decorate([
    (0, common_1.Post)(':loadId/bid'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Submit a bid on a load',
        description: 'Submit or update a bid on a specific load'
    }),
    (0, swagger_1.ApiBody)({ type: create_bid_dto_1.CreateBidDto }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_bid_dto_1.CreateBidDto, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "createBid", null);
__decorate([
    (0, common_1.Post)('bids/:bidId/respond'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Respond to admin counter-offer',
        description: 'Accept or decline an admin counter-offer on your bid'
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                response: {
                    type: 'string',
                    enum: ['accepted', 'declined'],
                    example: 'accepted'
                },
                notes: {
                    type: 'string',
                    example: 'Thank you for the counter-offer, I accept.'
                }
            },
            required: ['response']
        }
    }),
    __param(0, (0, common_1.Param)('bidId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "respondToCounterOffer", null);
__decorate([
    (0, common_1.Get)('bids'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get carrier bids',
        description: 'Fetch all bids submitted by the authenticated carrier'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "getCarrierBids", null);
__decorate([
    (0, common_1.Delete)('bids/:bidId'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Withdraw a bid',
        description: 'Withdraw a bid that was previously submitted'
    }),
    __param(0, (0, common_1.Param)('bidId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "withdrawBid", null);
__decorate([
    (0, common_1.Post)('bid-withdraw/:bidId'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Withdraw a bid (alternative route)',
        description: 'Alternative POST route to withdraw a bid that was previously submitted'
    }),
    __param(0, (0, common_1.Param)('bidId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "withdrawBidPost", null);
__decorate([
    (0, common_1.Post)('bids/:bidId/withdraw'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Withdraw a bid (frontend expected route)',
        description: 'Exact route that frontend is calling: POST /bids/{bidId}/withdraw'
    }),
    __param(0, (0, common_1.Param)('bidId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "withdrawBidFrontendRoute", null);
__decorate([
    (0, common_1.Post)(':loadId/debug-bids'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug Airtable bid updates',
        description: 'Test endpoint to debug Airtable bid field updates'
    }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "debugAirtableBids", null);
__decorate([
    (0, common_1.Get)('saved-searches'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get saved searches for user',
        description: 'Retrieve all saved search criteria for the authenticated user'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "getSavedSearches", null);
__decorate([
    (0, common_1.Post)('saved-searches'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Save search criteria',
        description: 'Save search criteria for the authenticated user'
    }),
    (0, swagger_1.ApiBody)({ type: advanced_filters_dto_1.SavedSearchDto }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, advanced_filters_dto_1.SavedSearchDto]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "saveSearch", null);
__decorate([
    (0, common_1.Put)('saved-searches/:searchId'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Update saved search',
        description: 'Update an existing saved search for the authenticated user'
    }),
    (0, swagger_1.ApiBody)({ type: advanced_filters_dto_1.SavedSearchDto }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('searchId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, advanced_filters_dto_1.SavedSearchDto]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "updateSavedSearch", null);
__decorate([
    (0, common_1.Delete)('saved-searches/:searchId'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete saved search',
        description: 'Delete a saved search for the authenticated user'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('searchId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "deleteSavedSearch", null);
__decorate([
    (0, common_1.Get)(':loadId/debug'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug load bidding eligibility',
        description: 'Diagnostic endpoint to check why a specific load cannot be found for bidding'
    }),
    __param(0, (0, common_1.Param)('loadId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AirtableOrdersController.prototype, "debugLoadForBidding", null);
exports.AirtableOrdersController = AirtableOrdersController = AirtableOrdersController_1 = __decorate([
    (0, swagger_1.ApiTags)('airtable-orders'),
    (0, common_1.Controller)('airtable-orders'),
    __metadata("design:paramtypes", [airtable_orders_service_1.AirtableOrdersService])
], AirtableOrdersController);
//# sourceMappingURL=airtable-orders.controller.js.map