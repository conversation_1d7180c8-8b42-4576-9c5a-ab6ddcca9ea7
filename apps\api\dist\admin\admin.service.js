"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AdminService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const auth_service_1 = require("../auth/auth.service");
const db_1 = require("@repo/db");
const notifications_service_1 = require("../notifications/notifications.service");
let AdminService = AdminService_1 = class AdminService {
    prisma;
    authService;
    notificationsService;
    logger = new common_1.Logger(AdminService_1.name);
    constructor(prisma, authService, notificationsService) {
        this.prisma = prisma;
        this.authService = authService;
        this.notificationsService = notificationsService;
    }
    async getCurrentUserInfo(req) {
        try {
            const authorizationHeader = req.headers['authorization'];
            if (!authorizationHeader || !authorizationHeader.startsWith('Bearer ')) {
                return { error: 'No authorization token provided' };
            }
            const token = authorizationHeader.substring(7);
            const clerkUserId = await this.authService.verifyToken(token);
            if (!clerkUserId) {
                return { error: 'Invalid authorization token' };
            }
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: clerkUserId.toString() },
                include: {
                    carrierProfile: {
                        select: {
                            id: true,
                            companyName: true,
                            isVerifiedByAdmin: true,
                        },
                    },
                },
            });
            return {
                clerkUserId: clerkUserId.toString(),
                user,
                message: user ? 'User found in database' : 'User not found in database',
            };
        }
        catch (error) {
            return {
                error: 'Failed to get user info',
                details: error.message,
            };
        }
    }
    async promoteFirstAdmin() {
        try {
            const firstUser = await this.prisma.user.findFirst({
                orderBy: { createdAt: 'asc' },
                select: { id: true, email: true, firstName: true, lastName: true, role: true, createdAt: true },
            });
            if (!firstUser) {
                return {
                    message: 'No users found in the database',
                    success: false,
                };
            }
            if (firstUser.role === 'ADMIN') {
                return {
                    message: `User ${firstUser.email} is already an admin`,
                    user: firstUser,
                    success: true,
                };
            }
            const updatedUser = await this.prisma.user.update({
                where: { id: firstUser.id },
                data: { role: 'ADMIN' },
                select: { id: true, email: true, firstName: true, lastName: true, role: true, createdAt: true },
            });
            return {
                message: `Successfully promoted ${updatedUser.email} to admin`,
                user: updatedUser,
                success: true,
            };
        }
        catch (error) {
            return {
                message: 'Failed to promote first user to admin',
                error: 'Failed to promote first user to admin',
                details: error.message,
                success: false,
            };
        }
    }
    async getAllUsers() {
        try {
            const users = await this.prisma.user.findMany({
                include: {
                    carrierProfile: {
                        select: {
                            id: true,
                            companyName: true,
                            isVerifiedByAdmin: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            });
            return users;
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to fetch users');
        }
    }
    async verifyCarrier(userId, isVerified) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                include: {
                    carrierProfile: true,
                },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with ID ${userId} not found`);
            }
            if (!user.carrierProfile) {
                throw new common_1.BadRequestException('User does not have a carrier profile');
            }
            if (user.role !== 'CARRIER') {
                throw new common_1.BadRequestException('User is not a carrier');
            }
            const updatedCarrierProfile = await this.prisma.carrierProfile.update({
                where: { id: user.carrierProfile.id },
                data: { isVerifiedByAdmin: isVerified },
                select: {
                    id: true,
                    companyName: true,
                    isVerifiedByAdmin: true,
                },
            });
            return {
                message: `Carrier ${isVerified ? 'verified' : 'unverified'} successfully`,
                carrierProfile: updatedCarrierProfile,
                success: true,
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to update carrier verification status');
        }
    }
    async updateUserRole(userId, role) {
        try {
            const existingUser = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, role: true, email: true, firstName: true, lastName: true },
            });
            if (!existingUser) {
                throw new common_1.NotFoundException(`User with ID ${userId} not found`);
            }
            if (!['CARRIER', 'ADMIN'].includes(role)) {
                throw new common_1.BadRequestException('Invalid role. Must be CARRIER or ADMIN');
            }
            if (existingUser.role === role) {
                return {
                    message: `User role is already ${role}`,
                    user: existingUser,
                };
            }
            const updatedUser = await this.prisma.user.update({
                where: { id: userId },
                data: { role },
                include: {
                    carrierProfile: {
                        select: {
                            id: true,
                            companyName: true,
                            isVerifiedByAdmin: true,
                        },
                    },
                },
            });
            return {
                message: `User role updated from ${existingUser.role} to ${role}`,
                user: updatedUser,
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to update user role');
        }
    }
    async getSystemSettings() {
        const settings = await this.prisma.systemSettings.findFirst();
        if (!settings) {
            return this.getDefaultSettings();
        }
        return {
            platformName: settings.platformName ?? undefined,
            supportEmail: settings.supportEmail ?? undefined,
            maintenanceMode: settings.maintenanceMode ?? undefined,
            autoAssignLoads: settings.autoAssignLoads ?? undefined,
            requireLoadApproval: settings.requireLoadApproval ?? undefined,
            maxLoadsPerCarrier: settings.maxLoadsPerCarrier ?? undefined,
            loadExpirationHours: settings.loadExpirationHours ?? undefined,
            requireInsuranceVerification: settings.requireInsuranceVerification ?? undefined,
            requireDotVerification: settings.requireDotVerification ?? undefined,
            autoApproveCarriers: settings.autoApproveCarriers ?? undefined,
            verificationReminderDays: settings.verificationReminderDays ?? undefined,
            enableEmailNotifications: settings.enableEmailNotifications ?? undefined,
            enableSmsNotifications: settings.enableSmsNotifications ?? undefined,
            notificationFrequency: settings.notificationFrequency,
            requireTwoFactor: settings.requireTwoFactor ?? undefined,
            sessionTimeoutMinutes: settings.sessionTimeoutMinutes ?? undefined,
            maxLoginAttempts: settings.maxLoginAttempts ?? undefined,
            passwordExpirationDays: settings.passwordExpirationDays ?? undefined,
            defaultPaymentTerms: settings.defaultPaymentTerms ?? undefined,
            latePaymentFeePercent: settings.latePaymentFeePercent ?? undefined,
            invoiceReminderDays: settings.invoiceReminderDays ?? undefined,
            maxFileUploadSize: settings.maxFileUploadSize ?? undefined,
            rateLimitPerMinute: settings.rateLimitPerMinute ?? undefined,
            enableLoadTracking: settings.enableLoadTracking ?? undefined,
            enableRealTimeUpdates: settings.enableRealTimeUpdates ?? undefined,
            enableAdvancedReporting: settings.enableAdvancedReporting ?? undefined,
            enableApiAccess: settings.enableApiAccess ?? undefined,
            maintenanceWindowStart: settings.maintenanceWindowStart ?? undefined,
            maintenanceWindowEnd: settings.maintenanceWindowEnd ?? undefined,
            backupFrequency: settings.backupFrequency,
        };
    }
    async updateSystemSettings(updateData) {
        const existingSettings = await this.prisma.systemSettings.findFirst();
        let updatedSettings;
        if (existingSettings) {
            updatedSettings = await this.prisma.systemSettings.update({
                where: { id: existingSettings.id },
                data: updateData,
            });
        }
        else {
            const defaultSettings = this.getDefaultSettings();
            updatedSettings = await this.prisma.systemSettings.create({
                data: {
                    ...defaultSettings,
                    ...updateData,
                },
            });
        }
        return this.convertToDto(updatedSettings);
    }
    async resetSystemSettings() {
        const defaultSettings = this.getDefaultSettings();
        await this.prisma.systemSettings.deleteMany({});
        const newSettings = await this.prisma.systemSettings.create({
            data: defaultSettings,
        });
        return this.convertToDto(newSettings);
    }
    getDefaultSettings() {
        return {
            platformName: 'Carrier Portal',
            supportEmail: '<EMAIL>',
            maintenanceMode: false,
            autoAssignLoads: false,
            requireLoadApproval: true,
            maxLoadsPerCarrier: 10,
            loadExpirationHours: 72,
            requireInsuranceVerification: true,
            requireDotVerification: true,
            autoApproveCarriers: false,
            verificationReminderDays: 30,
            enableEmailNotifications: true,
            enableSmsNotifications: false,
            notificationFrequency: 'DAILY',
            requireTwoFactor: false,
            sessionTimeoutMinutes: 60,
            maxLoginAttempts: 5,
            passwordExpirationDays: 90,
            defaultPaymentTerms: 'Net 30',
            latePaymentFeePercent: 2.5,
            invoiceReminderDays: 7,
            maxFileUploadSize: 10,
            rateLimitPerMinute: 100,
            enableLoadTracking: true,
            enableRealTimeUpdates: true,
            enableAdvancedReporting: false,
            enableApiAccess: false,
            maintenanceWindowStart: '02:00',
            maintenanceWindowEnd: '04:00',
            backupFrequency: 'DAILY',
        };
    }
    convertToDto(settings) {
        return {
            platformName: settings.platformName,
            supportEmail: settings.supportEmail,
            maintenanceMode: settings.maintenanceMode,
            autoAssignLoads: settings.autoAssignLoads,
            requireLoadApproval: settings.requireLoadApproval,
            maxLoadsPerCarrier: settings.maxLoadsPerCarrier,
            loadExpirationHours: settings.loadExpirationHours,
            requireInsuranceVerification: settings.requireInsuranceVerification,
            requireDotVerification: settings.requireDotVerification,
            autoApproveCarriers: settings.autoApproveCarriers,
            verificationReminderDays: settings.verificationReminderDays,
            enableEmailNotifications: settings.enableEmailNotifications,
            enableSmsNotifications: settings.enableSmsNotifications,
            notificationFrequency: settings.notificationFrequency,
            requireTwoFactor: settings.requireTwoFactor,
            sessionTimeoutMinutes: settings.sessionTimeoutMinutes,
            maxLoginAttempts: settings.maxLoginAttempts,
            passwordExpirationDays: settings.passwordExpirationDays,
            defaultPaymentTerms: settings.defaultPaymentTerms,
            latePaymentFeePercent: settings.latePaymentFeePercent,
            invoiceReminderDays: settings.invoiceReminderDays,
            maxFileUploadSize: settings.maxFileUploadSize,
            rateLimitPerMinute: settings.rateLimitPerMinute,
            enableLoadTracking: settings.enableLoadTracking,
            enableRealTimeUpdates: settings.enableRealTimeUpdates,
            enableAdvancedReporting: settings.enableAdvancedReporting,
            enableApiAccess: settings.enableApiAccess,
            maintenanceWindowStart: settings.maintenanceWindowStart,
            maintenanceWindowEnd: settings.maintenanceWindowEnd,
            backupFrequency: settings.backupFrequency,
        };
    }
    async getPendingBids() {
        try {
            const pendingBids = await this.prisma.bid.findMany({
                where: {
                    admin_response: db_1.AdminResponse.PENDING,
                    negotiation_status: db_1.NegotiationStatus.OPEN
                },
                include: {
                    load: {
                        select: {
                            id: true,
                            airtableRecordId: true,
                            originCity: true,
                            originState: true,
                            destinationCity: true,
                            destinationState: true,
                            pickupDateUtc: true,
                            deliveryDateUtc: true,
                            equipmentRequired: true,
                            rate: true,
                            status: true
                        }
                    },
                    carrierProfile: {
                        select: {
                            id: true,
                            companyName: true,
                            mcNumber: true,
                            dotNumber: true,
                            contact_name: true,
                            contact_email: true,
                            contact_phone: true
                        }
                    }
                },
                orderBy: [
                    { expires_at: 'asc' },
                    { createdAt: 'desc' }
                ]
            });
            const bidsWithTimeRemaining = pendingBids.map(bid => ({
                ...bid,
                timeRemaining: bid.expires_at ? this.calculateTimeRemaining(bid.expires_at) : null,
                isExpiringSoon: bid.expires_at ? this.isExpiringSoon(bid.expires_at) : false
            }));
            this.logger.log(`Retrieved ${bidsWithTimeRemaining.length} pending bids for admin review`);
            return bidsWithTimeRemaining;
        }
        catch (error) {
            this.logger.error(`Failed to retrieve pending bids: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve pending bids');
        }
    }
    async getAllBidsWithFilters(filters) {
        try {
            const whereConditions = {};
            if (filters.status) {
                whereConditions.admin_response = filters.status.toUpperCase();
            }
            if (filters.carrierId) {
                whereConditions.carrierProfileId = filters.carrierId;
            }
            if (filters.loadId) {
                whereConditions.loadId = filters.loadId;
            }
            if (filters.dateFrom || filters.dateTo) {
                whereConditions.createdAt = {};
                if (filters.dateFrom) {
                    whereConditions.createdAt.gte = filters.dateFrom;
                }
                if (filters.dateTo) {
                    whereConditions.createdAt.lte = filters.dateTo;
                }
            }
            const totalCount = await this.prisma.bid.count({
                where: whereConditions
            });
            const bids = await this.prisma.bid.findMany({
                where: whereConditions,
                include: {
                    load: {
                        select: {
                            id: true,
                            airtableRecordId: true,
                            originCity: true,
                            originState: true,
                            destinationCity: true,
                            destinationState: true,
                            pickupDateUtc: true,
                            deliveryDateUtc: true,
                            equipmentRequired: true,
                            rate: true,
                            status: true
                        }
                    },
                    carrierProfile: {
                        select: {
                            id: true,
                            companyName: true,
                            mcNumber: true,
                            dotNumber: true,
                            contact_name: true,
                            contact_email: true,
                            contact_phone: true
                        }
                    },
                    bid_responses: {
                        orderBy: { created_at: 'desc' },
                        take: 5
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip: (filters.page - 1) * filters.limit,
                take: filters.limit
            });
            this.logger.log(`Retrieved ${bids.length} bids with filters for admin (page ${filters.page})`);
            return {
                bids,
                totalCount,
                page: filters.page,
                pageSize: filters.limit
            };
        }
        catch (error) {
            this.logger.error(`Failed to retrieve filtered bids: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve bids');
        }
    }
    async respondToBid(bidId, responseDto, adminUserId) {
        try {
            const existingBid = await this.prisma.bid.findUnique({
                where: { id: bidId },
                include: {
                    load: true,
                    carrierProfile: {
                        include: {
                            user: true
                        }
                    }
                }
            });
            if (!existingBid) {
                throw new common_1.NotFoundException(`Bid ${bidId} not found`);
            }
            if (existingBid.negotiation_status !== db_1.NegotiationStatus.OPEN) {
                throw new common_1.BadRequestException(`Bid ${bidId} is no longer open for responses (status: ${existingBid.negotiation_status})`);
            }
            if (responseDto.response === 'countered' && (!responseDto.counterOfferAmount || responseDto.counterOfferAmount <= 0)) {
                throw new common_1.BadRequestException('Counter offer amount is required and must be greater than 0 when countering a bid');
            }
            let newNegotiationStatus;
            let newAdminResponse;
            switch (responseDto.response) {
                case 'accepted':
                    newNegotiationStatus = db_1.NegotiationStatus.CLOSED;
                    newAdminResponse = db_1.AdminResponse.ACCEPTED;
                    break;
                case 'countered':
                    newNegotiationStatus = db_1.NegotiationStatus.OPEN;
                    newAdminResponse = db_1.AdminResponse.COUNTERED;
                    break;
                case 'declined':
                    newNegotiationStatus = db_1.NegotiationStatus.CLOSED;
                    newAdminResponse = db_1.AdminResponse.DECLINED;
                    break;
            }
            const result = await this.prisma.$transaction(async (tx) => {
                const updatedBid = await tx.bid.update({
                    where: { id: bidId },
                    data: {
                        admin_response: newAdminResponse,
                        counter_offer_amount: responseDto.counterOfferAmount || null,
                        response_timestamp: new Date(),
                        negotiation_status: newNegotiationStatus,
                        admin_notes: responseDto.notes || null
                    },
                    include: {
                        load: true,
                        carrierProfile: {
                            include: {
                                user: true
                            }
                        }
                    }
                });
                await tx.bid_responses.create({
                    data: {
                        id: `response_${bidId}_${Date.now()}`,
                        bid_id: bidId,
                        response_type: db_1.BidResponseType.ADMIN_RESPONSE,
                        amount: responseDto.counterOfferAmount || null,
                        notes: responseDto.notes || null,
                        created_by: adminUserId
                    }
                });
                if (responseDto.response === 'accepted') {
                    await tx.load.update({
                        where: { id: existingBid.loadId },
                        data: {
                            awardedToCarrierProfileId: existingBid.carrierProfileId,
                            status: 'ASSIGNED'
                        }
                    });
                    await tx.bid.updateMany({
                        where: {
                            loadId: existingBid.loadId,
                            id: { not: bidId },
                            negotiation_status: db_1.NegotiationStatus.OPEN
                        },
                        data: {
                            negotiation_status: db_1.NegotiationStatus.CLOSED,
                            admin_response: db_1.AdminResponse.DECLINED,
                            response_timestamp: new Date(),
                            admin_notes: 'Load awarded to another carrier'
                        }
                    });
                }
                return updatedBid;
            });
            await this.sendAdminDashboardNotifications(result, responseDto.response, adminUserId);
            this.logger.log(`Admin ${adminUserId} responded to bid ${bidId} with: ${responseDto.response}`);
            return {
                success: true,
                message: `Bid ${responseDto.response} successfully`,
                bid: result,
                response: responseDto.response,
                counterOfferAmount: responseDto.counterOfferAmount
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to respond to bid ${bidId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to process bid response');
        }
    }
    async getBidHistory(bidId) {
        try {
            const bid = await this.prisma.bid.findUnique({
                where: { id: bidId },
                include: {
                    load: {
                        select: {
                            airtableRecordId: true,
                            originCity: true,
                            originState: true,
                            destinationCity: true,
                            destinationState: true
                        }
                    },
                    carrierProfile: {
                        select: {
                            companyName: true,
                            mcNumber: true
                        }
                    }
                }
            });
            if (!bid) {
                throw new common_1.NotFoundException(`Bid ${bidId} not found`);
            }
            const bidResponses = await this.prisma.bid_responses.findMany({
                where: { bid_id: bidId },
                orderBy: { created_at: 'asc' }
            });
            const history = [
                {
                    id: `initial_${bidId}`,
                    type: 'INITIAL_BID',
                    amount: bid.bidAmount,
                    notes: bid.carrierNotes,
                    created_at: bid.createdAt,
                    created_by: bid.carrierProfile.companyName,
                    created_by_type: 'carrier'
                },
                ...bidResponses.map(response => ({
                    ...response,
                    created_by_type: response.response_type === db_1.BidResponseType.ADMIN_RESPONSE ? 'admin' : 'carrier'
                }))
            ];
            this.logger.log(`Retrieved ${history.length} history entries for bid ${bidId}`);
            return history;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to retrieve bid history for ${bidId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve bid history');
        }
    }
    async getBiddingStatistics() {
        try {
            const [totalBids, pendingBids, acceptedBids, declinedBids, counteredBids, expiredBids, averageBidAmount, bidsLast24Hours, bidsLast7Days, topCarriers] = await Promise.all([
                this.prisma.bid.count(),
                this.prisma.bid.count({
                    where: { admin_response: db_1.AdminResponse.PENDING }
                }),
                this.prisma.bid.count({
                    where: { admin_response: db_1.AdminResponse.ACCEPTED }
                }),
                this.prisma.bid.count({
                    where: { admin_response: db_1.AdminResponse.DECLINED }
                }),
                this.prisma.bid.count({
                    where: { admin_response: db_1.AdminResponse.COUNTERED }
                }),
                this.prisma.bid.count({
                    where: { negotiation_status: db_1.NegotiationStatus.EXPIRED }
                }),
                this.prisma.bid.aggregate({
                    _avg: { bidAmount: true }
                }),
                this.prisma.bid.count({
                    where: {
                        createdAt: {
                            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
                        }
                    }
                }),
                this.prisma.bid.count({
                    where: {
                        createdAt: {
                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                        }
                    }
                }),
                this.prisma.bid.groupBy({
                    by: ['carrierProfileId'],
                    _count: { id: true },
                    orderBy: { _count: { id: 'desc' } },
                    take: 5
                })
            ]);
            const topCarriersWithNames = await Promise.all(topCarriers.map(async (carrier) => {
                const profile = await this.prisma.carrierProfile.findUnique({
                    where: { id: carrier.carrierProfileId },
                    select: { companyName: true, mcNumber: true }
                });
                return {
                    ...carrier,
                    companyName: profile?.companyName || 'Unknown',
                    mcNumber: profile?.mcNumber || 'N/A'
                };
            }));
            const stats = {
                overview: {
                    totalBids,
                    pendingBids,
                    acceptedBids,
                    declinedBids,
                    counteredBids,
                    expiredBids,
                    averageBidAmount: averageBidAmount._avg.bidAmount || 0
                },
                activity: {
                    bidsLast24Hours,
                    bidsLast7Days,
                    acceptanceRate: totalBids > 0 ? ((acceptedBids / totalBids) * 100).toFixed(1) : 0,
                    responseRate: totalBids > 0 ? (((acceptedBids + declinedBids + counteredBids) / totalBids) * 100).toFixed(1) : 0
                },
                topCarriers: topCarriersWithNames
            };
            this.logger.log('Retrieved bidding statistics for admin dashboard');
            return stats;
        }
        catch (error) {
            this.logger.error(`Failed to retrieve bidding statistics: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve bidding statistics');
        }
    }
    async processExpiredBids() {
        this.logger.log('Processing expired bids...');
        try {
            const expiredBids = await this.prisma.bid.findMany({
                where: {
                    expires_at: {
                        lt: new Date()
                    },
                    negotiation_status: db_1.NegotiationStatus.OPEN
                },
                include: {
                    load: {
                        select: {
                            airtableRecordId: true,
                            originCity: true,
                            originState: true,
                            destinationCity: true,
                            destinationState: true
                        }
                    },
                    carrierProfile: {
                        include: {
                            user: true
                        }
                    }
                }
            });
            if (expiredBids.length === 0) {
                this.logger.log('No expired bids found');
                return { expiredCount: 0, notifiedCount: 0 };
            }
            this.logger.log(`Found ${expiredBids.length} expired bids to process`);
            await this.prisma.bid.updateMany({
                where: {
                    id: {
                        in: expiredBids.map(bid => bid.id)
                    }
                },
                data: {
                    negotiation_status: db_1.NegotiationStatus.EXPIRED,
                    response_timestamp: new Date(),
                    admin_notes: 'Bid expired automatically after 24 hours'
                }
            });
            let notifiedCount = 0;
            for (const bid of expiredBids) {
                try {
                    const carrierUserId = bid.carrierProfile.user?.airtableUserId;
                    if (carrierUserId && this.notificationsService) {
                        await this.notificationsService.broadcastToUser(carrierUserId, {
                            type: 'bid_expired',
                            title: 'Bid Expired',
                            message: `Your bid of $${bid.bidAmount.toFixed(2)} on load ${bid.load.airtableRecordId} has expired.`,
                            data: {
                                bidId: bid.id,
                                loadId: bid.loadId,
                                bidAmount: bid.bidAmount,
                                loadDetails: bid.load
                            }
                        });
                        notifiedCount++;
                    }
                }
                catch (notificationError) {
                    this.logger.error(`Failed to notify carrier about expired bid ${bid.id}: ${notificationError.message}`);
                }
            }
            this.logger.log(`Successfully expired ${expiredBids.length} bids and notified ${notifiedCount} carriers`);
            return {
                expiredCount: expiredBids.length,
                notifiedCount
            };
        }
        catch (error) {
            this.logger.error(`Failed to process expired bids: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to process expired bids');
        }
    }
    async getBidsExpiringSoon(hoursFromNow = 2) {
        try {
            const expiryThreshold = new Date();
            expiryThreshold.setHours(expiryThreshold.getHours() + hoursFromNow);
            const expiringSoonBids = await this.prisma.bid.findMany({
                where: {
                    expires_at: {
                        lte: expiryThreshold,
                        gt: new Date()
                    },
                    negotiation_status: db_1.NegotiationStatus.OPEN
                },
                include: {
                    load: {
                        select: {
                            airtableRecordId: true,
                            originCity: true,
                            originState: true,
                            destinationCity: true,
                            destinationState: true
                        }
                    },
                    carrierProfile: {
                        select: {
                            companyName: true,
                            mcNumber: true,
                            contact_email: true
                        }
                    }
                },
                orderBy: {
                    expires_at: 'asc'
                }
            });
            const bidsWithTimeRemaining = expiringSoonBids.map(bid => ({
                ...bid,
                timeRemaining: bid.expires_at ? this.calculateTimeRemaining(bid.expires_at) : null
            }));
            this.logger.log(`Found ${bidsWithTimeRemaining.length} bids expiring within ${hoursFromNow} hours`);
            return bidsWithTimeRemaining;
        }
        catch (error) {
            this.logger.error(`Failed to get bids expiring soon: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve bids expiring soon');
        }
    }
    calculateTimeRemaining(expiresAt) {
        const now = new Date();
        const diffMs = expiresAt.getTime() - now.getTime();
        if (diffMs <= 0) {
            return 'Expired';
        }
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        }
        else {
            return `${minutes}m`;
        }
    }
    isExpiringSoon(expiresAt) {
        const now = new Date();
        const diffMs = expiresAt.getTime() - now.getTime();
        const twoHoursMs = 2 * 60 * 60 * 1000;
        return diffMs > 0 && diffMs <= twoHoursMs;
    }
    async sendAdminDashboardNotifications(bid, response, adminUserId) {
        try {
            if (!this.notificationsService) {
                this.logger.warn('NotificationsService not available, skipping admin dashboard notifications');
                return;
            }
            await this.notificationsService.broadcastToAdmins({
                type: 'bid_processed',
                title: `Bid ${response.charAt(0).toUpperCase() + response.slice(1)}`,
                message: `Bid ${bid.id} has been ${response} by admin`,
                data: {
                    bidId: bid.id,
                    loadId: bid.loadId,
                    response,
                    amount: bid.bidAmount,
                    counterOfferAmount: bid.counter_offer_amount,
                    processedBy: adminUserId,
                    carrierMcNumber: bid.carrierProfile?.mcNumber
                }
            });
            this.logger.log(`Admin dashboard notified of bid ${response}: ${bid.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to send admin dashboard notifications: ${error.message}`, error.stack);
        }
    }
    getBidResponseMessage(response, bidAmount, counterOfferAmount) {
        switch (response) {
            case 'accepted':
                return `Your bid of $${bidAmount.toFixed(2)} has been accepted! The load has been assigned to you.`;
            case 'countered':
                return `Your bid of $${bidAmount.toFixed(2)} has been countered with $${counterOfferAmount?.toFixed(2) || 0}. You can accept or decline this counter-offer.`;
            case 'declined':
                return `Your bid of $${bidAmount.toFixed(2)} was not accepted. Keep bidding on other loads!`;
            default:
                return `Your bid status has been updated.`;
        }
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = AdminService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        auth_service_1.AuthService,
        notifications_service_1.NotificationsService])
], AdminService);
//# sourceMappingURL=admin.service.js.map