"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const jwt_strategy_1 = require("./jwt.strategy");
const redis_service_1 = require("./redis.service");
const core_1 = require("@nestjs/core");
const auth_guard_1 = require("./auth.guard");
const admin_guard_1 = require("./admin.guard");
const carrier_profile_guard_1 = require("./carrier-profile.guard");
const auth_controller_1 = require("./auth.controller");
const carrier_profiles_module_1 = require("../carrier-profiles/carrier-profiles.module");
const prisma_module_1 = require("../prisma/prisma.module");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [(0, common_1.forwardRef)(() => carrier_profiles_module_1.CarrierProfilesModule), prisma_module_1.PrismaModule],
        controllers: [auth_controller_1.AuthController],
        providers: [
            auth_service_1.AuthService,
            jwt_strategy_1.JwtStrategy,
            redis_service_1.RedisService,
            core_1.Reflector,
            auth_guard_1.AuthGuard,
            admin_guard_1.AdminGuard,
            carrier_profile_guard_1.CarrierProfileGuard,
        ],
        exports: [auth_service_1.AuthService, redis_service_1.RedisService, auth_guard_1.AuthGuard, admin_guard_1.AdminGuard, carrier_profile_guard_1.CarrierProfileGuard],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map