'use client';

import { useAuth } from '@/contexts/auth-context';
import { useState } from 'react';
import { TestTube, Check, X, Loader2 } from 'lucide-react';

export function ApiAuthTester() {
  const { getToken } = useAuth();
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<any>(null);

  const testApiAuth = async () => {
    setTesting(true);
    setResults(null);

    try {
      console.log('🧪 Testing API authentication...');
      
      const token = await getToken();
      if (!token) {
        throw new Error('No JWT token available');
      }

      // Test the auth endpoint
      const authResponse = await fetch('/api/v1/auth/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const authResult: {
        status: number;
        ok: boolean;
        data: any;
        error: string | null;
      } = {
        status: authResponse.status,
        ok: authResponse.ok,
        data: null,
        error: null
      };

      if (authResponse.ok) {
        authResult.data = await authResponse.json();
      } else {
        authResult.error = await authResponse.text();
      }

      // Test the airtable-orders endpoint
      const ordersResponse = await fetch('/api/v1/airtable-orders/bids', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const ordersResult: {
        status: number;
        ok: boolean;
        data: any;
        error: string | null;
      } = {
        status: ordersResponse.status,
        ok: ordersResponse.ok,
        data: null,
        error: null
      };

      if (ordersResponse.ok) {
        try {
          ordersResult.data = await ordersResponse.json();
        } catch (e) {
          ordersResult.data = 'Response OK but not JSON';
        }
      } else {
        ordersResult.error = await ordersResponse.text();
      }

      // Decode JWT for debugging
      let jwtPayload = null;
      try {
        const parts = token.split('.');
        if (parts.length >= 2 && parts[1]) {
          jwtPayload = JSON.parse(atob(parts[1]));
        }
      } catch (e) {
        console.error('Failed to decode JWT:', e);
      }

      setResults({
        timestamp: new Date().toLocaleTimeString(),
        jwt: {
          hasToken: !!token,
          payload: jwtPayload,
          hasOrgClaims: !!(jwtPayload?.org_id || jwtPayload?.o?.id)
        },
        auth: authResult,
        orders: ordersResult
      });

      console.log('🧪 API Test Results:', {
        authStatus: authResult.status,
        ordersStatus: ordersResult.status,
        jwtHasOrgClaims: !!(jwtPayload?.org_id || jwtPayload?.o?.id)
      });

    } catch (error) {
      console.error('🧪 API Test Failed:', error);
      setResults({
        timestamp: new Date().toLocaleTimeString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setTesting(false);
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div className="fixed bottom-20 left-4 z-50">
      <button
        onClick={testApiAuth}
        disabled={testing}
        className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-3 py-2 rounded-lg shadow-lg flex items-center text-sm"
      >
        {testing ? (
          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
        ) : (
          <TestTube className="h-4 w-4 mr-1" />
        )}
        {testing ? 'Testing...' : 'Test API Auth'}
      </button>
      
      {results && (
        <div className="bg-white border border-gray-300 rounded-lg shadow-lg mt-2 p-4 max-w-md text-xs">
          <h4 className="font-bold mb-3 text-gray-800">🧪 API Auth Test Results</h4>
          <div className="text-xs text-gray-500 mb-2">Last test: {results.timestamp}</div>
          
          {results.error ? (
            <div className="bg-red-100 p-2 rounded text-red-700">
              <strong>Error:</strong> {results.error}
            </div>
          ) : (
            <div className="space-y-3">
              <div className="border-b pb-2">
                <div className="font-semibold text-gray-700">JWT Token:</div>
                <div className="flex items-center">
                  {results.jwt.hasToken ? <Check className="h-3 w-3 text-green-600 mr-1" /> : <X className="h-3 w-3 text-red-600 mr-1" />}
                  Token: {results.jwt.hasToken ? 'Present' : 'Missing'}
                </div>
                <div className="flex items-center">
                  {results.jwt.hasOrgClaims ? <Check className="h-3 w-3 text-green-600 mr-1" /> : <X className="h-3 w-3 text-red-600 mr-1" />}
                  Org Claims: {results.jwt.hasOrgClaims ? 'Present' : 'Missing'}
                </div>
                {results.jwt.payload?.org_id && (
                  <div className="text-xs text-gray-600">Org ID: {results.jwt.payload.org_id}</div>
                )}
              </div>
              
              <div className="border-b pb-2">
                <div className="font-semibold text-gray-700">/api/v1/auth/me:</div>
                <div className="flex items-center">
                  {results.auth.ok ? <Check className="h-3 w-3 text-green-600 mr-1" /> : <X className="h-3 w-3 text-red-600 mr-1" />}
                  Status: {results.auth.status}
                </div>
                {results.auth.error && (
                  <div className="text-xs text-red-600 mt-1">
                    Error: {results.auth.error.substring(0, 100)}...
                  </div>
                )}
              </div>
              
              <div>
                <div className="font-semibold text-gray-700">/api/v1/airtable-orders/bids:</div>
                <div className="flex items-center">
                  {results.orders.ok ? <Check className="h-3 w-3 text-green-600 mr-1" /> : <X className="h-3 w-3 text-red-600 mr-1" />}
                  Status: {results.orders.status}
                </div>
                {results.orders.error && (
                  <div className="text-xs text-red-600 mt-1">
                    Error: {results.orders.error.substring(0, 100)}...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 