"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

interface SidebarState {
  isCollapsed: boolean;
  toggleSidebar: () => void;
  setCollapsed: (collapsed: boolean) => void;
}

const SidebarContext = createContext<SidebarState | undefined>(undefined);

export const SidebarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [userPreference, setUserPreference] = useState<boolean | null>(null);

  // Initialize from localStorage on client-side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebar-collapsed');
      if (saved !== null) {
        const preference = JSON.parse(saved);
        setUserPreference(preference);
        setIsCollapsed(preference);
      }
    }
  }, []);

  // Handle responsive behavior
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
      
      if (isMobile) {
        // On mobile, the sidebar is hidden anyway (hidden md:block), so keep preference
        return;
      } else if (isTablet) {
        // On tablet, auto-collapse but don't override user preference for desktop
        setIsCollapsed(true);
      } else {
        // On desktop, use user preference or default to expanded
        setIsCollapsed(userPreference ?? false);
      }
    };

    // Initial check
    handleResize();

    // Listen for resize events
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [userPreference]);

  // Persist user preference to localStorage when toggled manually
  useEffect(() => {
    if (typeof window !== 'undefined' && userPreference !== null) {
      localStorage.setItem('sidebar-collapsed', JSON.stringify(userPreference));
    }
  }, [userPreference]);

  const toggleSidebar = useCallback(() => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    
    // Only update user preference on desktop
    if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
      setUserPreference(newState);
    }
  }, [isCollapsed]);

  const setCollapsed = useCallback((collapsed: boolean) => {
    setIsCollapsed(collapsed);
    
    // Only update user preference on desktop
    if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
      setUserPreference(collapsed);
    }
  }, []);

  // Keyboard shortcut handler (Ctrl/Cmd + B) - only on desktop
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        // Only allow keyboard shortcuts on desktop
        if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
          toggleSidebar();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleSidebar]);

  return (
    <SidebarContext.Provider value={{ isCollapsed, toggleSidebar, setCollapsed }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebar = (): SidebarState => {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}; 