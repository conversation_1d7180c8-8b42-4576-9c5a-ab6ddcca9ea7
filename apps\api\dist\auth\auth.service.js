"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const config_1 = require("@nestjs/config");
const jwt = __importStar(require("jsonwebtoken"));
const db_1 = require("@repo/db");
let AuthService = AuthService_1 = class AuthService {
    prisma;
    configService;
    logger = new common_1.Logger(AuthService_1.name);
    jwtSecret;
    airtableBaseId;
    airtableApiKey;
    normalizeRole(airtableRole) {
        const role = (airtableRole || 'Carrier').toLowerCase();
        return role === 'admin' ? db_1.Role.ADMIN : db_1.Role.CARRIER;
    }
    constructor(prisma, configService) {
        this.prisma = prisma;
        this.configService = configService;
        this.jwtSecret = this.configService.get('JWT_SECRET') || this.configService.get('N8N_JWT_SECRET') || '';
        this.airtableBaseId = this.configService.get('AIRTABLE_BASE_ID') || '';
        this.airtableApiKey = this.configService.get('AIRTABLE_API_KEY') || '';
        if (!this.jwtSecret) {
            this.logger.error('JWT_SECRET or N8N_JWT_SECRET is not set in environment variables.');
            throw new common_1.InternalServerErrorException('JWT secret key is not configured.');
        }
        this.logger.log('N8N JWT Authentication Service initialized');
    }
    async verifyToken(token) {
        try {
            const payload = jwt.verify(token, this.jwtSecret);
            if (!payload.id || !payload.email || !payload.role) {
                throw new common_1.UnauthorizedException('Invalid JWT payload structure');
            }
            this.logger.debug(`Token verified for user ${payload.id} (${payload.email})`);
            return payload;
        }
        catch (error) {
            this.logger.warn(`Token verification failed: ${error.message}`);
            throw new common_1.UnauthorizedException('Invalid or expired token');
        }
    }
    async getUserProfile(airtableUserId, forceRefresh = false) {
        try {
            this.logger.debug(`Fetching fresh profile from Airtable for ${airtableUserId}`);
            return await this.fetchUserProfileFromAirtable(airtableUserId);
        }
        catch (error) {
            this.logger.error(`Error getting user profile for ${airtableUserId}: ${error.message}`);
            return null;
        }
    }
    async fetchUserProfileFromAirtable(airtableUserId) {
        try {
            const response = await fetch(`https://api.airtable.com/v0/${this.airtableBaseId}/UserManagement/${airtableUserId}`, {
                headers: {
                    'Authorization': `Bearer ${this.airtableApiKey}`,
                    'Content-Type': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Error(`Airtable API error: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            const fields = data.fields;
            this.logger.debug(`Airtable fields for user ${airtableUserId}:`, JSON.stringify(fields, null, 2));
            this.logger.debug(`Available field names:`, Object.keys(fields));
            const mcNumber = fields['MC Number (from Company Name)']
                ? String(Array.isArray(fields['MC Number (from Company Name)'])
                    ? fields['MC Number (from Company Name)'][0]
                    : fields['MC Number (from Company Name)'])
                : null;
            const dotNumber = fields['DOT Number (from Company Name)']
                ? String(Array.isArray(fields['DOT Number (from Company Name)'])
                    ? fields['DOT Number (from Company Name)'][0]
                    : fields['DOT Number (from Company Name)'])
                : null;
            let companyName = null;
            const possibleCompanyFields = [
                'Company Name Text',
                'Company Name',
                'Company Name (from Company)',
                'Company',
                'companyName',
                'Organization',
                'Org Name',
                'Company_Name',
                'company_name',
                'carrier_company',
                'CarrierCompany',
                'business_name',
                'BusinessName'
            ];
            for (const fieldName of possibleCompanyFields) {
                if (fields[fieldName]) {
                    companyName = Array.isArray(fields[fieldName])
                        ? fields[fieldName][0]
                        : fields[fieldName];
                    this.logger.debug(`Found company name in field '${fieldName}': ${companyName}`);
                    break;
                }
            }
            if (!companyName) {
                this.logger.warn(`No company name found in any expected field for user ${airtableUserId}`);
                this.logger.debug(`Available fields:`, Object.keys(fields));
            }
            const userProfile = {
                airtableUserId: data.id,
                email: fields.Email,
                firstName: fields['First Name'],
                lastName: fields['Last Name'],
                companyName: companyName || undefined,
                mcNumber: mcNumber || undefined,
                dotNumber: dotNumber || undefined,
                role: fields.Role || 'Carrier',
                verificationStatus: fields['Verification Status'] || 'Pending',
            };
            this.logger.log(`Fresh user profile fetched from Airtable for ${airtableUserId} (${userProfile.email}) with role: ${userProfile.role}`);
            return userProfile;
        }
        catch (error) {
            this.logger.error(`Failed to fetch user profile from Airtable for ${airtableUserId}: ${error.message}`);
            return null;
        }
    }
    async validateUser(jwtPayload, isLoginRequest = false) {
        try {
            const userProfile = await this.getUserProfile(jwtPayload.id, isLoginRequest);
            if (!userProfile) {
                this.logger.warn(`User profile not found for JWT user ${jwtPayload.id}`);
                return null;
            }
            const mcNumber = userProfile.mcNumber || jwtPayload.mcNumber;
            if (isLoginRequest) {
                this.logger.log(`Login profile refresh for ${jwtPayload.id} - MC: ${mcNumber}, Role: ${userProfile.role}`);
            }
            const normalizedRole = userProfile.role.toLowerCase() === 'admin' ? 'ADMIN' : 'CARRIER';
            return {
                airtableUserId: userProfile.airtableUserId,
                email: userProfile.email,
                mcNumber: mcNumber,
                companyName: userProfile.companyName,
                role: normalizedRole,
                isAdmin: normalizedRole === 'ADMIN',
            };
        }
        catch (error) {
            this.logger.error(`User validation failed for ${jwtPayload.id}: ${error.message}`);
            return null;
        }
    }
    async updateCachedProfile(airtableUserId, updates) {
        this.logger.debug(`updateCachedProfile called but no longer needed for ${airtableUserId}`);
    }
    async clearCachedProfile(airtableUserId) {
        this.logger.debug(`clearCachedProfile called but no longer needed for ${airtableUserId}`);
    }
    async refreshUserProfile(airtableUserId) {
        this.logger.log(`Manual profile refresh requested for ${airtableUserId}`);
        return await this.fetchUserProfileFromAirtable(airtableUserId);
    }
    async syncAllCachedProfiles(maxAge = 24) {
        const maxAgeDate = new Date(Date.now() - (maxAge * 60 * 60 * 1000));
        const errors = [];
        let updated = 0;
        try {
            const staleProfiles = await this.prisma.userProfile.findMany({
                where: {
                    updatedAt: {
                        lt: maxAgeDate
                    }
                },
                select: { airtableUserId: true, email: true, updatedAt: true }
            });
            this.logger.log(`Found ${staleProfiles.length} profiles older than ${maxAge} hours for refresh`);
            for (const profile of staleProfiles) {
                try {
                    await this.fetchUserProfileFromAirtable(profile.airtableUserId);
                    updated++;
                    this.logger.debug(`Refreshed profile for ${profile.airtableUserId} (${profile.email})`);
                }
                catch (error) {
                    const errorMsg = `Failed to refresh profile ${profile.airtableUserId}: ${error.message}`;
                    errors.push(errorMsg);
                    this.logger.error(errorMsg);
                }
            }
            this.logger.log(`Profile sync completed: ${updated} updated, ${errors.length} errors`);
            return { updated, errors };
        }
        catch (error) {
            this.logger.error(`Profile sync failed: ${error.message}`);
            return { updated: 0, errors: [`Sync operation failed: ${error.message}`] };
        }
    }
    async findUserByClerkUserId(clerkUserId) {
        this.logger.warn(`Legacy findUserByClerkUserId called with ${clerkUserId} - returning null (using N8N authentication)`);
        return null;
    }
    async findOrCreateUserByClerkPayload(clerkPayload) {
        this.logger.warn('Legacy findOrCreateUserByClerkPayload called - returning null (using N8N authentication)');
        return null;
    }
    async findUserByAirtableId(airtableUserId) {
        return await this.getUserProfile(airtableUserId);
    }
    async findOrCreateUserByN8NPayload(payload) {
        try {
            const userProfile = await this.getUserProfile(payload.id, true);
            if (!userProfile) {
                this.logger.error(`Unable to find or create user profile for ${payload.id} (${payload.email})`);
                return null;
            }
            await this.findUserByAirtableUserId(payload.id);
            this.logger.log(`User profile synchronized for ${payload.id} (${payload.email}) with role: ${userProfile.role}`);
            return userProfile;
        }
        catch (error) {
            this.logger.error(`Error in findOrCreateUserByN8NPayload for ${payload.id}: ${error.message}`);
            return null;
        }
    }
    async findUserByAirtableUserId(airtableUserId) {
        try {
            let user = await this.prisma.user.findUnique({
                where: { airtableUserId },
            });
            const userProfile = await this.getUserProfile(airtableUserId, true);
            if (!userProfile) {
                throw new Error(`User profile not found in Airtable for ${airtableUserId}`);
            }
            if (!user) {
                try {
                    user = await this.prisma.user.create({
                        data: {
                            airtableUserId: airtableUserId,
                            email: userProfile.email,
                            firstName: userProfile.firstName || 'Unknown',
                            lastName: userProfile.lastName || 'User',
                            role: this.normalizeRole(userProfile.role),
                        },
                    });
                    this.logger.log(`Created User record for airtableUserId: ${airtableUserId} with role: ${userProfile.role}`);
                }
                catch (createError) {
                    this.logger.error(`Failed to create User record for ${airtableUserId}: ${createError.message}`);
                    throw new Error(`Failed to create user record: ${createError.message}`);
                }
            }
            else {
                const normalizedRole = this.normalizeRole(userProfile.role);
                if (user.role !== normalizedRole || user.email !== userProfile.email) {
                    try {
                        user = await this.prisma.user.update({
                            where: { airtableUserId },
                            data: {
                                role: normalizedRole,
                                email: userProfile.email,
                                firstName: userProfile.firstName || user.firstName,
                                lastName: userProfile.lastName || user.lastName,
                                updatedAt: new Date(),
                            },
                        });
                        this.logger.log(`Synced User role from Airtable for ${airtableUserId}: ${user.role} (was ${user.role}, now ${normalizedRole})`);
                    }
                    catch (updateError) {
                        this.logger.error(`Failed to sync User role for ${airtableUserId}: ${updateError.message}`);
                    }
                }
            }
            return {
                ...user,
                airtableUserId: userProfile.airtableUserId,
                email: userProfile.email,
                firstName: userProfile.firstName,
                lastName: userProfile.lastName,
                companyName: userProfile.companyName,
                mcNumber: userProfile.mcNumber,
                dotNumber: userProfile.dotNumber,
                role: userProfile.role,
                verificationStatus: userProfile.verificationStatus,
            };
        }
        catch (error) {
            this.logger.error(`Error finding user by airtableUserId ${airtableUserId}: ${error.message}`);
            throw error;
        }
    }
    async updateUserProfile(airtableUserId, updateData) {
        try {
            this.logger.debug(`Starting updateUserProfile for ${airtableUserId} with data:`, updateData);
            let user = await this.prisma.user.findUnique({
                where: { airtableUserId },
            });
            this.logger.debug(`Found existing user:`, user ? 'YES' : 'NO');
            if (!user) {
                this.logger.debug(`Creating new user record for ${airtableUserId}`);
                user = await this.prisma.user.create({
                    data: {
                        airtableUserId: airtableUserId,
                        email: updateData.email || `${airtableUserId}@placeholder.com`,
                        firstName: updateData.firstName || 'Unknown',
                        lastName: updateData.lastName || 'User',
                        role: db_1.Role.CARRIER,
                    },
                });
                this.logger.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
            }
            this.logger.debug(`Updating user record for ${airtableUserId}`);
            const updatedUser = await this.prisma.user.update({
                where: { airtableUserId },
                data: {
                    ...updateData,
                    updatedAt: new Date(),
                },
            });
            this.logger.debug(`User update successful:`, updatedUser);
            this.logger.log(`Updated user profile for ${airtableUserId}`);
            return updatedUser;
        }
        catch (error) {
            this.logger.error(`Error updating user profile for ${airtableUserId}: ${error.message}`, error.stack);
            this.logger.error(`Prisma error details:`, error);
            throw error;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map