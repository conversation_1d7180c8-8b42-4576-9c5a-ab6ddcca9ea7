{
  "name": "Admin Response Workflow",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "GET",
        "path": "admin/respond",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "9e5f6a7b-8c9d-0e1f-2a3b-4c5d6e7f8a9b",
      "name": "Webhook - Admin Response",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "webhookId": "admin-respond"
    },
    {
      "parameters": {
        "functionCode": "const jwt = require('jsonwebtoken');\nconst crypto = require('crypto');\nconst query = $json.query;\nconst action = query.action; // accept, counter, decline\nconst bidId = query.bidId;\nconst token = query.token;\n\n// Decode and validate response token\nlet tokenData;\ntry {\n  tokenData = JSON.parse(Buffer.from(token, 'base64').toString());\n} catch (error) {\n  throw new Error('Invalid response token format');\n}\n\n// Basic token validation\nif (tokenData.bidId !== bidId) {\n  throw new Error('Token bid ID mismatch');\n}\n\nif (tokenData.action !== action) {\n  throw new Error('Token action mismatch');\n}\n\n// Check if token is not too old (24 hours)\nconst tokenAge = Date.now() - tokenData.timestamp;\nif (tokenAge > 24 * 60 * 60 * 1000) {\n  throw new Error('Response token expired');\n}\n\n// Validate HMAC signature if present\nif (tokenData.signature) {\n  const secret = process.env.N8N_JWT_SECRET || 'fallback-secret';\n  const { signature, ...dataToVerify } = tokenData;\n  \n  const expectedSignature = crypto.createHmac('sha256', secret)\n    .update(JSON.stringify(dataToVerify))\n    .digest('hex');\n  \n  if (signature !== expectedSignature) {\n    throw new Error('Invalid token signature - possible tampering detected');\n  }\n}\n\n// Validate admin action flag\nif (!tokenData.adminAction) {\n  throw new Error('Invalid admin action token');\n}\n\n// Generate admin JWT for internal operations\nconst adminPayload = {\n  id: 'admin_system',\n  role: 'admin',\n  action: action,\n  bidId: bidId,\n  carrierMcNumber: tokenData.carrierMcNumber,\n  timestamp: Date.now(),\n  originalTokenTimestamp: tokenData.timestamp\n};\n\nconst adminJWT = jwt.sign(adminPayload, process.env.N8N_JWT_SECRET, { expiresIn: '1h' });\n\nreturn {\n  action,\n  bidId,\n  carrierMcNumber: tokenData.carrierMcNumber,\n  responseTime: new Date().toISOString(),\n  validToken: true,\n  secureToken: !!tokenData.signature,\n  adminJWT,\n  adminPayload\n};"
      },
      "id": "0f6a7b8c-9d0e-1f2a-3b4c-5d6e7f8a9b0c",
      "name": "Authenticate Admin Response",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "operation": "list",
        "application": "airtable",
        "base": "YOUR_AIRTABLE_BASE_ID",
        "table": "Bids",
        "filterByFormula": "={BidId} = '{{ $json.bidId }}'"
      },
      "id": "1a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d",
      "name": "Get Bid Details",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2,
      "position": [
        680,
        300
      ],
      "credentials": {
        "airtableApi": {
          "id": "AIRTABLE_CREDENTIAL_ID",
          "name": "Airtable API"
        }
      }
    },
    {
      "parameters": {
        "mode": "expression",
        "output": "input",
        "rules": {
          "rules": [
            {
              "operation": "equal",
              "value2": "accept",
              "output": 0
            },
            {
              "operation": "equal",
              "value2": "decline",
              "output": 1
            },
            {
              "operation": "equal",
              "value2": "counter",
              "output": 2
            }
          ]
        },
        "expression": "={{ $json.action }}"
      },
      "id": "2b8c9d0e-1f2a-3b4c-5d6e-7f8a9b0c1d2e",
      "name": "Switch - Action Type",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [
        900,
        300
      ]
    },
    {
      "parameters": {
        "operation": "update",
        "application": "airtable",
        "base": "YOUR_AIRTABLE_BASE_ID",
        "table": "Bids",
        "id": "={{ $node[\"Get Bid Details\"].json.records[0].id }}",
        "updateAllFields": false,
        "fields": {
          "Status": "accepted",
          "AdminResponse": "accepted",
          "RespondedAt": "={{ $node[\"Authenticate Admin Response\"].json.responseTime }}",
          "AdminJWT": "={{ $node[\"Authenticate Admin Response\"].json.adminJWT }}"
        }
      },
      "id": "3c9d0e1f-2a3b-4c5d-6e7f-8a9b0c1d2e3f",
      "name": "Update Bid - Accept",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2,
      "position": [
        1120,
        200
      ],
      "credentials": {
        "airtableApi": {
          "id": "AIRTABLE_CREDENTIAL_ID",
          "name": "Airtable API"
        }
      }
    },
    {
      "parameters": {
        "operation": "update",
        "application": "airtable",
        "base": "YOUR_AIRTABLE_BASE_ID",
        "table": "Bids",
        "id": "={{ $node[\"Get Bid Details\"].json.records[0].id }}",
        "updateAllFields": false,
        "fields": {
          "Status": "declined",
          "AdminResponse": "declined",
          "RespondedAt": "={{ $node[\"Authenticate Admin Response\"].json.responseTime }}",
          "AdminJWT": "={{ $node[\"Authenticate Admin Response\"].json.adminJWT }}"
        }
      },
      "id": "4d0e1f2a-3b4c-5d6e-7f8a-9b0c1d2e3f4g",
      "name": "Update Bid - Decline",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2,
      "position": [
        1120,
        300
      ],
      "credentials": {
        "airtableApi": {
          "id": "AIRTABLE_CREDENTIAL_ID",
          "name": "Airtable API"
        }
      }
    },
    {
      "parameters": {
        "respondWith": "html",
        "responseBody": "=<div style=\"font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 30px; border: 1px solid #e2e8f0; border-radius: 8px;\">\n  <h2>Counter Offer for Bid {{ $json.bidId }}</h2>\n  <p>Original Bid: ${{ $node[\"Get Bid Details\"].json.records[0].fields['Bid Amount'] || 'N/A' }}</p>\n  <p>Carrier: {{ $node[\"Get Bid Details\"].json.records[0].fields['Carrier MC Number'] || 'Unknown' }}</p>\n  \n  <form method=\"POST\" action=\"https://firstcutproduce.app.n8n.cloud/webhook/admin/counter-submit\">\n    <input type=\"hidden\" name=\"bidId\" value=\"{{ $json.bidId }}\">\n    <input type=\"hidden\" name=\"originalAmount\" value=\"{{ $node[\"Get Bid Details\"].json.records[0].fields['Bid Amount'] || '0' }}\">\n    <input type=\"hidden\" name=\"carrierEmail\" value=\"{{ $node[\"Get Bid Details\"].json.records[0].fields['Carrier Email'] || '' }}\">\n    <input type=\"hidden\" name=\"carrierCompanyName\" value=\"{{ $node[\"Get Bid Details\"].json.records[0].fields['Carrier Company Name'] || '' }}\">\n    <div style=\"margin: 20px 0;\">\n      <label><strong>Counter Offer Amount:</strong></label><br>\n      <input type=\"number\" name=\"counterAmount\" step=\"0.01\" required style=\"width: 100%; padding: 10px; margin-top: 5px; border: 1px solid #d1d5db; border-radius: 4px;\" placeholder=\"Enter counter amount\">\n    </div>\n    <div style=\"margin: 20px 0;\">\n      <label><strong>Notes (optional):</strong></label><br>\n      <textarea name=\"notes\" rows=\"3\" style=\"width: 100%; padding: 10px; margin-top: 5px; border: 1px solid #d1d5db; border-radius: 4px;\" placeholder=\"Add any notes for the carrier...\"></textarea>\n    </div>\n    <button type=\"submit\" style=\"background: #d97706; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;\">\n      Submit Counter Offer\n    </button>\n  </form>\n</div>"
      },
      "id": "5e1f2a3b-4c5d-6e7f-8a9b-0c1d2e3f4g5h",
      "name": "Counter Offer Form",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 2,
      "position": [
        1120,
        400
      ]
    },
    {
      "parameters": {
        "fromEmail": "<EMAIL>",
        "toEmail": "={{ $node[\"Get Bid Details\"].json.records[0].fields.CarrierEmail || '<EMAIL>' }}",
        "subject": "✅ Bid Accepted - Load Assignment Details",
        "emailType": "html",
        "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #059669; color: white; padding: 20px; border-radius: 8px 8px 0 0;\">\n    <h1 style=\"margin: 0; font-size: 24px;\">✅ Bid Accepted!</h1>\n    <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">Congratulations! Your bid has been accepted.</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0;\">\n    <div style=\"background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h2 style=\"color: #1f2937; margin-top: 0;\">Load Assignment Details</h2>\n      <div style=\"margin: 15px 0;\">\n        <strong>Bid ID:</strong> {{ $json.bidId }}<br>\n        <strong>Accepted Amount:</strong> <span style=\"color: #059669; font-size: 18px; font-weight: bold;\">${{ $node[\"Get Bid Details\"].json.records[0].fields.BidAmount }}</span><br>\n        <strong>Company:</strong> {{ $node[\"Get Bid Details\"].json.records[0].fields.CarrierCompanyName }}<br>\n      </div>\n    </div>\n\n    <div style=\"background: #dcfdf7; padding: 15px; border-radius: 8px; border-left: 4px solid #059669;\">\n      <strong>Next Steps:</strong>\n      <ul style=\"margin: 10px 0;\">\n        <li>You will receive detailed load information shortly</li>\n        <li>Please confirm your availability and ETA</li>\n        <li>Contact dispatch for any questions</li>\n      </ul>\n    </div>\n  </div>\n</div>",
        "options": {}
      },
      "id": "6f2a3b4c-5d6e-7f8a-9b0c-1d2e3f4g5h6i",
      "name": "Email - Bid Accepted",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        1340,
        200
      ],
      "credentials": {
        "smtp": {
          "id": "EMAIL_CREDENTIAL_ID",
          "name": "SMTP Email"
        }
      }
    },
    {
      "parameters": {
        "fromEmail": "<EMAIL>",
        "toEmail": "="{{ $node[\"Get Bid Details\"].json.records[0].fields.CarrierEmail || '<EMAIL>' }}",
        "subject": "❌ Bid Update - Not Selected This Time",
        "emailType": "html",
        "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #dc2626; color: white; padding: 20px; border-radius: 8px 8px 0 0;\">\n    <h1 style=\"margin: 0; font-size: 24px;\">❌ Bid Update</h1>\n    <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">Thank you for your interest in this load.</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0;\">\n    <div style=\"background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h2 style=\"color: #1f2937; margin-top: 0;\">Bid Status Update</h2>\n      <p>Unfortunately, your bid for <strong>Bid ID: {{ $json.bidId }}</strong> was not selected for this load.</p>\n      <p>Your bid amount was: <strong>${{ $node[\"Get Bid Details\"].json.records[0].fields.BidAmount }}</strong></p>\n    </div>\n\n    <div style=\"background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;\">\n      <strong>Keep Looking:</strong>\n      <p style=\"margin: 10px 0;\">We have many loads available daily. Please continue checking the loadboard for new opportunities that match your equipment and routes.</p>\n    </div>\n  </div>\n</div>",
        "options": {}
      },
      "id": "7a3b4c5d-6e7f-8a9b-0c1d-2e3f4g5h6i7j",
      "name": "Email - Bid Declined",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ],
      "credentials": {
        "smtp": {
          "id": "EMAIL_CREDENTIAL_ID",
          "name": "SMTP Email"
        }
      }
    }
  ],
  "connections": {
    "Webhook - Admin Response": {
      "main": [
        [
          {
            "node": "Authenticate Admin Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Authenticate Admin Response": {
      "main": [
        [
          {
            "node": "Get Bid Details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Bid Details": {
      "main": [
        [
          {
            "node": "Switch - Action Type",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Switch - Action Type": {
      "main": [
        [
          {
            "node": "Update Bid - Accept",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Update Bid - Decline",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Counter Offer Form",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Bid - Accept": {
      "main": [
        [
          {
            "node": "Email - Bid Accepted",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Bid - Decline": {
      "main": [
        [
          {
            "node": "Email - Bid Declined",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {},
  "versionId": "1",
  "id": "admin-response-workflow",
  "meta": {
    "instanceId": "n8n-instance"
  }
} 