'use client';

import React from 'react';
import { X, Truck, AlertCircle, CheckCircle, DollarSign, Bell } from 'lucide-react';
// Define NotificationData interface locally since socket.ts was removed
interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  data?: any;
}
import { cn } from '../../lib/utils';

interface NotificationToastProps {
  notification: NotificationData;
  onClose: () => void;
  onMarkAsRead: () => void;
  isRead: boolean;
}

export const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onClose,
  onMarkAsRead,
  isRead,
}) => {
  const getIcon = () => {
    switch (notification.type) {
      case 'load_new':
        return <Truck className="h-5 w-5" />;
      case 'load_status_change':
        return <AlertCircle className="h-5 w-5" />;
      case 'load_assigned':
        return <CheckCircle className="h-5 w-5" />;
      case 'bid_update':
      case 'bid_new':
        return <DollarSign className="h-5 w-5" />;
      case 'booking_confirmation':
      case 'booking_request':
        return <CheckCircle className="h-5 w-5" />;
      case 'system_announcement':
        return <Bell className="h-5 w-5" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getColorScheme = () => {
    switch (notification.type) {
      case 'load_new':
        return {
          bg: 'bg-blue-50 border-blue-200',
          icon: 'text-blue-600',
          title: 'text-blue-900',
          message: 'text-blue-700',
        };
      case 'load_status_change':
        return {
          bg: 'bg-yellow-50 border-yellow-200',
          icon: 'text-yellow-600',
          title: 'text-yellow-900',
          message: 'text-yellow-700',
        };
      case 'load_assigned':
        return {
          bg: 'bg-green-50 border-green-200',
          icon: 'text-green-600',
          title: 'text-green-900',
          message: 'text-green-700',
        };
      case 'bid_update':
      case 'bid_new':
        return {
          bg: 'bg-purple-50 border-purple-200',
          icon: 'text-purple-600',
          title: 'text-purple-900',
          message: 'text-purple-700',
        };
      case 'booking_confirmation':
      case 'booking_request':
        return {
          bg: 'bg-emerald-50 border-emerald-200',
          icon: 'text-emerald-600',
          title: 'text-emerald-900',
          message: 'text-emerald-700',
        };
      case 'system_announcement':
        return {
          bg: 'bg-red-50 border-red-200',
          icon: 'text-red-600',
          title: 'text-red-900',
          message: 'text-red-700',
        };
      default:
        return {
          bg: 'bg-gray-50 border-gray-200',
          icon: 'text-gray-600',
          title: 'text-gray-900',
          message: 'text-gray-700',
        };
    }
  };

  const colorScheme = getColorScheme();
  const timeAgo = React.useMemo(() => {
    const now = new Date();
    const notificationTime = new Date(notification.timestamp);
    const diffInSeconds = Math.floor((now.getTime() - notificationTime.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    }
  }, [notification.timestamp]);

  const handleClick = () => {
    if (!isRead) {
      onMarkAsRead();
    }
  };

  return (
    <div
      className={cn(
        'relative w-full max-w-md p-4 border rounded-lg shadow-lg transition-all duration-300 ease-in-out cursor-pointer hover:shadow-xl',
        colorScheme.bg,
        isRead ? 'opacity-75' : 'opacity-100'
      )}
      onClick={handleClick}
    >
      {/* Unread indicator */}
      {!isRead && (
        <div className="absolute top-2 right-2 w-2 h-2 bg-blue-500 rounded-full" />
      )}

      {/* Close button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onClose();
        }}
        className="absolute top-2 right-6 p-1 rounded-full hover:bg-gray-200 transition-colors"
        aria-label="Close notification"
      >
        <X className="h-4 w-4 text-gray-500" />
      </button>

      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className={cn('flex-shrink-0 mt-0.5', colorScheme.icon)}>
          {getIcon()}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={cn('text-sm font-semibold truncate', colorScheme.title)}>
              {notification.title}
            </h4>
            <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
              {timeAgo}
            </span>
          </div>
          
          <p className={cn('text-sm mt-1', colorScheme.message)}>
            {notification.message}
          </p>

          {/* Additional data display */}
          {notification.data && notification.type === 'load_new' && (
            <div className="mt-2 text-xs space-y-1">
              {notification.data.rate && (
                <div className="flex justify-between">
                  <span>Rate:</span>
                  <span className="font-medium">${notification.data.rate}</span>
                </div>
              )}
              {notification.data.equipmentRequired && (
                <div className="flex justify-between">
                  <span>Equipment:</span>
                  <span className="font-medium">{notification.data.equipmentRequired}</span>
                </div>
              )}
            </div>
          )}

          {notification.data && notification.type === 'bid_update' && (
            <div className="mt-2 text-xs">
              <div className="flex justify-between">
                <span>Bid Amount:</span>
                <span className="font-medium">${notification.data.bidAmount}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 