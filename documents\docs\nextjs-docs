TITLE: Linking to Dynamic Segments (App Router)
DESCRIPTION: This snippet demonstrates how to create links to dynamic routes, such as individual blog posts, in the App Router. It shows mapping over an array of post data and using template literals to construct the `href` for each Next.js Link component based on a dynamic segment like the post slug.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/02-components/link.mdx#_snippet_9

LANGUAGE: TypeScript
CODE:
```
import Link from 'next/link'

interface Post {
  id: number
  title: string
  slug: string
}

export default function PostList({ posts }: { posts: Post[] }) {
  return (
    <ul>
      {posts.map((post) => (
        <li key={post.id}>
          <Link href={`/blog/${post.slug}`}>{post.title}</Link>
        </li>
      ))}
    </ul>
  )
}
```

LANGUAGE: JavaScript
CODE:
```
import Link from 'next/link'

export default function PostList({ posts }) {
  return (
    <ul>
      {posts.map((post) => (
        <li key={post.id}>
          <Link href={`/blog/${post.slug}`}>{post.title}</Link>
        </li>
      ))}
    </ul>
  )
}
```

----------------------------------------

TITLE: Server-Side Form Validation with Zod (TypeScript)
DESCRIPTION: Shows a Next.js Server Action (`'use server'`) that performs server-side validation of form data using the Zod library. It defines a Zod schema, parses the incoming `FormData`, and returns validation errors if the data is invalid. Requires the Zod library to be installed.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_9

LANGUAGE: TypeScript
CODE:
```
'use server'

import { z } from 'zod'

const schema = z.object({
  email: z.string({
    invalid_type_error: 'Invalid Email',
  }),
})

export default async function createUser(formData: FormData) {
  const validatedFields = schema.safeParse({
    email: formData.get('email'),
  })

  // Return early if the form data is invalid
  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
    }
  }

  // Mutate data
}
```

----------------------------------------

TITLE: Submitting Form Data to API Route - JavaScript
DESCRIPTION: This code snippet demonstrates how to submit form data to an API route in Next.js using JavaScript. It prevents the default form submission, creates a FormData object from the form, sends a POST request to the API route, and handles the response.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/02-pages/03-building-your-application/03-data-fetching/03-forms-and-mutations.mdx#_snippet_3

LANGUAGE: javascript
CODE:
```
export default function Page() {
  async function onSubmit(event) {
    event.preventDefault()

    const formData = new FormData(event.target)
    const response = await fetch('/api/submit', {
      method: 'POST',
      body: formData,
    })

    // Handle response if necessary
    const data = await response.json()
    // ...
  }

  return (
    <form onSubmit={onSubmit}>
      <input type="text" name="name" />
      <button type="submit">Submit</button>
    </form>
  )
}
```

----------------------------------------

TITLE: Updating Next.js, React, and React DOM
DESCRIPTION: This command updates Next.js, React, and React DOM to the latest versions using npm. It's a prerequisite for migrating to the App Router.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/migrating/app-router-migration.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install next@latest react@latest react-dom@latest
```

----------------------------------------

TITLE: Creating Database Session in Next.js App Router
DESCRIPTION: This asynchronous function creates a new database session for a given user ID in a Next.js App Router context. It calculates the session's expiration date, inserts a new record into a 'sessions' database table, retrieves the generated session ID, encrypts it, and stores the encrypted session data as a cookie in the user's browser for future optimistic authentication checks.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_19

LANGUAGE: ts
CODE:
```
import cookies from 'next/headers'
import { db } from '@/app/lib/db'
import { encrypt } from '@/app/lib/session'

export async function createSession(id: number) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

  // 1. Create a session in the database
  const data = await db
    .insert(sessions)
    .values({
      userId: id,
      expiresAt,
    })
    // Return the session ID
    .returning({ id: sessions.id })

  const sessionId = data[0].id

  // 2. Encrypt the session ID
  const session = await encrypt({ sessionId, expiresAt })

  // 3. Store the session in cookies for optimistic auth checks
  const cookieStore = await cookies()
  cookieStore.set('session', session, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  })
}
```

LANGUAGE: js
CODE:
```
import cookies from 'next/headers'
import { db } from '@/app/lib/db'
import { encrypt } from '@/app/lib/session'

export async function createSession(id) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

  // 1. Create a session in the database
  const data = await db
    .insert(sessions)
    .values({
      userId: id,
      expiresAt,
    })
    // Return the session ID
    .returning({ id: sessions.id })

  const sessionId = data[0].id

  // 2. Encrypt the session ID
  const session = await encrypt({ sessionId, expiresAt })

  // 3. Store the session in cookies for optimistic auth checks
  const cookieStore = await cookies()
  cookieStore.set('session', session, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  })
}
```

----------------------------------------

TITLE: Installing Next.js 15 Using Codemod
DESCRIPTION: Command to automatically upgrade to the latest Next.js version using the upgrade codemod.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/upgrading/version-15.mdx#2025-04-21_snippet_0

LANGUAGE: bash
CODE:
```
npx @next/codemod@canary upgrade latest
```

----------------------------------------

TITLE: Handling Cookies in Next.js Route Handlers
DESCRIPTION: Example of reading and setting cookies in a Route Handler using the 'cookies' function from next/headers. This demonstrates how to access and manipulate cookies in both TypeScript and JavaScript.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/01-routing/13-route-handlers.mdx#2025-04-21_snippet_2

LANGUAGE: typescript
CODE:
```
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  const cookieStore = await cookies()
  const token = cookieStore.get('token')

  return new Response('Hello, Next.js!', {
    status: 200,
    headers: { 'Set-Cookie': `token=${token.value}` },
  })
}
```

LANGUAGE: javascript
CODE:
```
import { cookies } from 'next/headers'

export async function GET(request) {
  const cookieStore = await cookies()
  const token = cookieStore.get('token')

  return new Response('Hello, Next.js!', {
    status: 200,
    headers: { 'Set-Cookie': `token=${token}` },
  })
}
```

----------------------------------------

TITLE: Creating Route Segment Loading UI (TSX/JSX)
DESCRIPTION: Define an instant loading state for a route segment by creating a `loading.js` or `loading.tsx` file. The component exported here provides fallback UI that is automatically wrapped in a Suspense boundary by Next.js and displayed while the actual page or layout content loads.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/01-routing/06-loading-ui-and-streaming.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
export default function Loading() {
  // You can add any UI inside Loading, including a Skeleton.
  return <LoadingSkeleton />
}
```

LANGUAGE: jsx
CODE:
```
export default function Loading() {
  // You can add any UI inside Loading, including a Skeleton.
  return <LoadingSkeleton />
}
```

----------------------------------------

TITLE: Checking Active Link Using usePathname (App Router)
DESCRIPTION: This snippet shows how to identify the active link in a navigation menu within the App Router. It uses the `usePathname` hook to get the current path and compares it to the `href` of each Next.js Link component, conditionally applying a CSS class like 'active' to highlight the current page.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/02-components/link.mdx#_snippet_10

LANGUAGE: TypeScript
CODE:
```
'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'

export function Links() {
  const pathname = usePathname()

  return (
    <nav>
      <Link className={`link ${pathname === '/' ? 'active' : ''}`} href="/">
        Home
      </Link>

      <Link
        className={`link ${pathname === '/about' ? 'active' : ''}`}
        href="/about"
      >
        About
      </Link>
    </nav>
  )
}
```

LANGUAGE: JavaScript
CODE:
```
'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'

export function Links() {
  const pathname = usePathname()

  return (
    <nav>
      <Link className={`link ${pathname === '/' ? 'active' : ''}`} href="/">
        Home
      </Link>

      <Link
        className={`link ${pathname === '/about' ? 'active' : ''}`}
        href="/about"
      >
        About
      </Link>
    </nav>
  )
}
```

----------------------------------------

TITLE: Creating Secure Session Cookie in Next.js App Router (JavaScript)
DESCRIPTION: This asynchronous function `createSession` encrypts a user ID and expiration time (7 days) using a separate `encrypt` function, then sets the resulting session string as an HTTP-only, secure cookie named 'session' using Next.js `cookies()` API. Recommended cookie options like `sameSite` and `path` are included.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_8

LANGUAGE: js
CODE:
```
import 'server-only';
import { cookies } from 'next/headers';

export async function createSession(userId) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  const session = await encrypt({ userId, expiresAt });
  const cookieStore = await cookies();

  cookieStore.set('session', session, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  });
}
```

----------------------------------------

TITLE: Using Server Action Passed as Prop in Client Component (TSX/JSX)
DESCRIPTION: Demonstrates how a Server Action defined elsewhere can be passed as a prop to a Client Component and then used as the value for a `<form>` element's `action` attribute. This allows logic defined on the server to handle form submissions from the client.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
'use client'

export default function ClientComponent({
  updateItemAction,
}: {
  updateItemAction: (formData: FormData) => void
}) {
  return <form action={updateItemAction}>{/* ... */}</form>
}
```

LANGUAGE: jsx
CODE:
```
'use client'

export default function ClientComponent({ updateItemAction }) {
  return <form action={updateItemAction}>{/* ... */}</form>
}
```

----------------------------------------

TITLE: Wildcard Path Matching in Next.js Redirects
DESCRIPTION: This example illustrates how to match wildcard paths using the `*` character in the `source` route. The snippet captures any nested paths and redirects them to a specified `destination`, retaining the matched segments.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/05-config/01-next-config-js/redirects.mdx#2025-04-21_snippet_2

LANGUAGE: JavaScript
CODE:
```
module.exports = {
  async redirects() {
    return [
      {
        source: '/blog/:slug*',
        destination: '/news/:slug*',
        permanent: true,
      },
    ]
  },
}
```

----------------------------------------

TITLE: Remove <a> Tags From Link Components
DESCRIPTION: Remove `<a>` tags inside Link Components, or add a `legacyBehavior` prop to Links that cannot be auto-fixed.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/upgrading/codemods.mdx#_snippet_25

LANGUAGE: Terminal
CODE:
```
npx @next/codemod@latest new-link .
```

----------------------------------------

TITLE: Fetch User in Layout (Next.js) - TSX
DESCRIPTION: This Next.js Layout component demonstrates calling `getUser()` from the DAL to fetch user data. Since `getUser` internally includes the session verification, this pattern ensures the authentication check is performed when the layout fetches data, rather than performing the auth check directly in the layout.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_30

LANGUAGE: TSX
CODE:
```
import { getUser } from "@/app/lib/dal";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getUser();

  return (
    // ...
    <main>{children}</main>
    // ...
  );
}
```

----------------------------------------

TITLE: Redirecting Request with Next.js Middleware JavaScript
DESCRIPTION: This snippet demonstrates a basic Next.js Middleware function written in JavaScript that redirects any incoming request matching the configured `matcher` path to the `/home` URL using `NextResponse.redirect`. It requires importing `NextResponse` from `next/server`.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/01-routing/14-middleware.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
import { NextResponse } from 'next/server'

// This function can be marked `async` if using `await` inside
export function middleware(request) {
  return NextResponse.redirect(new URL('/home', request.url))
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: '/about/:path*',
}
```

----------------------------------------

TITLE: Importing Global Styles in app/layout.js
DESCRIPTION: This code snippet demonstrates how to import global styles in the `app/layout.js` file. This allows you to apply global styles to your entire application.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/migrating/app-router-migration.mdx#_snippet_43

LANGUAGE: jsx
CODE:
```
import '../styles/globals.css'

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
```

----------------------------------------

TITLE: Passing Additional Arguments to Server Action via Bind (TSX/JSX)
DESCRIPTION: Shows how to use JavaScript's `bind()` method to pass additional, fixed arguments to a Server Action function before binding it to a form's `action` prop. This allows passing contextual data, like an item ID, alongside the form data during submission.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
'use client'

import { updateUser } from './actions'

export function UserProfile({ userId }: { userId: string }) {
  const updateUserWithId = updateUser.bind(null, userId)

  return (
    <form action={updateUserWithId}>
      <input type="text" name="name" />
      <button type="submit">Update User Name</button>
    </form>
  )
}
```

LANGUAGE: jsx
CODE:
```
'use client'

import { updateUser } from './actions'

export function UserProfile({ userId }) {
  const updateUserWithId = updateUser.bind(null, userId)

  return (
    <form action={updateUserWithId}>
      <input type="text" name="name" />
      <button type="submit">Update User Name</button>
    </form>
  )
}
```

----------------------------------------

TITLE: Updating Internal Navigation to Use Next.js Link Component - JavaScript
DESCRIPTION: This snippet demonstrates how to replace an `<a>` element with the `Link` component from Next.js to enable client-side navigation within a Next.js application, thereby preventing full-page refreshes. The updated component ensures seamless transitions between pages.
SOURCE: https://github.com/vercel/next.js/blob/canary/errors/no-html-link-for-pages.mdx#2025-04-21_snippet_0

LANGUAGE: javascript
CODE:
```
function Home() {
  return (
    <div>
      <Link href="/about">About Us</Link>
    </div>
  )
}

export default Home
```

----------------------------------------

TITLE: Creating API Route for Form Submission - TypeScript
DESCRIPTION: This code snippet demonstrates creating an API route in Next.js using TypeScript to handle form submissions. It retrieves data from the request body, calls a function (createItem) to process the data, and sends a response with the created item's ID.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/02-pages/03-building-your-application/03-data-fetching/03-forms-and-mutations.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const data = req.body
  const id = await createItem(data)
  res.status(200).json({ id })
}
```

----------------------------------------

TITLE: Revalidating A Specific URL
DESCRIPTION: Demonstrates how to use `revalidatePath` to invalidate the cache for a single, specific URL path. This ensures that the next visit to '/blog/post-1' will fetch fresh data instead of using the cache.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/04-functions/revalidatePath.mdx#_snippet_1

LANGUAGE: typescript
CODE:
```
import { revalidatePath } from 'next/cache'
revalidatePath('/blog/post-1')
```

----------------------------------------

TITLE: Implementing Redirects in Next.js Route Handlers
DESCRIPTION: Example of implementing redirects in a Route Handler using the 'redirect' function from next/navigation. This demonstrates how to perform redirects in both TypeScript and JavaScript.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/01-routing/13-route-handlers.mdx#2025-04-21_snippet_4

LANGUAGE: typescript
CODE:
```
import { redirect } from 'next/navigation'

export async function GET(request: Request) {
  redirect('https://nextjs.org/')
}
```

LANGUAGE: javascript
CODE:
```
import { redirect } from 'next/navigation'

export async function GET(request) {
  redirect('https://nextjs.org/')
}
```

----------------------------------------

TITLE: Implementing Global Error Handling in Next.js with TypeScript
DESCRIPTION: TypeScript implementation of a global error boundary component for handling application-wide errors in Next.js. Must include html and body tags as it replaces the root layout when active.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/03-file-conventions/error.mdx#2025-04-21_snippet_2

LANGUAGE: typescript
CODE:
```
'use client' // Error boundaries must be Client Components

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    // global-error must include html and body tags
    <html>
      <body>
        <h2>Something went wrong!</h2>
        <button onClick={() => reset()}>Try again</button>
      </body>
    </html>
  )
}
```

----------------------------------------

TITLE: Handling Form Data with Server Action (TSX/JSX)
DESCRIPTION: Explains that Server Actions invoked via a `<form>`'s `action` attribute automatically receive the `FormData` object as their first argument. The example shows how to extract values for individual form fields from this object using `formData.get()`.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
export default function Page() {
  async function createInvoice(formData: FormData) {
    'use server'

    const rawFormData = {
      customerId: formData.get('customerId'),
      amount: formData.get('amount'),
      status: formData.get('status'),
    }

    // mutate data
    // revalidate cache
  }

  return <form action={createInvoice}>...</form>
}
```

LANGUAGE: jsx
CODE:
```
export default function Page() {
  async function createInvoice(formData) {
    'use server'

    const rawFormData = {
      customerId: formData.get('customerId'),
      amount: formData.get('amount'),
      status: formData.get('status'),
    }

    // mutate data
    // revalidate cache
  }

  return <form action={createInvoice}>...</form>
}
```

----------------------------------------

TITLE: Revalidating Cache by Path in Server Action
DESCRIPTION: Demonstrates using the `revalidatePath` function within a Server Action (`createPost`) to clear the Next.js Data Cache for a specific path (`/posts`) after an operation (e.g., creating a post). This ensures that subsequent requests to that path fetch fresh data.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_24

LANGUAGE: ts
CODE:
```
'use server'

import { revalidatePath } from 'next/cache'

export async function createPost() {
  try {
    // ...
  } catch (error) {
    // ...
  }

  revalidatePath('/posts')
}
```

LANGUAGE: js
CODE:
```
'use server'

import { revalidatePath } from 'next/cache'

export async function createPost() {
  try {
    // ...
  } catch (error) {
    // ...
  }

  revalidatePath('/posts')
}
```

----------------------------------------

TITLE: Implementing Parallel Data Fetching with Promise.all in Next.js
DESCRIPTION: Demonstrates the parallel data fetching pattern where multiple independent data requests are initiated concurrently within the same route segment or component. By calling the async fetching functions (`getArtist`, `getAlbums`) and then awaiting their promises together using `Promise.all`, both requests run in parallel, potentially reducing the overall data loading time compared to sequential fetching. The component awaits the resolution of all promises before rendering.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/01-fetching.mdx#_snippet_8

LANGUAGE: typescript
CODE:
```
import Albums from './albums';

async function getArtist(username: string) {
  const res = await fetch(`https://api.example.com/artist/${username}`);
  return res.json();
}

async function getAlbums(username: string) {
  const res = await fetch(`https://api.example.com/artist/${username}/albums`);
  return res.json();
}

export default async function Page({
  params,
}: {
  params: Promise<{ username: string }>;
}) {
  const { username } = await params;
  const artistData = getArtist(username);
  const albumsData = getAlbums(username);

  // Initiate both requests in parallel
  const [artist, albums] = await Promise.all([artistData, albumsData]);

  return (
    <>
      <h1>{artist.name}</h1>
      <Albums list={albums} />
    </>
  );
}
```

LANGUAGE: javascript
CODE:
```
import Albums from './albums';

async function getArtist(username) {
  const res = await fetch(`https://api.example.com/artist/${username}`);
  return res.json();
}

async function getAlbums(username) {
  const res = await fetch(`https://api.example.com/artist/${username}/albums`);
  return res.json();
}

export default async function Page({ params }) {
  const { username } = await params;
  const artistData = getArtist(username);
  const albumsData = getAlbums(username);

  // Initiate both requests in parallel
  const [artist, albums] = await Promise.all([artistData, albumsData]);

  return (
    <>
      <h1>{artist.name}</h1>
      <Albums list={albums} />
    </>
  );
}
```

----------------------------------------

TITLE: Checking if a Cookie Exists - JS
DESCRIPTION: This snippet illustrates how to use the `has` method to verify the existence of a specific cookie in a Next.js page component. It retrieves the cookie store asynchronously for this check.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/04-functions/cookies.mdx#2025-04-21_snippet_7

LANGUAGE: js
CODE:
```
import { cookies } from 'next/headers'

export default async function Page() {
  const cookieStore = await cookies()
  const hasCookie = cookieStore.has('theme')
  return '...'
}
```

----------------------------------------

TITLE: Implementing Authorization Check in App Router GET Handler (JS)
DESCRIPTION: Shows how to secure an App Router `GET` Route Handler in JavaScript. The code performs a two-step security check: first verifying user authentication via a session, and then checking for the 'admin' role to ensure proper authorization, returning appropriate HTTP status codes (401/403) for restricted access.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_37

LANGUAGE: js
CODE:
```
import { verifySession } from '@/app/lib/dal'\n\nexport async function GET() {\n  // User authentication and role verification\n  const session = await verifySession()\n\n  // Check if the user is authenticated\n  if (!session) {\n    // User is not authenticated\n    return new Response(null, { status: 401 })\n  }\n\n  // Check if the user has the 'admin' role\n  if (session.user.role !== 'admin') {\n    // User is authenticated but does not have the right permissions\n    return new Response(null, { status: 403 })\n  }\n\n  // Continue for authorized users\n}
```

----------------------------------------

TITLE: Receiving Bound Arguments in Server Action (TS/JS)
DESCRIPTION: Demonstrates the function signature for a Server Action that receives additional arguments passed via `bind()`. The bound arguments (`userId` in this example) are received first, followed by the `FormData` object that is automatically provided when the action is invoked by a form.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_6

LANGUAGE: ts
CODE:
```
'use server'

export async function updateUser(userId: string, formData: FormData) {}
```

LANGUAGE: js
CODE:
```
'use server'

export async function updateUser(userId, formData) {}
```

----------------------------------------

TITLE: Using revalidatePath in a Server Action
DESCRIPTION: Demonstrates how to call `revalidatePath` within a Next.js Server Action. After performing a data submission (simulated by `submitForm()`), `revalidatePath('/')` is called to invalidate the cache for the root path, ensuring the UI reflects the changes on subsequent visits.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/04-functions/revalidatePath.mdx#_snippet_5

LANGUAGE: typescript
CODE:
```
'use server'

import { revalidatePath } from 'next/cache'

export default async function submit() {
  await submitForm()
  revalidatePath('/')
}
```

----------------------------------------

TITLE: Authenticated Data Fetching with Route Handlers (TypeScript)
DESCRIPTION: This TypeScript snippet governs data fetching capabilities through route handlers while verifying access authentication. If a session is invalid, the unauthorized function is invoked and a 401 page is rendered, safeguarding endpoint access.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/04-functions/unauthorized.mdx#2025-04-21_snippet_8

LANGUAGE: TypeScript
CODE:
```
import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/app/lib/dal'
import { unauthorized } from 'next/navigation'

export async function GET(req: NextRequest): Promise<NextResponse> {
  // Verify the user's session
  const session = await verifySession()

  // If no session exists, return a 401 and render unauthorized.tsx
  if (!session) {
    unauthorized()
  }

  // Fetch data
  // ...
}
```

----------------------------------------

TITLE: Set Priority with Local Import (App Router JSX)
DESCRIPTION: Demonstrates setting the `priority` prop for a local image imported directly into an App Router page. Marking the image with `priority` helps Next.js optimize preloading for improved LCP performance.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/06-optimizing/01-images.mdx#_snippet_7

LANGUAGE: jsx
CODE:
```
import Image from 'next/image'
import profilePic from '../public/me.png'

export default function Page() {
  return <Image src={profilePic} alt="Picture of the author" priority />
}
```

----------------------------------------

TITLE: Implementing Responsive Image using next/image (JSX)
DESCRIPTION: This snippet demonstrates using the `next/image` component to create a responsive image. It imports a local image, automatically setting dimensions, and applies `sizes="100vw"` along with inline styles (`width: '100%', height: 'auto'`) to ensure the image scales correctly with the viewport width.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/06-optimizing/01-images.mdx#_snippet_8

LANGUAGE: JSX
CODE:
```
import Image from 'next/image'
import mountains from '../public/mountains.jpg'

export default function Responsive() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <Image
        alt="Mountains"
        // Importing an image will
        // automatically set the width and height
        src={mountains}
        sizes="100vw"
        // Make the image display full width
        style={{
          width: '100%',
          height: 'auto',
        }}
      />
    </div>
  )
}
```

----------------------------------------

TITLE: Nesting Submit Button Using useFormStatus (JavaScript)
DESCRIPTION: Demonstrates how to integrate a `SubmitButton` component (which uses `useFormStatus` internally) within a form element that uses a Server Action. By nesting the button, it automatically gains access to the form's submission status to manage its disabled state.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_18

LANGUAGE: JavaScript
CODE:
```
import { SubmitButton } from './button'
import { createUser } from '@/app/actions'

export function Signup() {
  return (
    <form action={createUser}>
      {/* Other form elements */}
      <SubmitButton />
    </form>
  )
}
```

----------------------------------------

TITLE: Defining Font Variable in Next.js (TSX)
DESCRIPTION: This code snippet shows how to define a font variable using Next.js's font optimization features. It imports the Inter font from 'next/font/google' and initializes it with a CSS variable name. This allows the font to be referenced in CSS files.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/02-components/font.mdx#_snippet_46

LANGUAGE: TypeScript
CODE:
```
import { Inter } from 'next/font/google'
import styles from '../styles/component.module.css'

const inter = Inter({
  variable: '--font-inter',
})
```

----------------------------------------

TITLE: Fetching Data in Server Component with fetch API (Next.js)
DESCRIPTION: This snippet demonstrates how to fetch data in a Next.js Server Component using the fetch API. It retrieves blog posts from an API and renders them as a list.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/01-getting-started/06-fetching-data.mdx#2025-04-21_snippet_0

LANGUAGE: tsx
CODE:
```
export default async function Page() {
  const data = await fetch('https://api.vercel.app/blog')
  const posts = await data.json()
  return (
    <ul>
      {posts.map((post) => (
        <li key={post.id}>{post.title}</li>
      ))}
    </ul>
  )
}
```

LANGUAGE: jsx
CODE:
```
export default async function Page() {
  const data = await fetch('https://api.vercel.app/blog')
  const posts = await data.json()
  return (
    <ul>
      {posts.map((post) => (
        <li key={post.id}>{post.title}</li>
      ))}
    </ul>
  )
}
```

----------------------------------------

TITLE: Implementing Optimistic UI Updates with useOptimistic (TSX)
DESCRIPTION: Illustrates using the `useOptimistic` hook to provide instant UI feedback before a Server Action completes. It creates an optimistic state that immediately reflects the user's intended action, while the background action performs the actual data mutation. The UI renders the optimistic state until the action finishes.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/02-data-fetching/03-server-actions-and-mutations.mdx#_snippet_19

LANGUAGE: TSX
CODE:
```
'use client'

import { useOptimistic } from 'react'
import { send } from './actions'

type Message = {
  message: string
}

export function Thread({ messages }: { messages: Message[] }) {
  const [optimisticMessages, addOptimisticMessage] = useOptimistic<
    Message[],
    string
  >(messages, (state, newMessage) => [...state, { message: newMessage }])

  const formAction = async (formData: FormData) => {
    const message = formData.get('message') as string
    addOptimisticMessage(message)
    await send(message)
  }

  return (
    <div>
      {optimisticMessages.map((m, i) => (
        <div key={i}>{m.message}</div>
      ))}
      <form action={formAction}>
        <input type="text" name="message" />
        <button type="submit">Send</button>
      </form>
    </div>
  )
}
```

----------------------------------------

TITLE: Example Usage of unstable_cache in Next.js JSX Page Component
DESCRIPTION: This snippet provides a full example of using unstable_cache within a Next.js JSX App Router page component. It defines a cached function that returns user data, using the userId from the page parameters as part of the cache key, and includes options for tags and revalidation.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/04-functions/unstable_cache.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import { unstable_cache } from 'next/cache';

export default async function Page({ params } }) {
  const { userId } = await params
  const getCachedUser = unstable_cache(
    async () => {
      return { id: userId };
    },
    [userId], // add the user ID to the cache key
    {
      tags: ["users"],
      revalidate: 60,
    }
  );

  //...
}
```

----------------------------------------

TITLE: Implementing Server-side Rendering with getServerSideProps in Next.js
DESCRIPTION: This code demonstrates how to implement Server-side Rendering in Next.js by using the getServerSideProps function. The function fetches data from an external API on every request and passes it to the Page component as props. This approach is useful for pages that need frequently updated data.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/02-pages/03-building-your-application/02-rendering/01-server-side-rendering.mdx#2025-04-21_snippet_0

LANGUAGE: jsx
CODE:
```
export default function Page({ data }) {
  // Render data...
}

// This gets called on every request
export async function getServerSideProps() {
  // Fetch data from external API
  const res = await fetch(`https://.../data`)
  const data = await res.json()

  // Pass data to the page via props
  return { props: { data } }
}
```

----------------------------------------

TITLE: Generating Dynamic OG Image with ImageResponse in TSX
DESCRIPTION: This code snippet demonstrates how to generate a dynamic Open Graph image using the ImageResponse constructor in a Next.js application. It fetches data for a blog post based on the slug parameter and renders the post title within a styled div. The ImageResponse constructor takes JSX and converts it into a PNG image.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/01-getting-started/10-metadata-and-og-images.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
import { ImageResponse } from 'next/og'
import { getPost } from '@/app/lib/data'

// Image metadata
export const size = {
  width: 1200,
  height: 630,
}

export const contentType = 'image/png'

// Image generation
export default async function Image({ params }: { params: { slug: string } }) {
  const post = await getPost(params.slug)

  return new ImageResponse(
    (
      // ImageResponse JSX element
      <div
        style={{
          fontSize: 128,
          background: 'white',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {post.title}
      </div>
    )
  )
}
```

----------------------------------------

TITLE: Deleting Session Cookie in Next.js App Router (JavaScript)
DESCRIPTION: This asynchronous function `deleteSession` removes the session cookie named 'session' using the `delete` method provided by the Next.js `cookies()` API. This is typically used during a logout process.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_14

LANGUAGE: js
CODE:
```
import 'server-only';
import { cookies } from 'next/headers';

export async function deleteSession() {
  const cookieStore = await cookies();
  cookieStore.delete('session');
}
```

----------------------------------------

TITLE: Define Page Component for Dynamic Segment in Next.js App Router
DESCRIPTION: This snippet shows how to define a page component that utilizes a dynamic route segment (`[slug]`) in the App Router. The component receives the dynamic segment value via the `params` prop, which is a Promise (in Next.js 15+). It demonstrates accessing the parameter using async/await and rendering it.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/03-building-your-application/01-routing/10-dynamic-routes.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params
  return <div>My Post: {slug}</div>
}
```

LANGUAGE: jsx
CODE:
```
export default async function Page({ params }) {
  const { slug } = await params
  return <div>My Post: {slug}</div>
}
```

----------------------------------------

TITLE: Migrate CRA to Next.js
DESCRIPTION: Migrates a Create React App project to Next.js, creating a Pages Router and necessary config to match behavior. Client-side only rendering is leveraged initially to prevent breaking compatibility due to `window` usage during SSR and can be enabled seamlessly to allow the gradual adoption of Next.js specific features.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/upgrading/codemods.mdx#_snippet_32

LANGUAGE: Terminal
CODE:
```
npx @next/codemod cra-to-next
```

----------------------------------------

TITLE: Conditionally Applying Headers with has and missing Next.js JavaScript
DESCRIPTION: This extensive snippet demonstrates using the `has` and `missing` arrays to apply headers only when specific request properties (headers, cookies, host, or queries) match or do not match a defined pattern. It shows examples matching the presence of a header, the absence of a header, matching multiple conditions (query and cookie), matching header values with capture groups, and matching the host.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/05-config/01-next-config-js/headers.mdx#_snippet_6

LANGUAGE: JavaScript
CODE:
```
module.exports = {
  async headers() {
    return [
      // if the header `x-add-header` is present,
      // the `x-another-header` header will be applied
      {
        source: '/:path*',
        has: [
          {
            type: 'header',
            key: 'x-add-header',
          },
        ],
        headers: [
          {
            key: 'x-another-header',
            value: 'hello',
          },
        ],
      },
      // if the header `x-no-header` is not present,
      // the `x-another-header` header will be applied
      {
        source: '/:path*',
        missing: [
          {
            type: 'header',
            key: 'x-no-header',
          },
        ],
        headers: [
          {
            key: 'x-another-header',
            value: 'hello',
          },
        ],
      },
      // if the source, query, and cookie are matched,
      // the `x-authorized` header will be applied
      {
        source: '/specific/:path*',
        has: [
          {
            type: 'query',
            key: 'page',
            // the page value will not be available in the
            // header key/values since value is provided and
            // doesn't use a named capture group e.g. (?<page>home)
            value: 'home',
          },
          {
            type: 'cookie',
            key: 'authorized',
            value: 'true',
          },
        ],
        headers: [
          {
            key: 'x-authorized',
            value: ':authorized',
          },
        ],
      },
      // if the header `x-authorized` is present and
      // contains a matching value, the `x-another-header` will be applied
      {
        source: '/:path*',
        has: [
          {
            type: 'header',
            key: 'x-authorized',
            value: '(?<authorized>yes|true)',
          },
        ],
        headers: [
          {
            key: 'x-another-header',
            value: ':authorized',
          },
        ],
      },
      // if the host is `example.com`,
      // this header will be applied
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'example.com',
          },
        ],
        headers: [
          {
            key: 'x-another-header',
            value: ':authorized',
          },
        ],
      },
    ]
  },
}
```

----------------------------------------

TITLE: Robots object type definition (TypeScript/TSX)
DESCRIPTION: Defines the TypeScript type signature for the `Robots` object expected when dynamically generating `robots.txt`. It details the structure of the `rules` property (single object or array) and includes optional `sitemap` and `host` properties.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/03-file-conventions/01-metadata/robots.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
type Robots = {
  rules:
    | {
        userAgent?: string | string[]
        allow?: string | string[]
        disallow?: string | string[]
        crawlDelay?: number
      }
    | Array<{
        userAgent: string | string[]
        allow?: string | string[]
        disallow?: string | string[]
        crawlDelay?: number
      }>
  sitemap?: string | string[]
  host?: string
}
```

----------------------------------------

TITLE: Generating ImageResponse in Route Handlers (app/api/route.js)
DESCRIPTION: Illustrates how to use `ImageResponse` within a Next.js Route Handler to dynamically generate an image at request time. It creates a simple layout with text elements and styles, then returns a new `ImageResponse` with the JSX element and specified width and height.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/05-api-reference/04-functions/image-response.mdx#_snippet_1

LANGUAGE: js
CODE:
```
import { ImageResponse } from 'next/og'

export async function GET() {
  try {
    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
            padding: '40px',
          }}
        >
          <div
            style={{
              fontSize: 60,
              fontWeight: 'bold',
              color: 'black',
              textAlign: 'center',
            }}
          >
            Welcome to My Site
          </div>
          <div
            style={{
              fontSize: 30,
              color: '#666',
              marginTop: '20px',
            }}
          >
            Generated with Next.js ImageResponse
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    )
  } catch (e) {
    console.log(`${e.message}`)
    return new Response(`Failed to generate the image`, {
      status: 500,
    })
  }
}
```

----------------------------------------

TITLE: Creating Secure Session Cookie in Next.js App Router (TypeScript)
DESCRIPTION: This asynchronous function `createSession` encrypts a user ID and expiration time (7 days) using a separate `encrypt` function, then sets the resulting session string as an HTTP-only, secure cookie named 'session' using Next.js `cookies()` API. Recommended cookie options like `sameSite` and `path` are included.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_7

LANGUAGE: ts
CODE:
```
import 'server-only';
import { cookies } from 'next/headers';

export async function createSession(userId: string) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  const session = await encrypt({ userId, expiresAt });
  const cookieStore = await cookies();

  cookieStore.set('session', session, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  });
}
```

----------------------------------------

TITLE: Encrypting Session Payload with Jose in TypeScript
DESCRIPTION: This snippet defines an asynchronous function `encrypt` to sign and encrypt a given payload using the `jose` library with HS256 algorithm. It sets the protected header, issued at time, and expiration time (7 days) before signing with a key derived from a session secret environment variable. It requires the `server-only` package and a `SessionPayload` type definition.
SOURCE: https://github.com/vercel/next.js/blob/canary/docs/01-app/02-guides/authentication.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
import 'server-only';
import { SignJWT, jwtVerify } from 'jose';
import { SessionPayload } from '@/app/lib/definitions';

const secretKey = process.env.SESSION_SECRET;
const encodedKey = new TextEncoder().encode(secretKey);

export async function encrypt(payload: SessionPayload) {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(encodedKey);
}

export async function decrypt(session: string | undefined = '') {
  try {
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ['HS256'],
    });
    return payload;
  } catch (error) {
    console.log('Failed to verify session');
  }
}
```