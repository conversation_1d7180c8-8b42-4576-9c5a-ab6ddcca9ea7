import { Module, forwardRef } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';
import { RedisService } from './redis.service';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { AuthGuard } from './auth.guard';
import { AdminGuard } from './admin.guard';
import { CarrierProfileGuard } from './carrier-profile.guard';
// import { WebhooksController } from './webhooks.controller'; // Removed - deprecated Clerk webhooks
import { AuthController } from './auth.controller';
import { CarrierProfilesModule } from '../carrier-profiles/carrier-profiles.module';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [forwardRef(() => CarrierProfilesModule), PrismaModule],
  controllers: [AuthController], // Removed WebhooksController - deprecated Clerk webhooks
  providers: [
    AuthService,
    JwtStrategy,
    RedisService,
    Reflector,
    AuthGuard,
    AdminGuard,
    CarrierProfileGuard,
  ],
  exports: [AuthService, RedisService, AuthGuard, AdminGuard, CarrierProfileGuard],
})
export class AuthModule {} 