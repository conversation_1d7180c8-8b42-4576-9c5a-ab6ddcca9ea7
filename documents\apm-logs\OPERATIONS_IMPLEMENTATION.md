# 🎯 Operations Page Implementation - Phase 1 Complete

## Overview

The Operations page provides a dedicated interface for **First Cut Produce** organization members and administrators to create freight orders using a lane-based workflow. This implementation significantly improves operational efficiency by providing:

- **Lane Library**: Searchable collection of pickup→delivery routes based on historical data
- **Smart Order Creation**: Auto-populated forms with validation and smart defaults
- **Access Control**: Restricted to First Cut Produce organization and admin users
- **Database Integration**: Orders stored in PostgreSQL with proper validation

## 🏗️ Architecture

### Frontend Components
```
apps/web/src/app/org/[orgId]/operations/
├── page.tsx                    # Main operations page with access control
├── types.ts                    # TypeScript interfaces and enums
└── components/
    ├── LaneLibrary.tsx         # Searchable lane selection component
    ├── LaneDetails.tsx         # Selected lane information display
    └── OrderCreationForm.tsx   # Order creation form with validation
```

### Backend API
```
apps/api/src/operations/
├── operations.controller.ts    # REST endpoints with access control
├── operations.service.ts       # Business logic and data processing
├── operations.module.ts        # NestJS module configuration
└── dto/
    └── create-order.dto.ts     # Request validation and documentation
```

## 🔐 Access Control

**Authorized Users:**
- ✅ First Cut Produce organization members (`orgName === 'First Cut Produce'`)
- ✅ Admin users (`role === 'ADMIN'`)

**Unauthorized Access:**
- ❌ Other organization members
- ❌ Users without organization assignment
- ❌ Unauthenticated users

## 🛣️ Lane Generation

Lanes are dynamically generated from historical load data:

1. **Data Source**: Existing loads in the database
2. **Grouping**: Unique combinations of `originCity`, `originState`, `destinationCity`, `destinationState`
3. **Metrics Calculation**:
   - **Frequency Rank**: 1-10 scale based on usage frequency
   - **Estimated Miles**: State-to-state distance calculation
   - **Estimated Duration**: Based on 500 miles/day average
   - **Last Used**: Most recent load creation date

## 📝 Order Creation Process

### 1. Lane Selection
- Browse searchable lane library
- Filter by favorites, recent usage, or search terms
- View lane details including distance and frequency

### 2. Form Completion
- **Auto-generated PO Number**: `PO-YYYYMMDD-XXX` format
- **Smart Date Calculation**: Delivery date auto-calculated from pickup + transit time
- **Equipment Selection**: Dropdown with temperature fields for reefer loads
- **Validation**: Comprehensive form validation with error messages

### 3. Order Creation
- **Database Storage**: Order saved to `loads` table
- **Organization Targeting**: Automatically targeted to First Cut Produce
- **Metadata Tracking**: Creation source, user, and lane information stored
- **Unique Validation**: PO numbers must be unique across all orders

## 🔌 API Endpoints

### GET `/api/v1/operations/lanes`
Returns available lanes for order creation.

**Response:**
```json
{
  "lanes": [
    {
      "id": "lane_1_CA_TX",
      "originCity": "Los Angeles",
      "originState": "CA", 
      "destinationCity": "Houston",
      "destinationState": "TX",
      "estimatedMiles": 1400,
      "estimatedDuration": "3 days",
      "frequencyRank": 8,
      "lastUsed": "2024-01-27T10:30:00.000Z"
    }
  ],
  "total": 25
}
```

### POST `/api/v1/operations/orders`
Creates a new freight order.

**Request Body:**
```json
{
  "laneId": "lane_1_CA_TX",
  "originCity": "Los Angeles",
  "originState": "CA",
  "destinationCity": "Houston", 
  "destinationState": "TX",
  "estimatedMiles": 1400,
  "poNumber": "***********-001",
  "soNumber": "SO-2024-456",
  "pickupDate": "2024-01-28T00:00:00.000Z",
  "deliveryDate": "2024-01-30T00:00:00.000Z",
  "equipmentRequired": "Dry Van",
  "weightLbs": 45000,
  "rate": 2500.00,
  "temperature": "Fresh (32°F to 40°F)",
  "notes": "Handle with care",
  "priority": "normal"
}
```

**Response:**
```json
{
  "id": "clr123abc456def789",
  "airtableRecordId": "temp_1706356800000_abc123def",
  "message": "Order created successfully",
  "success": true
}
```

## 🎨 UI Features

### Lane Library
- **Search**: Filter by city or state name
- **Favorites**: Show frequently used lanes (rank ≥ 8)
- **Recent**: Show recently used lanes
- **Visual Indicators**: Frequency badges and last used timestamps
- **Responsive Design**: Works on desktop and mobile

### Order Form
- **Auto-generation**: PO numbers and delivery dates
- **Smart Validation**: Real-time form validation with helpful error messages
- **Conditional Fields**: Temperature field appears for reefer equipment
- **Date Constraints**: Pickup dates must be in the future
- **Equipment Types**: Full range of freight equipment options

### Lane Details
- **Route Visualization**: Origin → Destination with distance
- **Usage Statistics**: Frequency ranking and last used date
- **Transit Information**: Estimated duration and tips
- **Visual Design**: Color-coded origin/destination markers

## 🔧 Technical Implementation

### Form Management
- **React Hook Form**: Efficient form state management
- **Zod Validation**: Type-safe schema validation
- **Error Handling**: User-friendly error messages
- **Auto-reset**: Form clears after successful submission

### State Management
- **React Hooks**: useState, useEffect, useCallback for optimal performance
- **Optimistic Updates**: Immediate UI feedback during form submission
- **Error Boundaries**: Graceful error handling and recovery

### Styling
- **shadcn/ui**: Consistent component library
- **Tailwind CSS**: Utility-first styling approach
- **Responsive Design**: Mobile-first responsive layout
- **Accessibility**: ARIA labels and keyboard navigation

## 📊 Database Schema

Orders are stored in the `loads` table with these key fields:

```sql
-- Core order information
proNumber VARCHAR(50) -- PO Number (unique)
originCity VARCHAR(100)
originState VARCHAR(2)
destinationCity VARCHAR(100) 
destinationState VARCHAR(2)
pickupDateUtc TIMESTAMP
deliveryDateUtc TIMESTAMP
equipmentRequired VARCHAR(50)
weightLbs INTEGER
rate DECIMAL
temperature VARCHAR(100)
status LoadStatus DEFAULT 'Available'

-- Organization targeting
targetOrganizations JSON -- ['First Cut Produce']
isPublic BOOLEAN DEFAULT false
isTargeted BOOLEAN DEFAULT true

-- Metadata
rawAirtableData JSON -- Creation tracking and additional data
createdAt TIMESTAMP
updatedAt TIMESTAMP
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Clerk authentication setup
- First Cut Produce organization in Clerk

### Setup Steps

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Database Migration**
   ```bash
   cd apps/api
   npx prisma migrate dev
   ```

3. **Start Development Servers**
   ```bash
   # Terminal 1: API Server
   cd apps/api && npm run start:dev
   
   # Terminal 2: Web Server  
   cd apps/web && npm run dev
   ```

4. **Access Operations Page**
   - Navigate to `/org/[orgId]/operations`
   - Must be logged in as First Cut Produce member or admin

### Testing

1. **Manual Testing**
   - Login as First Cut Produce user
   - Navigate to operations page
   - Select a lane and create an order
   - Verify order appears in database

2. **API Testing**
   - Use the provided `test-operations.js` script
   - Replace JWT token with valid user token
   - Run: `node test-operations.js`

## ✅ Phase 1 Success Criteria

**Functional Requirements:**
- ✅ Access restricted to First Cut Produce and admin users
- ✅ Lane library displays all pickup→delivery combinations  
- ✅ Search/filter lanes by city name
- ✅ Single order creation with all required fields
- ✅ Form validation prevents duplicate PO numbers
- ✅ Orders sync to database (Airtable sync in Phase 2)
- ✅ Error handling with user-friendly messages

**Technical Requirements:**
- ✅ TypeScript interfaces for all data structures
- ✅ shadcn/ui components for consistent styling
- ✅ React Hook Form + Zod for form management
- ✅ Proper error boundaries and loading states
- ✅ Responsive design (desktop-first, mobile-friendly)

**Performance Requirements:**
- ✅ Lane generation completes in <2 seconds
- ✅ Order creation completes in <5 seconds  
- ✅ Search/filter responds in <500ms
- ✅ Optimistic UI updates during form submission

## 🔮 Phase 2 Roadmap

**Planned Enhancements:**
- **Airtable Integration**: Bi-directional sync with Airtable
- **Bulk Operations**: Multiple order creation from single lane
- **Templates**: Saved order templates for common scenarios
- **Advanced Validation**: Real-time PO number uniqueness checking
- **Audit Trail**: Complete order creation history and tracking

## 🐛 Troubleshooting

### Common Issues

**Access Denied Error:**
- Verify user belongs to "First Cut Produce" organization
- Check user role is ADMIN if not in First Cut Produce
- Ensure JWT token is valid and not expired

**No Lanes Found:**
- Verify database has existing load data
- Check database connection and migrations
- Ensure loads have non-null origin/destination data

**Form Validation Errors:**
- PO Number format: Only letters, numbers, hyphens, underscores
- Pickup Date: Must be in the future
- Delivery Date: Must be after pickup date
- Weight: Must be positive and ≤ 80,000 lbs

**API Connection Issues:**
- Verify API server is running on port 3001
- Check CORS configuration for frontend requests
- Ensure environment variables are properly set

## 📞 Support

For technical issues or questions:
1. Check the troubleshooting section above
2. Review API logs for detailed error messages
3. Verify user permissions and organization assignment
4. Test with the provided test script

---

**Implementation Status:** ✅ **Phase 1 Complete**  
**Next Phase:** Airtable Integration & Bulk Operations  
**Priority:** 🔴 **HIGH** - Ready for First Cut Produce team usage 