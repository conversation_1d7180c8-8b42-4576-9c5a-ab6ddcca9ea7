# Functions API Reference

Functions are defined similar to a [Route Handler](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) in Next.js. When using Next.js App Router, you can define a function in a file under `app/api/my-route/route.ts.ts` in your project. Vercel will deploy any file under `app/api/` as a function.

## [Function signature](#function-signature)

Vercel Functions use a Web Handler, which consists of the `request` parameter that is an instance of the web standard [`Request`](https://developer.mozilla.org/en-US/docs/Web/API/Request) API. Next.js [extends](https://nextjs.org/docs/app/api-reference/functions/next-request) the standard `Request` object with additional properties and methods.

To use a Web Handler, you must be using Node.js 18 or later. If you are using an earlier version, you must use the [Node.js signature](/docs/functions/runtimes/node-js).

| Parameter | Description | Next.js | Other Frameworks |
| --- | --- | --- | --- |
| `request` | An instance of the `Request` object | [`NextRequest`](https://nextjs.org/docs/api-reference/next/server#nextrequest) | [`Request`](https://developer.mozilla.org/docs/Web/API/Request) |

Next.js (/app)Next.js (/pages)Other frameworks

```
export function GET(request: Request) {
  return new Response('Hello from Vercel!');
}
```

### [Cancel requests](#cancel-requests)

This feature is only available in the Node.js runtime.

You can cancel requests in Vercel Functions by using the `AbortController` API. This is useful for cleaning up resources or stopping long-running tasks when the client aborts the request — for example, during AI workloads where a user cancels a chat or navigates away from a page.

```
export function GET(request: Request) {{
  const abortController = new AbortController();
 
  request.signal.addEventListener("abort", () => {
    console.log("request aborted");
    abortController.abort();
  });
 
  const response = await fetch("https://my-backend-service.example.com", {
    headers: {
      Authorization: `Bearer ${process.env.AUTH_TOKEN}`,
    },
    signal: abortController.signal,
  });
 
  return new Response(response.body, {
    status: response.status,
    headers: response.headers,
  });
};
```

## [Route segment config](#route-segment-config)

To configure your function when using the App Router in Next.js, you use [segment options](https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config), rather than a `config` object.

```
export const runtime = 'nodejs';
export const maxDuration = 15;
```

The table below shows a highlight of the valid config options. For detailed information on all the config options, see the [Configuring Functions](/docs/functions/configuring-functions) docs.

| Property | Type | Description |
| --- | --- | --- |
| [`runtime`](/docs/functions/configuring-functions/runtime) | `string` | This optional property defines the runtime to use, and if not set the runtime will default to `nodejs`. |
| [`preferredRegion`](/docs/functions/configuring-functions/region) | `string` | This optional property and can be used to specify the [regions](/docs/edge-network/regions#region-list) in which your function should execute. This can only be set when the `runtime` is set to `edge` |
| [`maxDuration`](/docs/functions/configuring-functions/duration) | `int` | This optional property can be used to specify the maximum duration in seconds that your function can run for. This can't be set when the `runtime` is set to `edge` |

## [The `@vercel/functions` package](#the-@vercel/functions-package)

The `@vercel/functions` package provides a set of helper methods and utilities for working with Vercel Functions.

### [Helper methods](#helper-methods)

*   [`waitUntil()`](/docs/functions/functions-api-reference/vercel-functions-package#waituntil): This method allows you to extend the lifetime of a request handler for the duration of a given Promise . It's useful for tasks that can be performed after the response is sent, such as logging or updating a cache.
*   [`getEnv`](/docs/functions/functions-api-reference/vercel-functions-package#getenv): This function retrieves System Environment Variables exposed by Vercel.
*   [`geolocation()`](/docs/functions/functions-api-reference/vercel-functions-package#geolocation): Returns location information for the incoming request, including details like city, country, and coordinates.
*   [`ipAddress()`](/docs/functions/functions-api-reference/vercel-functions-package#ipaddress): Extracts the IP address of the request from the headers.
*   [OIDC (OpenID Connect) Methods](/docs/functions/functions-api-reference/vercel-functions-package#oidc-methods): Methods for working with OIDC, which are imported from `@vercel/functions/oidc`. These include:
    *   `awsCredentialsProvider()`: This function helps in obtaining AWS credentials using Vercel's OIDC token.
    *   `getVercelOidcToken()`: Retrieves the OIDC token from the request context or environment variable.

See the [`@vercel/functions`](/docs/functions/functions-api-reference/vercel-functions-package) documentation for more information.

Last updated on March 4, 2025