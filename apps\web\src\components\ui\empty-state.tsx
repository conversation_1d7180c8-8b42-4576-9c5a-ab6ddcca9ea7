'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Package, 
  Search, 
  Plus, 
  RefreshCw, 
  FileText, 
  Truck, 
  AlertCircle,
  HelpCircle,
  ArrowRight
} from 'lucide-react';

interface EmptyStateProps {
  icon?: React.ComponentType<any>;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary';
    icon?: React.ComponentType<any>;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary';
    icon?: React.ComponentType<any>;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const sizeConfig = {
  sm: {
    container: 'py-8',
    icon: 'h-12 w-12',
    title: 'text-lg',
    description: 'text-sm'
  },
  md: {
    container: 'py-12',
    icon: 'h-16 w-16',
    title: 'text-xl',
    description: 'text-base'
  },
  lg: {
    container: 'py-16',
    icon: 'h-20 w-20',
    title: 'text-2xl',
    description: 'text-lg'
  }
};

export function EmptyState({
  icon: Icon = Package,
  title,
  description,
  action,
  secondaryAction,
  className = '',
  size = 'md'
}: EmptyStateProps) {
  const config = sizeConfig[size];

  return (
    <Card className={`border-dashed border-2 ${className}`}>
      <CardContent className={`text-center ${config.container}`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="rounded-full bg-muted/50 p-4">
            <Icon className={`${config.icon} text-muted-foreground/50`} />
          </div>
          
          <div className="space-y-2">
            <h3 className={`font-semibold ${config.title}`}>{title}</h3>
            <p className={`text-muted-foreground max-w-md ${config.description}`}>
              {description}
            </p>
          </div>
          
          {(action || secondaryAction) && (
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              {action && (
                <Button
                  onClick={action.onClick}
                  variant={action.variant || 'default'}
                  className="flex items-center"
                >
                  {action.icon && <action.icon className="h-4 w-4 mr-2" />}
                  {action.label}
                </Button>
              )}
              
              {secondaryAction && (
                <Button
                  onClick={secondaryAction.onClick}
                  variant={secondaryAction.variant || 'outline'}
                  className="flex items-center"
                >
                  {secondaryAction.icon && <secondaryAction.icon className="h-4 w-4 mr-2" />}
                  {secondaryAction.label}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Predefined empty states for common scenarios
export function NoLoadsEmptyState({ onRefresh, onViewAll }: { 
  onRefresh?: () => void; 
  onViewAll?: () => void; 
}) {
  return (
    <EmptyState
      icon={Truck}
      title="No loads available"
      description="There are currently no loads matching your criteria. Try adjusting your filters or check back later for new opportunities."
      action={onRefresh ? {
        label: 'Refresh Loads',
        onClick: onRefresh,
        icon: RefreshCw
      } : undefined}
      secondaryAction={onViewAll ? {
        label: 'View All Loads',
        onClick: onViewAll,
        variant: 'outline',
        icon: Search
      } : undefined}
    />
  );
}

export function NoBidsEmptyState({ onFindLoads }: { onFindLoads?: () => void }) {
  return (
    <EmptyState
      icon={FileText}
      title="No active bids"
      description="You haven't placed any bids yet. Browse the loadboard to find freight opportunities and start bidding."
      action={onFindLoads ? {
        label: 'Find Loads',
        onClick: onFindLoads,
        icon: Search
      } : undefined}
      size="sm"
    />
  );
}

export function NoAssignedLoadsEmptyState({ onFindLoads }: { onFindLoads?: () => void }) {
  return (
    <EmptyState
      icon={Package}
      title="No assigned loads"
      description="You don't have any loads assigned yet. Start by browsing available loads and placing competitive bids."
      action={onFindLoads ? {
        label: 'Browse Loadboard',
        onClick: onFindLoads,
        icon: ArrowRight
      } : undefined}
      size="sm"
    />
  );
}

export function ProfileIncompleteEmptyState({ onCompleteProfile }: { onCompleteProfile?: () => void }) {
  return (
    <EmptyState
      icon={AlertCircle}
      title="Complete your profile"
      description="Your carrier profile is incomplete. Complete all required fields to start bidding on loads and accessing full portal features."
      action={onCompleteProfile ? {
        label: 'Complete Profile',
        onClick: onCompleteProfile,
        icon: ArrowRight
      } : undefined}
      className="border-orange-200 bg-orange-50/50"
    />
  );
}

export function SearchEmptyState({ searchTerm, onClearSearch }: { 
  searchTerm?: string; 
  onClearSearch?: () => void; 
}) {
  return (
    <EmptyState
      icon={Search}
      title="No results found"
      description={searchTerm 
        ? `No results found for "${searchTerm}". Try different keywords or clear your search.`
        : "No results found. Try adjusting your search criteria."
      }
      action={onClearSearch ? {
        label: 'Clear Search',
        onClick: onClearSearch,
        variant: 'outline'
      } : undefined}
      size="sm"
    />
  );
}

export function ErrorEmptyState({ 
  error, 
  onRetry 
}: { 
  error?: string; 
  onRetry?: () => void; 
}) {
  return (
    <EmptyState
      icon={AlertCircle}
      title="Something went wrong"
      description={error || "An error occurred while loading data. Please try again."}
      action={onRetry ? {
        label: 'Try Again',
        onClick: onRetry,
        icon: RefreshCw
      } : undefined}
      className="border-red-200 bg-red-50/50"
    />
  );
}

export function FirstTimeUserEmptyState({ 
  onTakeTour, 
  onCompleteProfile 
}: { 
  onTakeTour?: () => void; 
  onCompleteProfile?: () => void; 
}) {
  return (
    <EmptyState
      icon={HelpCircle}
      title="Welcome to FCP Carrier Portal!"
      description="Get started by completing your profile and taking a quick tour to learn how to find loads, place bids, and manage your freight."
      action={onCompleteProfile ? {
        label: 'Complete Profile',
        onClick: onCompleteProfile,
        icon: ArrowRight
      } : undefined}
      secondaryAction={onTakeTour ? {
        label: 'Take Tour',
        onClick: onTakeTour,
        variant: 'outline',
        icon: HelpCircle
      } : undefined}
      className="border-blue-200 bg-blue-50/50"
      size="lg"
    />
  );
} 