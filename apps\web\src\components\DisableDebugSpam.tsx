'use client';

import { useEffect } from 'react';

export function DisableDebugSpam() {
  useEffect(() => {
    // SECURITY: Only activate in development or for admin users
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    // Check if user is admin by looking at window location or other indicators
    const isAdminContext = typeof window !== 'undefined' && 
      (window.location.hostname === 'localhost' || 
       window.location.search.includes('debug=admin'));
    
    if (!isDevelopment && !isAdminContext) {
      return; // Don't activate spam filter for regular users
    }
    // Override console.log temporarily to filter out debug spam
    const originalLog = console.log;
    const originalError = console.error;
    
    // List of spam patterns to block
    const spamPatterns = [
      'EMERGENCY DEBUG - CURRENT STATE',
      'CLERK ORGANIZATION STATE DEBUG',
      'User Memberships:',
      'JWT Payload:',
      'Org Claims:',
      'Available Organizations:',
      'END EMERGENCY DEBUG',
      'END CLERK STATE DEBUG'
    ];
    
    // Override console.log to filter spam
    console.log = (...args: unknown[]) => {
      const message = args.join(' ');
      const isSpam = spamPatterns.some(pattern => message.includes(pattern));
      
      if (!isSpam) {
        originalLog(...args);
      }
    };
    
    // Also filter console.error for debug-related errors
    console.error = (...args: unknown[]) => {
      const message = args.join(' ');
      const isDebugSpam = message.includes('EMERGENCY') || message.includes('CLERK STATE') || message.includes('JWT Payload');
      
      if (!isDebugSpam) {
        originalError(...args);
      }
    };
    
    // Log once that we've disabled spam
    originalLog('🛑 DEBUG SPAM FILTER ACTIVATED - Blocking infinite debug logs');
    
    // Cleanup function to restore original console methods
    return () => {
      console.log = originalLog;
      console.error = originalError;
      originalLog('✅ DEBUG SPAM FILTER DISABLED - Console restored');
    };
  }, []);

  // Show indicator only in development or admin context
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isAdminContext = typeof window !== 'undefined' && 
    (window.location.hostname === 'localhost' || 
     window.location.search.includes('debug=admin'));
  
  if (!isDevelopment && !isAdminContext) {
    return null;
  }
  
  return (
    <div className="fixed top-20 right-4 bg-orange-100 border border-orange-400 p-2 rounded text-xs text-orange-800 z-40">
      🛑 Debug Spam Filter Active
    </div>
  );
} 