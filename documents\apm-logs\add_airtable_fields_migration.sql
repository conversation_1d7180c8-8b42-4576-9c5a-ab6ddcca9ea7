-- Manual migration to add additional Airtable fields
-- Run this on the production database when ready

-- Create the InvStatus enum first
CREATE TYPE "InvStatus" AS ENUM ('Not Sent', 'Sent', 'Paid');

-- Add new columns to the loads table
ALTER TABLE loads ADD COLUMN po_number TEXT;
ALTER TABLE loads ADD COLUMN so_number TEXT;
ALTER TABLE loads ADD COLUMN pickup_number TEXT;
ALTER TABLE loads ADD COLUMN delivery_number TEXT;
ALTER TABLE loads ADD COLUMN shipper_address TEXT;
ALTER TABLE loads ADD COLUMN receiver_name TEXT;
ALTER TABLE loads ADD COLUMN receiver_address TEXT;
ALTER TABLE loads ADD COLUMN inv_status "InvStatus";
ALTER TABLE loads ADD COLUMN carrier TEXT;
ALTER TABLE loads ADD COLUMN cases INTEGER;
ALTER TABLE loads ADD COLUMN pallets INTEGER;

-- Add any indexes if needed (optional, but good for performance)
-- CREATE INDEX idx_loads_po_number ON loads(po_number);
-- CREATE INDEX idx_loads_inv_status ON loads(inv_status); 