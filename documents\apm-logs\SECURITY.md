# 🔒 Security Documentation

## Overview
This document outlines the security measures implemented in the Carrier Portal application and provides guidelines for maintaining a secure deployment.

## 🛡️ Security Measures Implemented

### 1. Authentication & Authorization
- **JWT Authentication**: Using Clerk with RS256 algorithm and JWKS validation
- **Role-Based Access Control (RBAC)**: Clear separation between CARRIER, ADMIN roles
- **Guard Protection**: All sensitive endpoints protected with authentication guards
- **Token Validation**: Proper JWT verification with issuer and audience validation

### 2. Input Validation & Sanitization
- **Global Validation Pipes**: All inputs validated using class-validator
- **DTO Validation**: Strongly typed DTOs with comprehensive validation rules
- **SQL Injection Protection**: Using Prisma ORM with parameterized queries
- **File Upload Security**: Secure URL validation with HTTPS-only and file type restrictions

### 3. Security Headers
- **Content Security Policy (CSP)**: Strict CSP to prevent XSS attacks
- **HSTS**: HTTP Strict Transport Security with 1-year max-age
- **X-Content-Type-Options**: nosniff to prevent MIME type sniffing
- **X-Frame-Options**: DENY to prevent clickjacking
- **X-XSS-Protection**: Browser XSS filter enabled
- **Referrer Policy**: Strict origin when cross-origin

### 4. Rate Limiting
- **Global Rate Limiting**: Multi-tier rate limiting (short/medium/long term)
- **Production Limits**: Stricter limits in production environment
- **Per-IP Tracking**: Individual IP-based rate limiting
- **Health Check Exemption**: Health endpoints excluded from rate limiting

### 5. CORS Security
- **Strict Origin Control**: Production requires origin header
- **Allowlist-Based**: Only specific domains allowed
- **Environment-Aware**: Different rules for development/production
- **Credential Support**: Secure credential handling with specific origins

### 6. Network Security
- **HTTPS Enforcement**: All external URLs must use HTTPS
- **Secure Cookie Settings**: Production cookies with secure flags
- **TLS Configuration**: Modern TLS versions only

### 7. Error Handling
- **Information Disclosure Prevention**: Generic error messages in production
- **Detailed Logging**: Comprehensive logging for security monitoring
- **Graceful Degradation**: Proper error boundaries and fallbacks

## 🚨 Security Checklist for Deployment

### Pre-Deployment
- [ ] Environment variables properly configured
- [ ] All secrets stored securely (not in code)
- [ ] Database connections use TLS
- [ ] File upload destinations are secure
- [ ] CORS origins updated for production domains
- [ ] Rate limiting rules appropriate for expected traffic

### Production Environment
- [ ] HTTPS enforced across all endpoints
- [ ] Security headers properly configured
- [ ] Error messages don't expose sensitive information
- [ ] Logging configured for security monitoring
- [ ] Database backups encrypted
- [ ] API keys rotated regularly

### Monitoring & Maintenance
- [ ] Security logs monitored regularly
- [ ] Failed authentication attempts tracked
- [ ] Rate limiting violations monitored
- [ ] Dependency vulnerabilities scanned regularly
- [ ] Security updates applied promptly

## 🔧 Configuration

### Environment Variables
Ensure these environment variables are properly configured:

```bash
# Required for production
NODE_ENV=production
CLERK_SECRET_KEY=sk_live_...  # Use live keys in production
CLERK_JWT_ISSUER=https://your-clerk-domain.clerk.accounts.dev
DATABASE_URL=postgresql://...  # Use connection pooling
NEXT_PUBLIC_API_URL=https://api.your-domain.com

# Security headers
CSP_REPORT_URI=https://your-domain.com/csp-report  # Optional
```

### Rate Limiting Configuration
Current production limits:
- **Short term**: 20 requests per minute
- **Medium term**: 100 requests per 10 minutes  
- **Long term**: 500 requests per hour

Adjust these in `apps/api/src/app.module.ts` based on your traffic patterns.

### File Upload Security
- Only HTTPS URLs accepted
- File types restricted to: pdf, jpg, jpeg, png, doc, docx
- All file URLs validated before storage
- Consider implementing virus scanning for uploaded files

## 🔍 Security Monitoring

### Key Metrics to Monitor
1. **Failed Authentication Attempts**: Unusual patterns may indicate attacks
2. **Rate Limit Violations**: High rates may indicate abuse
3. **CORS Violations**: Unexpected origins may indicate attacks
4. **File Upload Patterns**: Monitor for suspicious file uploads
5. **Error Rates**: Spikes may indicate attacks or vulnerabilities

### Log Analysis
Security-relevant logs are marked with specific prefixes:
- `[MAIN.TS CORS DEBUG]`: CORS-related security events
- `[ClerkGuard]`: Authentication-related events
- `SERVICE: uploadLoadDocument`: File upload security events

## 🚀 Security Best Practices

### Development
1. Never commit secrets to version control
2. Use environment-specific configurations
3. Test security headers in development
4. Validate all inputs thoroughly
5. Keep dependencies updated

### Production
1. Use HTTPS everywhere
2. Implement proper secret management
3. Monitor security logs actively
4. Perform regular security audits
5. Have an incident response plan

### Code Reviews
Focus on these security aspects:
- Input validation completeness
- Authentication/authorization bypass potential
- Error message information disclosure
- Resource access control
- Dependency security

## 🔗 Additional Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [NestJS Security](https://docs.nestjs.com/security/authentication)
- [Next.js Security](https://nextjs.org/docs/app/building-your-application/routing/security)
- [Clerk Security](https://clerk.com/docs/security)

## 📧 Security Contact

For security-related issues or questions, contact the development team immediately.

---

**Last Updated**: January 2025
**Security Review**: Required before each major release 