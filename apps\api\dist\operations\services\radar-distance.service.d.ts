import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
export interface DistanceResult {
    distanceMiles: number;
    durationHours: number;
    success: boolean;
    source: 'radar' | 'fallback' | 'cache';
}
export interface Coordinates {
    lat: number;
    lng: number;
}
export interface AddressDistanceRequest {
    originAddress: string;
    destinationAddress: string;
}
export declare class RadarDistanceService {
    private configService;
    private prisma;
    private readonly logger;
    private readonly radarApiKey;
    private readonly BASE_URL;
    private readonly geocodingRateLimiter;
    private readonly routingRateLimiter;
    constructor(configService: ConfigService, prisma: PrismaService);
    calculateDistanceByAddress(originAddress: string, destinationAddress: string): Promise<DistanceResult>;
    calculateDistancesBatchByAddress(requests: AddressDistanceRequest[]): Promise<Array<DistanceResult & {
        route: string;
    }>>;
    private getCachedDistance;
    private cacheDistance;
    private normalizeAddress;
    private calculateFallbackDistanceFromAddresses;
    private extractStateFromAddress;
    private calculateAddressSimilarity;
    private levenshteinDistance;
    private geocodeAddress;
    private calculateRoute;
    private calculateFallbackDistance;
    calculateDistance(originCity: string, originState: string, destinationCity: string, destinationState: string): Promise<DistanceResult>;
    calculateDistancesBatch(requests: Array<{
        originCity: string;
        originState: string;
        destinationCity: string;
        destinationState: string;
    }>): Promise<Array<DistanceResult & {
        route: string;
    }>>;
}
