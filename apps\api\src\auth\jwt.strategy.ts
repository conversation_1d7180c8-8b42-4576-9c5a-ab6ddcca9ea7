import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from './auth.service';
import { N8NJwtPayload } from './authenticated-request.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(private authService: AuthService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKeyProvider: async (request, rawJwtToken, done) => {
        try {
          // N8N JWT validation is handled by the AuthService
          // This strategy is used for passport integration
          const secret = process.env.N8N_JWT_SECRET || 'fallback-secret';
          done(null, secret);
        } catch (error) {
          done(error, undefined);
        }
      },
      issuer: process.env.N8N_JWT_ISSUER || 'n8n-auth',
      algorithms: ['HS256'], // <PERSON><PERSON><PERSON> typically uses HMAC
    });
  }

  /**
   * This method is called by passport-jwt after token validation.
   * @param payload The decoded N8N JWT payload.
   * @returns The user record (from local DB) to be attached to req.user.
   */
  async validate(payload: N8NJwtPayload): Promise<any> {
    try {
      // Use AuthService to find or create the user in the local database
      const userProfile = await this.authService.findOrCreateUserByN8NPayload(payload);
      if (!userProfile) {
        throw new UnauthorizedException('User could not be processed or found after token validation.');
      }
      
      // Return user data in the expected format
      return {
        id: userProfile.airtableUserId,
        email: userProfile.email,
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        role: userProfile.role,
        mcNumber: userProfile.mcNumber,
        companyName: userProfile.companyName,
      };
    } catch (error) {
        console.error("Error during JWT strategy user processing:", error);
        throw new UnauthorizedException('Error processing user from token.');
    }
  }
} 