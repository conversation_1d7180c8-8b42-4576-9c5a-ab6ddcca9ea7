import { PrismaService } from './prisma/prisma.service';
export declare class AppService {
    private prisma;
    constructor(prisma: PrismaService);
    getHello(): string;
    getUserWithOrganization(clerkUserId: string): Promise<null>;
    findUserByAirtableId(airtableUserId: string): Promise<{
        id: string;
        airtableUserId: string | null;
        email: string | null;
        firstName: string | null;
        lastName: string | null;
        role: import("@repo/db").$Enums.Role;
        mcNumber: string | null;
        createdAt: Date;
        updatedAt: Date;
    } | {
        airtableUserId: any;
        email: any;
        firstName: any;
        lastName: any;
        companyName: any;
        mcNumber: any;
        role: any;
    } | null>;
}
