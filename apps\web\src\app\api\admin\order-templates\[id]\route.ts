import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const templateId = params.id;

    // Authentication check - simplified and more robust (same as order creation)
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!token) {
      return NextResponse.json({ error: 'No authentication token provided' }, { status: 401 });
    }

    // Validate JWT token format
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 401 });
    }

    // Decode JWT payload directly (since we know the structure)
    let payload;
    try {
      payload = JSON.parse(atob(tokenParts[1]));
    } catch {
      return NextResponse.json({ error: 'Invalid token payload' }, { status: 401 });
    }

    // Check if user has operations access (First Cut Produce team members or admins)
    const userRole = (payload.role || '').toUpperCase();
    const userEmail = payload.email || '';
    const companyName = payload.companyName || '';
    
    const hasOperationsAccess = userRole === 'ADMIN' ||
                              companyName === 'First Cut Produce' ||
                              companyName === 'FIRST CUT PRODUCE' ||
                              userEmail.includes('@firstcutproduce.com');
    
    if (!hasOperationsAccess) {
      return NextResponse.json({ error: 'First Cut Produce team access required' }, { status: 403 });
    }

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // Delete template from database
    await (prisma as any).orderTemplate.delete({
      where: { id: templateId }
    });

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting order template:', error);
    
    // Handle case where template doesn't exist
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to delete order template' },
      { status: 500 }
    );
  }
} 