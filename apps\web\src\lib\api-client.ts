import { withRelatedProject } from '@vercel/related-projects';

const apiHost = withRelatedProject({
  projectName: 'api', // This matches the conceptual name for your API project
  /**
   * Specify a default host that will be used for your API project
   * if VERCEL_RELATED_PROJECTS environment variable is not available
   * (e.g., during local development or if "Related Projects" is not configured yet).
   * You should set this to your local API's URL.
   * Using NEXT_PUBLIC_API_BASE_URL environment variable is a good practice for this.
   */
  defaultHost: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001', // Default to port 3001 if API runs there locally
});

// Add validation for API host
if (!process.env.NEXT_PUBLIC_API_BASE_URL) {
  console.warn('NEXT_PUBLIC_API_BASE_URL is not set. Using default localhost:3001');
}

class ApiError extends Error {
  status: number;
  correlationId?: string;
  details?: any;
  retryable: boolean;

  constructor(
    status: number,
    message: string,
    retryable: boolean,
    correlationId?: string | null,
    details?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.retryable = retryable;
    this.correlationId = correlationId || undefined;
    this.details = details;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  retryCondition: (error: ApiError) => boolean;
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  retryCondition: (error) => error.retryable && error.status >= 500
};

/**
 * Get N8N JWT token from localStorage
 */
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('n8n_auth_token');
}

/**
 * Enhanced API client with retry logic, error classification, and offline detection
 * @param endpoint The API endpoint to call (e.g., 'users', 'loads/123').
 * @param options Optional RequestInit options for the fetch call.
 * @param retryConfig Optional retry configuration.
 * @returns The JSON response from the API.
 */
export async function fetchFromApi<T = unknown>(
  endpoint: string, 
  options?: RequestInit,
  retryConfig: Partial<RetryConfig> = {}
): Promise<T> {
  const config = { ...defaultRetryConfig, ...retryConfig };
  // Add the api/v1 prefix to match the server configuration
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
  const url = `${apiHost}/api/v1/${cleanEndpoint}`;
  
  console.log('Making API request to:', url);

  // Check if user is offline
  if (typeof window !== 'undefined' && !navigator.onLine) {
    throw createApiError(0, 'You appear to be offline. Please check your internet connection.', false);
  }
  
  let lastError: ApiError;
  
  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      // Get authentication token
      const authToken = getAuthToken();
      const authHeaders: Record<string, string> = {};
      
      if (authToken) {
        authHeaders['Authorization'] = `Bearer ${authToken}`;
      }

      const response = await Promise.race([
        fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            ...authHeaders,
            ...options?.headers,
          },
        }),
        // Timeout after 30 seconds
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 30000)
        )
      ]);
      
      // Get correlation ID from response headers
      const correlationId = response.headers.get('X-Correlation-Id');
      
      if (!response.ok) {
        const apiError = await parseErrorResponse(response, correlationId);
        
        // Log error details
        console.error('API Request Failed:', {
          status: response.status,
          statusText: response.statusText,
          url,
          correlationId,
          attempt: attempt + 1,
          maxRetries: config.maxRetries + 1,
          errorData: apiError.details,
        });
        
        // Check if we should retry
        if (attempt < config.maxRetries && config.retryCondition(apiError)) {
          lastError = apiError;
          const delay = Math.min(
            config.baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
            config.maxDelay
          );
          console.log(`Retrying API request in ${delay}ms (attempt ${attempt + 2}/${config.maxRetries + 1})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        throw apiError;
      }
      
      if (response.status === 204) {
        return undefined as T;
      }

      const data = await response.json();
      
      // Log successful request if it was retried
      if (attempt > 0) {
        console.log(`API request succeeded after ${attempt + 1} attempts`);
      }
      
      return data as T;
      
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Handle network errors, timeouts, etc.
      const apiError = classifyNetworkError(error);
      
      if (attempt < config.maxRetries && config.retryCondition(apiError)) {
        lastError = apiError;
        const delay = Math.min(
          config.baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
          config.maxDelay
        );
        console.log(`Retrying API request after network error in ${delay}ms (attempt ${attempt + 2}/${config.maxRetries + 1})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      console.error(`Error fetching from API:`, {
        endpoint,
        url,
        attempt: attempt + 1,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw apiError;
    }
  }
  
  throw lastError!;
}

async function parseErrorResponse(response: Response, correlationId?: string | null): Promise<ApiError> {
  let errorData;
  let message = `Request failed with status ${response.status}`;
  
  try {
    errorData = await response.json();
    message = errorData.message || errorData.error || message;
  } catch {
    // Response is not JSON or empty, use default message
    message = `${response.status} ${response.statusText}`;
  }
  
  return createApiError(
    response.status,
    message,
    isRetryableStatus(response.status),
    correlationId,
    errorData
  );
}

function classifyNetworkError(error: any): ApiError {
  if (error && error.name === 'AbortError') {
    return createApiError(0, 'Request was cancelled', false);
  }
  
  if (error && error.message === 'Request timeout') {
    return createApiError(408, 'Request timed out. Please try again.', true);
  }
  
  if (error && (error.message.includes('fetch') || error.message.includes('NetworkError') || error.message.includes('Failed to fetch'))) {
    return createApiError(0, 'Network error. Please check your connection and try again.', true);
  }
  
  return createApiError(0, 'An unexpected error occurred. Please try again.', true);
}

function createApiError(
  status: number, 
  message: string, 
  retryable: boolean, 
  correlationId?: string | null,
  details?: any
): ApiError {
  return new ApiError(status, message, retryable, correlationId, details);
}

function isRetryableStatus(status: number): boolean {
  return status >= 500 || 
         status === 408 || // Request Timeout
         status === 429;   // Too Many Requests
}

// Convenience functions for common usage patterns
export async function fetchCritical<T = unknown>(
  endpoint: string, 
  options?: RequestInit
): Promise<T> {
  return fetchFromApi<T>(endpoint, options, {
    maxRetries: 5,
    retryCondition: (error) => error.retryable
  });
}

export async function fetchOptional<T = unknown>(
  endpoint: string, 
  options?: RequestInit
): Promise<T | null> {
  try {
    return await fetchFromApi<T>(endpoint, options, {
      maxRetries: 1,
      retryCondition: () => false
    });
  } catch (error) {
    console.warn(`Optional API call failed:`, error);
    return null;
  }
}

// Health check function
export async function checkApiHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    await fetchFromApi('health', { method: 'GET' }, { maxRetries: 0 });
    const responseTime = Date.now() - startTime;
    
    return {
      status: responseTime < 2000 ? 'healthy' : 'degraded',
      responseTime
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Network status utilities
export function isOnline(): boolean {
  return typeof window !== 'undefined' ? navigator.onLine : true;
}

export function onConnectionChange(callback: (online: boolean) => void): () => void {
  if (typeof window === 'undefined') return () => {};
  
  const handleOnline = () => callback(true);
  const handleOffline = () => callback(false);
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}

// API Client object with common HTTP methods
const apiClient = {
  get: <T = unknown>(endpoint: string, options?: Omit<RequestInit, 'method' | 'body'>) => 
    fetchFromApi<T>(endpoint, { ...options, method: 'GET' }),
  
  post: <T = unknown>(endpoint: string, data?: any, options?: Omit<RequestInit, 'method' | 'body'>) => 
    fetchFromApi<T>(endpoint, { 
      ...options, 
      method: 'POST', 
      body: data ? JSON.stringify(data) : undefined 
    }),
  
  patch: <T = unknown>(endpoint: string, data?: any, options?: Omit<RequestInit, 'method' | 'body'>) => 
    fetchFromApi<T>(endpoint, { 
      ...options, 
      method: 'PATCH', 
      body: data ? JSON.stringify(data) : undefined 
    }),
  
  put: <T = unknown>(endpoint: string, data?: any, options?: Omit<RequestInit, 'method' | 'body'>) => 
    fetchFromApi<T>(endpoint, { 
      ...options, 
      method: 'PUT', 
      body: data ? JSON.stringify(data) : undefined 
    }),
  
  delete: <T = unknown>(endpoint: string, options?: Omit<RequestInit, 'method' | 'body'>) => 
    fetchFromApi<T>(endpoint, { ...options, method: 'DELETE' }),
};

export { apiClient };
export default apiClient; 