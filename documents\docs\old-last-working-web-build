[14:35:32.305] Running build in Washington, D.C., USA (East) – iad1
[14:35:32.306] Build machine configuration: 4 cores, 8 GB
[14:35:32.317] Cloning github.com/Apathy117/carrier-portal (Branch: main, Commit: 424d3e1)
[14:35:34.005] Cloning completed: 1.688s
[14:35:38.357] Restored build cache from previous deployment (YXtKA7wpEN5aVMTXNaNZWHVTVVHY)
[14:35:39.513] Running "vercel build"
[14:35:39.983] Vercel CLI 42.1.1
[14:35:40.114] > Detected Turbo. Adjusting default settings...
[14:35:40.239] > Detected ENABLE_EXPERIMENTAL_COREPACK=1 and "pnpm@9.1.0" in package.json
[14:35:40.452] Running "install" command: `pnpm install`...
[14:35:41.120] Scope: all 8 workspace projects
[14:35:42.474] 
[14:35:42.542] Done in 1.9s
[14:35:42.558] Detected Next.js version: 15.3.2
[14:35:42.559] Running "turbo run build"
[14:35:42.693] 
[14:35:42.694] Attention:
[14:35:42.694] Turborepo now collects completely anonymous telemetry regarding usage.
[14:35:42.694] This information is used to shape the Turborepo roadmap and prioritize features.
[14:35:42.694] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[14:35:42.694] https://turborepo.com/docs/telemetry
[14:35:42.694] 
[14:35:42.766] • Packages in scope: web
[14:35:42.766] • Running build in 1 packages
[14:35:42.766] • Remote caching enabled
[14:35:43.654] @repo/db:build: cache hit, replaying logs 87c3b2ca5066424e
[14:35:43.654] @repo/db:build: 
[14:35:43.654] @repo/db:build: > @repo/db@0.0.0 build /vercel/path0/packages/db
[14:35:43.654] @repo/db:build: > pnpm exec prisma generate --schema=../../apps/api/prisma/schema.prisma
[14:35:43.654] @repo/db:build: 
[14:35:43.654] @repo/db:build: Prisma schema loaded from ../../apps/api/prisma/schema.prisma
[14:35:43.654] @repo/db:build: 
[14:35:43.654] @repo/db:build: ✔ Generated Prisma Client (v6.6.0) to ./generated/client in 89ms
[14:35:43.654] @repo/db:build: 
[14:35:43.654] @repo/db:build: Start by importing your Prisma Client (See: https://pris.ly/d/importing-client)
[14:35:43.655] @repo/db:build: 
[14:35:43.655] @repo/db:build: Tip: Need your database queries to be 1000x faster? Accelerate offers you that and more: https://pris.ly/tip-2-accelerate
[14:35:43.655] @repo/db:build: 
[14:35:43.861] web:build: cache hit, replaying logs 505fd0ffe90d3aca
[14:35:43.862] web:build: 
[14:35:43.862] web:build: > web@0.1.0 build /vercel/path0/apps/web
[14:35:43.862] web:build: > next build
[14:35:43.862] web:build: 
[14:35:43.862] web:build:    ▲ Next.js 15.3.2
[14:35:43.862] web:build: 
[14:35:43.862] web:build:    Creating an optimized production build ...
[14:35:43.862] web:build: Next.js Environment Variables during build: {
[14:35:43.862] web:build:   NEXT_PUBLIC_API_BASE_URL: 'https://api.fcp-portal.com',
[14:35:43.862] web:build:   NODE_ENV: 'production',
[14:35:43.862] web:build:   VERCEL_ENV: 'production'
[14:35:43.863] web:build: }
[14:35:43.863] web:build: Next.js Environment Variables during build: {
[14:35:43.863] web:build:   NEXT_PUBLIC_API_BASE_URL: 'https://api.fcp-portal.com',
[14:35:43.863] web:build:   NODE_ENV: 'production',
[14:35:43.863] web:build:   VERCEL_ENV: 'production'
[14:35:43.863] web:build: }
[14:35:43.863] web:build: Next.js Environment Variables during build: {
[14:35:43.863] web:build:   NEXT_PUBLIC_API_BASE_URL: 'https://api.fcp-portal.com',
[14:35:43.863] web:build:   NODE_ENV: 'production',
[14:35:43.863] web:build:   VERCEL_ENV: 'production'
[14:35:43.863] web:build: }
[14:35:43.863] web:build:  ✓ Compiled successfully in 6.0s
[14:35:43.863] web:build:    Linting and checking validity of types ...
[14:35:43.863] web:build: 
[14:35:43.863] web:build: ./lib/api-client.ts
[14:35:43.864] web:build: 26:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[14:35:43.864] web:build: 44:16  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
[14:35:43.864] web:build: 48:18  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
[14:35:43.864] web:build: 
[14:35:43.864] web:build: ./src/app/api/clerk-webhooks/route.ts
[14:35:43.864] web:build: 44:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[14:35:43.864] web:build: 74:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
[14:35:43.864] web:build: 
[14:35:43.864] web:build: ./src/app/org/[orgId]/loadboard/page.tsx
[14:35:43.864] web:build: 76:18  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
[14:35:43.864] web:build: 
[14:35:43.865] web:build: ./src/components/layout/page-layout.tsx
[14:35:43.865] web:build: 30:65  Warning: '_title' is defined but never used.  @typescript-eslint/no-unused-vars
[14:35:43.865] web:build: 30:86  Warning: '_description' is defined but never used.  @typescript-eslint/no-unused-vars
[14:35:43.865] web:build: 
[14:35:43.865] web:build: info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
[14:35:43.865] web:build:    Collecting page data ...
[14:35:43.865] web:build:    Generating static pages (0/8) ...
[14:35:43.865] web:build:    Generating static pages (2/8) 
[14:35:43.865] web:build:    Generating static pages (4/8) 
[14:35:43.865] web:build:    Generating static pages (6/8) 
[14:35:43.865] web:build:  ✓ Generating static pages (8/8)
[14:35:43.865] web:build:    Finalizing page optimization ...
[14:35:43.865] web:build:    Collecting build traces ...
[14:35:43.865] web:build: 
[14:35:43.866] web:build: Route (app)                                 Size  First Load JS
[14:35:43.866] web:build: ┌ ƒ /                                    1.31 kB         127 kB
[14:35:43.866] web:build: ├ ƒ /_not-found                            977 B         102 kB
[14:35:43.866] web:build: ├ ƒ /api/clerk-webhooks                    142 B         101 kB
[14:35:43.866] web:build: ├ ƒ /api/test-auth                         142 B         101 kB
[14:35:43.866] web:build: ├ ƒ /debug-env                             924 B         102 kB
[14:35:43.866] web:build: ├ ƒ /org/[orgId]                         3.89 kB         143 kB
[14:35:43.866] web:build: ├ ƒ /org/[orgId]/billing                   142 B         101 kB
[14:35:43.866] web:build: ├ ƒ /org/[orgId]/dashboard               1.11 kB         132 kB
[14:35:43.866] web:build: ├ ƒ /org/[orgId]/loadboard               69.8 kB         230 kB
[14:35:43.866] web:build: ├ ƒ /org/[orgId]/my-loads                1.56 kB         141 kB
[14:35:43.866] web:build: └ ƒ /org/[orgId]/settings                6.87 kB         167 kB
[14:35:43.866] web:build: + First Load JS shared by all             101 kB
[14:35:43.866] web:build:   ├ chunks/587d2e0b-9eaffeab3a5932b2.js  53.2 kB
[14:35:43.867] web:build:   ├ chunks/806-710fecad2eb094ac.js         46 kB
[14:35:43.867] web:build:   └ other shared chunks (total)          1.99 kB
[14:35:43.867] web:build: 
[14:35:43.867] web:build: 
[14:35:43.867] web:build: ƒ Middleware                             76.3 kB
[14:35:43.867] web:build: 
[14:35:43.867] web:build: ƒ  (Dynamic)  server-rendered on demand
[14:35:43.867] web:build: 
[14:35:43.867] 
[14:35:43.867]   Tasks:    2 successful, 2 total
[14:35:43.867]  Cached:    2 cached, 2 total
[14:35:43.867]    Time:    1.152s >>> FULL TURBO
[14:35:43.867] Summary:    /vercel/path0/.turbo/runs/2xgsF4uLvJcQRqRLXPWfaqzDvax.json
[14:35:43.867] 
[14:35:44.121] Traced Next.js server files in: 72.186ms
[14:35:44.318] Created all serverless functions in: 197.064ms
[14:35:44.372] Collected static files (public/, static/, .next/static): 5.148ms
[14:35:44.457] Build Completed in /vercel/output [4s]
[14:35:44.525] Deploying outputs...
[14:35:53.852] 
[14:35:54.155] Deployment completed
[14:36:28.453] Uploading build cache [292.19 MB]...
[14:36:33.488] Build cache uploaded: 5.035s
[14:36:35.780] Exiting build container