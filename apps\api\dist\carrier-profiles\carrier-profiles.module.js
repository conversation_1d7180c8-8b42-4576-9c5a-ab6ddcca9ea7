"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarrierProfilesModule = void 0;
const common_1 = require("@nestjs/common");
const carrier_profiles_service_1 = require("./carrier-profiles.service");
const carrier_profiles_controller_1 = require("./carrier-profiles.controller");
const prisma_module_1 = require("../prisma/prisma.module");
const auth_module_1 = require("../auth/auth.module");
let CarrierProfilesModule = class CarrierProfilesModule {
};
exports.CarrierProfilesModule = CarrierProfilesModule;
exports.CarrierProfilesModule = CarrierProfilesModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, (0, common_1.forwardRef)(() => auth_module_1.AuthModule)],
        controllers: [carrier_profiles_controller_1.CarrierProfilesController],
        providers: [carrier_profiles_service_1.CarrierProfilesService],
        exports: [carrier_profiles_service_1.CarrierProfilesService],
    })
], CarrierProfilesModule);
//# sourceMappingURL=carrier-profiles.module.js.map