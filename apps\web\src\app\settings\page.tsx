"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { 
  User, 
  Building, 
  Phone,
  Mail,
  Shield,
  Bell,
  Palette,
  Settings as SettingsIcon,
  Save,
  Loader2,
  CheckCircle,
  Key,
  Lock
} from "lucide-react";
import { toast } from "sonner";
import { apiClient } from '@/lib/api-client';

interface UserProfile {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  airtableUserId: string;
  role: string;
}

interface CarrierProfile {
  id?: string;
  userId?: string;
  companyName?: string;
  mcNumber?: string;
  dotNumber?: string;
  phoneNumber?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  equipmentTypes?: string[];
  serviceableRegions?: string[];
  isVerifiedByAdmin?: boolean;
  adminNotes?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface CarrierProfileFormData {
  companyName?: string;
  mcNumber?: string;
  dotNumber?: string;
  phoneNumber?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  equipmentTypes?: string[];
  serviceableRegions?: string[];
}

interface NotificationPreferences {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  bidAlerts: boolean;
  loadUpdates: boolean;
  systemAnnouncements: boolean;
}

export default function SettingsPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const { getToken } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [carrierProfile, setCarrierProfile] = useState<CarrierProfile | null>(null);
  const [userFormData, setUserFormData] = useState<Partial<UserProfile>>({});
  const [carrierFormData, setCarrierFormData] = useState<CarrierProfileFormData>({});
  const [notificationPrefs, setNotificationPrefs] = useState<NotificationPreferences>({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    bidAlerts: true,
    loadUpdates: true,
    systemAnnouncements: true,
  });

  const fetchProfiles = useCallback(async () => {
    if (!isUserLoaded || !user) return;
    
    setIsLoading(true);
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Fetch user profile
      try {
        const userResponse = await apiClient.get<UserProfile>('/auth/profile');
        if (userResponse) {
          setUserProfile(userResponse);
          setUserFormData(userResponse);
        }
      } catch (error) {
        console.warn('User profile not found, using current user data');
        if (user) {
          const userData = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            airtableUserId: user.id,
            role: user.role
          };
          setUserProfile(userData);
          setUserFormData(userData);
        }
      }

      // Fetch carrier profile
      try {
        const carrierResponse = await apiClient.get<CarrierProfile>('/carrier-profiles/me');
        if (carrierResponse) {
          setCarrierProfile(carrierResponse);
          // Only set editable fields in form data to avoid validation errors
          const editableFields = {
            companyName: carrierResponse.companyName,
            mcNumber: carrierResponse.mcNumber,
            dotNumber: carrierResponse.dotNumber,
            phoneNumber: carrierResponse.phoneNumber,
            contact_name: carrierResponse.contact_name,
            contact_email: carrierResponse.contact_email,
            contact_phone: carrierResponse.contact_phone,
            equipmentTypes: carrierResponse.equipmentTypes,
            serviceableRegions: carrierResponse.serviceableRegions,
          };
          setCarrierFormData(editableFields);
        }
      } catch (error) {
        console.warn('Carrier profile not found');
        // Initialize empty carrier profile form
        setCarrierFormData({});
      }

    } catch (error) {
      console.error('Error fetching profiles:', error);
      toast.error('Failed to load profile data');
    } finally {
      setIsLoading(false);
    }
  }, [isUserLoaded, user, getToken]);

  useEffect(() => {
    fetchProfiles();
  }, [fetchProfiles]);

  const handleUserInputChange = (field: keyof UserProfile, value: string) => {
    setUserFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCarrierInputChange = (field: keyof CarrierProfileFormData, value: string | string[]) => {
    setCarrierFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNotificationChange = (field: keyof NotificationPreferences, value: boolean) => {
    setNotificationPrefs(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveUserProfile = async () => {
    setIsSaving(true);
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Only send the editable fields to avoid validation errors
      const editableUserFields = {
        firstName: userFormData.firstName,
        lastName: userFormData.lastName,
        email: userFormData.email,
      };

      // Update user profile via auth endpoint
      const response = await apiClient.patch<UserProfile>('/auth/profile', editableUserFields);
      
      if (response) {
        setUserProfile(response);
        setUserFormData(response);
        toast.success('Personal information updated successfully!');
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
      toast.error('Failed to update personal information');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveCarrierProfile = async () => {
    setIsSaving(true);
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      let response: CarrierProfile;
      if (carrierProfile?.id) {
        // Update existing carrier profile
        response = await apiClient.patch<CarrierProfile>('/carrier-profiles/me', carrierFormData);
      } else {
        // Create new carrier profile
        response = await apiClient.post<CarrierProfile>('/carrier-profiles', carrierFormData);
      }

      if (response) {
        setCarrierProfile(response);
        // Only set editable fields in form data to avoid validation errors
        const editableFields = {
          companyName: response.companyName,
          mcNumber: response.mcNumber,
          dotNumber: response.dotNumber,
          phoneNumber: response.phoneNumber,
          contact_name: response.contact_name,
          contact_email: response.contact_email,
          contact_phone: response.contact_phone,
          equipmentTypes: response.equipmentTypes,
          serviceableRegions: response.serviceableRegions,
        };
        setCarrierFormData(editableFields);
        toast.success('Company information updated successfully!');
      }
    } catch (error) {
      console.error('Error updating carrier profile:', error);
      toast.error('Failed to update company information');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveNotifications = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement notification preferences API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Notification preferences updated successfully!');
    } catch (error) {
      console.error('Error updating notifications:', error);
      toast.error('Failed to update notification preferences');
    } finally {
      setIsSaving(false);
    }
  };

  if (!isUserLoaded || isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading settings...</span>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Manage your account and profile settings
            </p>
          </div>
        </div>

        <Tabs defaultValue="profile" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">Profile & Company</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={userFormData.firstName || ''}
                      onChange={(e) => handleUserInputChange('firstName', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={userFormData.lastName || ''}
                      onChange={(e) => handleUserInputChange('lastName', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={userFormData.email || ''}
                    onChange={(e) => handleUserInputChange('email', e.target.value)}
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Company Information
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        value={carrierFormData.companyName || ''}
                        onChange={(e) => handleCarrierInputChange('companyName', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber">Main Phone Number</Label>
                      <Input
                        id="phoneNumber"
                        value={carrierFormData.phoneNumber || ''}
                        onChange={(e) => handleCarrierInputChange('phoneNumber', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mcNumber">MC Number</Label>
                      <Input
                        id="mcNumber"
                        value={carrierFormData.mcNumber || ''}
                        onChange={(e) => handleCarrierInputChange('mcNumber', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dotNumber">DOT Number</Label>
                      <Input
                        id="dotNumber"
                        value={carrierFormData.dotNumber || ''}
                        onChange={(e) => handleCarrierInputChange('dotNumber', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Contact Information
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contact_name">Contact Name</Label>
                      <Input
                        id="contact_name"
                        value={carrierFormData.contact_name || ''}
                        onChange={(e) => handleCarrierInputChange('contact_name', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contact_phone">Contact Phone</Label>
                      <Input
                        id="contact_phone"
                        value={carrierFormData.contact_phone || ''}
                        onChange={(e) => handleCarrierInputChange('contact_phone', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contact_email">Contact Email</Label>
                      <Input
                        id="contact_email"
                        type="email"
                        value={carrierFormData.contact_email || ''}
                        onChange={(e) => handleCarrierInputChange('contact_email', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="equipmentTypes">Equipment Types</Label>
                  <Textarea
                    id="equipmentTypes"
                    value={Array.isArray(carrierFormData.equipmentTypes) 
                      ? carrierFormData.equipmentTypes.join(', ') 
                      : carrierFormData.equipmentTypes || ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      const equipmentArray = value.split(',').map(item => item.trim()).filter(item => item);
                      handleCarrierInputChange('equipmentTypes', equipmentArray);
                    }}
                    rows={3}
                  />
                  <p className="text-sm text-muted-foreground">
                    Separate multiple equipment types with commas
                  </p>
                </div>

                <div className="flex justify-between pt-4">
                  <div className="flex space-x-2">
                    <Button 
                      onClick={handleSaveUserProfile} 
                      disabled={isSaving}
                      variant="outline"
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Personal Info
                        </>
                      )}
                    </Button>
                    <Button 
                      onClick={handleSaveCarrierProfile} 
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Company Info
                        </>
                      )}
                    </Button>
                  </div>
                  <Button 
                    onClick={async () => {
                      await handleSaveUserProfile();
                      await handleSaveCarrierProfile();
                    }} 
                    disabled={isSaving}
                    className="bg-primary"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving All...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save All Changes
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferences" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Preferences
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications via email
                      </p>
                    </div>
                    <Switch
                      checked={notificationPrefs.emailNotifications}
                      onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>SMS Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications via text message
                      </p>
                    </div>
                    <Switch
                      checked={notificationPrefs.smsNotifications}
                      onCheckedChange={(checked) => handleNotificationChange('smsNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Push Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive browser push notifications
                      </p>
                    </div>
                    <Switch
                      checked={notificationPrefs.pushNotifications}
                      onCheckedChange={(checked) => handleNotificationChange('pushNotifications', checked)}
                    />
                  </div>

                  <Separator />

                  <h3 className="text-lg font-semibold">Notification Types</h3>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Bid Alerts</Label>
                      <p className="text-sm text-muted-foreground">
                        Get notified about new bidding opportunities
                      </p>
                    </div>
                    <Switch
                      checked={notificationPrefs.bidAlerts}
                      onCheckedChange={(checked) => handleNotificationChange('bidAlerts', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Load Updates</Label>
                      <p className="text-sm text-muted-foreground">
                        Get notified about load status changes
                      </p>
                    </div>
                    <Switch
                      checked={notificationPrefs.loadUpdates}
                      onCheckedChange={(checked) => handleNotificationChange('loadUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>System Announcements</Label>
                      <p className="text-sm text-muted-foreground">
                        Get notified about system updates and announcements
                      </p>
                    </div>
                    <Switch
                      checked={notificationPrefs.systemAnnouncements}
                      onCheckedChange={(checked) => handleNotificationChange('systemAnnouncements', checked)}
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleSaveNotifications} disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        <Label className="text-base">Password</Label>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Last changed 30 days ago
                      </p>
                    </div>
                    <Button variant="outline">
                      Change Password
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        <Label className="text-base">Two-Factor Authentication</Label>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Button variant="outline">
                      Enable 2FA
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <Label className="text-base">Active Sessions</Label>
                      <p className="text-sm text-muted-foreground">
                        Manage your active login sessions
                      </p>
                    </div>
                    <Button variant="outline">
                      View Sessions
                    </Button>
                  </div>
                </div>

                <div className="pt-4">
                  <p className="text-sm text-muted-foreground">
                    Security features are currently being implemented. 
                    Contact support if you need immediate assistance with account security.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
} 