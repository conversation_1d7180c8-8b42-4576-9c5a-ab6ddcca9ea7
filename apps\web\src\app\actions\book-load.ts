'use server';

import { revalidatePath } from 'next/cache';
import Airtable from 'airtable'; // Assuming Turborepo makes this available

// Configure Airtable
const airtableApiKey = process.env.AIRTABLE_API_KEY;
const airtableBaseId = 'appXKT0rHSLqOPrSA';
const airtableTableName = 'tbl4OnEHrzUNG2V2X'; // Table Name or ID
const statusFieldName = 'Status';
const assignedStatusValue = 'Assigned';

if (!airtableApiKey) {
  console.error('AIRTABLE_API_KEY is not set in environment variables.');
  // Optionally throw an error or return a more specific error response
}

Airtable.configure({
  // endpointUrl: 'https://api.airtable.com', // Default, usually not needed
  apiKey: airtableApiKey,
});

const base = Airtable.base(airtableBaseId);
const table = base(airtableTableName);

interface BookLoadResult {
  success: boolean;
  error?: string;
}

/**
 * Updates the status of a load record in Airtable to "Assigned".
 * @param recordId The Airtable Record ID of the load to update.
 * @param loadboardPath The path of the loadboard page to revalidate (e.g., /org/yourOrgId/loadboard)
 * @returns {Promise<BookLoadResult>} Object indicating success or failure.
 */
export async function bookLoadAction(recordId: string, loadboardPath: string): Promise<BookLoadResult> {
  if (!airtableApiKey) {
      return { success: false, error: 'Airtable API Key not configured on the server.' };
  }
  if (!recordId) {
      return { success: false, error: 'Record ID is required.' };
  }
   if (!loadboardPath) {
      console.warn('Loadboard path not provided for revalidation.');
      // Decide if this should be an error or just a warning
      // return { success: false, error: 'Loadboard path is required for revalidation.' };
  }

  console.log(`Attempting to book load with Record ID: ${recordId}`);

  try {
    await table.update(recordId, {
      [statusFieldName]: assignedStatusValue,
    });

    console.log(`Successfully updated Record ID ${recordId} to status "${assignedStatusValue}"`);

    // Revalidate the loadboard page path to refresh the data
    if (loadboardPath) {
        revalidatePath(loadboardPath);
        console.log(`Revalidated path: ${loadboardPath}`);
    }


    return { success: true };
  } catch (error: unknown) {
    console.error(`Error updating Airtable record ${recordId}:`, error);
    let errorMessage = 'Failed to update Airtable record.';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return { success: false, error: errorMessage };
  }
} 