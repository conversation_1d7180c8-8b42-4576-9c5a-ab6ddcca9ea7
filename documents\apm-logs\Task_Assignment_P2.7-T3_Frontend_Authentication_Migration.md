# APM Task Assignment: Phase 2.7-T3 Frontend Authentication Migration

## 1. Agent Role & APM Context

**Introduction:** You are activated as an Implementation Agent within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As an Implementation Agent, you will execute the assigned task diligently, make necessary technical decisions within the defined scope, and meticulously log all work to the Memory Bank for project continuity and knowledge transfer.

**Workflow:** You will interact with the Manager Agent via the User interface and contribute to the centralized Memory Bank system that tracks all project progress and decisions.

## 2. Onboarding / Context from Prior Work

**Previous Work Completed:**
- **P2.7-T1: N8N Authentication Workflow Setup** has been completed successfully (Agent_N8N_Specialist)
- **P2.7-T2: Backend Authentication Migration** has been completed successfully (Agent_API_Backend)

**Backend Authentication Infrastructure (COMPLETED):**
- ✅ Complete Clerk removal from backend API
- ✅ N8N JWT authentication system operational with enhanced payload structure
- ✅ UserProfile table created with persistent caching strategy
- ✅ AuthGuard/AdminGuard implemented replacing <PERSON><PERSON><PERSON>/ClerkAdminGuard
- ✅ MC Number-based load targeting system implemented
- ✅ All 15+ API endpoints migrated to N8N authentication
- ✅ Database schema deployed and functional

**Enhanced JWT Structure (Backend Ready):**
```json
{
  "id": "rec1ZWHpLXuKEw",      // Airtable UserManagement record ID
  "email": "<EMAIL>",  // User email
  "role": "Carrier",           // User role
  "mcNumber": "802125",        // MC Number from Company lookup
  "iat": 1234567890,
  "exp": 1234567890
}
```

**Current System State:**
- ✅ **Backend API:** Fully functional N8N JWT authentication system
- ❌ **Frontend Web App:** Still using Clerk components (BROKEN STATE)
- ⚠️ **User Experience:** Authentication pages non-functional due to Clerk removal
- 🎯 **Critical Need:** Frontend migration to restore user access

**Critical Dependencies:**
- N8N authentication workflow operational with 4 endpoints
- Backend API ready to validate N8N JWT tokens
- Frontend auth context partially implemented (`apps/web/src/contexts/auth-context.tsx`)
- N8N login/register forms exist but need full integration

## 3. Task Assignment

**Reference Implementation Plan:** This assignment corresponds to `Phase 2.7, Task P2.7-T3: Frontend Authentication Migration` in the Implementation_Plan.md.

**Objective:** Complete the frontend authentication migration from Clerk to N8N JWT system, implement simplified URL structure, and restore full user authentication functionality while eliminating all Clerk dependencies.

**Detailed Action Steps (Incorporating Plan Guidance):**

### Step 1: Authentication Pages Migration
1. **Replace Clerk Authentication Forms:**
   - Remove Clerk forms from `apps/web/src/app/sign-in/[[...sign-in]]/page.tsx`
   - Remove Clerk forms from `apps/web/src/app/sign-up/[[...sign-up]]/page.tsx`
   - Implement N8N authentication forms using existing components:
     - `apps/web/src/components/auth/N8NLoginForm.tsx`
     - `apps/web/src/components/auth/N8NRegisterForm.tsx`

2. **Update Authentication Flow:**
   - Ensure login form posts to N8N authentication endpoint
   - Implement JWT token storage and management
   - Handle authentication responses and error states
   - Update form validation and user feedback
   - Test redirect flows after successful authentication

3. **Integrate with Existing Auth Context:**
   - Ensure forms work with `apps/web/src/contexts/auth-context.tsx`
   - Verify `useAuth()`, `useUser()`, `getToken()` methods function correctly
   - Test authentication state management across app

### Step 2: Middleware and Route Protection Migration
1. **Update Authentication Middleware:**
   - Modify `apps/web/src/middleware.ts` to remove Clerk dependencies
   - Implement N8N JWT token validation
   - Update route protection logic for new authentication system
   - Handle token expiration and refresh scenarios

2. **Remove Clerk Providers:**
   - Update `apps/web/src/app/layout.tsx` to remove ClerkProvider
   - Remove all Clerk configuration and imports
   - Ensure application layout works with new authentication context
   - Test theme and navigation functionality post-migration

3. **Update Protected Route Components:**
   - Identify all components using Clerk authentication hooks
   - Replace `useAuth()` from `@clerk/nextjs` with context version
   - Update user profile components to use new user structure
   - Ensure proper loading states and error handling

### Step 3: URL Structure Simplification
1. **Implement Direct Routing Structure:**
   - Create new pages to replace `/org/[orgId]/` structure:
     - `/loadboard` (replace `/org/[orgId]/loadboard`)
     - `/dashboard` (replace `/org/[orgId]/dashboard`)
     - `/my-loads` (replace `/org/[orgId]/my-loads`)
     - `/admin` (replace `/org/[orgId]/admin`)
     - `/settings` (replace `/org/[orgId]/settings`)

2. **Migrate Page Functionality:**
   - Copy core functionality from org-based pages to new direct routes
   - Remove `orgId` parameter dependencies from page logic
   - Update data fetching to use user-based authentication instead of org context
   - Ensure all existing features work with simplified routing

3. **Update Navigation and Links:**
   - Modify navigation components to use direct routes
   - Update all internal links throughout the application
   - Remove organization-based route protection logic
   - Implement user-based route protection using new auth system

### Step 4: Component Migration and Cleanup
1. **Update Components Using Clerk Hooks:**
   - **Priority Components (High Usage):**
     - Load display and booking components
     - User profile and settings components
     - Admin dashboard components
     - Document upload and management components
   - Replace all Clerk hook usage with auth context equivalents
   - Update authentication state checks and user data access

2. **Remove Clerk Debug Components:**
   - Delete unused Clerk debug components:
     - `ClerkDebugger.tsx`, `SafeOrgSelector.tsx`, `EmergencyOrgFixer.tsx`
     - `CriticalOrgSelector.tsx`, `ClerkOrgStateChecker.tsx`
   - Clean up related imports and references
   - Remove Clerk-specific testing and debugging code

3. **Update API Client Integration:**
   - Ensure API client uses JWT tokens from new auth context
   - Update request headers to use `Authorization: Bearer <token>`
   - Handle token refresh and expiration scenarios
   - Test all API integrations with new authentication

### Step 5: Testing and Validation
1. **End-to-End Authentication Testing:**
   - Test complete registration flow (sign-up → email verification if needed → login)
   - Test login flow (credentials → JWT token → authenticated state)
   - Test logout functionality and token cleanup
   - Verify protected route access and redirects

2. **Feature Functionality Testing:**
   - Test loadboard access and load filtering
   - Test load booking and assignment functionality
   - Test user profile management and updates
   - Test admin functions with new authentication
   - Test document upload and management features

3. **URL Structure Validation:**
   - Verify all new direct routes work correctly
   - Test navigation between simplified routes
   - Ensure bookmarks and direct URL access work
   - Validate SEO and routing functionality

**Guidance from Implementation Plan:**
- **Clean Break Strategy:** Complete removal of Clerk with no gradual migration
- **Simplified URL Structure:** Direct routes (/loadboard vs /org/{orgId}/loadboard)
- **Enhanced JWT Integration:** Use full JWT payload with id, email, mcNumber, role
- **User-Centric Design:** Replace organization-based features with user-based features
- **Performance Optimization:** Leverage local JWT validation for improved performance

## 4. Expected Output & Deliverables

**Define Success:**
- Complete elimination of all Clerk dependencies from frontend
- Functional N8N authentication system for user login/registration
- Simplified URL structure implemented and working
- All existing functionality preserved with new authentication
- Clean build with no Clerk-related errors or warnings

**Specify Deliverables:**
1. **Migrated Authentication System:**
   - Updated sign-in/sign-up pages using N8N authentication
   - Functional JWT token management and storage
   - Working authentication context and hooks

2. **Simplified Routing Structure:**
   - New direct route pages (/loadboard, /dashboard, /my-loads, /admin, /settings)
   - Updated navigation and internal linking
   - Removed organization-based routing complexity

3. **Updated Frontend Components:**
   - Migrated all components from Clerk to auth context
   - Removed unused Clerk debug components
   - Updated API client with JWT authentication

4. **Validation Results:**
   - Successful build of web application
   - Working authentication flows tested on all pages
   - End-to-end testing results for user registration and login
   - Performance testing with new authentication system

## 5. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Adhere strictly to the established logging format. Ensure your log includes:
- A reference to "Phase 2.7-T3: Frontend Authentication Migration"
- A clear description of all actions taken with component-by-component migration details
- Code snippets for critical authentication changes and URL structure updates
- Any key decisions made during Clerk removal and N8N integration process
- Any challenges encountered and how they were resolved
- Confirmation of successful execution with build and test results
- Specific validation that all authentication flows and simplified routing work correctly

## 6. Clarification Instruction

If any part of this task assignment is unclear, please state your specific questions before proceeding. Given the complexity of this frontend authentication migration and URL structure simplification, clarification is encouraged to ensure successful completion without breaking existing functionality.

**Critical Areas for Clarification:**
- N8N authentication endpoint integration requirements
- Specific user experience flows that must be preserved
- Admin vs carrier role handling in new URL structure
- Any existing features that require special consideration during migration 