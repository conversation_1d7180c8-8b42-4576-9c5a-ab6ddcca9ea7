{"name": "Carrier Counter Response Workflow", "nodes": [{"parameters": {"httpMethod": "GET", "path": "carrier/counter-accept", "responseMode": "responseNode", "options": {}}, "id": "webhook-counter-accept", "name": "Webhook - Counter Accept", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 200], "webhookId": "carrier-counter-accept"}, {"parameters": {"httpMethod": "GET", "path": "carrier/counter-decline", "responseMode": "responseNode", "options": {}}, "id": "webhook-counter-decline", "name": "Webhook - Counter Decline", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 400], "webhookId": "carrier-counter-decline"}, {"parameters": {"functionCode": "// Process Carrier Counter Response\n\nconst query = $json.query;\nconst bidId = query.bidId;\nconst action = $input.item.json.action || ($input.first().node.name.includes('Accept') ? 'accept' : 'decline');\n\n// Validate required data\nif (!bidId) {\n  throw new Error('Missing bidId parameter');\n}\n\n// Generate response data\nconst responseData = {\n  bidId: bidId,\n  carrierAction: action,\n  responseTime: new Date().toISOString(),\n  finalStatus: action === 'accept' ? 'counter_accepted' : 'counter_declined'\n};\n\nreturn responseData;"}, "id": "process-carrier-response", "name": "Process Carrier Response", "type": "n8n-nodes-base.function", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"authentication": "airtableTokenApi", "operation": "list", "base": {"__rl": true, "value": "YOUR_AIRTABLE_BASE_ID", "mode": "id"}, "table": {"__rl": true, "value": "Bids", "mode": "id"}, "filterByFormula": "={Bid ID} = '{{ $json.bidId }}'", "options": {}}, "id": "get-bid-details", "name": "Get Bid <PERSON>", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [600, 300], "credentials": {"airtableTokenApi": {"id": "airtable-credential", "name": "Airtable API"}}}, {"parameters": {"authentication": "airtableTokenApi", "operation": "update", "base": {"__rl": true, "value": "YOUR_AIRTABLE_BASE_ID", "mode": "id"}, "table": {"__rl": true, "value": "Bids", "mode": "id"}, "id": "={{ $json.records[0].id }}", "updateAllFields": false, "fieldsUi": {"fieldValues": [{"fieldId": "Carrier Counter Response", "fieldValue": "={{ $node['Process Carrier Response'].json.carrierAction }}"}, {"fieldId": "Counter Responded At", "fieldValue": "={{ $node['Process Carrier Response'].json.responseTime }}"}, {"fieldId": "Bid Status", "fieldValue": "={{ $node['Process Carrier Response'].json.finalStatus }}"}]}, "options": {}}, "id": "update-bid-response", "name": "Update Bid Response", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [800, 300], "credentials": {"airtableTokenApi": {"id": "airtable-credential", "name": "Airtable API"}}}, {"parameters": {"mode": "expression", "output": "input", "rules": {"rules": [{"operation": "equal", "value2": "accept", "output": 0}, {"operation": "equal", "value2": "decline", "output": 1}]}, "expression": "={{ $node['Process Carrier Response'].json.carrierAction }}"}, "id": "switch-carrier-action", "name": "Switch - Carrier Action", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1000, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "✅ Counter Offer ACCEPTED - Load Assignment Confirmed", "emailType": "html", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #059669; color: white; padding: 20px; border-radius: 8px 8px 0 0;\">\n    <h1 style=\"margin: 0; font-size: 24px;\">✅ Counter Offer Accepted!</h1>\n    <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">Carrier has accepted your counter offer</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0;\">\n    <div style=\"background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h2 style=\"color: #1f2937; margin-top: 0;\">Load Assignment Confirmed</h2>\n      <div style=\"margin: 15px 0;\">\n        <strong>Bid ID:</strong> {{ $node['Process Carrier Response'].json.bidId }}<br>\n        <strong>Carrier:</strong> MC{{ $json.records[0].fields['Carrier MC Number'] }}<br>\n        <strong>Original Bid:</strong> ${{ $json.records[0].fields['Bid Amount'] }}<br>\n        <strong>Final Amount:</strong> <span style=\"color: #059669; font-size: 18px; font-weight: bold;\">${{ $json.records[0].fields['Counter Amount'] }}</span><br>\n        <strong>Carrier Email:</strong> {{ $json.records[0].fields['Carrier Email'] }}<br>\n      </div>\n    </div>\n\n    <div style=\"background: #dcfdf7; padding: 15px; border-radius: 8px; border-left: 4px solid #059669;\">\n      <strong>Next Steps:</strong>\n      <ul style=\"margin: 10px 0;\">\n        <li>Generate load assignment paperwork</li>\n        <li>Send BOL and pickup instructions to carrier</li>\n        <li>Update load status to \"Assigned\"</li>\n        <li>Close any competing bids for this load</li>\n      </ul>\n    </div>\n  </div>\n</div>", "options": {}}, "id": "email-admin-accepted", "name": "<PERSON><PERSON> - Counter Accepted", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1200, 200], "credentials": {"smtp": {"id": "EMAIL_CREDENTIAL_ID", "name": "SMTP Email"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "❌ Counter Offer DECLINED - B<PERSON> Rejected", "emailType": "html", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #dc2626; color: white; padding: 20px; border-radius: 8px 8px 0 0;\">\n    <h1 style=\"margin: 0; font-size: 24px;\">❌ Counter Offer Declined</h1>\n    <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">Carrier has declined your counter offer</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0;\">\n    <div style=\"background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h2 style=\"color: #1f2937; margin-top: 0;\">Bid Declined</h2>\n      <div style=\"margin: 15px 0;\">\n        <strong>Bid ID:</strong> {{ $node['Process Carrier Response'].json.bidId }}<br>\n        <strong>Carrier:</strong> MC{{ $json.records[0].fields['Carrier MC Number'] }}<br>\n        <strong>Original Bid:</strong> ${{ $json.records[0].fields['Bid Amount'] }}<br>\n        <strong>Counter Offer:</strong> ${{ $json.records[0].fields['Counter Amount'] }}<br>\n        <strong>Status:</strong> <span style=\"color: #dc2626; font-weight: bold;\">DECLINED</span><br>\n      </div>\n    </div>\n\n    <div style=\"background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;\">\n      <strong>Recommended Actions:</strong>\n      <ul style=\"margin: 10px 0;\">\n        <li>Review other pending bids for this load</li>\n        <li>Consider reaching out to other carriers</li>\n        <li>Adjust rate if needed to attract bids</li>\n        <li>Post load to additional load boards</li>\n      </ul>\n    </div>\n  </div>\n</div>", "options": {}}, "id": "email-admin-declined", "name": "<PERSON>ail <PERSON>min - Counter Declined", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1200, 400], "credentials": {"smtp": {"id": "EMAIL_CREDENTIAL_ID", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "html", "responseBody": "=<div style=\"font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 30px; border: 1px solid #e2e8f0; border-radius: 8px; text-align: center;\">\n  <div style=\"background: #059669; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n    <h2 style=\"margin: 0;\">✅ Counter Offer Accepted!</h2>\n  </div>\n  \n  <div style=\"margin: 20px 0;\">\n    <p><strong>Bid ID:</strong> {{ $node['Process Carrier Response'].json.bidId }}</p>\n    <p><strong>Final Amount:</strong> <span style=\"color: #059669; font-size: 18px; font-weight: bold;\">${{ $json.records[0].fields['Counter Amount'] }}</span></p>\n  </div>\n  \n  <div style=\"background: #dcfdf7; padding: 15px; border-radius: 8px; border-left: 4px solid #059669;\">\n    <p><strong>🎉 Congratulations!</strong></p>\n    <p>You have successfully accepted the counter offer. You will receive load assignment details and pickup instructions via email shortly.</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 15px; border-radius: 8px; margin-top: 20px;\">\n    <p><strong>Next Steps:</strong></p>\n    <ul style=\"text-align: left; margin: 10px 0;\">\n      <li>Check your email for load assignment details</li>\n      <li>Prepare your equipment for pickup</li>\n      <li>Contact dispatch if you have questions</li>\n    </ul>\n  </div>\n  \n  <div style=\"margin-top: 20px;\">\n    <a href=\"javascript:window.close()\" style=\"background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;\">Close Window</a>\n  </div>\n</div>"}, "id": "respond-accepted", "name": "Respond - Counter Accepted", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 2, "position": [1400, 200]}, {"parameters": {"respondWith": "html", "responseBody": "=<div style=\"font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 30px; border: 1px solid #e2e8f0; border-radius: 8px; text-align: center;\">\n  <div style=\"background: #dc2626; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n    <h2 style=\"margin: 0;\">❌ Counter Offer Declined</h2>\n  </div>\n  \n  <div style=\"margin: 20px 0;\">\n    <p><strong>Bid ID:</strong> {{ $node['Process Carrier Response'].json.bidId }}</p>\n    <p>You have declined the counter offer of <strong>${{ $json.records[0].fields['Counter Amount'] }}</strong></p>\n  </div>\n  \n  <div style=\"background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;\">\n    <p><strong>Thank You</strong></p>\n    <p>Thank you for considering our counter offer. We appreciate your time and look forward to working with you on future loads.</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 15px; border-radius: 8px; margin-top: 20px;\">\n    <p><strong>Keep Looking:</strong></p>\n    <p>Please continue checking our loadboard for new opportunities that may better match your rate requirements.</p>\n  </div>\n  \n  <div style=\"margin-top: 20px;\">\n    <a href=\"javascript:window.close()\" style=\"background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;\">Close Window</a>\n  </div>\n</div>"}, "id": "respond-declined", "name": "Respond - Counter Declined", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 2, "position": [1400, 400]}], "connections": {"Webhook - Counter Accept": {"main": [[{"node": "Process Carrier Response", "type": "main", "index": 0}]]}, "Webhook - Counter Decline": {"main": [[{"node": "Process Carrier Response", "type": "main", "index": 0}]]}, "Process Carrier Response": {"main": [[{"node": "Get Bid <PERSON>", "type": "main", "index": 0}]]}, "Get Bid Details": {"main": [[{"node": "Update Bid Response", "type": "main", "index": 0}]]}, "Update Bid Response": {"main": [[{"node": "Switch - Carrier Action", "type": "main", "index": 0}]]}, "Switch - Carrier Action": {"main": [[{"node": "<PERSON><PERSON> - Counter Accepted", "type": "main", "index": 0}], [{"node": "<PERSON>ail <PERSON>min - Counter Declined", "type": "main", "index": 0}]]}, "Email Admin - Counter Accepted": {"main": [[{"node": "Respond - Counter Accepted", "type": "main", "index": 0}]]}, "Email Admin - Counter Declined": {"main": [[{"node": "Respond - Counter Declined", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "id": "carrier-counter-response-workflow", "meta": {"instanceId": "n8n-instance"}}