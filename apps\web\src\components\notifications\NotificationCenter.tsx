'use client';

import React, { useState } from 'react';
import { Bell, X, CheckChe<PERSON>, Trash2, Wif<PERSON>, <PERSON>if<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
// Real-time notifications now handled by N8N via email/SMS
import { NotificationToast } from './NotificationToast';
import { cn } from '../../lib/utils';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';

interface NotificationCenterProps {
  className?: string;
  trigger?: React.ReactNode;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ 
  className,
  trigger 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'urgent'>('all');
  
  // Real-time notifications now handled by N8N via email/SMS
  // Mock state for UI consistency
  const notifications: any[] = [];
  const unreadCount = 0;
  const connected = false;
  const connecting = false;
  const error = 'N8N handles notifications via email/SMS';
  const markAsRead = (id: string) => {};
  const markAllAsRead = () => {};
  const clearNotifications = () => {};
  const removeNotification = (id: string) => {};
  const filteredNotifications = (opts: any) => [];
  const adminBiddingState = { urgentBids: [] };
  const getBiddingNotifications = () => [];

  const displayedNotifications = (() => {
    switch (filter) {
      case 'unread':
        return filteredNotifications({ read: false, limit: 50 });
      case 'urgent':
        return notifications.filter(n => 
          n.data?.priority === 'high' || 
          n.type === 'admin_alert'
        ).slice(0, 50);
      default:
        return filteredNotifications({ limit: 50 });
    }
  })();

  const urgentCount = notifications.filter(n => 
    n.data?.priority === 'high' || 
    n.type === 'admin_alert'
  ).length;

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const handleClearAll = () => {
    clearNotifications();
    setIsOpen(false);
  };

  // Default trigger if none provided
  const defaultTrigger = (
    <Button variant="outline" size="sm" className="relative">
      <Bell className="h-4 w-4" />
      {unreadCount > 0 && (
        <Badge 
          variant="destructive" 
          className="absolute -top-2 -right-2 text-xs min-w-[18px] h-4 flex items-center justify-center"
        >
          {unreadCount > 99 ? '99+' : unreadCount}
        </Badge>
      )}
      {urgentCount > 0 && (
        <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full animate-ping" />
      )}
    </Button>
  );

  return (
    <div className={cn('relative', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          {trigger || defaultTrigger}
        </PopoverTrigger>
        
        <PopoverContent 
          className="w-96 p-0" 
          align="end"
          sideOffset={8}
        >
          {/* Header */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">Notifications</h3>
              <Button
                onClick={() => setIsOpen(false)}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Connection Status */}
            <div className="flex items-center mt-2 text-sm">
              {connecting ? (
                <div className="flex items-center text-yellow-600">
                  <Wifi className="h-4 w-4 mr-1 animate-pulse" />
                  Connecting...
                </div>
              ) : connected ? (
                <div className="flex items-center text-green-600">
                  <Wifi className="h-4 w-4 mr-1" />
                  Real-time updates enabled
                  <div className="w-2 h-2 bg-green-400 rounded-full ml-2 animate-pulse" />
                </div>
              ) : (
                <div className="flex items-center text-red-600">
                  <WifiOff className="h-4 w-4 mr-1" />
                  {error || 'Disconnected'}
                </div>
              )}
            </div>

            {/* Filter and Actions */}
            <div className="flex items-center justify-between mt-3">
              <div className="flex space-x-2">
                <button
                  onClick={() => setFilter('all')}
                  className={cn(
                    'px-3 py-1 text-xs rounded-full transition-colors',
                    filter === 'all'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  )}
                >
                  All ({notifications.length})
                </button>
                <button
                  onClick={() => setFilter('unread')}
                  className={cn(
                    'px-3 py-1 text-xs rounded-full transition-colors',
                    filter === 'unread'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  )}
                >
                  Unread ({unreadCount})
                </button>
                {urgentCount > 0 && (
                  <button
                    onClick={() => setFilter('urgent')}
                    className={cn(
                      'px-3 py-1 text-xs rounded-full transition-colors',
                      filter === 'urgent'
                        ? 'bg-red-600 text-white'
                        : 'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-950 dark:text-red-400'
                    )}
                  >
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Urgent ({urgentCount})
                  </button>
                )}
              </div>

              <div className="flex space-x-1">
                {unreadCount > 0 && (
                  <Button
                    onClick={handleMarkAllAsRead}
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    title="Mark all as read"
                  >
                    <CheckCheck className="h-4 w-4" />
                  </Button>
                )}
                {notifications.length > 0 && (
                  <Button
                    onClick={handleClearAll}
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                    title="Clear all"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Urgent Alerts Banner */}
          {urgentCount > 0 && filter !== 'urgent' && (
            <div className="p-3 bg-red-50 border-b border-red-200 dark:bg-red-950 dark:border-red-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
                  <AlertTriangle className="h-4 w-4 animate-pulse" />
                  <span className="text-sm font-semibold">
                    {urgentCount} urgent notification{urgentCount > 1 ? 's' : ''}
                  </span>
                </div>
                <Button
                  onClick={() => setFilter('urgent')}
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300"
                >
                  View All
                </Button>
              </div>
            </div>
          )}

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {displayedNotifications.length > 0 ? (
              <div className="space-y-1">
                {displayedNotifications.map((notification) => {
                  const isUrgent = notification.data?.priority === 'high' || notification.type === 'admin_alert';
                  
                  return (
                    <div
                      key={notification.id}
                      className={cn(
                        'p-3 border-b border-border/50 hover:bg-muted/50 transition-colors cursor-pointer',
                        !notification.read && 'bg-blue-50/50 dark:bg-blue-950/20',
                        isUrgent && 'bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-800'
                      )}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {isUrgent && (
                              <AlertTriangle className="h-3 w-3 text-red-500 animate-pulse flex-shrink-0" />
                            )}
                            <h4 className={cn(
                              'text-sm font-medium truncate',
                              isUrgent && 'text-red-800 dark:text-red-200'
                            )}>
                              {notification.title}
                            </h4>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-muted-foreground">
                              {new Date(notification.timestamp).toLocaleTimeString()}
                            </span>
                            {notification.type === 'bid_new' && (
                              <Badge variant="secondary" className="text-xs">
                                New Bid
                              </Badge>
                            )}
                            {isUrgent && (
                              <Badge variant="destructive" className="text-xs animate-pulse">
                                Urgent
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeNotification(notification.id);
                          }}
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-8 text-center text-muted-foreground">
                <Bell className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <p className="text-sm">
                  {filter === 'unread' ? 'No unread notifications' : 
                   filter === 'urgent' ? 'No urgent notifications' :
                   'No notifications yet'}
                </p>
                {!connected && (
                  <p className="text-xs mt-2">
                    Connect to receive real-time updates
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 50 && (
            <div className="p-3 border-t border-border text-center">
              <p className="text-xs text-muted-foreground">
                Showing recent 50 notifications
              </p>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default NotificationCenter; 