Build Carrier Loadboard & Bidding Web Application

**Project Goal:** Develop a web application for motor carriers to create accounts, define their operational capabilities (equipment, service areas), view a "Loadboard" of freight shipments (sourced from Airtable), and bid on/accept these shipments. An admin interface is required for carrier verification, and load/bid oversight.

**Target Users:**
1.  **Motor Carrier:** Signs up, manages profile, defines capabilities, views loads, bids.
2.  **Administrator:** Verifies carriers, manages loads (view/status), manages bids (view/award).

---

**DECISIONS & SPECIFICATIONS:**

**1. Tech Stack:**
    *   **Frontend:** Next.js (React)
    *   **Styling:** Tailwind CSS
    *   **Backend API:** NestJS (Node.js with TypeScript)
    *   **Database:** PostgreSQL
    *   **Authentication:** Auth0
    *   **Airtable Sync Lib:** `airtable.js` (Node.js)
    *   **Deployment:** Vercel (Frontend), Render.com (Backend & DB)

**2. Airtable Integration:**
    *   **Method:** Push from Airtable Automations via Webhook to a NestJS API endpoint.
    *   **Trigger:** On new record creation or update in the designated Airtable "Loads" table.
    *   **Data Flow:** Airtable -> Webhook -> NestJS API -> PostgreSQL `Loads` table.
    *   **Initial Sync:** A one-time script/admin function to pull existing loads.

**3. Carrier "Roles/Choices" (Operational Capabilities):**
    *   Managed via `CarrierProfiles` table, linked to `Users`.
    *   **Key Fields:**
        *   `equipment_types`: JSONB array (e.g., `["Dry Van 53ft", "Reefer"]`)
        *   `serviceable_regions`: JSONB array (e.g., `["Northeast", "CA", "AZ"]`)
    *   **Verification:** Admin reviews profile and sets `is_verified_by_admin = TRUE` on `CarrierProfiles`.

**4. MVP Feature Set:**

    *   **Carrier Flow:**
        1.  Auth0 Signup & Login.
        2.  Onboarding/Profile: Enter Company Name, DOT, MC, contact, select Equipment Types & Serviceable Regions.
        3.  View Loadboard (list of available loads from app DB).
        4.  View Load Details.
        5.  Submit Bid (amount, optional notes).
        6.  View own active bids and their status.

    *   **Admin Flow:**
        1.  Auth0 Login (Admin role).
        2.  View list of Carriers & their Profiles.
        3.  Verify/Unverify Carrier Profiles.
        4.  View all Loads from app DB.
        5.  View all Bids for a specific Load.
        6.  "Award" Load: Update `Loads.status` & `Loads.awarded_to_carrier_profile_id`, update `Bids.status`. (MVP notification can be manual or a simple email).

    *   **System:**
        1.  Airtable webhook receiver on NestJS backend.
        2.  Logic to upsert load data into PostgreSQL `Loads` table based on `airtable_record_id`.
        3.  Secure RESTful API endpoints for all interactions.

**5. Core Database Schemas (PostgreSQL):**
    *   **`Users`**: `id`, `auth0_user_id`, `email`, `name`, `role` (`CARRIER`, `ADMIN`), `created_at`, `updated_at`.
    *   **`CarrierProfiles`**: `id`, `user_id` (FK to Users), `company_name`, `dot_number`, `mc_number`, `phone_number`, `equipment_types` (JSONB), `serviceable_regions` (JSONB), `is_verified_by_admin`, `admin_notes`, `created_at`, `updated_at`.
    *   **`Loads`**: `id`, `airtable_record_id` (UNIQUE), `origin_city`, `origin_state`, `destination_city`, `destination_state`, `pickup_date_utc`, `delivery_date_utc`, `equipment_required`, `weight_lbs`, `rate`, `status`, `awarded_to_carrier_profile_id` (FK), `raw_airtable_data` (JSONB), `created_at`, `updated_at`.
    *   **`Bids`**: `id`, `load_id` (FK to Loads), `carrier_profile_id` (FK to CarrierProfiles), `bid_amount`, `carrier_notes`, `status`, `created_at`, `updated_at`. (UNIQUE on `load_id`, `carrier_profile_id`).

---
