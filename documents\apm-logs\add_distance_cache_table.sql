-- Create distance cache table for storing calculated distances
-- This prevents repeated API calls to Radar.com for the same routes

CREATE TABLE IF NOT EXISTS public.distance_cache (
    id TEXT PRIMARY KEY,
    origin_address TEXT NOT NULL,
    destination_address TEXT NOT NULL,
    distance_miles DOUBLE PRECISION NOT NULL,
    duration_hours DOUBLE PRECISION NOT NULL,
    calculated_by TEXT NOT NULL, -- 'radar' or 'fallback'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique index on address combination for fast lookups and prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS distance_cache_addresses_unique 
ON public.distance_cache (origin_address, destination_address);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS distance_cache_origin_address_idx 
ON public.distance_cache (origin_address);

CREATE INDEX IF NOT EXISTS distance_cache_destination_address_idx 
ON public.distance_cache (destination_address);

CREATE INDEX IF NOT EXISTS distance_cache_calculated_by_idx 
ON public.distance_cache (calculated_by);

CREATE INDEX IF NOT EXISTS distance_cache_created_at_idx 
ON public.distance_cache (created_at);

-- Add a function to generate CUIDs for the ID field
CREATE OR REPLACE FUNCTION generate_cuid() RETURNS TEXT AS $$
BEGIN
    -- Simple CUID-like ID generation (timestamp + random)
    RETURN 'c' || EXTRACT(EPOCH FROM NOW())::BIGINT::TEXT || 
           LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
END;
$$ LANGUAGE plpgsql;

-- Set default value for ID column to use the CUID function
ALTER TABLE public.distance_cache 
ALTER COLUMN id SET DEFAULT generate_cuid();

-- Verify the table was created
SELECT 
    schemaname, 
    tablename, 
    tableowner 
FROM pg_tables 
WHERE tablename = 'distance_cache';

-- Show the indexes
SELECT 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE tablename = 'distance_cache'; 