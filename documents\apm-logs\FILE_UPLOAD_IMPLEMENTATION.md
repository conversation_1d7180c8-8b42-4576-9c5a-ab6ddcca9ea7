# File Upload & Airtable Sync Implementation

This document provides a comprehensive overview of the file upload and Airtable synchronization feature implementation.

## Overview

The file upload system allows carriers to upload BOL (Bill of Lading), POD (Proof of Delivery), invoices, and other documents for completed loads. The system includes:

- Drag & drop file upload interface
- Image optimization using Sharp
- Secure file storage with Vercel Blob
- Database metadata storage
- Automatic Airtable synchronization
- Progress indication and error handling

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Client  │───▶│  Next.js API     │───▶│  Vercel Blob    │
│  (DocumentUpload)│    │  (/api/loads/    │    │   Storage       │
└─────────────────┘    │   [id]/documents)│    └─────────────────┘
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Backend API     │───▶│   PostgreSQL    │
                       │  (Database Save) │    │   Database      │
                       └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │    Airtable      │
                       │      Sync        │
                       └──────────────────┘
```

## Implementation Details

### 1. Database Schema

**Document Model** (`apps/api/prisma/schema.prisma`):
```prisma
model Document {
  id        String   @id @default(cuid())
  loadId    String   @map("load_id")
  filename  String
  url       String
  type      String   // 'image/jpeg', 'application/pdf', etc.
  size      Int      // file size in bytes
  uploadedAt DateTime @default(now()) @map("uploaded_at")
  
  load      Load     @relation(fields: [loadId], references: [id], onDelete: Cascade)
  
  @@index([loadId])
  @@index([uploadedAt])
  @@map("documents")
}
```

**Load Model Updates**:
```prisma
model Load {
  // ... existing fields
  documents Document[]
}
```

### 2. API Endpoint

**File Upload API** (`apps/web/src/app/api/loads/[loadId]/documents/route.ts`):

- **Authentication**: Validates user via Clerk
- **File Validation**: Checks file type, size, and count limits
- **Image Processing**: Optimizes images using Sharp (resize to 2048x2048, 85% JPEG quality)
- **Storage**: Uploads to Vercel Blob with private access
- **Database**: Saves metadata via backend API
- **Airtable Sync**: Updates Airtable records with document information

**Key Features**:
- Maximum 5 files per upload
- 10MB file size limit
- Supported formats: PDF, JPG, PNG
- Automatic image optimization
- Graceful error handling

### 3. React Components

**DocumentUpload Component** (`apps/web/src/components/DocumentUpload.tsx`):

- **Drag & Drop**: Uses react-dropzone for file selection
- **Progress Indication**: Shows upload progress with visual feedback
- **File Preview**: Displays uploaded files with metadata
- **Error Handling**: Shows user-friendly error messages
- **Status Management**: Visual states for idle, uploading, success, and error

**Load Detail Page** (`apps/web/src/app/org/[orgId]/my-loads/[loadId]/page.tsx`):

- **Load Information**: Displays comprehensive load details
- **Document Management**: Shows existing documents and upload interface
- **Conditional Upload**: Only allows uploads for completed loads
- **Navigation**: Integrates with existing my-loads page

### 4. Airtable Integration

**Airtable Module** (`apps/web/src/lib/airtable.ts`):

- **Document Sync**: Updates Airtable records with document URLs and metadata
- **Error Handling**: Graceful degradation if Airtable sync fails
- **Configuration Validation**: Checks for required environment variables

**Synced Fields**:
- `Documents`: Newline-separated URLs
- `Document Names`: Newline-separated filenames
- `Document Count`: Total number of documents
- `Total Document Size`: Combined size in KB
- `Last Document Upload`: Timestamp of last upload

## File Processing Pipeline

1. **Client Upload**: User selects files via drag & drop or file picker
2. **Validation**: Client-side validation for file type and size
3. **API Request**: FormData sent to Next.js API endpoint
4. **Authentication**: Clerk validates user session
5. **Load Verification**: Backend API confirms user owns the load
6. **File Processing**: 
   - Images optimized with Sharp
   - Files uploaded to Vercel Blob
7. **Database Storage**: Metadata saved via backend API
8. **Airtable Sync**: Document information synced to Airtable
9. **Response**: Success/error response sent to client
10. **UI Update**: Client updates interface with results

## Security Considerations

- **Authentication**: All requests require valid Clerk session
- **Authorization**: Users can only upload to their assigned loads
- **File Validation**: Strict file type and size limits
- **Private Storage**: Vercel Blob configured for private access
- **Input Sanitization**: Filenames sanitized before storage
- **Error Handling**: No sensitive information exposed in errors

## Configuration

### Environment Variables

```bash
# Airtable Configuration
AIRTABLE_API_KEY=your_airtable_api_key
AIRTABLE_BASE_ID=your_airtable_base_id

# Vercel Blob Storage
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token

# Backend API
NEXT_PUBLIC_API_BASE_URL=https://api.fcp-portal.com
```

### Dependencies

```json
{
  "dependencies": {
    "@vercel/blob": "^0.24.1",
    "react-dropzone": "^14.3.0",
    "sharp": "^0.33.5",
    "airtable": "^0.12.2"
  }
}
```

## Testing

### Manual Testing Checklist

- [ ] Upload single PDF file
- [ ] Upload multiple image files
- [ ] Test drag and drop functionality
- [ ] Verify image optimization works
- [ ] Test file size validation (>10MB)
- [ ] Test file type validation (unsupported formats)
- [ ] Verify Airtable sync
- [ ] Test error scenarios
- [ ] Test on different browsers
- [ ] Test mobile responsiveness

### Automated Testing

Basic test structure provided in `route.test.ts`:
- Authentication validation
- File validation (size, type, count)
- Error handling scenarios

## Performance Considerations

- **Image Optimization**: Sharp reduces file sizes significantly
- **Parallel Processing**: Multiple files processed concurrently
- **Progress Indication**: Simulated progress for better UX
- **Error Recovery**: Failed uploads don't affect successful ones
- **Lazy Loading**: Components only load when needed

## Monitoring & Debugging

### Logging

- Upload attempts and results
- File processing metrics
- Airtable sync status
- Error details and stack traces

### Metrics to Monitor

- Upload success/failure rates
- File processing times
- Storage usage
- Airtable sync reliability
- User engagement with feature

## Future Enhancements

### Planned Features (Not in Current Scope)

- **OCR Text Extraction**: Extract text from uploaded documents
- **Virus Scanning**: Scan files for malware
- **Document Thumbnails**: Generate preview images
- **Bulk Upload**: Upload multiple loads at once
- **Version Control**: Track document versions
- **Advanced Airtable Sync**: Webhook-based real-time sync

### Scalability Considerations

- **CDN Integration**: Serve files via CDN for better performance
- **Background Processing**: Move image optimization to background jobs
- **Database Optimization**: Add indexes for document queries
- **Caching**: Cache document metadata for faster retrieval

## Troubleshooting

### Common Issues

1. **Upload Fails**: Check environment variables and network connectivity
2. **Images Not Optimized**: Verify Sharp installation and configuration
3. **Airtable Sync Fails**: Check API key and base ID configuration
4. **Large File Uploads**: Verify Vercel function timeout limits
5. **Database Errors**: Check backend API connectivity and schema

### Debug Steps

1. Check browser console for client-side errors
2. Review server logs for API errors
3. Verify environment variable configuration
4. Test individual components (upload, processing, sync)
5. Check Vercel Blob dashboard for storage issues

## Support

For technical issues or questions:
- Review error logs in Vercel dashboard
- Check Airtable API status
- Verify environment configuration
- Test with minimal file uploads first
- Contact development team for complex issues 