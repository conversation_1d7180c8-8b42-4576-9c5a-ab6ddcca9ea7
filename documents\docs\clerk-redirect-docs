---
title: Customize your redirect URLs
description: Customize where your users are redirected to after they sign in or sign up.
---

To avoid breaking a user's flow through your app, when a user navigates to a Clerk sign up or sign in page via a link or button, Clerk will:

1. Persist the previous page's URL in a `redirect_url` query string
1. Navigate back to that page after the sign-up or sign-in is completed

For example, a user selecting a sign-in button on `example.com/foo` will navigate to `example.com/sign-in?redirect_url=example.com/foo`, then navigate back to `example.com/foo` upon completing the sign-in process.

However, you can customize this behavior to redirect users to a specific page by using the following methods:

- [Environment variables (recommended)](#environment-variables)
- [Props on Clerk components](#redirect-url-props)

## Environment variables

The following environment variables are available for customizing your redirect URLs.

<Include src="_partials/environment-variables" />

## Redirect URL props

This section describes the properties available for customizing your redirect URLs on Clerk components. In general, **it's recommended to use [environment variables](#environment-variables) instead.**

> [!WARNING]
> The `afterSignIn`, `afterSignUp`, and `redirectUrl` props are deprecated. If you're still using them, the props described in this section will override them.

### Fallback redirect URL props

The "fallback redirect URL" props will only be used if there is no `redirect_url` value. This can happen if the user has navigated directly to the sign up or sign in page.

- `fallbackRedirectUrl` - Used by sign-in and sign-up related components.
- `signInFallbackRedirectUrl` - Used for the 'Already have an account? Sign in' link that's rendered on sign-up components, such as `<SignUp />` and `<SignUpButton>`.
- `signUpFallbackRedirectUrl` - Used for the 'Don't have an account? Sign up' link that's rendered on sign-in components, such as `<SignIn />` and `<SignInButton>`.

### Force redirect URL props

The "force redirect URL" props will _always_ redirect to the provided URL after sign up or sign in, regardless of what page the user was on before, and will override the `redirect_url` value if present.

- `forceRedirectUrl` - Used by sign-in and sign-up related components.
- `signInForceRedirectUrl` - Used for the 'Already have an account? Sign in' link that's rendered on sign-up components, such as `<SignUp />` and `<SignUpButton>`.
- `signUpForceRedirectUrl` - Used for the 'Don't have an account? Sign up' link that's rendered on sign-in components, such as `<SignIn />` and `<SignInButton>`.

### Set the props

It is recommended to define both sign-up and sign-in variables, as some users may choose to sign up instead after attempting to sign in, and vice versa. For example, if you define `signInFallbackRedirectUrl`, you should also define `signUpFallbackRedirectUrl`.

The following components accept the redirect URL props:

- [`<RedirectToSignIn />`](/docs/components/control/redirect-to-signin)
- [`<RedirectToSignUp />`](/docs/components/control/redirect-to-signup)
- [`<ClerkProvider>`](/docs/components/clerk-provider)
- [`<SignInButton>`](/docs/components/unstyled/sign-in-button)
- [`<SignUpButton>`](/docs/components/unstyled/sign-up-button)
- [`<SignIn>`](/docs/components/authentication/sign-in)
- [`<SignUp>`](/docs/components/authentication/sign-up)

See the appropriate reference documentation for each component, as linked above, for more information on what specific props are available.

> [!NOTE]
> `<RedirectToSignIn />` or `<RedirectToSignUp />` child components will always take precedence over `<ClerkProvider>`.
