"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.maxDuration = exports.runtime = void 0;
exports.runtime = 'nodejs';
exports.maxDuration = 30;
async function initializeOTel() {
    if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV) {
        try {
            const otelModule = await Promise.resolve().then(() => __importStar(require("@vercel/otel")));
            if (otelModule && typeof otelModule.registerOTel === 'function') {
                otelModule.registerOTel({
                    serviceName: "fcp-portal-api",
                    instrumentationConfig: {
                        fetch: {
                            propagateContextUrls: [],
                        },
                    },
                });
                console.log('[MAIN.TS OTEL] OpenTelemetry for Vercel registered for fcp-portal-api.');
            }
            else {
                console.log('[MAIN.TS OTEL] OpenTelemetry module structure unexpected, skipping.');
            }
        }
        catch (error) {
            console.warn('[MAIN.TS OTEL] Failed to initialize OpenTelemetry (non-critical):', error.message);
        }
    }
    else {
        console.log('[MAIN.TS OTEL] OpenTelemetry for Vercel SKIPPED (not in Vercel production/preview/dev).');
    }
}
initializeOTel().catch(error => {
    console.warn('[MAIN.TS OTEL] OpenTelemetry initialization failed silently:', error.message);
});
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const express_1 = __importDefault(require("express"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const global_exception_filter_1 = require("./common/filters/global-exception.filter");
console.log('[MAIN.TS DEBUG] Top-level execution START');
let cachedServer;
async function bootstrapServer() {
    console.log('[MAIN.TS DEBUG] Entering bootstrapServer');
    if (!cachedServer) {
        console.log('[MAIN.TS DEBUG] cachedServer is null, initializing NestJS...');
        const expressApp = (0, express_1.default)();
        if (process.env.VERCEL || process.env.NODE_ENV === 'production') {
            expressApp.set('trust proxy', 1);
            console.log('[MAIN.TS DEBUG] Trust proxy enabled for Vercel/production environment');
        }
        console.log('[MAIN.TS DEBUG] Applying security middleware...');
        expressApp.use((0, helmet_1.default)({
            contentSecurityPolicy: false,
            crossOriginEmbedderPolicy: false,
            hsts: {
                maxAge: 31536000,
                includeSubDomains: true,
                preload: true
            },
            noSniff: true,
            frameguard: { action: 'deny' },
            xssFilter: true,
            referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
        }));
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000,
            max: process.env.NODE_ENV === 'production' ? 100 : 1000,
            message: {
                error: 'Too many requests from this IP, please try again later.',
                retryAfter: '15 minutes'
            },
            standardHeaders: true,
            legacyHeaders: false,
            skip: (req) => {
                return req.path === '/api/v1/health';
            }
        });
        expressApp.use('/api', limiter);
        console.log('[MAIN.TS DEBUG] Express app created.');
        const nestApp = await core_1.NestFactory.create(app_module_1.AppModule, new platform_express_1.ExpressAdapter(expressApp), {});
        console.log('[MAIN.TS DEBUG] NestJS app created.');
        console.log("[MAIN.TS DEBUG] Setting global prefix 'api/v1'.");
        nestApp.setGlobalPrefix('api/v1');
        console.log("[MAIN.TS DEBUG] Global prefix 'api/v1' set.");
        console.log('[MAIN.TS DEBUG] Setting up global validation pipe.');
        nestApp.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            transform: true,
            forbidNonWhitelisted: true,
            disableErrorMessages: false,
            exceptionFactory: (errors) => {
                console.log('[MAIN.TS VALIDATION] Validation errors:', JSON.stringify(errors, null, 2));
                return new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
            }
        }));
        console.log('[MAIN.TS DEBUG] Global validation pipe set.');
        console.log('[MAIN.TS DEBUG] Setting up global exception filter.');
        nestApp.useGlobalFilters(new global_exception_filter_1.GlobalExceptionFilter());
        console.log('[MAIN.TS DEBUG] Global exception filter set.');
        console.log('[MAIN.TS DEBUG] Configuring SECURE CORS...');
        const allowedOrigins = [
            'https://fcp-portal.com',
            'https://www.fcp-portal.com',
            'https://api.fcp-portal.com',
            'https://clerk.fcp-portal.com',
            'https://carrier-portal-q32rlxfk7-amers-projects-baa614f1.vercel.app',
            'https://carrier-portal-api-git-main-amers-projects-baa614f1.vercel.app',
            ...(process.env.NODE_ENV === 'development' ? [
                'http://localhost:3000',
                'http://localhost:3001',
                'http://localhost:4200'
            ] : []),
            ...(process.env.VERCEL_URL ? [`https://${process.env.VERCEL_URL}`] : []),
            ...(process.env.VERCEL_BRANCH_URL ? [`https://${process.env.VERCEL_BRANCH_URL}`] : []),
        ].filter(Boolean);
        nestApp.enableCors({
            origin: (requestOrigin, callback) => {
                console.log(`[MAIN.TS CORS DEBUG] Request origin: ${requestOrigin}`);
                console.log(`[MAIN.TS CORS DEBUG] Allowed origins: ${JSON.stringify(allowedOrigins)}`);
                if (process.env.NODE_ENV === 'production' && !requestOrigin) {
                    console.log('[MAIN.TS CORS DEBUG] Production mode: no origin header detected');
                    if (process.env.VERCEL) {
                        console.log('[MAIN.TS CORS DEBUG] Vercel environment: allowing request with no origin (internal request)');
                        return callback(null, true);
                    }
                    console.log('[MAIN.TS CORS DEBUG] Non-Vercel production: origin required but missing');
                    callback(new Error('Origin header required'), false);
                    return;
                }
                if (!requestOrigin && process.env.NODE_ENV === 'development') {
                    console.log('[MAIN.TS CORS DEBUG] Development: Request has no origin. Allowing.');
                    return callback(null, true);
                }
                if (requestOrigin && allowedOrigins.includes(requestOrigin)) {
                    console.log(`[MAIN.TS CORS DEBUG] Origin ALLOWED: ${requestOrigin}`);
                    return callback(null, true);
                }
                if (requestOrigin) {
                    const isVercelPreview = /.*\.vercel\.app$/.test(requestOrigin);
                    if (isVercelPreview) {
                        console.log(`[MAIN.TS CORS DEBUG] Vercel preview origin ALLOWED: ${requestOrigin}`);
                        return callback(null, true);
                    }
                    const isLocalhost = /^https?:\/\/localhost(:\d+)?$/.test(requestOrigin);
                    if (process.env.NODE_ENV === 'development' && isLocalhost) {
                        console.log(`[MAIN.TS CORS DEBUG] Development localhost ALLOWED: ${requestOrigin}`);
                        return callback(null, true);
                    }
                }
                console.log(`[MAIN.TS CORS DEBUG] Origin DISALLOWED: ${requestOrigin}`);
                callback(new Error('Not allowed by CORS'), false);
            },
            methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
            credentials: true,
            preflightContinue: false,
            optionsSuccessStatus: 204,
            maxAge: 86400,
            allowedHeaders: [
                'Content-Type',
                'Accept',
                'Authorization',
                'X-Requested-With',
                'X-CSRF-Token',
                'Origin',
                'Cache-Control',
                'Pragma'
            ],
        });
        console.log('[MAIN.TS DEBUG] SECURE CORS configured.');
        console.log('[MAIN.TS DEBUG] WebSocket adapter removed - using N8N for notifications');
        console.log('[MAIN.TS DEBUG] Initializing NestJS app modules...');
        await nestApp.init();
        console.log('[MAIN.TS DEBUG] NestJS app modules initialized.');
        cachedServer = expressApp;
        console.log('[MAIN.TS DEBUG] cachedServer set.');
    }
    else {
        console.log('[MAIN.TS DEBUG] Using cached server.');
    }
    console.log('[MAIN.TS DEBUG] Exiting bootstrapServer');
    return cachedServer;
}
console.log('[MAIN.TS DEBUG] Defining Vercel handler function.');
exports.default = async (req, res) => {
    console.log(`[MAIN.TS DEBUG] Vercel handler invoked. Method: ${req.method}, URL: ${req.url}`);
    try {
        const server = await bootstrapServer();
        console.log('[MAIN.TS DEBUG] Server bootstrapped, forwarding request to NestJS.');
        server(req, res);
    }
    catch (error) {
        console.error('[MAIN.TS DEBUG] Error in handler:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
console.log('[MAIN.TS DEBUG] Top-level execution END');
async function localBootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.setGlobalPrefix('api/v1');
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: { enableImplicitConversion: true }
    }));
    app.enableCors({
        origin: ["http://localhost:3000", "http://localhost:3001", "http://localhost:4200"],
        credentials: true
    });
    await app.listen(process.env.PORT || 3001);
    common_1.Logger.log(`🚀 API application is running on: ${await app.getUrl()}`, 'LocalBootstrap');
}
if (process.env.NODE_ENV !== 'production' && !process.env.VERCEL) {
    localBootstrap();
}
//# sourceMappingURL=main.js.map