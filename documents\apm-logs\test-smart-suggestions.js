// Using built-in fetch in Node.js 18+

// Test configuration
const BASE_URL = 'http://localhost:3001';
const TEST_TOKEN = 'test-jwt-token'; // Replace with actual JWT token

// Test data
const testLane = {
  originCity: 'Los Angeles',
  originState: 'CA',
  destinationCity: 'Phoenix',
  destinationState: 'AZ'
};

const testOrderData = {
  poNumber: 'PO-20241205-001',
  pickupDate: '2024-12-10',
  equipmentRequired: 'Dry Van',
  weightLbs: 35000,
  rate: 1200
};

async function testSmartSuggestions() {
  console.log('🧠 Testing Smart Suggestions API...\n');

  try {
    // Test 1: Get Smart Suggestions
    console.log('1. Testing GET /api/v1/operations/suggestions');
    const suggestionsParams = new URLSearchParams({
      originCity: testLane.originCity,
      originState: testLane.originState,
      destinationCity: testLane.destinationCity,
      destinationState: testLane.destinationState,
      currentValues: JSON.stringify(testOrderData)
    });

    const suggestionsResponse = await fetch(`${BASE_URL}/api/v1/operations/suggestions?${suggestionsParams}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (suggestionsResponse.ok) {
      const suggestionsData = await suggestionsResponse.json();
      console.log('✅ Smart suggestions retrieved successfully');
      console.log(`   - ${suggestionsData.suggestions.length} suggestions generated`);
      console.log(`   - Overall confidence: ${(suggestionsData.confidence * 100).toFixed(1)}%`);
      console.log(`   - Data points: ${suggestionsData.metadata.dataPoints}`);
      
      if (suggestionsData.suggestions.length > 0) {
        console.log('   - Sample suggestions:');
        suggestionsData.suggestions.slice(0, 3).forEach((suggestion, index) => {
          console.log(`     ${index + 1}. ${suggestion.type}: ${JSON.stringify(suggestion.suggestion)} (${(suggestion.confidence * 100).toFixed(0)}% confidence)`);
        });
      }
    } else {
      console.log('❌ Failed to get smart suggestions:', suggestionsResponse.status, suggestionsResponse.statusText);
      const errorText = await suggestionsResponse.text();
      console.log('   Error details:', errorText);
    }

    console.log('');

    // Test 2: Validate Order Data
    console.log('2. Testing POST /api/v1/operations/validate');
    const validationResponse = await fetch(`${BASE_URL}/api/v1/operations/validate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        orderData: testOrderData,
        context: testLane
      })
    });

    if (validationResponse.ok) {
      const validationData = await validationResponse.json();
      console.log('✅ Order validation completed successfully');
      console.log(`   - Valid: ${validationData.isValid}`);
      console.log(`   - Warnings: ${validationData.warnings.length}`);
      console.log(`   - Critical issues: ${validationData.criticalIssues.length}`);
      console.log(`   - Suggestions: ${validationData.suggestions.length}`);
      
      if (validationData.warnings.length > 0) {
        console.log('   - Sample warnings:');
        validationData.warnings.slice(0, 2).forEach((warning, index) => {
          console.log(`     ${index + 1}. ${warning.message}`);
        });
      }
    } else {
      console.log('❌ Failed to validate order:', validationResponse.status, validationResponse.statusText);
      const errorText = await validationResponse.text();
      console.log('   Error details:', errorText);
    }

    console.log('');

    // Test 3: Auto-complete Suggestions
    console.log('3. Testing GET /api/v1/operations/autocomplete/rate');
    const autocompleteResponse = await fetch(`${BASE_URL}/api/v1/operations/autocomplete/rate?value=12&context=${encodeURIComponent(JSON.stringify(testLane))}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (autocompleteResponse.ok) {
      const autocompleteData = await autocompleteResponse.json();
      console.log('✅ Auto-complete suggestions retrieved successfully');
      console.log(`   - ${autocompleteData.suggestions.length} suggestions for rate field`);
      if (autocompleteData.suggestions.length > 0) {
        console.log(`   - Suggestions: ${autocompleteData.suggestions.join(', ')}`);
      }
    } else {
      console.log('❌ Failed to get auto-complete suggestions:', autocompleteResponse.status, autocompleteResponse.statusText);
      const errorText = await autocompleteResponse.text();
      console.log('   Error details:', errorText);
    }

    console.log('');

    // Test 4: Record Feedback (simulate)
    console.log('4. Testing POST /api/v1/operations/feedback/test-order-id');
    const feedbackResponse = await fetch(`${BASE_URL}/api/v1/operations/feedback/test-order-id`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        suggestions: [
          {
            type: 'rate',
            suggestion: { rate: 1200 },
            confidence: 0.8,
            reasoning: 'Test suggestion',
            source: 'historical'
          }
        ],
        actualValues: testOrderData,
        orderContext: testLane
      })
    });

    if (feedbackResponse.ok) {
      const feedbackData = await feedbackResponse.json();
      console.log('✅ Feedback recorded successfully');
      console.log(`   - Success: ${feedbackData.success}`);
      console.log(`   - Message: ${feedbackData.message}`);
    } else {
      console.log('❌ Failed to record feedback:', feedbackResponse.status, feedbackResponse.statusText);
      const errorText = await feedbackResponse.text();
      console.log('   Error details:', errorText);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

async function testPatternAnalysis() {
  console.log('\n📊 Testing Pattern Analysis...\n');

  try {
    // Test lane pattern analysis by checking if lanes endpoint includes frequency data
    console.log('1. Testing lane frequency analysis via /api/v1/operations/lanes');
    const lanesResponse = await fetch(`${BASE_URL}/api/v1/operations/lanes`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (lanesResponse.ok) {
      const lanesData = await lanesResponse.json();
      console.log('✅ Lanes data retrieved successfully');
      console.log(`   - ${lanesData.lanes.length} lanes found`);
      
      if (lanesData.lanes.length > 0) {
        const sampleLane = lanesData.lanes[0];
        console.log('   - Sample lane analysis:');
        console.log(`     Origin: ${sampleLane.originCity}, ${sampleLane.originState}`);
        console.log(`     Destination: ${sampleLane.destinationCity}, ${sampleLane.destinationState}`);
        console.log(`     Frequency rank: ${sampleLane.frequencyRank}`);
        console.log(`     Estimated miles: ${sampleLane.estimatedMiles}`);
        console.log(`     Estimated duration: ${sampleLane.estimatedDuration}`);
      }
    } else {
      console.log('❌ Failed to get lanes data:', lanesResponse.status, lanesResponse.statusText);
    }

  } catch (error) {
    console.error('❌ Pattern analysis test failed:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Smart Order Assistant Tests\n');
  console.log('=' .repeat(50));
  
  await testSmartSuggestions();
  await testPatternAnalysis();
  
  console.log('\n' + '='.repeat(50));
  console.log('✨ Smart Order Assistant Tests Complete!');
  console.log('\nNext Steps:');
  console.log('1. Start the API server: cd apps/api && npm run dev');
  console.log('2. Update TEST_TOKEN with a valid JWT token');
  console.log('3. Run this script: node test-smart-suggestions.js');
  console.log('4. Test the frontend at: http://localhost:3000/org/[orgId]/operations');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testSmartSuggestions,
  testPatternAnalysis,
  runAllTests
}; 