-- Create<PERSON>num
CREATE TYPE "Role" AS ENUM ('CARRIER', 'ADMIN');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "LoadStatus" AS ENUM ('New Order', 'Available', 'Assigned', 'Invoiced', 'On Hold', 'Cancelled', 'Delivered / Empty');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "auth0_user_id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "role" "Role" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "carrier_profiles" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "company_name" TEXT,
    "dot_number" TEXT,
    "mc_number" TEXT,
    "phone_number" TEXT,
    "equipment_types" JSONB,
    "serviceable_regions" JSONB,
    "is_verified_by_admin" BOOLEAN NOT NULL DEFAULT false,
    "admin_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "carrier_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "loads" (
    "id" TEXT NOT NULL,
    "airtable_record_id" TEXT NOT NULL,
    "origin_city" TEXT,
    "origin_state" TEXT,
    "destination_city" TEXT,
    "destination_state" TEXT,
    "pickup_date_utc" TIMESTAMP(3),
    "delivery_date_utc" TIMESTAMP(3),
    "equipment_required" TEXT,
    "weight_lbs" INTEGER,
    "rate" DOUBLE PRECISION,
    "status" "LoadStatus",
    "temperature" TEXT,
    "awarded_to_carrier_profile_id" TEXT,
    "raw_airtable_data" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "loads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bids" (
    "id" TEXT NOT NULL,
    "load_id" TEXT NOT NULL,
    "carrier_profile_id" TEXT NOT NULL,
    "bid_amount" DOUBLE PRECISION NOT NULL,
    "carrier_notes" TEXT,
    "status" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "bids_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_auth0_user_id_key" ON "users"("auth0_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "carrier_profiles_user_id_key" ON "carrier_profiles"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "loads_airtable_record_id_key" ON "loads"("airtable_record_id");

-- CreateIndex
CREATE UNIQUE INDEX "bids_load_id_carrier_profile_id_key" ON "bids"("load_id", "carrier_profile_id");

-- AddForeignKey
ALTER TABLE "carrier_profiles" ADD CONSTRAINT "carrier_profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loads" ADD CONSTRAINT "loads_awarded_to_carrier_profile_id_fkey" FOREIGN KEY ("awarded_to_carrier_profile_id") REFERENCES "carrier_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bids" ADD CONSTRAINT "bids_load_id_fkey" FOREIGN KEY ("load_id") REFERENCES "loads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bids" ADD CONSTRAINT "bids_carrier_profile_id_fkey" FOREIGN KEY ("carrier_profile_id") REFERENCES "carrier_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
