'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  Settings,
  HelpCircle,
  Menu,
  X,
  User,
  LogOut,
  ChevronDown,
  Bell,
  Gavel,
  Plus,
} from 'lucide-react';
import { HelpModal } from '@/components/ui/help-modal';
import { SimpleThemeToggle } from '@/components/theme-toggle';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/auth-context';
// Real-time notifications now handled by N8N via email/SMS
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { NotificationCenter } from '@/components/notifications/NotificationCenter';

interface NavItem {
  linkPath: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
}

interface TopNavigationProps {
  navItems: NavItem[];
  settingsHref: string;
  isCheckingAdmin: boolean;
  hasCompletedTour: boolean;
  startTour: () => void;
}

const TopNavigation: React.FC<TopNavigationProps> = ({
  navItems,
  settingsHref,
  isCheckingAdmin,
  hasCompletedTour,
  startTour
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuth();
  
  // Real-time notifications now handled by N8N via email/SMS
  // Mock state for UI consistency
  const adminBiddingState = {
    pendingBidsCount: 0,
    urgentBids: []
  };
  const wsConnected = false;
  const unreadCount = 0;

  // Scroll detection for backdrop enhancement
  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = () => {
    logout();
    router.push('/sign-in');
  };

  // Helper function to check if navigation item is active
  const isNavItemActive = (href: string, pathname: string) => {
    // Handle root/dashboard routes
    if (href === '/') {
      return pathname === '/' || pathname === '/dashboard';
    }
    
    // Handle exact route matches
    return pathname === href || pathname.startsWith(href + '/');
  };

  const NavItemComponent = ({ item, isMobile = false }: { item: NavItem; isMobile?: boolean }) => {
    const Icon = item.icon;
    const isActive = isNavItemActive(item.href, pathname);
    
    return (
      <Link
        href={item.href}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium nav-item-micro",
          "hover:bg-accent hover:text-accent-foreground",
          isActive 
            ? "bg-primary/10 text-primary border border-primary/20 nav-item-active" 
            : "text-muted-foreground hover:text-foreground",
          isMobile ? "w-full justify-start" : ""
        )}
        onClick={() => isMobile && setIsMobileMenuOpen(false)}
      >
        <Icon className="h-4 w-4 flex-shrink-0" />
        <span className={isMobile ? "" : "hidden sm:inline"}>{item.label}</span>
        {isActive && !isMobile && (
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
        )}
      </Link>
    );
  };

  // Admin Real-Time Bid Counter Component
  const AdminBidCounter = () => {
    if (user?.role !== 'ADMIN') return null;

    const pendingCount = adminBiddingState.pendingBidsCount;
    const urgentCount = adminBiddingState.urgentBids.length;
    const hasActivity = pendingCount > 0 || urgentCount > 0;

    return (
      <Link
        href="/admin"
        className={cn(
          "relative flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
          "hover:bg-accent hover:text-accent-foreground",
          pathname.includes('/admin') 
            ? "bg-primary/10 text-primary border border-primary/20" 
            : "text-muted-foreground hover:text-foreground",
          hasActivity && "ring-2 ring-orange-200 bg-orange-50 dark:bg-orange-950 dark:ring-orange-800"
        )}
      >
        <Gavel className={cn(
          "h-4 w-4",
          urgentCount > 0 && "text-red-500 animate-pulse"
        )} />
        <span className="hidden sm:inline">Admin</span>
        
        {/* Pending Bids Counter */}
        {pendingCount > 0 && (
          <Badge 
            variant={urgentCount > 0 ? "destructive" : "secondary"} 
            className={cn(
              "text-xs min-w-[20px] h-5 flex items-center justify-center",
              urgentCount > 0 && "animate-pulse"
            )}
          >
            {pendingCount > 99 ? '99+' : pendingCount}
          </Badge>
        )}

        {/* Connection Status Indicator */}
        <div className="absolute -top-1 -right-1">
          {wsConnected ? (
            <div className="w-2 h-2 bg-green-400 rounded-full" title="Real-time updates active" />
          ) : (
            <div className="w-2 h-2 bg-gray-400 rounded-full" title="Offline mode" />
          )}
        </div>

        {/* Urgent Alert Indicator */}
        {urgentCount > 0 && (
          <div className="absolute -top-2 -left-2 w-3 h-3 bg-red-500 rounded-full animate-ping" />
        )}
      </Link>
    );
  };

  // Enhanced Notification Bell Component with Full Center Integration
  const NotificationBell = () => {
    return (
      <NotificationCenter 
        trigger={
          <Button variant="outline" size="sm" className="relative">
            <Bell className="h-4 w-4" />
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-2 -right-2 text-xs min-w-[18px] h-4 flex items-center justify-center"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
            {/* Show urgent indicator for admin users */}
            {user?.role === 'ADMIN' && adminBiddingState.urgentBids.length > 0 && (
              <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full animate-ping" />
            )}
          </Button>
        }
      />
    );
  };

  return (
    <>
      {/* Top Navigation Bar */}
      <header className={cn("fixed top-0 left-0 right-0 z-50 top-nav-backdrop", isScrolled && "scrolled")}>
        <div className="max-w-[1600px] mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            
            {/* Brand Section */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 nav-brand-logo rounded-lg flex items-center justify-center">
                  <span className="text-xs font-bold text-primary">FCP</span>
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-lg font-semibold text-foreground">FCP Carrier Portal</h1>
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center gap-1">
              {navItems.map((item) => (
                <div key={item.label} className="relative">
                  <NavItemComponent item={item} />
                </div>
              ))}
              
              {/* Admin functionality now handled via main navigation */}
              
              {/* Settings Link */}
              <Link
                href={settingsHref}
                className={cn(
                  "flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                  "text-muted-foreground hover:text-foreground hover:bg-accent",
                  pathname.includes('/settings') && "bg-primary/10 text-primary border border-primary/20"
                )}
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Settings</span>
              </Link>

              {isCheckingAdmin && (
                <div className="flex items-center gap-2 px-3 py-2">
                  <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>
                  <span className="text-sm text-muted-foreground hidden sm:inline">Loading...</span>
                </div>
              )}
            </nav>

            {/* Right Section */}
            <div className="flex items-center gap-3">
              
              {/* Notification Bell */}
              <NotificationBell />
              
              {/* Theme Toggle - Prominent Position */}
              <div className="flex">
                <SimpleThemeToggle />
              </div>

              {/* Help & Tour */}
              <div className="hidden md:flex items-center gap-2">
                <HelpModal
                  trigger={
                    <button className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors">
                      <HelpCircle className="h-4 w-4" />
                      <span className="hidden lg:inline">Help</span>
                    </button>
                  }
                />
                
                {!hasCompletedTour && (
                  <button
                    onClick={startTour}
                    className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 transition-colors"
                  >
                    <div className="h-4 w-4 rounded-full bg-blue-100 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                    </div>
                    <span className="hidden lg:inline">Take Tour</span>
                  </button>
                )}
              </div>

              {/* User Profile Dropdown */}
              <div className="hidden md:flex">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span className="hidden lg:inline">
                        {user?.firstName || 'User'}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuItem>
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push(settingsHref)}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    {/* Admin functionality now available via main navigation tabs */}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 text-muted-foreground hover:text-foreground"
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="max-w-[1600px] mx-auto px-4 py-4">
              <nav className="flex flex-col gap-2">
                {navItems.map((item) => (
                  <NavItemComponent key={item.label} item={item} isMobile={true} />
                ))}
                
                {/* Admin functionality now handled via main navigation */}
                
                <Link
                  href={settingsHref}
                  className={cn(
                    "flex items-center gap-2 w-full px-3 py-2 rounded-lg text-sm font-medium",
                    "text-muted-foreground hover:text-foreground hover:bg-accent",
                    pathname.includes('/settings') && "bg-primary/10 text-primary border border-primary/20"
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </nav>
            </div>
          </div>
        )}
      </header>
    </>
  );
};

export default TopNavigation; 