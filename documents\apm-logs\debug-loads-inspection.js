const { PrismaClient } = require('@repo/db');

async function inspectLoads() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 LOADS DATABASE INSPECTION');
    console.log('=============================\n');
    
    // 1. Check total loads count
    const totalLoads = await prisma.load.count();
    console.log(`📊 Total loads in database: ${totalLoads}`);
    
    // 2. Check recent loads (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const recentLoads = await prisma.load.findMany({
      where: {
        createdAt: {
          gte: yesterday
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10,
      select: {
        id: true,
        airtableRecordId: true,
        proNumber: true,
        status: true,
        isPublic: true,
        isTargeted: true,
        targetOrganizations: true,
        originCity: true,
        originState: true,
        destinationCity: true,
        destinationState: true,
        rate: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    console.log(`\n📅 Recent loads (last 24h): ${recentLoads.length}`);
    
    if (recentLoads.length > 0) {
      console.log('\n🔍 RECENT LOAD DETAILS:');
      recentLoads.forEach((load, index) => {
        console.log(`\n--- Load ${index + 1} ---`);
        console.log(`ID: ${load.id}`);
        console.log(`Airtable ID: ${load.airtableRecordId}`);
        console.log(`Pro Number: ${load.proNumber || 'N/A'}`);
        console.log(`Status: ${load.status || 'N/A'}`);
        console.log(`Origin: ${load.originCity || 'N/A'}, ${load.originState || 'N/A'}`);
        console.log(`Destination: ${load.destinationCity || 'N/A'}, ${load.destinationState || 'N/A'}`);
        console.log(`Rate: $${load.rate || 'N/A'}`);
        console.log(`Is Public: ${load.isPublic}`);
        console.log(`Is Targeted: ${load.isTargeted}`);
        console.log(`Target Organizations: ${JSON.stringify(load.targetOrganizations)}`);
        console.log(`Created: ${load.createdAt}`);
        console.log(`Updated: ${load.updatedAt}`);
      });
    }
    
    // 3. Check loads by status
    const statusCounts = await prisma.load.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    });
    
    console.log('\n📋 LOADS BY STATUS:');
    statusCounts.forEach(group => {
      console.log(`${group.status || 'NULL'}: ${group._count.status} loads`);
    });
    
    // 4. Check available loads (matching API filter)
    const availableLoads = await prisma.load.findMany({
      where: {
        status: {
          in: ['Available', 'Booking Requested']
        }
      },
      select: {
        id: true,
        airtableRecordId: true,
        proNumber: true,
        status: true,
        isPublic: true,
        isTargeted: true,
        targetOrganizations: true,
        rate: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });
    
    console.log(`\n✅ Available loads (Status = 'Available' OR 'Booking Requested'): ${availableLoads.length}`);
    
    if (availableLoads.length > 0) {
      console.log('\n🔍 AVAILABLE LOAD SAMPLE:');
      availableLoads.forEach((load, index) => {
        console.log(`\n--- Available Load ${index + 1} ---`);
        console.log(`Airtable ID: ${load.airtableRecordId}`);
        console.log(`Pro Number: ${load.proNumber || 'N/A'}`);
        console.log(`Status: ${load.status}`);
        console.log(`Rate: $${load.rate || 'N/A'}`);
        console.log(`Is Public: ${load.isPublic}`);
        console.log(`Target Orgs: ${JSON.stringify(load.targetOrganizations)}`);
        console.log(`Created: ${load.createdAt}`);
      });
    }
    
    // 5. Check users for organization filtering context
    const users = await prisma.user.findMany({
      select: {
        id: true,
        clerkUserId: true,
        orgName: true,
        role: true,
        email: true
      },
      take: 5
    });
    
    console.log(`\n👥 USERS (for organization context): ${users.length} total`);
    users.forEach(user => {
      console.log(`- ${user.email || 'No email'} (${user.role}) - Org: "${user.orgName || 'No org'}"`);
    });
    
    console.log('\n🎯 DEBUGGING RECOMMENDATIONS:');
    if (totalLoads === 0) {
      console.log('❌ No loads found in database - webhook processing may be failing');
    } else if (availableLoads.length === 0) {
      console.log('❌ No available loads found - check status filtering logic');
    } else {
      console.log('✅ Available loads exist - issue may be in API response or frontend');
    }
    
  } catch (error) {
    console.error('❌ Database inspection failed:', error);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

inspectLoads().catch(console.error); 