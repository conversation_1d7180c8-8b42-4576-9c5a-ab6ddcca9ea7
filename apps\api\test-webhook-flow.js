const { PrismaClient } = require('@repo/db');

async function testWebhookFlow() {
  const prisma = new PrismaClient();
  
  console.log('🔄 TESTING WEBHOOK DATA FLOW');
  console.log('=============================\n');
  
  try {
    // Test 1: Check if loads in database have fresh timestamps (indicating recent webhook activity)
    console.log('🔍 Test 1: Recent webhook activity analysis...');
    
    const recentLoads = await prisma.load.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      select: {
        id: true,
        airtableRecordId: true,
        proNumber: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });
    
    console.log(`📊 Loads created in last 7 days: ${recentLoads.length}`);
    
    if (recentLoads.length > 0) {
      console.log('\n📋 Recent load creation timestamps:');
      recentLoads.forEach((load, index) => {
        console.log(`${index + 1}. ${load.proNumber} (${load.airtableRecordId}) - Created: ${load.createdAt}`);
      });
    }
    
    // Test 2: Check for loads with recent updates (webhook modifications)
    console.log('\n🔍 Test 2: Recent load updates...');
    
    const recentUpdates = await prisma.load.findMany({
      where: {
        updatedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      select: {
        id: true,
        airtableRecordId: true,
        proNumber: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: 5
    });
    
    console.log(`📊 Loads updated in last 24 hours: ${recentUpdates.length}`);
    
    // Test 3: Sample a few available loads to check their data quality
    console.log('\n🔍 Test 3: Available load data quality check...');
    
    const sampleLoads = await prisma.load.findMany({
      where: {
        status: 'AVAILABLE'
      },
      select: {
        id: true,
        airtableRecordId: true,
        proNumber: true,
        status: true,
        isPublic: true,
        rate: true,
        originCity: true,
        originState: true,
        destinationCity: true,
        destinationState: true,
        createdAt: true,
        updatedAt: true,
        rawAirtableData: true
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: 3
    });
    
    console.log(`📊 Sample available loads for analysis: ${sampleLoads.length}`);
    
    if (sampleLoads.length > 0) {
      console.log('\n📋 Sample load data:');
      sampleLoads.forEach((load, index) => {
        console.log(`\n--- Load ${index + 1} ---`);
        console.log(`Airtable ID: ${load.airtableRecordId}`);
        console.log(`Pro Number: ${load.proNumber || 'MISSING'}`);
        console.log(`Status: ${load.status}`);
        console.log(`Rate: $${load.rate || 'MISSING'}`);
        console.log(`Origin: ${load.originCity || 'MISSING'}, ${load.originState || 'MISSING'}`);
        console.log(`Destination: ${load.destinationCity || 'MISSING'}, ${load.destinationState || 'MISSING'}`);
        console.log(`Is Public: ${load.isPublic}`);
        console.log(`Created: ${load.createdAt}`);
        console.log(`Updated: ${load.updatedAt}`);
        console.log(`Has Raw Data: ${load.rawAirtableData ? 'YES' : 'NO'}`);
      });
    }
    
    // Test 4: Check for common data issues
    console.log('\n🔍 Test 4: Data quality issues...');
    
    const missingRates = await prisma.load.count({
      where: {
        status: 'AVAILABLE',
        OR: [
          { rate: null },
          { rate: 0 }
        ]
      }
    });
    
    const missingLocations = await prisma.load.count({
      where: {
        status: 'AVAILABLE',
        OR: [
          { originCity: null },
          { destinationCity: null }
        ]
      }
    });
    
    console.log(`📊 Available loads missing rates: ${missingRates}`);
    console.log(`📊 Available loads missing location data: ${missingLocations}`);
    
    console.log('\n🎯 WEBHOOK FLOW ANALYSIS:');
    
    if (recentLoads.length === 0) {
      console.log('❌ No recent loads created - webhook may not be triggering');
      console.log('🔧 Check: Airtable automation configuration');
    } else {
      console.log('✅ Recent loads found - webhook is likely working');
    }
    
    if (missingRates > 0 || missingLocations > 0) {
      console.log('⚠️  Data quality issues found - webhook may not be mapping all fields correctly');
    } else {
      console.log('✅ Data quality looks good');
    }
    
    console.log('\n📋 RECOMMENDED NEXT STEPS:');
    console.log('1. ✅ Database has loads - webhook flow is working');
    console.log('2. ❌ Fix getAvailableLoads() to query database instead of Airtable');
    console.log('3. ⚡ This will make loads appear instantly when synced from Airtable');
    
  } catch (error) {
    console.error('❌ Webhook flow test failed:', error);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

testWebhookFlow(); 