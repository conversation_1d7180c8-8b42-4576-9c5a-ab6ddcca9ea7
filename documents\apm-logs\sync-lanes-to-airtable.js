// Sync Lanes to Airtable - Create Lane Library Table
// This script fetches lanes from the application database and pushes them to Airtable

const fs = require('fs');
const path = require('path');
const Airtable = require('airtable');
const { PrismaClient } = require('@prisma/client');

// Load environment variables from multiple possible locations
function loadEnvFile() {
  let envVars = {};
  
  // Try multiple environment file locations
  const envFiles = [
    path.join(__dirname, '.env'),
    path.join(__dirname, 'apps/api/.env.local'),
    path.join(__dirname, 'apps/api/.env'),
    path.join(__dirname, '.env.local')
  ];
  
  for (const envPath of envFiles) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      console.log(`📄 Loading environment from: ${envPath}`);
      
      envContent.split('\n').forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=');
            // Remove surrounding quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) || 
                (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }
            envVars[key] = value;
          }
        }
      });
      
      // If we found required vars, we can stop looking
      if (envVars.DATABASE_URL && envVars.AIRTABLE_API_KEY) {
        break;
      }
      
    } catch (error) {
      // File doesn't exist, continue to next
    }
  }
  
  // Use system environment variables as fallback
  if (!envVars.DATABASE_URL) {
    envVars.DATABASE_URL = process.env.DATABASE_URL;
  }
  if (!envVars.AIRTABLE_API_KEY) {
    envVars.AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
  }
  if (!envVars.AIRTABLE_BASE_ID) {
    envVars.AIRTABLE_BASE_ID = process.env.AIRTABLE_BASE_ID;
  }
  
  return envVars;
}

// State-to-state distance estimation (for quick calculations)
function calculateQuickDistance(originState, destinationState) {
  // Simplified state center coordinates for distance calculation
  const stateCoords = {
    'AL': { lat: 32.377716, lng: -86.300568 },
    'AK': { lat: 64.068, lng: -152.2782 },
    'AZ': { lat: 34.048927, lng: -111.093735 },
    'AR': { lat: 34.736009, lng: -92.331122 },
    'CA': { lat: 36.778261, lng: -119.417932 },
    'CO': { lat: 39.550051, lng: -105.782067 },
    'CT': { lat: 41.767, lng: -72.677 },
    'DE': { lat: 39.161921, lng: -75.526755 },
    'FL': { lat: 27.766279, lng: -81.686783 },
    'GA': { lat: 33.76, lng: -84.39 },
    'HI': { lat: 21.30895, lng: -157.826182 },
    'ID': { lat: 44.2394, lng: -114.5103 },
    'IL': { lat: 40.349457, lng: -88.986137 },
    'IN': { lat: 39.790942, lng: -86.147685 },
    'IA': { lat: 42.032974, lng: -93.581543 },
    'KS': { lat: 38.572954, lng: -98.580696 },
    'KY': { lat: 37.608, lng: -84.86311 },
    'LA': { lat: 30.45809, lng: -91.140229 },
    'ME': { lat: 45.367584, lng: -68.972168 },
    'MD': { lat: 39.045923, lng: -76.641271 },
    'MA': { lat: 42.2352, lng: -71.0275 },
    'MI': { lat: 44.182205, lng: -84.506836 },
    'MN': { lat: 46.39241, lng: -94.63623 },
    'MS': { lat: 32.354668, lng: -89.398528 },
    'MO': { lat: 38.572954, lng: -92.60376 },
    'MT': { lat: 47.042418, lng: -109.633835 },
    'NE': { lat: 41.492537, lng: -99.901813 },
    'NV': { lat: 38.544907, lng: -117.022027 },
    'NH': { lat: 43.220093, lng: -71.549896 },
    'NJ': { lat: 40.221741, lng: -74.756138 },
    'NM': { lat: 34.307144, lng: -106.018066 },
    'NY': { lat: 42.659829, lng: -75.615 },
    'NC': { lat: 35.771, lng: -78.638 },
    'ND': { lat: 47.650589, lng: -100.437012 },
    'OH': { lat: 40.367474, lng: -82.996216 },
    'OK': { lat: 35.482309, lng: -97.534994 },
    'OR': { lat: 44.931109, lng: -123.029159 },
    'PA': { lat: 40.269789, lng: -76.875613 },
    'RI': { lat: 41.82355, lng: -71.422132 },
    'SC': { lat: 33.836082, lng: -81.163727 },
    'SD': { lat: 44.367966, lng: -100.336378 },
    'TN': { lat: 35.771, lng: -86.25 },
    'TX': { lat: 31.106, lng: -97.6475 },
    'UT': { lat: 39.161921, lng: -111.313726 },
    'VT': { lat: 44.26639, lng: -72.580536 },
    'VA': { lat: 37.54, lng: -78.83 },
    'WA': { lat: 47.042418, lng: -122.893077 },
    'WV': { lat: 38.349497, lng: -81.633294 },
    'WI': { lat: 44.78444, lng: -89.814278 },
    'WY': { lat: 43.07424, lng: -107.290284 }
  };

  const origin = stateCoords[originState];
  const destination = stateCoords[destinationState];

  if (!origin || !destination) {
    return 500; // Default fallback distance
  }

  // Calculate distance using Haversine formula
  const R = 3959; // Earth's radius in miles
  const dLat = (destination.lat - origin.lat) * Math.PI / 180;
  const dLng = (destination.lng - origin.lng) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(origin.lat * Math.PI / 180) * Math.cos(destination.lat * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return Math.round(distance);
}

// Calculate estimated duration based on miles
function calculateEstimatedDuration(miles) {
  const hoursPerDay = 11; // DOT driving hours limit
  const avgSpeedMph = 55; // Average highway speed including stops
  
  const totalHours = miles / avgSpeedMph;
  const days = Math.ceil(totalHours / hoursPerDay);
  
  if (days === 1) {
    return `${Math.ceil(totalHours)} hours`;
  } else {
    return `${days} days`;
  }
}

// Sample lanes (from app's screenshots and common freight corridors)
function generateSampleLanes() {
  console.log('🎯 Generating sample lanes (matching your app display)...');
  
  const sampleLaneData = [
    // From the screenshots you shared
    { originCity: 'Chicopee', originState: 'MA', destinationCity: 'Londonderry', destinationState: 'NH', loadCount: 12 },
    { originCity: 'Chicopee', originState: 'MA', destinationCity: 'Montgomery', destinationState: 'NY', loadCount: 8 },
    { originCity: 'Edwardsville', originState: 'KS', destinationCity: 'Rialto', destinationState: 'CA', loadCount: 15 },
    { originCity: 'Edwardsville', originState: 'KS', destinationCity: 'Cedar Falls', destinationState: 'IA', loadCount: 6 },
    
    // Additional common freight lanes
    { originCity: 'Atlanta', originState: 'GA', destinationCity: 'Miami', destinationState: 'FL', loadCount: 25 },
    { originCity: 'Chicago', originState: 'IL', destinationCity: 'Dallas', destinationState: 'TX', loadCount: 30 },
    { originCity: 'Los Angeles', originState: 'CA', destinationCity: 'Phoenix', destinationState: 'AZ', loadCount: 22 },
    { originCity: 'New York', originState: 'NY', destinationCity: 'Boston', destinationState: 'MA', loadCount: 18 },
    { originCity: 'Denver', originState: 'CO', destinationCity: 'Salt Lake City', destinationState: 'UT', loadCount: 10 },
    { originCity: 'Houston', originState: 'TX', destinationCity: 'New Orleans', destinationState: 'LA', loadCount: 14 },
    { originCity: 'Seattle', originState: 'WA', destinationCity: 'Portland', destinationState: 'OR', loadCount: 16 },
    { originCity: 'Detroit', originState: 'MI', destinationCity: 'Cleveland', destinationState: 'OH', loadCount: 11 },
    { originCity: 'Memphis', originState: 'TN', destinationCity: 'Nashville', destinationState: 'TN', loadCount: 13 },
    { originCity: 'Kansas City', originState: 'MO', destinationCity: 'Oklahoma City', destinationState: 'OK', loadCount: 9 },
    { originCity: 'Las Vegas', originState: 'NV', destinationCity: 'San Diego', destinationState: 'CA', loadCount: 17 },
    
    // Cross-country high-volume lanes
    { originCity: 'Los Angeles', originState: 'CA', destinationCity: 'Atlanta', destinationState: 'GA', loadCount: 35 },
    { originCity: 'New York', originState: 'NY', destinationCity: 'Los Angeles', destinationState: 'CA', loadCount: 32 },
    { originCity: 'Chicago', originState: 'IL', destinationCity: 'Los Angeles', destinationState: 'CA', loadCount: 28 },
    { originCity: 'Miami', originState: 'FL', destinationCity: 'New York', destinationState: 'NY', loadCount: 24 },
    { originCity: 'Seattle', originState: 'WA', destinationCity: 'Los Angeles', destinationState: 'CA', loadCount: 20 },
    
    // Regional short-haul lanes
    { originCity: 'Philadelphia', originState: 'PA', destinationCity: 'Baltimore', destinationState: 'MD', loadCount: 19 },
    { originCity: 'Dallas', originState: 'TX', destinationCity: 'Houston', destinationState: 'TX', loadCount: 26 },
    { originCity: 'San Francisco', originState: 'CA', destinationCity: 'San Jose', destinationState: 'CA', loadCount: 21 },
    { originCity: 'Charlotte', originState: 'NC', destinationCity: 'Raleigh', destinationState: 'NC', loadCount: 12 },
    { originCity: 'Jacksonville', originState: 'FL', destinationCity: 'Tampa', destinationState: 'FL', loadCount: 15 }
  ];

  const lanes = sampleLaneData.map((rawLane, index) => {
    const frequency = rawLane.loadCount;
    const lastUsed = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(); // Random date within last 30 days
    const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
    
    // Use quick state-to-state distance calculation
    const estimatedMiles = calculateQuickDistance(rawLane.originState, rawLane.destinationState);
    const estimatedDuration = calculateEstimatedDuration(estimatedMiles);
    const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 5), 1), 10); // Scale to 1-10

    return {
      id: laneId,
      originCity: rawLane.originCity,
      originState: rawLane.originState,
      destinationCity: rawLane.destinationCity,
      destinationState: rawLane.destinationState,
      estimatedMiles,
      estimatedDuration,
      frequencyRank,
      lastUsed,
      loadCount: frequency
    };
  });

  // Sort by frequency rank (highest first), then alphabetically
  return lanes.sort((a, b) => {
    if (b.frequencyRank !== a.frequencyRank) {
      return b.frequencyRank - a.frequencyRank;
    }
    return a.originCity.localeCompare(b.originCity);
  });
}

// Generate lanes from database load combinations (same logic as in OperationsService)
async function generateLanesFromDatabase(prisma) {
  console.log('🔍 Generating lanes from database load combinations...');

  const rawLanes = await prisma.load.groupBy({
    by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
    where: {
      AND: [
        { originCity: { not: null } },
        { originState: { not: null } },
        { destinationCity: { not: null } },
        { destinationState: { not: null } }
      ]
    },
    _count: { id: true },
    _max: { createdAt: true }
  });

  console.log(`Found ${rawLanes.length} unique lane combinations from historical data`);

  const lanes = rawLanes.map((rawLane, index) => {
    const frequency = rawLane._count.id;
    const lastUsed = rawLane._max.createdAt?.toISOString();
    const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
    
    // Use quick state-to-state distance calculation
    const estimatedMiles = calculateQuickDistance(rawLane.originState, rawLane.destinationState);
    const estimatedDuration = calculateEstimatedDuration(estimatedMiles);
    const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);

    return {
      id: laneId,
      originCity: rawLane.originCity,
      originState: rawLane.originState,
      destinationCity: rawLane.destinationCity,
      destinationState: rawLane.destinationState,
      estimatedMiles,
      estimatedDuration,
      frequencyRank,
      lastUsed,
      loadCount: frequency
    };
  });

  // Sort by frequency rank (highest first), then alphabetically
  return lanes.sort((a, b) => {
    if (b.frequencyRank !== a.frequencyRank) {
      return b.frequencyRank - a.frequencyRank;
    }
    return a.originCity.localeCompare(b.originCity);
  });
}

// Check if "Lane Library" table exists in Airtable
async function checkLaneLibraryTable(base) {
  try {
    // Try to fetch a record from "Lane Library" table to see if it exists
    await base('Lane Library').select({ maxRecords: 1 }).firstPage();
    return true;
  } catch (error) {
    if (error.statusCode === 404 || error.message.includes('TABLE_NOT_FOUND')) {
      return false;
    }
    throw error; // Re-throw if it's a different error
  }
}

// Create Lane Library table structure (this will be manual for now since Airtable doesn't allow table creation via API)
function printTableCreationInstructions() {
  console.log('\n📋 LANE LIBRARY TABLE CREATION INSTRUCTIONS:');
  console.log('============================================');
  console.log('Since Airtable doesn\'t allow table creation via API, please:');
  console.log('');
  console.log('1. Go to your Airtable base in the browser');
  console.log('2. Create a new table called "Lane Library"');
  console.log('3. Add the following fields:');
  console.log('   - Lane ID (Single line text, Primary field)');
  console.log('   - Origin City (Single line text)');
  console.log('   - Origin State (Single select)');
  console.log('   - Destination City (Single line text)');
  console.log('   - Destination State (Single select)');
  console.log('   - Estimated Miles (Number)');
  console.log('   - Estimated Duration (Single line text)');
  console.log('   - Frequency Rank (Number, 1-10 scale)');
  console.log('   - Load Count (Number)');
  console.log('   - Last Used (Date)');
  console.log('   - Lane Route (Formula: {Origin City} & ", " & {Origin State} & " → " & {Destination City} & ", " & {Destination State})');
  console.log('   - Created At (Created time)');
  console.log('   - Updated At (Last modified time)');
  console.log('');
  console.log('4. Run this script again after creating the table');
  console.log('');
}

// Push lanes to Airtable "Lane Library" table
async function pushLanesToAirtable(lanes, base) {
  console.log(`\n📤 Pushing ${lanes.length} lanes to Airtable "Lane Library" table...`);
  
  const BATCH_SIZE = 10; // Airtable allows max 10 records per batch
  let successCount = 0;
  let errorCount = 0;
  const errors = [];

  for (let i = 0; i < lanes.length; i += BATCH_SIZE) {
    const batch = lanes.slice(i, i + BATCH_SIZE);
    
    try {
      const records = batch.map(lane => ({
        fields: {
          'Lane ID': lane.id,
          'Origin City': lane.originCity,
          'Origin State': lane.originState,
          'Destination City': lane.destinationCity,
          'Destination State': lane.destinationState,
          'Estimated Miles': lane.estimatedMiles,
          'Estimated Duration': lane.estimatedDuration,
          'Frequency Rank': lane.frequencyRank,
          'Load Count': lane.loadCount,
          'Last Used': lane.lastUsed ? new Date(lane.lastUsed).toISOString().split('T')[0] : null
        }
      }));

      await base('Lane Library').create(records);
      successCount += batch.length;
      
      console.log(`   ✅ Batch ${Math.floor(i / BATCH_SIZE) + 1}: Added ${batch.length} lanes`);
      
      // Rate limiting - wait between batches
      if (i + BATCH_SIZE < lanes.length) {
        await new Promise(resolve => setTimeout(resolve, 200)); // 200ms delay
      }
      
    } catch (error) {
      errorCount += batch.length;
      errors.push({ batch: Math.floor(i / BATCH_SIZE) + 1, error: error.message });
      console.log(`   ❌ Batch ${Math.floor(i / BATCH_SIZE) + 1}: Failed - ${error.message}`);
    }
  }

  console.log(`\n📊 SYNC RESULTS:`);
  console.log(`✅ Successfully added: ${successCount} lanes`);
  console.log(`❌ Failed: ${errorCount} lanes`);
  
  if (errors.length > 0) {
    console.log('\n🔍 Error Details:');
    errors.forEach(({ batch, error }) => {
      console.log(`   Batch ${batch}: ${error}`);
    });
  }

  return { successCount, errorCount, errors };
}

// Main function
async function syncLanesToAirtable() {
  console.log('🚀 LANES TO AIRTABLE SYNC');
  console.log('========================\n');
  
  try {
    // Load environment variables
    const envVars = loadEnvFile();
    
    const databaseUrl = envVars.DATABASE_URL;
    const airtableApiKey = envVars.AIRTABLE_API_KEY;
    const airtableBaseId = envVars.AIRTABLE_BASE_ID;
    
    console.log('🔑 Configuration:');
    console.log(`- Database URL: ${databaseUrl ? 'Found' : 'Missing'}`);
    console.log(`- Airtable API Key: ${airtableApiKey ? 'Found' : 'Missing'}`);
    console.log(`- Airtable Base ID: ${airtableBaseId ? 'Found' : 'Missing'}\n`);
    
    if (!airtableApiKey || !airtableBaseId) {
      console.log('❌ Missing Airtable configuration (AIRTABLE_API_KEY and AIRTABLE_BASE_ID required)');
      return;
    }
    
    // Initialize Airtable
    const airtable = new Airtable({ apiKey: airtableApiKey });
    const base = airtable.base(airtableBaseId);
    
    let lanes = [];
    
    // Try to get lanes from database first
    if (databaseUrl) {
      try {
        const prisma = new PrismaClient({
          datasources: {
            db: {
              url: databaseUrl
            }
          }
        });
        
        console.log('🔌 Testing database connection...');
        await prisma.$connect();
        console.log('✅ Database connected successfully');
        
        // Check load count
        const loadCount = await prisma.load.count();
        console.log(`📦 Total loads in database: ${loadCount}`);
        
        if (loadCount > 0) {
          // Generate lanes from database
          lanes = await generateLanesFromDatabase(prisma);
          console.log(`\n✅ Generated ${lanes.length} lanes from database`);
        } else {
          console.log('\n⚠️  No loads found in database, using sample lanes instead');
          lanes = generateSampleLanes();
        }
        
        await prisma.$disconnect();
        
      } catch (dbError) {
        console.log(`\n⚠️  Database connection failed: ${dbError.message}`);
        console.log('📄 Using sample lanes (same as shown in your app)');
        lanes = generateSampleLanes();
      }
    } else {
      console.log('\n📄 No database URL provided, using sample lanes');
      lanes = generateSampleLanes();
    }
    
    if (lanes.length === 0) {
      console.log('\n❌ No lanes could be generated');
      return;
    }
    
    console.log(`\n✅ Generated ${lanes.length} lanes total`);
    
    // Show sample lanes
    console.log('\n📋 Sample lanes to be synced:');
    lanes.slice(0, 5).forEach((lane, index) => {
      console.log(`   ${index + 1}. ${lane.originCity}, ${lane.originState} → ${lane.destinationCity}, ${lane.destinationState}`);
      console.log(`      Distance: ${lane.estimatedMiles} miles, Duration: ${lane.estimatedDuration}, Frequency: ${lane.frequencyRank}/10`);
    });
    
    if (lanes.length > 5) {
      console.log(`   ... and ${lanes.length - 5} more lanes`);
    }
    
    // Check if Lane Library table exists
    console.log('\n🔍 Checking if "Lane Library" table exists in Airtable...');
    const tableExists = await checkLaneLibraryTable(base);
    
    if (!tableExists) {
      console.log('❌ "Lane Library" table not found in Airtable');
      printTableCreationInstructions();
      return;
    }
    
    console.log('✅ "Lane Library" table found in Airtable');
    
    // Clear existing data (optional - ask user)
    console.log('\n🤔 Do you want to clear existing lanes in the "Lane Library" table first?');
    console.log('This script will proceed to add lanes. If you want to clear first, cancel and clear manually.');
    
    // Wait a bit for user to read
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Push lanes to Airtable
    const results = await pushLanesToAirtable(lanes, base);
    
    console.log('\n🎉 SYNC COMPLETED!');
    console.log(`✅ Successfully synced ${results.successCount} lanes to Airtable "Lane Library" table`);
    
    if (results.errorCount > 0) {
      console.log(`⚠️  ${results.errorCount} lanes failed to sync - check error details above`);
    }
    
    console.log('\n📋 Next Steps:');
    console.log('1. Go to your Airtable base and check the "Lane Library" table');
    console.log('2. You can now use this data for analysis, reporting, or integration with other tools');
    console.log('3. Consider setting up automation to keep this data updated as your load data changes');
    
  } catch (error) {
    console.error('💥 Error during sync:', error);
  }
}

// Run the sync
if (require.main === module) {
  syncLanesToAirtable().catch(console.error);
}

module.exports = { syncLanesToAirtable, generateLanesFromDatabase, generateSampleLanes }; 