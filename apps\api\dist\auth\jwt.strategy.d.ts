import { Strategy } from 'passport-jwt';
import { AuthService } from './auth.service';
import { N8NJwtPayload } from './authenticated-request.interface';
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private authService;
    constructor(authService: AuthService);
    validate(payload: N8NJwtPayload): Promise<any>;
}
export {};
