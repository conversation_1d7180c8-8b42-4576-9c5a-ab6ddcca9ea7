import { Injectable, OnModuleInit, OnModuleDestroy, <PERSON>gger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaClient } from '@repo/db';

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });

    // Event listeners removed for emergency compatibility
  }

  async onModuleInit() {
    try {
      this.logger.log('🔄 Connecting to database...');
      
      // Set connection timeout for serverless
      await Promise.race([
        this.$connect(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Database connection timeout')), 10000)
        )
      ]);
      
      this.logger.log('✅ Database connected successfully');
    } catch (error) {
      this.logger.error(`❌ Database connection failed: ${error.message}`);
      // Don't throw in serverless - allow the service to start
      if (!process.env.VERCEL) {
        throw error;
      }
    }
  }

  async onModuleDestroy() {
    try {
      this.logger.log('🔄 Disconnecting from database...');
      await this.$disconnect();
      this.logger.log('✅ Database disconnected successfully');
    } catch (error) {
      this.logger.error(`❌ Database disconnection failed: ${error.message}`);
      // Continue with shutdown process even if disconnect fails
    }
  }

  // 🔧 CONNECTION HEALTH CHECK
  async healthCheck(): Promise<boolean> {
    try {
      await Promise.race([
        this.$queryRaw`SELECT 1`,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Health check timeout')), 5000)
        )
      ]);
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error.message);
      return false;
    }
  }

  // 🔧 SAFE CONNECTION VERIFICATION
  async verifyConnection(): Promise<{ connected: boolean; error?: string }> {
    try {
      const result = await Promise.race([
        this.$queryRaw`SELECT 1 as test`,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Connection verification timeout')), 3000)
        )
      ]) as any[];
      
      if (result && result[0]?.test === 1) {
        return { connected: true };
      } else {
        return { connected: false, error: 'Unexpected query result' };
      }
    } catch (error) {
      return { connected: false, error: error.message };
    }
  }

  // 🔧 ENHANCED ERROR HANDLING WITH RETRY LOGIC
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        // Check if error is retryable - simple string-based check
        if (this.isRetryableError(error)) {
          this.logger.warn(`${operationName} failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
          
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
            this.logger.log(`Retrying ${operationName} in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }
        
        // If error is not retryable or max retries exceeded, throw
        this.logger.error(`${operationName} failed after ${attempt} attempts:`, error);
        throw this.enhanceError(error, operationName);
      }
    }
    
    throw this.enhanceError(lastError, operationName);
  }

  private isRetryableError(error: any): boolean {
    if (error && error.message) {
      const message = error.message.toLowerCase();
      // Check for retryable Prisma error codes or connection issues
      return message.includes('p1001') || 
             message.includes('p1002') || 
             message.includes('p2024') ||
             message.includes('connection') || 
             message.includes('timeout') || 
             message.includes('econnrefused') ||
             message.includes('network');
    }
    return false;
  }

  private enhanceError(error: any, operationName: string): Error {
    if (error && error.message) {
      const message = error.message.toLowerCase();
      
      if (message.includes('p1001') || message.includes('p1002')) {
        throw new HttpException(
          'Database connection error - please try again',
          HttpStatus.SERVICE_UNAVAILABLE
        );
      }
      if (message.includes('p2024')) {
        throw new HttpException(
          'Database timeout - please try again',
          HttpStatus.REQUEST_TIMEOUT
        );
      }
      if (message.includes('p2002')) {
        throw new HttpException(
          'Data conflict - record already exists',
          HttpStatus.CONFLICT
        );
      }
      if (message.includes('p2025')) {
        throw new HttpException(
          'Record not found',
          HttpStatus.NOT_FOUND
        );
      }
    }
    
    // For other errors, log and re-throw with generic message
    this.logger.error(`Error in ${operationName}:`, error);
    throw new HttpException(
      'Operation failed - please try again',
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  // 🔧 TRANSACTION WITH ERROR HANDLING
  async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>,
    operationName: string = 'transaction'
  ): Promise<T> {
    return this.executeWithRetry(
      () => this.$transaction(fn),
      operationName
    );
  }

  // 🔧 HEALTH CHECK ENDPOINT FOR MONITORING
  async getDetailedHealthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    database: {
      connected: boolean;
      responseTime?: number;
      error?: string;
    };
    timestamp: string;
  }> {
    const startTime = Date.now();
    
    try {
      await Promise.race([
        this.$queryRaw`SELECT 1 as health_check`,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Health check timeout')), 5000)
        )
      ]);
      
      const responseTime = Date.now() - startTime;
      
      return {
        status: responseTime > 3000 ? 'degraded' : 'healthy',
        database: {
          connected: true,
          responseTime
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      
      return {
        status: 'unhealthy',
        database: {
          connected: false,
          error: error.message
        },
        timestamp: new Date().toISOString()
      };
    }
  }
} 