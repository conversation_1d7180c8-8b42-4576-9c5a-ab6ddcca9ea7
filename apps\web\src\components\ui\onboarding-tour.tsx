'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  X,
  ArrowRight,
  ArrowLeft,
  Play,
  SkipForward,
  CheckCircle,
  User,
  Search,
  FileText,
  Settings,
  Eye,
  Navigation
} from 'lucide-react';

interface TourStep {
  id: string;
  title: string;
  content: string;
  target: string; // CSS selector or element ID
  position: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: {
    type: 'click' | 'hover' | 'focus';
    element?: string;
  };
  highlight?: {
    type: 'spotlight' | 'border' | 'pulse';
    color?: string;
  };
}

interface OnboardingTourProps {
  steps?: TourStep[];
  isOpen: boolean;
  onComplete: () => void;
  onSkip: () => void;
  showProgress?: boolean;
}

const defaultSteps: TourStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to FCP Carrier Portal!',
    content: 'Let\'s take a quick tour to help you get started. This will only take a few minutes and will help you understand how to use the portal effectively.',
    target: 'body',
    position: 'center',
    highlight: {
      type: 'spotlight'
    }
  },
  {
    id: 'navigation',
    title: 'Navigation Menu',
    content: 'This is your main navigation menu. You can access all portal features from here: Dashboard, Loadboard, My Loads, Billing, and Settings.',
    target: 'aside',
    position: 'right',
    highlight: {
      type: 'border',
      color: 'border-blue-500'
    }
  },
  {
    id: 'profile-completion',
    title: 'Complete Your Profile',
    content: 'First, complete your carrier profile in Settings. This includes your DOT number, MC number, equipment types, and contact information. This is required to bid on loads.',
    target: 'a[href*="/settings"]',
    position: 'right',
    highlight: {
      type: 'pulse',
      color: 'bg-green-500'
    }
  },
  {
    id: 'loadboard',
    title: 'Find Available Loads',
    content: 'The Loadboard shows all available freight. You can filter by location, equipment type, dates, and rates to find loads that match your operation.',
    target: 'a[href*="/loadboard"]',
    position: 'right',
    highlight: {
      type: 'pulse',
      color: 'bg-purple-500'
    }
  },
  {
    id: 'bidding',
    title: 'Bidding on Loads',
    content: 'When you find a load you want, click "Bid" to submit your rate. The shipper will review all bids and select the best carrier for their needs.',
    target: 'body',
    position: 'center',
    highlight: {
      type: 'spotlight'
    }
  },
  {
    id: 'my-loads',
    title: 'Track Your Loads',
    content: 'My Loads shows all your awarded loads, their status, and required documentation. You\'ll update load status and upload delivery documents here.',
    target: 'a[href*="/my-loads"]',
    position: 'right',
    highlight: {
      type: 'pulse',
      color: 'bg-orange-500'
    }
  },
  {
    id: 'help',
    title: 'Need Help?',
    content: 'If you have questions, click the Help button to access our FAQ and contact support. We\'re here to help you succeed!',
    target: 'body',
    position: 'center',
    highlight: {
      type: 'spotlight'
    }
  }
];

export function OnboardingTour({
  steps = defaultSteps,
  isOpen,
  onComplete,
  onSkip,
  showProgress = true
}: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [highlightedElement, setHighlightedElement] = useState<HTMLElement | null>(null);

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const progress = ((currentStep + 1) / steps.length) * 100;

  // Create overlay for highlighting elements
  const createOverlay = useCallback(() => {
    // Remove existing overlay and highlights
    const existingOverlay = document.getElementById('tour-overlay');
    const existingHighlights = document.querySelectorAll('[id^="tour-highlight"]');
    const existingSpotlight = document.getElementById('tour-spotlight');
    
    if (existingOverlay) existingOverlay.remove();
    if (existingSpotlight) existingSpotlight.remove();
    existingHighlights.forEach(el => el.remove());

    if (!currentStepData || !isOpen) return;

    const target = document.querySelector(currentStepData.target) as HTMLElement;
    if (!target) return;

    setHighlightedElement(target);

    // Create semi-transparent overlay that doesn't gray out content
    const overlay = document.createElement('div');
    overlay.id = 'tour-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.3);
      z-index: 9998;
      pointer-events: none;
    `;

    const targetRect = target.getBoundingClientRect();

    // Create highlight for the target element
    const highlight = document.createElement('div');
    highlight.id = 'tour-highlight';
    highlight.style.cssText = `
      position: fixed;
      top: ${targetRect.top - 4}px;
      left: ${targetRect.left - 4}px;
      width: ${targetRect.width + 8}px;
      height: ${targetRect.height + 8}px;
      background: transparent;
      border: 3px solid #3b82f6;
      border-radius: 8px;
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
      pointer-events: none;
      z-index: 10001;
    `;

    // Apply highlight type
    if (currentStepData.highlight?.type === 'pulse') {
      highlight.style.animation = 'pulse 2s infinite';
    }

    // Create spotlight effect that doesn't gray out everything
    if (currentStepData.highlight?.type === 'spotlight') {
      // Create a spotlight overlay with a cutout
      const spotlight = document.createElement('div');
      spotlight.id = 'tour-spotlight';
      spotlight.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: radial-gradient(
          circle at ${targetRect.left + targetRect.width / 2}px ${targetRect.top + targetRect.height / 2}px,
          transparent ${Math.max(targetRect.width, targetRect.height) / 2 + 20}px,
          rgba(0, 0, 0, 0.4) ${Math.max(targetRect.width, targetRect.height) / 2 + 40}px,
          rgba(0, 0, 0, 0.6) 100%
        );
        z-index: 9999;
        pointer-events: none;
      `;
      document.body.appendChild(spotlight);
      
      // Make the target element more visible by adding a bright background
      highlight.style.background = 'rgba(255, 255, 255, 0.1)';
      highlight.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5), inset 0 0 20px rgba(255, 255, 255, 0.1)';
    } else {
      // For non-spotlight highlights, use the regular overlay
      document.body.appendChild(overlay);
    }

    document.body.appendChild(highlight);
  }, [currentStepData, isOpen]);

  // Position the tour card relative to target
  const getTourCardPosition = useCallback(() => {
    if (!currentStepData || currentStepData.position === 'center') {
      return {
        position: 'fixed' as const,
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 10000
      };
    }

    const target = document.querySelector(currentStepData.target) as HTMLElement;
    if (!target) {
      return {
        position: 'fixed' as const,
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 10000
      };
    }

    const targetRect = target.getBoundingClientRect();
    const cardWidth = 320;
    const cardHeight = 200;
    const offset = 20;

    let position = {};

    switch (currentStepData.position) {
      case 'right':
        position = {
          position: 'fixed' as const,
          top: targetRect.top + targetRect.height / 2 - cardHeight / 2,
          left: targetRect.right + offset,
          zIndex: 10000
        };
        break;
      case 'left':
        position = {
          position: 'fixed' as const,
          top: targetRect.top + targetRect.height / 2 - cardHeight / 2,
          left: targetRect.left - cardWidth - offset,
          zIndex: 10000
        };
        break;
      case 'top':
        position = {
          position: 'fixed' as const,
          top: targetRect.top - cardHeight - offset,
          left: targetRect.left + targetRect.width / 2 - cardWidth / 2,
          zIndex: 10000
        };
        break;
      case 'bottom':
        position = {
          position: 'fixed' as const,
          top: targetRect.bottom + offset,
          left: targetRect.left + targetRect.width / 2 - cardWidth / 2,
          zIndex: 10000
        };
        break;
    }

    return position;
  }, [currentStepData]);

  // Cleanup function
  const cleanup = useCallback(() => {
    const overlay = document.getElementById('tour-overlay');
    const highlights = document.querySelectorAll('[id^="tour-highlight"]');
    const spotlight = document.getElementById('tour-spotlight');
    
    overlay?.remove();
    spotlight?.remove();
    highlights.forEach(el => el.remove());
    
    setHighlightedElement(null);
  }, []);

  // Handle step navigation
  const nextStep = () => {
    if (isLastStep) {
      cleanup();
      onComplete();
    } else {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipTour = () => {
    cleanup();
    onSkip();
  };

  // Create overlay when step changes
  useEffect(() => {
    if (isOpen) {
      // Small delay to ensure DOM is ready
      const timer = setTimeout(createOverlay, 100);
      return () => clearTimeout(timer);
    }
  }, [currentStep, isOpen, createOverlay]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  if (!isOpen || !currentStepData) {
    return null;
  }

  const cardStyle = getTourCardPosition();

  return (
    <div style={cardStyle}>
      <Card className="w-80 shadow-xl border-2 border-primary/20">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              Step {currentStep + 1} of {steps.length}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={skipTour}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          {showProgress && (
            <Progress value={progress} className="w-full h-2 mt-2" />
          )}
          <CardTitle className="text-lg flex items-center">
            {currentStepData.id === 'welcome' && <Play className="h-5 w-5 mr-2" />}
            {currentStepData.id === 'navigation' && <Navigation className="h-5 w-5 mr-2" />}
            {currentStepData.id === 'profile-completion' && <User className="h-5 w-5 mr-2" />}
            {currentStepData.id === 'loadboard' && <Search className="h-5 w-5 mr-2" />}
            {currentStepData.id === 'my-loads' && <Eye className="h-5 w-5 mr-2" />}
            {currentStepData.id === 'help' && <CheckCircle className="h-5 w-5 mr-2" />}
            {!['welcome', 'navigation', 'profile-completion', 'loadboard', 'my-loads', 'help'].includes(currentStepData.id) && <FileText className="h-5 w-5 mr-2" />}
            {currentStepData.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground leading-relaxed">
            {currentStepData.content}
          </p>
          
          <div className="flex items-center justify-between pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>
            
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={skipTour}
                className="flex items-center"
              >
                <SkipForward className="h-4 w-4 mr-1" />
                Skip Tour
              </Button>
              
              <Button
                onClick={nextStep}
                size="sm"
                className="flex items-center"
              >
                {isLastStep ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Finish
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Hook for managing tour state
export function useOnboardingTour() {
  const [isTourOpen, setIsTourOpen] = useState(false);
  const [hasCompletedTour, setHasCompletedTour] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('fcp-tour-completed') === 'true';
    }
    return false;
  });

  const startTour = () => {
    setIsTourOpen(true);
  };

  const completeTour = () => {
    setIsTourOpen(false);
    setHasCompletedTour(true);
    localStorage.setItem('fcp-tour-completed', 'true');
  };

  const skipTour = () => {
    setIsTourOpen(false);
    setHasCompletedTour(true);
    localStorage.setItem('fcp-tour-completed', 'true');
  };

  const resetTour = () => {
    setHasCompletedTour(false);
    localStorage.removeItem('fcp-tour-completed');
  };

  return {
    isTourOpen,
    hasCompletedTour,
    startTour,
    completeTour,
    skipTour,
    resetTour
  };
} 