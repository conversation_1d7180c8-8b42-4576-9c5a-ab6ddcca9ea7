import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsNumber, 
  IsPositive, 
  IsEnum, 
  IsDateString, 
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Matches
} from 'class-validator';

export enum EquipmentType {
  DRY_VAN = 'Dry Van',
  REEFER = 'Reefer',
  FLATBED = 'Flatbed',
  STEP_DECK = 'Step Deck',
  LOWBOY = 'Lowboy',
  TANKER = 'Tanker',
  BOX_TRUCK = 'Box Truck',
  STRAIGHT_TRUCK = 'Straight Truck'
}

export enum PriorityLevel {
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export class CreateOrderDto {
  // Lane information (auto-populated from selected lane)
  @ApiProperty({ 
    description: 'Lane ID from the selected lane',
    example: 'lane_123_CA_TX'
  })
  @IsString()
  @IsNotEmpty()
  laneId: string;

  @ApiProperty({ 
    description: 'Origin city',
    example: 'Los Angeles'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  originCity: string;

  @ApiProperty({ 
    description: 'Origin state',
    example: 'CA'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(2)
  originState: string;

  @ApiProperty({ 
    description: 'Destination city',
    example: 'Houston'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  destinationCity: string;

  @ApiProperty({ 
    description: 'Destination state',
    example: 'TX'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(2)
  destinationState: string;

  @ApiProperty({ 
    description: 'Estimated miles for the route',
    example: 1547
  })
  @IsNumber()
  @IsPositive()
  estimatedMiles: number;

  // Order identification
  @ApiProperty({ 
    description: 'Purchase Order number (must be unique)',
    example: 'PO-20240127-001',
    pattern: '^[A-Za-z0-9-_]+$'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[A-Za-z0-9-_]+$/, {
    message: 'PO Number can only contain letters, numbers, hyphens, and underscores'
  })
  poNumber: string;

  @ApiProperty({ 
    description: 'Sales Order number (optional)',
    example: 'SO-2024-456',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  soNumber?: string;

  // Schedule
  @ApiProperty({ 
    description: 'Pickup date in ISO format',
    example: '2024-01-28T00:00:00.000Z'
  })
  @IsDateString()
  pickupDate: string;

  @ApiProperty({ 
    description: 'Delivery date in ISO format (optional, can be auto-calculated)',
    example: '2024-01-30T00:00:00.000Z',
    required: false
  })
  @IsOptional()
  @IsDateString()
  deliveryDate?: string;

  // Load details
  @ApiProperty({ 
    description: 'Required equipment type',
    enum: EquipmentType,
    example: EquipmentType.DRY_VAN
  })
  @IsEnum(EquipmentType)
  equipmentRequired: EquipmentType;

  @ApiProperty({ 
    description: 'Weight in pounds',
    example: 45000,
    minimum: 1,
    maximum: 80000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Max(80000)
  weightLbs?: number;

  @ApiProperty({ 
    description: 'Rate in USD',
    example: 2500.00,
    minimum: 1,
    maximum: 50000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Max(50000)
  rate?: number;

  @ApiProperty({ 
    description: 'Temperature requirements (for reefer loads)',
    example: 'Fresh (32°F to 40°F)',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  temperature?: string;

  // Additional information
  @ApiProperty({ 
    description: 'Additional notes and special instructions',
    example: 'Handle with care, fragile cargo',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;

  @ApiProperty({ 
    description: 'Priority level for the order',
    enum: PriorityLevel,
    example: PriorityLevel.NORMAL,
    required: false
  })
  @IsOptional()
  @IsEnum(PriorityLevel)
  priority?: PriorityLevel;
} 