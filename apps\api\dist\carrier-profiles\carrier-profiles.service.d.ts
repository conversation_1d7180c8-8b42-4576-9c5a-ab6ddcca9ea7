import { PrismaService } from '../prisma/prisma.service';
import { CreateCarrierProfileDto, UpdateCarrierProfileDto } from './dto';
import { CarrierProfile } from '@repo/db';
export declare class CarrierProfilesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createCarrierProfileDto: CreateCarrierProfileDto, airtableUserId: string): Promise<CarrierProfile>;
    findAll(): Promise<CarrierProfile[]>;
    findOne(id: string): Promise<CarrierProfile>;
    findOneByUserId(userId: string): Promise<CarrierProfile>;
    findMyProfileByAirtableUserId(airtableUserId: string): Promise<CarrierProfile>;
    update(id: string, updateCarrierProfileDto: UpdateCarrierProfileDto): Promise<CarrierProfile>;
    updateByUserId(userId: string, updateCarrierProfileDto: UpdateCarrierProfileDto): Promise<CarrierProfile>;
    updateMyProfileByAirtableUserId(airtableUserId: string, updateCarrierProfileDto: UpdateCarrierProfileDto): Promise<CarrierProfile>;
    remove(id: string): Promise<CarrierProfile>;
}
