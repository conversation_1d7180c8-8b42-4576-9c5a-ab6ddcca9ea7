import { Injectable, InternalServerErrorException, Logger, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import * as jwt from 'jsonwebtoken';
import { N8NJwtPayload } from './authenticated-request.interface';
import { Role } from '@repo/db';

// Interface for cached user profile
interface UserProfile {
  airtableUserId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  mcNumber?: string;
  dotNumber?: string;
  role: string;
  verificationStatus: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly jwtSecret: string;
  private readonly airtableBaseId: string;
  private readonly airtableApiKey: string;

  /**
   * Normalize role from Airtable to database enum
   * Airtable stores: "Admin", "Carrier" (title case)
   * Database expects: Role.ADMIN, Role.CARRIER (enum)
   */
  private normalizeRole(airtableRole: string): Role {
    const role = (airtableRole || 'Carrier').toLowerCase();
    return role === 'admin' ? Role.ADMIN : Role.CARRIER;
  }

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    this.jwtSecret = this.configService.get<string>('JWT_SECRET') || this.configService.get<string>('N8N_JWT_SECRET') || '';
    this.airtableBaseId = this.configService.get<string>('AIRTABLE_BASE_ID') || '';
    this.airtableApiKey = this.configService.get<string>('AIRTABLE_API_KEY') || '';

    if (!this.jwtSecret) {
      this.logger.error('JWT_SECRET or N8N_JWT_SECRET is not set in environment variables.');
      throw new InternalServerErrorException('JWT secret key is not configured.');
    }

    this.logger.log('N8N JWT Authentication Service initialized');
  }

  /**
   * Verifies N8N JWT token and returns the payload
   * @param token - The JWT token to verify
   * @returns The JWT payload if valid
   */
  async verifyToken(token: string): Promise<N8NJwtPayload> {
    try {
      const payload = jwt.verify(token, this.jwtSecret) as N8NJwtPayload;
      
      // Validate required fields from N8N JWT
      if (!payload.id || !payload.email || !payload.role) {
        throw new UnauthorizedException('Invalid JWT payload structure');
      }

      this.logger.debug(`Token verified for user ${payload.id} (${payload.email})`);
      return payload;
    } catch (error) {
      this.logger.warn(`Token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Gets user profile from cache or fetches from Airtable
   * @param airtableUserId - The Airtable user ID from JWT
   * @param forceRefresh - Force refresh from Airtable (e.g., on login)
   * @returns Cached or fresh user profile
   */
  async getUserProfile(airtableUserId: string, forceRefresh: boolean = false): Promise<UserProfile | null> {
    try {
      // Check if we should force refresh (e.g., on login)
      if (forceRefresh) {
        this.logger.debug(`Forcing profile refresh for ${airtableUserId}`);
        return await this.fetchAndCacheUserProfile(airtableUserId);
      }

      // First, try to get from persistent cache
      const cachedProfile = await (this.prisma as any).userProfile.findUnique({
        where: { airtableUserId }
      });

      if (cachedProfile) {
        this.logger.debug(`User profile cache hit for ${airtableUserId} (cached: ${cachedProfile.updatedAt})`);
        return {
          airtableUserId: cachedProfile.airtableUserId,
          email: cachedProfile.email,
          firstName: cachedProfile.firstName,
          lastName: cachedProfile.lastName,
          companyName: cachedProfile.companyName,
          mcNumber: cachedProfile.mcNumber,
          dotNumber: cachedProfile.dotNumber,
          role: cachedProfile.role,
          verificationStatus: cachedProfile.verificationStatus,
        };
      }

      // If not in cache, fetch from Airtable and cache it
      this.logger.log(`User profile cache miss for ${airtableUserId}, fetching from Airtable`);
      return await this.fetchAndCacheUserProfile(airtableUserId);

    } catch (error) {
      this.logger.error(`Failed to get user profile for ${airtableUserId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Fetches user profile from Airtable and caches it locally
   * @param airtableUserId - The Airtable user ID
   * @returns Fresh user profile from Airtable
   */
  private async fetchAndCacheUserProfile(airtableUserId: string): Promise<UserProfile | null> {
    try {
      // Fetch user data from Airtable UserManagement table
      const response = await fetch(`https://api.airtable.com/v0/${this.airtableBaseId}/UserManagement/${airtableUserId}`, {
        headers: {
          'Authorization': `Bearer ${this.airtableApiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Airtable API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const fields = data.fields;

      // Extract MC Number from linked Company Name field (lookup) and convert to string
      const mcNumber = fields['MC Number (from Company Name)'] 
        ? String(Array.isArray(fields['MC Number (from Company Name)']) 
           ? fields['MC Number (from Company Name)'][0] 
           : fields['MC Number (from Company Name)'])
        : null;

      // Extract DOT Number similarly and convert to string
      const dotNumber = fields['DOT Number (from Company Name)']
        ? String(Array.isArray(fields['DOT Number (from Company Name)'])
           ? fields['DOT Number (from Company Name)'][0]
           : fields['DOT Number (from Company Name)'])
        : null;

      // Extract Company Name from linked field
      const companyName = fields['Company Name']
        ? (Array.isArray(fields['Company Name'])
           ? fields['Company Name'][0]
           : fields['Company Name'])
        : null;

      const userProfile: UserProfile = {
        airtableUserId: data.id,
        email: fields.Email,
        firstName: fields['First Name'],
        lastName: fields['Last Name'],
        companyName: companyName,
        mcNumber: mcNumber || undefined,
        dotNumber: dotNumber || undefined,
        role: fields.Role || 'Carrier',
        verificationStatus: fields['Verification Status'] || 'Pending',
      };

      // Cache the profile in local database
      await (this.prisma as any).userProfile.upsert({
        where: { airtableUserId },
        update: {
          email: userProfile.email,
          firstName: userProfile.firstName,
          lastName: userProfile.lastName,
          companyName: userProfile.companyName,
          mcNumber: userProfile.mcNumber,
          dotNumber: userProfile.dotNumber,
          role: userProfile.role,
          verificationStatus: userProfile.verificationStatus,
          updatedAt: new Date(),
        },
        create: {
          airtableUserId: userProfile.airtableUserId,
          email: userProfile.email,
          firstName: userProfile.firstName,
          lastName: userProfile.lastName,
          companyName: userProfile.companyName,
          mcNumber: userProfile.mcNumber,
          dotNumber: userProfile.dotNumber,
          role: userProfile.role,
          verificationStatus: userProfile.verificationStatus,
        },
      });

      this.logger.log(`User profile cached for ${airtableUserId} (${userProfile.email}) with MC Number ${mcNumber}`);
      return userProfile;

    } catch (error) {
      this.logger.error(`Failed to fetch user profile from Airtable for ${airtableUserId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Validates user from JWT payload and returns user info
   * @param jwtPayload - The decoded JWT payload
   * @param isLoginRequest - Whether this is a login request (triggers profile refresh)
   * @returns User profile with authorization info
   */
  async validateUser(jwtPayload: N8NJwtPayload, isLoginRequest: boolean = false): Promise<{
    airtableUserId: string;
    email: string;
    mcNumber?: string;
    role: string;
    isAdmin: boolean;
  } | null> {
    try {
      // Get user profile - refresh on login to ensure latest data
      const userProfile = await this.getUserProfile(jwtPayload.id, isLoginRequest);
      
      if (!userProfile) {
        this.logger.warn(`User profile not found for JWT user ${jwtPayload.id}`);
        return null;
      }

      // Use MC Number from profile (more up-to-date) or fallback to JWT
      const mcNumber = userProfile.mcNumber || jwtPayload.mcNumber;
      
      if (isLoginRequest) {
        this.logger.log(`Login profile refresh for ${jwtPayload.id} - MC: ${mcNumber}, Role: ${userProfile.role}`);
      }
      
      return {
        airtableUserId: userProfile.airtableUserId,
        email: userProfile.email,
        mcNumber: mcNumber,
        role: userProfile.role,
        isAdmin: userProfile.role.toLowerCase() === 'admin',
      };

    } catch (error) {
      this.logger.error(`User validation failed for ${jwtPayload.id}: ${error.message}`);
      return null;
    }
  }

  /**
   * Updates cached user profile
   * @param airtableUserId - Airtable user ID
   * @param updates - Profile updates
   */
  async updateCachedProfile(airtableUserId: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      this.logger.debug(`Updating cached profile for ${airtableUserId} with:`, updates);
      
      await (this.prisma as any).userProfile.update({
        where: { airtableUserId },
        data: {
          ...updates,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Updated cached profile for user ${airtableUserId}`);
    } catch (error) {
      this.logger.error(`Failed to update cached profile for ${airtableUserId}: ${error.message}`);
      this.logger.debug(`Cache update error details:`, error);
      // Don't throw here, just log
    }
  }

  /**
   * Clears cached user profile (for testing or manual refresh)
   * @param airtableUserId - Airtable user ID
   */
  async clearCachedProfile(airtableUserId: string): Promise<void> {
    try {
      await (this.prisma as any).userProfile.delete({
        where: { airtableUserId },
      });

      this.logger.log(`Cleared cached profile for user ${airtableUserId}`);
    } catch (error) {
      this.logger.warn(`Failed to clear cached profile for ${airtableUserId}: ${error.message}`);
    }
  }

  /**
   * Refreshes user profile from Airtable (manual refresh)
   * @param airtableUserId - Airtable user ID
   */
  async refreshUserProfile(airtableUserId: string): Promise<UserProfile | null> {
    this.logger.log(`Manual profile refresh requested for ${airtableUserId}`);
    return await this.fetchAndCacheUserProfile(airtableUserId);
  }

  /**
   * Periodic sync of all cached user profiles (background job)
   * @param maxAge - Maximum age in hours before forcing refresh (default: 24 hours)
   */
  async syncAllCachedProfiles(maxAge: number = 24): Promise<{ updated: number; errors: string[] }> {
    const maxAgeDate = new Date(Date.now() - (maxAge * 60 * 60 * 1000));
    const errors: string[] = [];
    let updated = 0;

    try {
      // Find profiles older than maxAge
      const staleProfiles = await (this.prisma as any).userProfile.findMany({
        where: {
          updatedAt: {
            lt: maxAgeDate
          }
        },
        select: { airtableUserId: true, email: true, updatedAt: true }
      });

      this.logger.log(`Found ${staleProfiles.length} profiles older than ${maxAge} hours for refresh`);

      for (const profile of staleProfiles) {
        try {
          await this.fetchAndCacheUserProfile(profile.airtableUserId);
          updated++;
          this.logger.debug(`Refreshed profile for ${profile.airtableUserId} (${profile.email})`);
        } catch (error) {
          const errorMsg = `Failed to refresh profile ${profile.airtableUserId}: ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(`Profile sync completed: ${updated} updated, ${errors.length} errors`);
      return { updated, errors };

    } catch (error) {
      this.logger.error(`Profile sync failed: ${error.message}`);
      return { updated: 0, errors: [`Sync operation failed: ${error.message}`] };
    }
  }

  /**
   * Legacy method - kept for backward compatibility during migration
   * @deprecated Use validateUser with N8N JWT payload instead
   */
  async findUserByClerkUserId(clerkUserId: string): Promise<any> {
    this.logger.warn(`Legacy findUserByClerkUserId called with ${clerkUserId} - returning null (using N8N authentication)`);
    return null;
  }

  /**
   * Legacy method - kept for backward compatibility during migration
   * @deprecated Use findOrCreateUserByN8NPayload instead
   */
  async findOrCreateUserByClerkPayload(clerkPayload: any): Promise<any> {
    this.logger.warn('Legacy findOrCreateUserByClerkPayload called - returning null (using N8N authentication)');
    return null;
  }

  /**
   * Find user by Airtable user ID
   * @param airtableUserId - The Airtable user ID from N8N JWT
   * @returns User profile data
   */
  async findUserByAirtableId(airtableUserId: string): Promise<UserProfile | null> {
    return await this.getUserProfile(airtableUserId);
  }

  /**
   * Find or create user based on N8N JWT payload
   * This method handles user synchronization from N8N authentication
   */
  async findOrCreateUserByN8NPayload(payload: N8NJwtPayload): Promise<UserProfile | null> {
    try {
      // Get or refresh user profile from Airtable
      const userProfile = await this.getUserProfile(payload.id, true); // Force refresh on authentication

      if (!userProfile) {
        this.logger.error(`Unable to find or create user profile for ${payload.id} (${payload.email})`);
        return null;
      }

      // Sync user data from Airtable (source of truth) to PostgreSQL database
      await this.findUserByAirtableUserId(payload.id);

      this.logger.log(`User profile synchronized for ${payload.id} (${payload.email}) with role: ${userProfile.role}`);
      return userProfile;
    } catch (error) {
      this.logger.error(`Error in findOrCreateUserByN8NPayload for ${payload.id}: ${error.message}`);
      return null;
    }
  }

  /**
   * Find user by Airtable user ID from the User table
   * @param airtableUserId - The Airtable user ID from N8N JWT
   * @returns User data from the User table
   */
  async findUserByAirtableUserId(airtableUserId: string): Promise<any> {
    try {
      let user = await this.prisma.user.findUnique({
        where: { airtableUserId },
      });

      // Always sync user data from Airtable (source of truth) to PostgreSQL
      const userProfile = await this.getUserProfile(airtableUserId, true);
      if (!userProfile) {
        throw new Error(`User profile not found in Airtable for ${airtableUserId}`);
      }

      if (!user) {
        // User doesn't exist in local database yet, create them with Airtable data
        try {
          user = await this.prisma.user.create({
            data: {
              airtableUserId: airtableUserId,
              email: userProfile.email,
              firstName: userProfile.firstName || 'Unknown',
              lastName: userProfile.lastName || 'User',
              role: this.normalizeRole(userProfile.role),
            },
          });
          this.logger.log(`Created User record for airtableUserId: ${airtableUserId} with role: ${userProfile.role}`);
        } catch (createError) {
          this.logger.error(`Failed to create User record for ${airtableUserId}: ${createError.message}`);
          throw new Error(`Failed to create user record: ${createError.message}`);
        }
      } else {
        // User exists, sync their role and basic info from Airtable (source of truth)
        const normalizedRole = this.normalizeRole(userProfile.role);
        if (user.role !== normalizedRole || user.email !== userProfile.email) {
          try {
            user = await this.prisma.user.update({
              where: { airtableUserId },
              data: {
                role: normalizedRole,
                email: userProfile.email,
                firstName: userProfile.firstName || user.firstName,
                lastName: userProfile.lastName || user.lastName,
                updatedAt: new Date(),
              },
            });
            this.logger.log(`Synced User role from Airtable for ${airtableUserId}: ${user.role} (was ${user.role}, now ${normalizedRole})`);
          } catch (updateError) {
            this.logger.error(`Failed to sync User role for ${airtableUserId}: ${updateError.message}`);
            // Continue with existing user data if update fails
          }
        }
      }

      return user;
    } catch (error) {
      this.logger.error(`Error finding user by airtableUserId ${airtableUserId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update user profile in the User table
   * @param airtableUserId - The Airtable user ID
   * @param updateData - The data to update
   * @returns Updated user data
   */
  async updateUserProfile(airtableUserId: string, updateData: { firstName?: string; lastName?: string; email?: string }): Promise<any> {
    try {
      this.logger.debug(`Starting updateUserProfile for ${airtableUserId} with data:`, updateData);
      
      // First ensure the user exists in the User table
      let user = await this.prisma.user.findUnique({
        where: { airtableUserId },
      });

      this.logger.debug(`Found existing user:`, user ? 'YES' : 'NO');

      if (!user) {
        // User doesn't exist, create it first
        this.logger.debug(`Creating new user record for ${airtableUserId}`);
        user = await this.prisma.user.create({
          data: {
            airtableUserId: airtableUserId,
            email: updateData.email || `${airtableUserId}@placeholder.com`,
            firstName: updateData.firstName || 'Unknown',
            lastName: updateData.lastName || 'User',
            role: Role.CARRIER,
          },
        });
        this.logger.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
      }

      // Now update the user
      this.logger.debug(`Updating user record for ${airtableUserId}`);
      const updatedUser = await this.prisma.user.update({
        where: { airtableUserId },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      });

      this.logger.debug(`User update successful:`, updatedUser);

      // Also update the cached profile if it exists
      try {
        await this.updateCachedProfile(airtableUserId, updateData);
        this.logger.debug(`Cache update successful for ${airtableUserId}`);
      } catch (cacheError) {
        this.logger.warn(`Failed to update cached profile for ${airtableUserId}: ${cacheError.message}`);
      }

      this.logger.log(`Updated user profile for ${airtableUserId}`);
      return updatedUser;
    } catch (error) {
      this.logger.error(`Error updating user profile for ${airtableUserId}: ${error.message}`, error.stack);
      this.logger.error(`Prisma error details:`, error);
      throw error;
    }
  }
} 