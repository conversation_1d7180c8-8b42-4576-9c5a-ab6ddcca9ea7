{"name": "@repo/db", "version": "0.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"generate": "pnpm exec prisma generate --schema=../../apps/api/prisma/schema.prisma", "build": "pnpm run generate && tsc -p tsconfig.json && xcopy /E /I generated dist\\generated", "lint": "eslint . --max-warnings 0", "prisma:studio": "prisma studio --schema=../../apps/api/prisma/schema.prisma"}, "dependencies": {"@prisma/client": "file:./generated/client"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^9.26.0", "prisma": "6.6.0", "typescript": "5.8.2"}}