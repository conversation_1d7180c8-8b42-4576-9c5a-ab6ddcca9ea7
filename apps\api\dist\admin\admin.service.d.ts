import { PrismaService } from '../prisma/prisma.service';
import { AuthService } from '../auth/auth.service';
import { SystemSettingsDto, UpdateSystemSettingsDto } from './admin.controller';
import { NotificationsService } from '../notifications/notifications.service';
interface VerifyCarrierResponse {
    message: string;
    carrierProfile: {
        id: string;
        companyName: string | null;
        isVerifiedByAdmin: boolean;
    };
    success: boolean;
}
interface UserInfoResponse {
    clerkUserId?: string;
    user?: any;
    message?: string;
    error?: string;
    details?: string;
}
interface PromoteAdminResponse {
    message: string;
    user?: {
        id: string;
        email: string | null;
        firstName: string | null;
        lastName: string | null;
        role: string;
        createdAt: Date;
    };
    success: boolean;
    error?: string;
    details?: string;
}
export declare class AdminService {
    private prisma;
    private authService;
    private notificationsService?;
    private readonly logger;
    constructor(prisma: PrismaService, authService: AuthService, notificationsService?: NotificationsService | undefined);
    getCurrentUserInfo(req: any): Promise<UserInfoResponse>;
    promoteFirstAdmin(): Promise<PromoteAdminResponse>;
    getAllUsers(): Promise<({
        carrierProfile: {
            id: string;
            companyName: string | null;
            isVerifiedByAdmin: boolean;
        } | null;
    } & {
        id: string;
        airtableUserId: string | null;
        email: string | null;
        firstName: string | null;
        lastName: string | null;
        role: import("@repo/db").$Enums.Role;
        mcNumber: string | null;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    verifyCarrier(userId: string, isVerified: boolean): Promise<VerifyCarrierResponse>;
    updateUserRole(userId: string, role: 'CARRIER' | 'ADMIN'): Promise<{
        message: string;
        user: {
            id: string;
            email: string | null;
            firstName: string | null;
            lastName: string | null;
            role: import("@repo/db").$Enums.Role;
        };
    }>;
    getSystemSettings(): Promise<SystemSettingsDto>;
    updateSystemSettings(updateData: UpdateSystemSettingsDto): Promise<SystemSettingsDto>;
    resetSystemSettings(): Promise<SystemSettingsDto>;
    private getDefaultSettings;
    private convertToDto;
    getPendingBids(): Promise<any[]>;
    getAllBidsWithFilters(filters: {
        status?: string;
        carrierId?: string;
        loadId?: string;
        dateFrom?: Date;
        dateTo?: Date;
        page: number;
        limit: number;
    }): Promise<{
        bids: any[];
        totalCount: number;
        page: number;
        pageSize: number;
    }>;
    respondToBid(bidId: string, responseDto: {
        response: 'accepted' | 'countered' | 'declined';
        counterOfferAmount?: number;
        notes?: string;
    }, adminUserId: string): Promise<any>;
    getBidHistory(bidId: string): Promise<any[]>;
    getBiddingStatistics(): Promise<any>;
    processExpiredBids(): Promise<{
        expiredCount: number;
        notifiedCount: number;
    }>;
    getBidsExpiringSoon(hoursFromNow?: number): Promise<any[]>;
    private calculateTimeRemaining;
    private isExpiringSoon;
    private sendAdminDashboardNotifications;
    private getBidResponseMessage;
}
export {};
