import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Authentication check - simplified and more robust (same as order creation)
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!token) {
      return NextResponse.json({ error: 'No authentication token provided' }, { status: 401 });
    }

    // Validate JWT token format
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 401 });
    }

    // Decode JWT payload directly (since we know the structure)
    let payload;
    try {
      payload = JSON.parse(atob(tokenParts[1]));
    } catch {
      return NextResponse.json({ error: 'Invalid token payload' }, { status: 401 });
    }

    // Check if user has operations access (First Cut Produce team members or admins)
    const userRole = (payload.role || '').toUpperCase();
    const userEmail = payload.email || '';
    const companyName = payload.companyName || '';
    
    const hasOperationsAccess = userRole === 'ADMIN' ||
                              companyName === 'First Cut Produce' ||
                              companyName === 'FIRST CUT PRODUCE' ||
                              userEmail.includes('@firstcutproduce.com');
    
    if (!hasOperationsAccess) {
      return NextResponse.json({ error: 'First Cut Produce team access required' }, { status: 403 });
    }

    // Fetch templates from database
    const templates = await (prisma as any).orderTemplate.findMany({
      orderBy: { name: 'asc' }
    });

    return NextResponse.json({
      success: true,
      templates
    });

  } catch (error) {
    console.error('Error fetching order templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authentication check - simplified and more robust (same as order creation)
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!token) {
      return NextResponse.json({ error: 'No authentication token provided' }, { status: 401 });
    }

    // Validate JWT token format
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 401 });
    }

    // Decode JWT payload directly (since we know the structure)
    let payload;
    try {
      payload = JSON.parse(atob(tokenParts[1]));
    } catch {
      return NextResponse.json({ error: 'Invalid token payload' }, { status: 401 });
    }

    // Check if user has operations access (First Cut Produce team members or admins)
    const userRole = (payload.role || '').toUpperCase();
    const userEmail = payload.email || '';
    const companyName = payload.companyName || '';
    
    const hasOperationsAccess = userRole === 'ADMIN' ||
                              companyName === 'First Cut Produce' ||
                              companyName === 'FIRST CUT PRODUCE' ||
                              userEmail.includes('@firstcutproduce.com');
    
    if (!hasOperationsAccess) {
      return NextResponse.json({ error: 'First Cut Produce team access required' }, { status: 403 });
    }

    const body = await request.json();
    const { name, pickupLocationId, deliveryLocationId, rate, daysToDelivery } = body;

    // Validate required fields
    if (!name || !pickupLocationId || !deliveryLocationId || !rate || daysToDelivery === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create template in database
    const template = await (prisma as any).orderTemplate.create({
      data: {
        name,
        pickupLocationId,
        deliveryLocationId,
        rate: parseFloat(rate),
        daysToDelivery: parseInt(daysToDelivery),
        createdBy: payload.id
      }
    });

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error('Error creating order template:', error);
    return NextResponse.json(
      { error: 'Failed to create order template' },
      { status: 500 }
    );
  }
}


