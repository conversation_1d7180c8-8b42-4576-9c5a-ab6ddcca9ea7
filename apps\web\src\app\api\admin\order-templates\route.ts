import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here when user roles are implemented

    // For demo purposes, return empty templates array if database is not available
    try {
      const templates = await prisma.orderTemplate.findMany({
        orderBy: { name: 'asc' }
      });

      return NextResponse.json({
        success: true,
        templates
      });
    } catch (dbError) {
      // Return empty array if database table doesn't exist yet
      return NextResponse.json({
        success: true,
        templates: []
      });
    }

  } catch (error) {
    console.error('Error fetching order templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here when user roles are implemented

    const body = await request.json();
    const { name, pickupLocationId, deliveryLocationId, rate, daysToDelivery } = body;

    // Validate required fields
    if (!name || !pickupLocationId || !deliveryLocationId || !rate || daysToDelivery === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Try to create template in database, return demo response if table doesn't exist
    try {
      const template = await prisma.orderTemplate.create({
        data: {
          name,
          pickupLocationId,
          deliveryLocationId,
          rate: parseFloat(rate),
          daysToDelivery: parseInt(daysToDelivery),
          createdBy: userId
        }
      });

      return NextResponse.json({
        success: true,
        template
      });
    } catch (dbError) {
      // Return demo response if database table doesn't exist yet
      return NextResponse.json({
        success: true,
        template: {
          id: 'demo-template-' + Date.now(),
          name,
          pickupLocationId,
          deliveryLocationId,
          rate: parseFloat(rate),
          daysToDelivery: parseInt(daysToDelivery),
          createdBy: userId,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

  } catch (error) {
    console.error('Error creating order template:', error);
    return NextResponse.json(
      { error: 'Failed to create order template' },
      { status: 500 }
    );
  }
}
