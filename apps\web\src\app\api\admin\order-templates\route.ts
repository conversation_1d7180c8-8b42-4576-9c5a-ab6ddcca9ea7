import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    await requireAdmin(request);

    // Fetch templates from database
    const templates = await prisma.orderTemplate.findMany({
      orderBy: { name: 'asc' }
    });

    return NextResponse.json({
      success: true,
      templates
    });

  } catch (error) {
    console.error('Error fetching order templates:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to fetch order templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await requireAdmin(request);

    const body = await request.json();
    const { name, pickupLocationId, deliveryLocationId, rate, daysToDelivery } = body;

    // Validate required fields
    if (!name || !pickupLocationId || !deliveryLocationId || !rate || daysToDelivery === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create template in database
    const template = await prisma.orderTemplate.create({
      data: {
        name,
        pickupLocationId,
        deliveryLocationId,
        rate: parseFloat(rate),
        daysToDelivery: parseInt(daysToDelivery),
        createdBy: user.id
      }
    });

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error('Error creating order template:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to create order template' },
      { status: 500 }
    );
  }
}
