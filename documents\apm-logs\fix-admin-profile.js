const { PrismaClient } = require('../../packages/db/generated/client');

async function fixAdminProfile() {
  const prisma = new PrismaClient();
  const userClerkId = 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE';
  
  try {
    console.log('🔧 Fixing admin carrier profile...\n');
    
    // Get user record
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userClerkId },
      select: { id: true }
    });
    
    // Delete the placeholder profile
    await prisma.carrierProfile.deleteMany({
      where: { userId: user.id }
    });
    console.log('✅ Deleted placeholder profile');
    
    // Create a proper admin carrier profile
    const carrierProfile = await prisma.carrierProfile.create({
      data: {
        userId: user.id,
        companyName: 'First Cut Produce',
        mcNumber: 'MC123456', // Replace with your actual MC number
        dotNumber: 'DOT123456', // Replace with your actual DOT number
        phoneNumber: '************', // Replace with actual phone
        equipmentTypes: ['DRY_VAN', 'REEFER', 'FLATBED'], 
        serviceableRegions: ['US', 'CA'], 
        isVerifiedByAdmin: true,
        adminNotes: 'Admin user - restored after database migration',
        contact_name: 'Amer Begovic',
        contact_email: '<EMAIL>',
        contact_phone: '************'
      }
    });
    
    console.log('✅ Created proper admin carrier profile:', JSON.stringify(carrierProfile, null, 2));
    
    // Also update user role to ADMIN if needed
    await prisma.user.update({
      where: { clerkUserId: userClerkId },
      data: { role: 'ADMIN' }
    });
    console.log('✅ Updated user role to ADMIN');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAdminProfile(); 