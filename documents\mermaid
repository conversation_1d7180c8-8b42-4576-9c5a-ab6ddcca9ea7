graph TD
    subgraph "User Interaction Layer"
        U_Carrier[Motor Carrier User]
        U_Admin[Administrator User]
    end

    subgraph "Application Platform - Deployed on Vercel & Render"
        direction LR
        subgraph "Frontend (Vercel)"
            FE[Next.js Web App <br/> (React, Tailwind CSS)]
        end

        subgraph "Backend (Render.com)"
            BE[NestJS API Server <br/> (Node.js, TypeScript)]
            DB[PostgreSQL Database <br/> (Hosted on Render.com)]
        end
    end

    subgraph "External Services"
        Auth0[Auth0 <br/> (Authentication & User Management)]
        Airtable[Airtable <br/> (Load Data Source)]
        EmailSvc[(Post-MVP) Email Service <br/> (e.g., SendGrid)]
        RealtimeSvc[(Post-MVP) Real-time Service <br/> (Socket.IO on NestJS)]
    end

    %% User Flows
    U_Carrier -- HTTPS --> FE
    U_Admin -- HTTPS --> FE

    %% Frontend - Auth0
    FE -- OAuth 2.0 / OIDC Flow --> Auth0
    Auth0 -- JWT / User Session --> FE

    %% Frontend - Backend API
    FE -- Authenticated API Calls (HTTPS, JWT) --> BE

    %% Backend - Database
    BE -- SQL Queries (TypeORM/Prisma) --> DB

    %% Backend - Auth0 (Validation/User Sync)
    BE -- JWT Validation / User Info --> Auth0

    %% Airtable Sync (Push via Webhook)
    Airtable_Automations[Airtable Automations]
    Airtable_Automations -- HTTPS Webhook (Load Data) --> BE
    BE -- Upserts Load Data --> DB

    %% Post-MVP Features
    BE -- Sends Emails via --> EmailSvc
    BE -- WebSocket Msgs --> RealtimeSvc
    FE -- Listens for WebSocket Msgs --> RealtimeSvc


    %% Data Flow Lines
    U_Carrier -->|Signup/Login| FE
    FE -->|Auth Request| Auth0
    Auth0 -->|Token/Session| FE
    FE -->|Authenticated API Request| BE
    BE -->|Validate Token| Auth0
    BE -->|DB Operations| DB
    DB -->|Data| BE
    BE -->|API Response| FE
    FE -->|Display to User| U_Carrier
    FE -->|Display to User| U_Admin

    Airtable -->|Record Create/Update| Airtable_Automations
    Airtable_Automations -->|Push Load Data (Webhook)| BE


    classDef user fill:#D6EAF8,stroke:#333,stroke-width:2px;
    classDef app fill:#D5F5E3,stroke:#333,stroke-width:2px;
    classDef external fill:#FCF3CF,stroke:#333,stroke-width:2px;
    classDef deployment fill:#E8DAEF,stroke:#333,stroke-width:2px;


    class U_Carrier,U_Admin user;
    class FE,BE,DB app;
    class Auth0,Airtable,EmailSvc,RealtimeSvc,Airtable_Automations external;