"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
let RedisService = class RedisService {
    cache = new Map();
    async get(key) {
        const entry = this.cache.get(key);
        return entry ? entry.value : null;
    }
    async set(key, value, ttlSeconds) {
        this.del(key);
        const entry = { value };
        if (ttlSeconds) {
            entry.timeout = setTimeout(() => {
                this.cache.delete(key);
            }, ttlSeconds * 1000);
        }
        this.cache.set(key, entry);
    }
    async del(key) {
        const entry = this.cache.get(key);
        if (entry && entry.timeout) {
            clearTimeout(entry.timeout);
        }
        this.cache.delete(key);
    }
    async onModuleDestroy() {
        for (const entry of this.cache.values()) {
            if (entry.timeout)
                clearTimeout(entry.timeout);
        }
        this.cache.clear();
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = __decorate([
    (0, common_1.Injectable)()
], RedisService);
//# sourceMappingURL=redis.service.js.map