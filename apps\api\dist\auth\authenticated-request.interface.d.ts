import { Request } from 'express';
export interface N8NJwtPayload {
    id: string;
    email: string;
    role: 'CARRIER' | 'ADMIN';
    mcNumber?: string;
    firstName?: string;
    lastName?: string;
    companyName?: string;
    iat: number;
    exp: number;
}
export interface AuthenticatedRequest extends Request {
    user?: {
        airtableUserId: string;
        email: string;
        mcNumber?: string;
        companyName?: string;
        role: 'CARRIER' | 'ADMIN';
        isAdmin: boolean;
    };
    auth?: N8NJwtPayload;
}
