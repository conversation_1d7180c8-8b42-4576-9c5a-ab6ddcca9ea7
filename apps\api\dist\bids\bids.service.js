"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var BidsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BidsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const https = __importStar(require("https"));
const http = __importStar(require("http"));
let BidsService = BidsService_1 = class BidsService {
    configService;
    logger = new common_1.Logger(BidsService_1.name);
    n8nBaseUrl;
    n8nJwtSecret;
    constructor(configService) {
        this.configService = configService;
        this.n8nBaseUrl = this.configService.get('NEXT_PUBLIC_N8N_BASE_URL') || 'https://firstcutproduce.app.n8n.cloud';
        this.n8nJwtSecret = this.configService.get('N8N_JWT_SECRET');
        if (!this.n8nJwtSecret) {
            this.logger.warn('N8N_JWT_SECRET not configured - N8N authentication may fail');
        }
    }
    async submitToN8N(bidData, jwtToken) {
        try {
            if (!jwtToken) {
                throw new common_1.UnauthorizedException('JWT token is required for bid submission');
            }
            const requiredFields = ['loadId', 'bidAmount'];
            for (const field of requiredFields) {
                if (!bidData[field]) {
                    throw new common_1.BadRequestException(`Missing required field: ${field}`);
                }
            }
            const webhookUrl = `${this.n8nBaseUrl}/webhook/bidding/submit`;
            this.logger.log(`Submitting bid to N8N: ${webhookUrl}`);
            this.logger.log(`Bid data: ${JSON.stringify(bidData, null, 2)}`);
            this.logger.log(`Using JWT token: ${jwtToken ? jwtToken.substring(0, 20) + '...' : 'None'}`);
            const result = await this.makeHttpRequest(webhookUrl, bidData, jwtToken);
            this.logger.log(`Successful bid submission result:`, result);
            return result;
        }
        catch (error) {
            this.logger.error('Failed to submit bid to N8N:', error);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new Error(`Bid submission failed: ${error.message}`);
        }
    }
    async makeHttpRequest(url, data, jwtToken) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const httpModule = isHttps ? https : http;
            const postData = JSON.stringify(data);
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData),
                    'Authorization': `Bearer ${jwtToken}`,
                },
            };
            const req = httpModule.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => {
                    body += chunk;
                });
                res.on('end', () => {
                    this.logger.log(`N8N Response Status: ${res.statusCode}`);
                    this.logger.log(`N8N Response: ${body}`);
                    if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
                        try {
                            const result = JSON.parse(body);
                            resolve(result);
                        }
                        catch (e) {
                            resolve({ success: true, message: body });
                        }
                    }
                    else {
                        this.logger.error(`N8N webhook failed:`, {
                            status: res.statusCode,
                            statusText: res.statusMessage,
                            response: body,
                            url: url,
                        });
                        reject(new Error(`N8N webhook failed with status ${res.statusCode}: ${body}`));
                    }
                });
            });
            req.on('error', (error) => {
                this.logger.error('HTTP request error:', error);
                reject(error);
            });
            req.write(postData);
            req.end();
        });
    }
};
exports.BidsService = BidsService;
exports.BidsService = BidsService = BidsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], BidsService);
//# sourceMappingURL=bids.service.js.map