"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RadarDistanceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RadarDistanceService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
class RateLimiter {
    maxRequestsPerSecond;
    windowSizeMs;
    lastRequestTime = 0;
    requestCount = 0;
    resetTime = 0;
    constructor(maxRequestsPerSecond, windowSizeMs = 1000) {
        this.maxRequestsPerSecond = maxRequestsPerSecond;
        this.windowSizeMs = windowSizeMs;
    }
    async waitForRateLimit() {
        const now = Date.now();
        if (now >= this.resetTime) {
            this.requestCount = 0;
            this.resetTime = now + this.windowSizeMs;
        }
        if (this.requestCount >= this.maxRequestsPerSecond) {
            const waitTime = this.resetTime - now;
            if (waitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, waitTime));
                this.requestCount = 0;
                this.resetTime = Date.now() + this.windowSizeMs;
            }
        }
        this.requestCount++;
        this.lastRequestTime = Date.now();
    }
}
let RadarDistanceService = RadarDistanceService_1 = class RadarDistanceService {
    configService;
    prisma;
    logger = new common_1.Logger(RadarDistanceService_1.name);
    radarApiKey;
    BASE_URL = 'https://api.radar.io/v1';
    geocodingRateLimiter = new RateLimiter(50);
    routingRateLimiter = new RateLimiter(10);
    constructor(configService, prisma) {
        this.configService = configService;
        this.prisma = prisma;
        this.radarApiKey = this.configService.get('RADAR_SECRET_KEY') || '';
        if (!this.radarApiKey) {
            this.logger.warn('RADAR_SECRET_KEY not found in environment variables - using fallback distance calculations only');
            this.logger.warn('To enable accurate distance calculations, please set RADAR_SECRET_KEY environment variable');
        }
        else {
            this.logger.log('RadarDistanceService initialized with Radar.com API, caching, and rate limiting');
            this.logger.log('Rate limits: Geocoding 50/sec, Routing 10/sec (conservative)');
            this.logger.log('Cache: Distance calculations stored in database to avoid repeated API calls');
        }
    }
    async calculateDistanceByAddress(originAddress, destinationAddress) {
        this.logger.log(`Calculating distance: ${originAddress} → ${destinationAddress}`);
        const normalizedOrigin = this.normalizeAddress(originAddress);
        const normalizedDestination = this.normalizeAddress(destinationAddress);
        const cachedResult = await this.getCachedDistance(normalizedOrigin, normalizedDestination);
        if (cachedResult) {
            this.logger.debug(`Cache hit: ${normalizedOrigin} → ${normalizedDestination} = ${cachedResult.distanceMiles} miles`);
            return {
                distanceMiles: cachedResult.distanceMiles,
                durationHours: cachedResult.durationHours,
                success: cachedResult.calculatedBy === 'radar',
                source: 'cache'
            };
        }
        if (!this.radarApiKey) {
            this.logger.debug('No Radar API key available, using fallback distance calculation');
            const fallbackResult = this.calculateFallbackDistanceFromAddresses(normalizedOrigin, normalizedDestination);
            await this.cacheDistance(normalizedOrigin, normalizedDestination, fallbackResult.distanceMiles, fallbackResult.durationHours, 'fallback');
            return {
                distanceMiles: fallbackResult.distanceMiles,
                durationHours: fallbackResult.durationHours,
                success: false,
                source: 'fallback'
            };
        }
        try {
            await this.geocodingRateLimiter.waitForRateLimit();
            await this.geocodingRateLimiter.waitForRateLimit();
            const [originCoords, destCoords] = await Promise.all([
                this.geocodeAddress(normalizedOrigin),
                this.geocodeAddress(normalizedDestination)
            ]);
            await this.routingRateLimiter.waitForRateLimit();
            const routeResult = await this.calculateRoute(originCoords, destCoords);
            this.logger.log(`Radar distance calculation successful: ${routeResult.distanceMiles} miles, ${routeResult.durationHours} hours`);
            await this.cacheDistance(normalizedOrigin, normalizedDestination, routeResult.distanceMiles, routeResult.durationHours, 'radar');
            return {
                distanceMiles: routeResult.distanceMiles,
                durationHours: routeResult.durationHours,
                success: true,
                source: 'radar'
            };
        }
        catch (error) {
            this.logger.error(`Radar distance calculation failed: ${error.message}`, error.stack);
            const fallbackResult = this.calculateFallbackDistanceFromAddresses(normalizedOrigin, normalizedDestination);
            this.logger.warn(`Using fallback distance calculation: ${fallbackResult.distanceMiles} miles`);
            await this.cacheDistance(normalizedOrigin, normalizedDestination, fallbackResult.distanceMiles, fallbackResult.durationHours, 'fallback');
            return {
                distanceMiles: fallbackResult.distanceMiles,
                durationHours: fallbackResult.durationHours,
                success: false,
                source: 'fallback'
            };
        }
    }
    async calculateDistancesBatchByAddress(requests) {
        this.logger.log(`Starting batch distance calculation for ${requests.length} routes using full addresses`);
        const results = [];
        let radarSuccess = 0;
        let fallbackUsed = 0;
        let cacheHits = 0;
        for (let i = 0; i < requests.length; i++) {
            const request = requests[i];
            const route = `${request.originAddress} → ${request.destinationAddress}`;
            try {
                this.logger.debug(`Processing ${i + 1}/${requests.length}: ${route}`);
                const result = await this.calculateDistanceByAddress(request.originAddress, request.destinationAddress);
                results.push({
                    ...result,
                    route
                });
                if (result.source === 'cache') {
                    cacheHits++;
                    if (result.success)
                        radarSuccess++;
                    else
                        fallbackUsed++;
                }
                else if (result.success && result.source === 'radar') {
                    radarSuccess++;
                }
                else {
                    fallbackUsed++;
                }
                if ((i + 1) % 10 === 0) {
                    this.logger.log(`Progress: ${i + 1}/${requests.length} routes processed. Cache: ${cacheHits}, Radar: ${radarSuccess}, Fallback: ${fallbackUsed}`);
                }
            }
            catch (error) {
                this.logger.error(`Failed to calculate distance for ${route}: ${error.message}`);
                const fallbackResult = this.calculateFallbackDistanceFromAddresses(request.originAddress, request.destinationAddress);
                results.push({
                    ...fallbackResult,
                    route,
                    success: false,
                    source: 'fallback'
                });
                fallbackUsed++;
            }
        }
        this.logger.log(`Batch calculation complete! Total: ${results.length}, Cache hits: ${cacheHits}, Radar API: ${radarSuccess}, Fallback: ${fallbackUsed}`);
        this.logger.log(`Cache efficiency: ${((cacheHits / results.length) * 100).toFixed(1)}%`);
        this.logger.log(`Radar success rate: ${(((radarSuccess + cacheHits) / results.length) * 100).toFixed(1)}%`);
        return results;
    }
    async getCachedDistance(originAddress, destinationAddress) {
        try {
            const cached = await this.prisma.distanceCache.findUnique({
                where: {
                    originAddress_destinationAddress: {
                        originAddress,
                        destinationAddress
                    }
                }
            });
            return cached;
        }
        catch (error) {
            this.logger.error(`Error retrieving cached distance: ${error.message}`);
            return null;
        }
    }
    async cacheDistance(originAddress, destinationAddress, distanceMiles, durationHours, calculatedBy) {
        try {
            await this.prisma.distanceCache.upsert({
                where: {
                    originAddress_destinationAddress: {
                        originAddress: this.normalizeAddress(originAddress),
                        destinationAddress: this.normalizeAddress(destinationAddress)
                    }
                },
                update: {
                    distanceMiles,
                    durationHours,
                    calculatedBy,
                    updatedAt: new Date()
                },
                create: {
                    originAddress: this.normalizeAddress(originAddress),
                    destinationAddress: this.normalizeAddress(destinationAddress),
                    distanceMiles,
                    durationHours,
                    calculatedBy
                }
            });
            this.logger.debug(`Cached distance: ${originAddress} → ${destinationAddress} = ${distanceMiles} miles`);
        }
        catch (error) {
            this.logger.warn(`Failed to cache distance: ${error.message}`);
        }
    }
    normalizeAddress(address) {
        return address
            .toLowerCase()
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[,.]/g, '')
            .replace(/\b(street|st|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|court|ct|place|pl)\b/gi, '')
            .trim();
    }
    calculateFallbackDistanceFromAddresses(originAddress, destinationAddress) {
        const originState = this.extractStateFromAddress(originAddress);
        const destinationState = this.extractStateFromAddress(destinationAddress);
        if (originState && destinationState) {
            return this.calculateFallbackDistance(originState, destinationState);
        }
        const similarity = this.calculateAddressSimilarity(originAddress, destinationAddress);
        if (similarity > 0.8) {
            return { distanceMiles: 25, durationHours: 0.5 };
        }
        else if (similarity > 0.6) {
            return { distanceMiles: 150, durationHours: 2.5 };
        }
        else {
            return { distanceMiles: 1200, durationHours: 20 };
        }
    }
    extractStateFromAddress(address) {
        const stateRegex = /\b([A-Z]{2})\b/g;
        const matches = address.match(stateRegex);
        return matches ? matches[matches.length - 1] : null;
    }
    calculateAddressSimilarity(addr1, addr2) {
        const normalize = (str) => str.toLowerCase().replace(/[^a-z0-9]/g, '');
        const norm1 = normalize(addr1);
        const norm2 = normalize(addr2);
        if (norm1 === norm2)
            return 1;
        const longer = norm1.length > norm2.length ? norm1 : norm2;
        const shorter = norm1.length > norm2.length ? norm2 : norm1;
        if (longer.length === 0)
            return 1;
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    levenshteinDistance(str1, str2) {
        const matrix = [];
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                }
                else {
                    matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1);
                }
            }
        }
        return matrix[str2.length][str1.length];
    }
    async geocodeAddress(address) {
        const response = await fetch(`${this.BASE_URL}/geocode/forward?query=${encodeURIComponent(address)}`, {
            headers: {
                'Authorization': this.radarApiKey
            }
        });
        if (!response.ok) {
            throw new Error(`Radar geocoding failed: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.addresses || data.addresses.length === 0) {
            throw new Error(`No geocoding results found for address: ${address}`);
        }
        const coords = data.addresses[0];
        this.logger.debug(`Geocoded ${address} → ${coords.latitude}, ${coords.longitude}`);
        return {
            lat: coords.latitude,
            lng: coords.longitude
        };
    }
    async calculateRoute(origin, destination) {
        const params = new URLSearchParams({
            origin: `${origin.lat},${origin.lng}`,
            destination: `${destination.lat},${destination.lng}`,
            modes: 'car',
            units: 'imperial'
        });
        const response = await fetch(`${this.BASE_URL}/route/distance?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Authorization': this.radarApiKey
            }
        });
        if (!response.ok) {
            throw new Error(`Radar routing failed: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.routes || !data.routes.car) {
            throw new Error('No car route found between coordinates');
        }
        const route = data.routes.car;
        const distanceMiles = route.distance ? route.distance.value / 5280 : 0;
        const durationHours = route.duration ? route.duration.value / 60 : 0;
        return {
            distanceMiles: Math.round(distanceMiles * 10) / 10,
            durationHours: Math.round(durationHours * 10) / 10
        };
    }
    calculateFallbackDistance(originState, destinationState) {
        const stateDistances = {
            'CA-AZ': 500, 'AZ-CA': 500,
            'CA-NV': 250, 'NV-CA': 250,
            'CA-TX': 1200, 'TX-CA': 1200,
            'FL-GA': 300, 'GA-FL': 300,
            'FL-AL': 350, 'AL-FL': 350,
            'NY-MA': 200, 'MA-NY': 200,
            'NY-CT': 100, 'CT-NY': 100,
            'IL-TX': 900, 'TX-IL': 900,
            'IL-IN': 150, 'IN-IL': 150,
            'CO-UT': 350, 'UT-CO': 350,
            'WA-OR': 300, 'OR-WA': 300,
        };
        const key = `${originState}-${destinationState}`;
        const distance = stateDistances[key] || 800;
        const duration = distance / 60;
        return {
            distanceMiles: distance,
            durationHours: Math.round(duration * 10) / 10
        };
    }
    async calculateDistance(originCity, originState, destinationCity, destinationState) {
        const originAddress = `${originCity}, ${originState}`;
        const destinationAddress = `${destinationCity}, ${destinationState}`;
        return this.calculateDistanceByAddress(originAddress, destinationAddress);
    }
    async calculateDistancesBatch(requests) {
        const addressRequests = requests.map(req => ({
            originAddress: `${req.originCity}, ${req.originState}`,
            destinationAddress: `${req.destinationCity}, ${req.destinationState}`
        }));
        return this.calculateDistancesBatchByAddress(addressRequests);
    }
};
exports.RadarDistanceService = RadarDistanceService;
exports.RadarDistanceService = RadarDistanceService = RadarDistanceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService])
], RadarDistanceService);
//# sourceMappingURL=radar-distance.service.js.map