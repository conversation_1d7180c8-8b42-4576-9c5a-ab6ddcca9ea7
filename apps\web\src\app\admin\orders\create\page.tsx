"use client";

import React, { useState, useEffect } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Plus,
  Save,
  Truck,
  MapPin,
  Calendar,
  DollarSign,
  Package,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { apiClient } from '@/lib/api-client';

interface Location {
  id: string;
  name: string;
  city: string;
  state: string;
  address?: string;
}

interface LaneInfo {
  id: string;
  name: string;
  originCity: string;
  originState: string;
  destinationCity: string;
  destinationState: string;
  distance: number;
  rate: number;
}

interface Template {
  id: string;
  name: string;
  pickupLocationId: string;
  deliveryLocationId: string;
  rate: number;
  daysToDelivery: number;
}

export default function OrderCreationPage() {
  const { user } = useUser();
  const { getToken } = useAuth();
  
  // Check if user is admin
  if (user?.role !== 'ADMIN') {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">This page is only accessible to administrators.</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // State management
  const [locations, setLocations] = useState<Location[]>([]);
  const [laneInfo, setLaneInfo] = useState<LaneInfo[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form state
  const [orderMode, setOrderMode] = useState<'single' | 'batch'>('single');
  const [pickupLocationId, setPickupLocationId] = useState('');
  const [deliveryLocationId, setDeliveryLocationId] = useState('');
  const [pickupDate, setPickupDate] = useState('');
  const [daysToDelivery, setDaysToDelivery] = useState(1);
  const [rate, setRate] = useState('');
  const [soNumber, setSoNumber] = useState('');
  const [coyoteLoadNo, setCoyoteLoadNo] = useState('');
  const [batchData, setBatchData] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [newTemplateName, setNewTemplateName] = useState('');

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      
      // Load locations, lane info, and templates in parallel
      const [locationsRes, laneInfoRes, templatesRes] = await Promise.all([
        apiClient.get('/admin/locations'),
        apiClient.get('/admin/lane-info'),
        apiClient.get('/admin/order-templates')
      ]);

      setLocations((locationsRes as any)?.locations || []);
      setLaneInfo((laneInfoRes as any)?.lanes || []);
      setTemplates((templatesRes as any)?.templates || []);
      
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast.error('Failed to load page data');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fill rate from lane info when locations are selected
  useEffect(() => {
    if (pickupLocationId && deliveryLocationId) {
      const matchingLane = laneInfo.find(lane => {
        const pickupLocation = locations.find(loc => loc.id === pickupLocationId);
        const deliveryLocation = locations.find(loc => loc.id === deliveryLocationId);
        
        return pickupLocation && deliveryLocation &&
               lane.originCity === pickupLocation.city &&
               lane.originState === pickupLocation.state &&
               lane.destinationCity === deliveryLocation.city &&
               lane.destinationState === deliveryLocation.state;
      });
      
      if (matchingLane && !rate) {
        setRate(matchingLane.rate.toString());
      }
    }
  }, [pickupLocationId, deliveryLocationId, laneInfo, locations, rate]);

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setPickupLocationId(template.pickupLocationId);
      setDeliveryLocationId(template.deliveryLocationId);
      setRate(template.rate.toString());
      setDaysToDelivery(template.daysToDelivery);
      setSelectedTemplate(templateId);
    }
  };

  const handleSaveTemplate = async () => {
    if (!newTemplateName || !pickupLocationId || !deliveryLocationId || !rate) {
      toast.error('Please fill in all fields before saving template');
      return;
    }

    try {
      const templateData = {
        name: newTemplateName,
        pickupLocationId,
        deliveryLocationId,
        rate: parseFloat(rate),
        daysToDelivery
      };

      const response = await apiClient.post('/admin/order-templates', templateData);

      setTemplates([...templates, (response as any).template]);
      setNewTemplateName('');
      toast.success('Template saved successfully');
      
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    }
  };

  const handleSubmit = async () => {
    if (!pickupLocationId || !deliveryLocationId || !pickupDate || !rate) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (orderMode === 'single' && (!soNumber || !coyoteLoadNo)) {
      toast.error('Please fill in SO Number and Coyote Load Number for single order');
      return;
    }

    if (orderMode === 'batch' && !batchData.trim()) {
      toast.error('Please enter batch data for batch orders');
      return;
    }

    try {
      setIsSubmitting(true);

      const orderData = {
        pickupLocationId,
        deliveryLocationId,
        pickupDate,
        daysToDelivery,
        rate: parseFloat(rate),
        mode: orderMode,
        ...(orderMode === 'single' ? {
          soNumber,
          coyoteLoadNo
        } : {
          batchData
        })
      };

      const response = await apiClient.post('/admin/orders/create', orderData) as any;

      if (response.success) {
        toast.success(`Successfully created ${response.createdCount} order(s)`);
        
        // Reset form
        if (orderMode === 'single') {
          setSoNumber('');
          setCoyoteLoadNo('');
        } else {
          setBatchData('');
        }
      }
      
    } catch (error) {
      console.error('Error creating orders:', error);
      toast.error('Failed to create orders');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Creation</h1>
            <p className="text-muted-foreground">
              Create single orders or batch orders for the loadboard
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Order Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Order Mode Toggle */}
                <Tabs value={orderMode} onValueChange={(value) => setOrderMode(value as 'single' | 'batch')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="single">Single Order</TabsTrigger>
                    <TabsTrigger value="batch">Batch Orders</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="single" className="space-y-4 mt-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="soNumber">SO Number *</Label>
                        <Input
                          id="soNumber"
                          value={soNumber}
                          onChange={(e) => setSoNumber(e.target.value)}
                          placeholder="e.g., ITRN1234"
                        />
                      </div>
                      <div>
                        <Label htmlFor="coyoteLoadNo">Coyote Load Number *</Label>
                        <Input
                          id="coyoteLoadNo"
                          value={coyoteLoadNo}
                          onChange={(e) => setCoyoteLoadNo(e.target.value)}
                          placeholder="e.g., LoadA"
                        />
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="batch" className="space-y-4 mt-6">
                    <div>
                      <Label htmlFor="batchData">Batch Data *</Label>
                      <Textarea
                        id="batchData"
                        value={batchData}
                        onChange={(e) => setBatchData(e.target.value)}
                        placeholder="Enter SO No. & Coyote Load No. pairs, separated by SEMICOLONS (;)&#10;Example: ITRN1,LoadA;ITRN2,LoadB;ITRN3,LoadC"
                        rows={6}
                      />
                      <p className="text-sm text-muted-foreground mt-1">
                        Format: SO_Number,Coyote_Load_Number separated by semicolons
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Location Selection */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="pickupLocation">Pickup Location *</Label>
                    <Select value={pickupLocationId} onValueChange={setPickupLocationId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select pickup location" />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name} ({location.city}, {location.state})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="deliveryLocation">Delivery Location *</Label>
                    <Select value={deliveryLocationId} onValueChange={setDeliveryLocationId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select delivery location" />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name} ({location.city}, {location.state})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Date and Rate */}
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="pickupDate">Pickup Date *</Label>
                    <Input
                      id="pickupDate"
                      type="date"
                      value={pickupDate}
                      onChange={(e) => setPickupDate(e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="daysToDelivery">Days to Delivery</Label>
                    <Input
                      id="daysToDelivery"
                      type="number"
                      min="0"
                      value={daysToDelivery}
                      onChange={(e) => setDaysToDelivery(parseInt(e.target.value) || 1)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="rate">Rate ($) *</Label>
                    <Input
                      id="rate"
                      type="number"
                      step="0.01"
                      min="0"
                      value={rate}
                      onChange={(e) => setRate(e.target.value)}
                      placeholder="e.g., 1500.00"
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end">
                  <Button 
                    onClick={handleSubmit} 
                    disabled={isSubmitting}
                    className="min-w-[120px]"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Create {orderMode === 'single' ? 'Order' : 'Orders'}
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Templates Sidebar */}
          <div className="space-y-6">
            {/* Load Template */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Templates</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="templateSelect">Load Template</Label>
                  <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Save Template */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Save Template</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="templateName">Template Name</Label>
                  <Input
                    id="templateName"
                    value={newTemplateName}
                    onChange={(e) => setNewTemplateName(e.target.value)}
                    placeholder="e.g., Dallas to Atlanta"
                  />
                </div>
                <Button 
                  onClick={handleSaveTemplate}
                  variant="outline"
                  className="w-full"
                  disabled={!newTemplateName || !pickupLocationId || !deliveryLocationId}
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save Template
                </Button>
              </CardContent>
            </Card>

            {/* Lane Info Helper */}
            {pickupLocationId && deliveryLocationId && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Lane Information</CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const pickupLocation = locations.find(loc => loc.id === pickupLocationId);
                    const deliveryLocation = locations.find(loc => loc.id === deliveryLocationId);
                    const matchingLane = laneInfo.find(lane => 
                      pickupLocation && deliveryLocation &&
                      lane.originCity === pickupLocation.city &&
                      lane.originState === pickupLocation.state &&
                      lane.destinationCity === deliveryLocation.city &&
                      lane.destinationState === deliveryLocation.state
                    );
                    
                    if (matchingLane) {
                      return (
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Distance:</span>
                            <span className="text-sm font-medium">{matchingLane.distance} miles</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Suggested Rate:</span>
                            <span className="text-sm font-medium">${matchingLane.rate.toLocaleString()}</span>
                          </div>
                        </div>
                      );
                    } else {
                      return (
                        <p className="text-sm text-muted-foreground">
                          No lane information found for this route.
                        </p>
                      );
                    }
                  })()}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
