import { Injectable, Logger } from '@nestjs/common';

export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
}

export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN', 
  HALF_OPEN = 'HALF_OPEN'
}

interface CircuitBreakerStats {
  failures: number;
  successes: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  state: CircuitState;
  halfOpenCalls: number;
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private circuits: Map<string, CircuitBreakerStats> = new Map();
  
  private readonly defaultConfig: CircuitBreakerConfig = {
    failureThreshold: 5,           // Open circuit after 5 failures
    recoveryTimeout: 60000,        // Wait 60 seconds before attempting recovery
    monitoringPeriod: 300000,      // Reset counters every 5 minutes
    halfOpenMaxCalls: 3            // Allow 3 calls in half-open state
  };

  async execute<T>(
    circuitName: string,
    operation: () => Promise<T>,
    fallback?: () => Promise<T>,
    config: Partial<CircuitBreakerConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const circuit = this.getOrCreateCircuit(circuitName);
    
    // Check if circuit should be opened/closed
    this.updateCircuitState(circuit, finalConfig);
    
    // Log circuit state for monitoring
    this.logCircuitState(circuitName, circuit);

    switch (circuit.state) {
      case CircuitState.OPEN:
        if (fallback) {
          this.logger.warn(`Circuit breaker OPEN for ${circuitName}, executing fallback`);
          return await fallback();
        } else {
          throw new Error(`Service ${circuitName} is temporarily unavailable`);
        }

      case CircuitState.HALF_OPEN:
        if (circuit.halfOpenCalls >= finalConfig.halfOpenMaxCalls) {
          // Too many calls in half-open state, circuit should open
          circuit.state = CircuitState.OPEN;
          circuit.lastFailureTime = Date.now();
          
          if (fallback) {
            this.logger.warn(`Circuit breaker reopened for ${circuitName}, executing fallback`);
            return await fallback();
          } else {
            throw new Error(`Service ${circuitName} is temporarily unavailable`);
          }
        }
        circuit.halfOpenCalls++;
        break;

      case CircuitState.CLOSED:
        // Circuit is closed, proceed normally
        break;
    }

    try {
      const result = await this.executeWithTimeout(operation, 30000); // 30 second timeout
      
      // Operation succeeded
      circuit.successes++;
      circuit.lastSuccessTime = Date.now();
      
      if (circuit.state === CircuitState.HALF_OPEN) {
        // Successful call in half-open state, close the circuit
        circuit.state = CircuitState.CLOSED;
        circuit.failures = 0;
        circuit.halfOpenCalls = 0;
        this.logger.log(`Circuit breaker CLOSED for ${circuitName} after successful recovery`);
      }
      
      return result;
    } catch (error) {
      // Operation failed
      circuit.failures++;
      circuit.lastFailureTime = Date.now();
      
      if (circuit.state === CircuitState.HALF_OPEN) {
        // Failed call in half-open state, open the circuit
        circuit.state = CircuitState.OPEN;
        circuit.halfOpenCalls = 0;
        this.logger.warn(`Circuit breaker reopened for ${circuitName} after failure in half-open state`);
      } else if (circuit.failures >= finalConfig.failureThreshold) {
        // Too many failures, open the circuit
        circuit.state = CircuitState.OPEN;
        this.logger.error(`Circuit breaker OPENED for ${circuitName} after ${circuit.failures} failures`);
      }
      
      // If we have a fallback and circuit is open, use it
      if (circuit.state === CircuitState.OPEN && fallback) {
        this.logger.warn(`Circuit breaker OPEN for ${circuitName}, executing fallback after failure`);
        return await fallback();
      }
      
      throw error;
    }
  }

  private executeWithTimeout<T>(operation: () => Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<T>((_, reject) => 
        setTimeout(() => reject(new Error('Operation timeout')), timeoutMs)
      )
    ]);
  }

  private getOrCreateCircuit(circuitName: string): CircuitBreakerStats {
    if (!this.circuits.has(circuitName)) {
      this.circuits.set(circuitName, {
        failures: 0,
        successes: 0,
        lastFailureTime: 0,
        lastSuccessTime: 0,
        state: CircuitState.CLOSED,
        halfOpenCalls: 0
      });
    }
    
    return this.circuits.get(circuitName)!;
  }

  private updateCircuitState(circuit: CircuitBreakerStats, config: CircuitBreakerConfig): void {
    const now = Date.now();
    
    // Reset counters if monitoring period has passed
    if (now - Math.max(circuit.lastFailureTime, circuit.lastSuccessTime) > config.monitoringPeriod) {
      circuit.failures = 0;
      circuit.successes = 0;
    }
    
    // Check if we should transition from OPEN to HALF_OPEN
    if (circuit.state === CircuitState.OPEN && 
        now - circuit.lastFailureTime > config.recoveryTimeout) {
      circuit.state = CircuitState.HALF_OPEN;
      circuit.halfOpenCalls = 0;
    }
  }

  private logCircuitState(circuitName: string, circuit: CircuitBreakerStats): void {
    // Log circuit state changes and periodic health status
    const now = Date.now();
    const timeSinceLastLog = now - (circuit as any).lastLogTime || 0;
    
    // Log every 5 minutes or on state changes
    if (timeSinceLastLog > 300000 || !(circuit as any).lastLoggedState || 
        (circuit as any).lastLoggedState !== circuit.state) {
      
      this.logger.log(`Circuit Breaker Status [${circuitName}]`, {
        circuitName,
        state: circuit.state,
        failures: circuit.failures,
        successes: circuit.successes,
        halfOpenCalls: circuit.halfOpenCalls,
        lastFailureTime: circuit.lastFailureTime ? new Date(circuit.lastFailureTime).toISOString() : 'never',
        lastSuccessTime: circuit.lastSuccessTime ? new Date(circuit.lastSuccessTime).toISOString() : 'never'
      });
      
      (circuit as any).lastLogTime = now;
      (circuit as any).lastLoggedState = circuit.state;
    }
  }

  // Get current status of all circuits for health checks
  getCircuitStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [name, circuit] of this.circuits.entries()) {
      status[name] = {
        state: circuit.state,
        failures: circuit.failures,
        successes: circuit.successes,
        healthyStatus: circuit.state === CircuitState.CLOSED ? 'healthy' : 'degraded',
        lastFailureTime: circuit.lastFailureTime ? new Date(circuit.lastFailureTime).toISOString() : null,
        lastSuccessTime: circuit.lastSuccessTime ? new Date(circuit.lastSuccessTime).toISOString() : null
      };
    }
    
    return status;
  }

  // Reset a specific circuit (for admin/debugging purposes)
  resetCircuit(circuitName: string): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.failures = 0;
      circuit.successes = 0;
      circuit.state = CircuitState.CLOSED;
      circuit.halfOpenCalls = 0;
      this.logger.log(`Circuit breaker ${circuitName} has been manually reset`);
    }
  }

  // Get circuit health check endpoint data
  getHealthCheck(): { status: string; circuits: Record<string, any> } {
    const circuits = this.getCircuitStatus();
    const hasOpenCircuits = Object.values(circuits).some((c: any) => c.state === CircuitState.OPEN);
    
    return {
      status: hasOpenCircuits ? 'degraded' : 'healthy',
      circuits
    };
  }
} 