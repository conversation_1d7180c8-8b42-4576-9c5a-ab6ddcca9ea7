-- Add OrderTemplate model for admin order creation templates
-- This is a purely additive migration that does not affect existing data

-- Create order_templates table
CREATE TABLE "order_templates" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "pickup_location_id" TEXT NOT NULL,
    "delivery_location_id" TEXT NOT NULL,
    "rate" DOUBLE PRECISION NOT NULL,
    "days_to_delivery" INTEGER NOT NULL,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "order_templates_pkey" PRIMARY KEY ("id")
);

-- Create indexes for performance
CREATE INDEX "order_templates_created_by_idx" ON "order_templates"("created_by");
CREATE INDEX "order_templates_name_idx" ON "order_templates"("name");
