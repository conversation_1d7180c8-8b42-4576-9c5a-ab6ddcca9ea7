"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SmartSuggestionsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmartSuggestionsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const pattern_analysis_service_1 = require("./pattern-analysis.service");
let SmartSuggestionsService = SmartSuggestionsService_1 = class SmartSuggestionsService {
    prisma;
    patternAnalysis;
    logger = new common_1.Logger(SmartSuggestionsService_1.name);
    healthCheckCache = { isHealthy: true, lastCheck: 0 };
    HEALTH_CHECK_INTERVAL = 30000;
    DB_TIMEOUT = 5000;
    constructor(prisma, patternAnalysis) {
        this.prisma = prisma;
        this.patternAnalysis = patternAnalysis;
    }
    async checkDatabaseHealth() {
        const now = Date.now();
        if (now - this.healthCheckCache.lastCheck < this.HEALTH_CHECK_INTERVAL) {
            return this.healthCheckCache.isHealthy;
        }
        try {
            const startTime = Date.now();
            await Promise.race([
                this.prisma.$queryRaw `SELECT 1`,
                new Promise((_, reject) => setTimeout(() => reject(new Error('Database health check timeout')), this.DB_TIMEOUT))
            ]);
            const responseTime = Date.now() - startTime;
            this.healthCheckCache = { isHealthy: true, lastCheck: now };
            this.logger.log(`Database health check passed in ${responseTime}ms`);
            return true;
        }
        catch (error) {
            this.logger.error('Database health check failed:', error.message);
            this.healthCheckCache = { isHealthy: false, lastCheck: now };
            return false;
        }
    }
    async safeDbOperation(operation, fallback, operationName) {
        try {
            const isHealthy = await this.checkDatabaseHealth();
            if (!isHealthy) {
                this.logger.warn(`Database unhealthy, using fallback for ${operationName}`);
                return fallback;
            }
            const result = await Promise.race([
                operation(),
                new Promise((_, reject) => setTimeout(() => reject(new Error(`${operationName} operation timeout`)), this.DB_TIMEOUT))
            ]);
            return result;
        }
        catch (error) {
            this.logger.error(`Database operation failed for ${operationName}:`, error.message);
            return fallback;
        }
    }
    async getOrderSuggestions(orderContext) {
        this.logger.log(`[DEBUG] Generating smart suggestions for user ${orderContext.userId}`);
        this.logger.log(`[DEBUG] Lane: ${orderContext.originCity}, ${orderContext.originState} → ${orderContext.destinationCity}, ${orderContext.destinationState}`);
        this.logger.log(`[DEBUG] Current values: ${JSON.stringify(orderContext.currentValues)}`);
        const fallbackResult = {
            suggestions: [],
            smartDefaults: {},
            confidence: 0,
            metadata: {
                error: 'Service temporarily unavailable',
                generatedAt: new Date(),
                dataPoints: 0,
                learningActive: false
            }
        };
        try {
            this.logger.log('[DEBUG] Step 1: Getting base suggestions from pattern analysis...');
            const baseSuggestions = await this.safeDbOperation(() => this.patternAnalysis.generateSmartSuggestions(orderContext.originCity, orderContext.originState, orderContext.destinationCity, orderContext.destinationState, orderContext.userId, orderContext.currentValues), [], 'pattern analysis');
            this.logger.log(`[DEBUG] Step 1 Complete: Got ${baseSuggestions.length} base suggestions`);
            this.logger.log('[DEBUG] Step 2: Enhancing with learned insights...');
            const enhancedSuggestions = await this.enhanceWithLearnings(baseSuggestions, orderContext);
            this.logger.log(`[DEBUG] Step 2 Complete: Enhanced ${enhancedSuggestions.length} suggestions`);
            this.logger.log('[DEBUG] Step 3: Applying market adjustments...');
            const marketAdjustedSuggestions = await this.applyMarketAdjustments(enhancedSuggestions);
            this.logger.log(`[DEBUG] Step 3 Complete: Market adjusted ${marketAdjustedSuggestions.length} suggestions`);
            this.logger.log('[DEBUG] Step 4: Generating smart defaults...');
            const smartDefaults = await this.generateSmartDefaults(orderContext);
            this.logger.log(`[DEBUG] Step 4 Complete: Generated ${Object.keys(smartDefaults).length} smart defaults`);
            const confidence = this.calculateOverallConfidence(marketAdjustedSuggestions);
            const result = {
                suggestions: marketAdjustedSuggestions,
                smartDefaults,
                confidence,
                metadata: {
                    generatedAt: new Date(),
                    dataPoints: baseSuggestions.length,
                    learningActive: true,
                    databaseHealthy: this.healthCheckCache.isHealthy
                }
            };
            this.logger.log(`[DEBUG] SUCCESS: Generated ${result.suggestions.length} suggestions with ${(confidence * 100).toFixed(1)}% confidence`);
            return result;
        }
        catch (error) {
            this.logger.error('[ERROR] Error generating order suggestions:', error);
            this.logger.error('[ERROR] Stack trace:', error.stack);
            this.logger.log('[DEBUG] Returning fallback result due to error');
            return fallbackResult;
        }
    }
    async recordSuggestionFeedback(feedback) {
        this.logger.log(`Recording suggestion feedback for user ${feedback.userId}`);
        try {
            const feedbackRecords = [];
            feedback.suggestions.forEach(suggestion => {
                let actualValue = null;
                let accepted = false;
                switch (suggestion.type) {
                    case 'rate':
                        actualValue = feedback.actualValues.rate;
                        accepted = actualValue && Math.abs(actualValue - suggestion.suggestion.rate) < (suggestion.suggestion.rate * 0.1);
                        break;
                    case 'equipment':
                        actualValue = feedback.actualValues.equipmentRequired;
                        accepted = actualValue === suggestion.suggestion.equipment;
                        break;
                    case 'weight':
                        actualValue = feedback.actualValues.weightLbs;
                        accepted = actualValue && Math.abs(actualValue - suggestion.suggestion.weight) < (suggestion.suggestion.weight * 0.2);
                        break;
                    case 'timing':
                        if (feedback.actualValues.pickupDate && feedback.actualValues.deliveryDate) {
                            const actualDays = this.calculateDaysDifference(feedback.actualValues.pickupDate, feedback.actualValues.deliveryDate);
                            accepted = Math.abs(actualDays - suggestion.suggestion.transitDays) <= 1;
                            actualValue = actualDays;
                        }
                        break;
                }
                feedbackRecords.push({
                    userId: feedback.userId,
                    suggestionType: suggestion.type,
                    suggestedValue: suggestion.suggestion,
                    actualValue,
                    accepted,
                    timestamp: new Date(),
                    orderContext: {
                        laneId: `${feedback.orderContext.originState}_${feedback.orderContext.destinationState}`,
                        equipment: feedback.actualValues.equipmentRequired,
                        weight: feedback.actualValues.weightLbs
                    }
                });
            });
            await this.storeFeedbackData(feedback.orderId, feedbackRecords);
            await this.updateLearnedInsights(feedbackRecords);
            this.logger.log(`Recorded ${feedbackRecords.length} feedback items`);
        }
        catch (error) {
            this.logger.error('Error recording suggestion feedback:', error);
        }
    }
    async getAutoCompleteSuggestions(field, partialValue, context) {
        this.logger.log(`Getting auto-complete suggestions for ${field}: "${partialValue}"`);
        try {
            switch (field) {
                case 'poNumber':
                    return this.suggestPONumbers(partialValue, context.userId);
                case 'rate':
                    return this.suggestRates(partialValue, context);
                case 'weight':
                    return this.suggestWeights(partialValue, context);
                case 'notes':
                    return this.suggestNotes(partialValue, context);
                default:
                    return [];
            }
        }
        catch (error) {
            this.logger.error(`Error getting auto-complete for ${field}:`, error);
            return [];
        }
    }
    async validateOrderData(orderData, context) {
        this.logger.log('[VALIDATION-DEBUG] Starting order data validation');
        this.logger.log(`[VALIDATION-DEBUG] Order data: ${JSON.stringify(orderData)}`);
        this.logger.log(`[VALIDATION-DEBUG] Context: ${JSON.stringify(context)}`);
        const validationResults = {
            isValid: true,
            warnings: [],
            suggestions: [],
            criticalIssues: []
        };
        try {
            this.logger.log('[VALIDATION-DEBUG] Step 1: Running business rule validations...');
            await this.validateBusinessRules(orderData, validationResults);
            this.logger.log(`[VALIDATION-DEBUG] Step 1 Complete: ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);
            this.logger.log('[VALIDATION-DEBUG] Step 2: Running historical pattern validations...');
            await this.safeDbOperation(() => this.validateAgainstPatterns(orderData, context, validationResults), undefined, 'pattern validation');
            this.logger.log(`[VALIDATION-DEBUG] Step 2 Complete: ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);
            this.logger.log('[VALIDATION-DEBUG] Step 3: Running market condition validations...');
            await this.validateMarketConditions(orderData, validationResults);
            this.logger.log(`[VALIDATION-DEBUG] Step 3 Complete: ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);
            this.logger.log(`[VALIDATION-DEBUG] SUCCESS: Validation complete - ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);
        }
        catch (error) {
            this.logger.error('[VALIDATION-ERROR] Error in smart validation:', error);
            this.logger.error('[VALIDATION-ERROR] Stack trace:', error.stack);
            validationResults.criticalIssues.push({
                type: 'system_error',
                message: 'Validation system temporarily unavailable',
                details: error.message
            });
        }
        validationResults.isValid = validationResults.criticalIssues.length === 0;
        return validationResults;
    }
    async enhanceWithLearnings(baseSuggestions, context) {
        const userInsights = await this.getUserLearnedInsights(context.userId);
        return baseSuggestions.map(suggestion => {
            const relevantInsight = userInsights.find(insight => insight.type.includes(suggestion.type) && insight.confidence > 0.6);
            if (relevantInsight) {
                return {
                    ...suggestion,
                    confidence: Math.min(suggestion.confidence * 1.2, 1.0),
                    suggestion: this.adjustSuggestionWithInsight(suggestion.suggestion, relevantInsight),
                    reasoning: `${suggestion.reasoning} (Enhanced with your preferences)`
                };
            }
            return suggestion;
        });
    }
    async applyMarketAdjustments(suggestions) {
        const currentMonth = new Date().getMonth() + 1;
        const seasonalFactors = {
            12: 1.1, 1: 1.1, 2: 1.05,
            3: 1.0, 4: 1.0, 5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
            9: 1.05, 10: 1.1, 11: 1.15
        };
        const seasonalFactor = seasonalFactors[currentMonth] || 1.0;
        return suggestions.map(suggestion => {
            if (suggestion.type === 'rate' && seasonalFactor !== 1.0) {
                return {
                    ...suggestion,
                    suggestion: {
                        ...suggestion.suggestion,
                        rate: Math.round(suggestion.suggestion.rate * seasonalFactor),
                        seasonalAdjustment: seasonalFactor
                    },
                    reasoning: `${suggestion.reasoning} (Seasonal adjustment: ${((seasonalFactor - 1) * 100).toFixed(0)}%)`
                };
            }
            return suggestion;
        });
    }
    async generateSmartDefaults(context) {
        const defaults = {};
        try {
            const userPrefs = await this.patternAnalysis.analyzeUserPreferences(context.userId);
            if (userPrefs && userPrefs.confidenceScore > 0.5) {
                const preferredEquipment = Object.entries(userPrefs.preferredEquipment)
                    .sort(([, a], [, b]) => b - a)[0];
                if (preferredEquipment) {
                    defaults.equipmentRequired = preferredEquipment[0];
                }
                if (defaults.equipmentRequired && userPrefs.preferredWeightRanges[defaults.equipmentRequired]) {
                    const weightRange = userPrefs.preferredWeightRanges[defaults.equipmentRequired];
                    defaults.weightLbs = Math.round((weightRange.min + weightRange.max) / 2);
                }
            }
            defaults.poNumber = await this.generateSmartPONumber(context.userId);
        }
        catch (error) {
            this.logger.error('Error generating smart defaults:', error);
        }
        return defaults;
    }
    calculateOverallConfidence(suggestions) {
        if (suggestions.length === 0)
            return 0;
        const avgConfidence = suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length;
        return Math.round(avgConfidence * 100) / 100;
    }
    calculateDaysDifference(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    }
    async storeFeedbackData(orderId, feedbackRecords) {
        await this.prisma.load.update({
            where: { id: orderId },
            data: {
                rawAirtableData: {
                    suggestionFeedback: feedbackRecords.map(record => ({
                        userId: record.userId,
                        suggestionType: record.suggestionType,
                        suggestedValue: record.suggestedValue,
                        actualValue: record.actualValue,
                        accepted: record.accepted,
                        timestamp: record.timestamp.toISOString(),
                        orderContext: record.orderContext
                    }))
                }
            }
        });
    }
    async updateLearnedInsights(feedbackRecords) {
        feedbackRecords.forEach(record => {
            if (record.accepted) {
                this.logger.log(`Positive feedback: ${record.suggestionType} suggestion accepted`);
            }
            else {
                this.logger.log(`Negative feedback: ${record.suggestionType} suggestion rejected, actual: ${JSON.stringify(record.actualValue)}`);
            }
        });
    }
    async getUserLearnedInsights(userId) {
        return [
            {
                type: 'rate_adjustment',
                userId,
                pattern: 'typically_adjusts_rates_up_by_5_percent',
                confidence: 0.8,
                usageCount: 15,
                lastUpdated: new Date()
            }
        ];
    }
    adjustSuggestionWithInsight(suggestion, insight) {
        if (insight.type === 'rate_adjustment' && suggestion.rate) {
            return {
                ...suggestion,
                rate: Math.round(suggestion.rate * 1.05)
            };
        }
        return suggestion;
    }
    async suggestPONumbers(partial, userId) {
        const suggestions = [];
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        if (partial.startsWith('PO-')) {
            for (let i = 1; i <= 5; i++) {
                suggestions.push(`PO-${year}${month}${day}-${String(i).padStart(3, '0')}`);
            }
        }
        return suggestions.filter(s => s.toLowerCase().includes(partial.toLowerCase()));
    }
    async suggestRates(partial, context) {
        const lanePattern = await this.patternAnalysis.analyzeLanePatterns(context.originCity, context.originState, context.destinationCity, context.destinationState);
        if (!lanePattern)
            return [];
        const baseRate = lanePattern.medianRate;
        return [
            Math.round(baseRate * 0.95),
            baseRate,
            Math.round(baseRate * 1.05),
            Math.round(baseRate * 1.1)
        ].filter(rate => rate.toString().includes(partial));
    }
    async suggestWeights(partial, context) {
        const commonWeights = {
            'Dry Van': [25000, 35000, 45000],
            'Reefer': [20000, 30000, 40000],
            'Flatbed': [35000, 45000, 48000]
        };
        const equipment = context.equipmentRequired || 'Dry Van';
        const weights = commonWeights[equipment] || commonWeights['Dry Van'];
        return weights.filter(weight => weight.toString().includes(partial));
    }
    async suggestNotes(partial, context) {
        const commonNotes = [
            'Handle with care - fragile cargo',
            'Temperature sensitive - maintain cold chain',
            'Call before delivery',
            'Delivery appointment required',
            'Dock high loading/unloading',
            'Inside delivery required'
        ];
        return commonNotes.filter(note => note.toLowerCase().includes(partial.toLowerCase()));
    }
    async generateSmartPONumber(userId) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        let counter = 1;
        let poNumber;
        do {
            poNumber = `PO-${year}${month}${day}-${String(counter).padStart(3, '0')}`;
            counter++;
            const existing = await this.prisma.load.findFirst({
                where: { proNumber: poNumber }
            });
            if (!existing)
                break;
        } while (counter <= 999);
        return poNumber;
    }
    async validateBusinessRules(orderData, results) {
        if (orderData.rate && orderData.rate < 100) {
            results.warnings.push({
                type: 'low_rate',
                message: 'Rate appears unusually low',
                suggestion: 'Double-check rate calculation'
            });
        }
        if (orderData.weightLbs && orderData.equipmentRequired) {
            const maxWeights = {
                'Dry Van': 48000,
                'Reefer': 45000,
                'Flatbed': 48000
            };
            const maxWeight = maxWeights[orderData.equipmentRequired];
            if (maxWeight && orderData.weightLbs > maxWeight) {
                results.criticalIssues.push({
                    type: 'overweight',
                    message: `Weight exceeds capacity for ${orderData.equipmentRequired}`,
                    maxAllowed: maxWeight
                });
                results.isValid = false;
            }
        }
    }
    async validateAgainstPatterns(orderData, context, results) {
        const lanePattern = await this.patternAnalysis.analyzeLanePatterns(context.originCity, context.originState, context.destinationCity, context.destinationState);
        if (lanePattern && orderData.rate) {
            if (orderData.rate < lanePattern.rateRange.min * 0.7) {
                results.warnings.push({
                    type: 'rate_below_market',
                    message: 'Rate is significantly below market average',
                    marketRange: lanePattern.rateRange
                });
            }
        }
    }
    async validateMarketConditions(orderData, results) {
        const currentMonth = new Date().getMonth() + 1;
        if ([11, 12].includes(currentMonth)) {
            results.suggestions.push({
                type: 'peak_season',
                message: 'Peak shipping season - consider rate premiums and extended transit times',
                impact: 'Expect 10-15% higher rates and potential delays'
            });
        }
    }
};
exports.SmartSuggestionsService = SmartSuggestionsService;
exports.SmartSuggestionsService = SmartSuggestionsService = SmartSuggestionsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        pattern_analysis_service_1.PatternAnalysisService])
], SmartSuggestionsService);
//# sourceMappingURL=smart-suggestions.service.js.map