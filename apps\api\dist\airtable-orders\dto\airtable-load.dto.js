"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AirtableWebhookEventDto = exports.AirtableWebhookPayloadDto = exports.AirtableLoadDataDto = exports.AirtableLoadWebhookDto = void 0;
class AirtableLoadWebhookDto {
    airtable_record_id;
    status;
    payout;
    origin_city;
    origin_state;
    destination_city;
    destination_state;
    distance;
    pro_number;
    pickup_date_utc;
    delivery_date_utc;
    equipment_required;
    weight_lbs;
    rate;
    temperature;
    target_organizations;
    is_public;
    raw_airtable_data;
}
exports.AirtableLoadWebhookDto = AirtableLoadWebhookDto;
class AirtableLoadDataDto {
    'Record ID';
    'Status';
    'Payout';
    'Origin City';
    'Origin State';
    'Destination City';
    'Destination State';
    'Distance (Miles)';
    'Pro Number';
}
exports.AirtableLoadDataDto = AirtableLoadDataDto;
class AirtableWebhookPayloadDto {
    airtableRecordId;
    fields;
}
exports.AirtableWebhookPayloadDto = AirtableWebhookPayloadDto;
class AirtableWebhookEventDto {
    payload;
}
exports.AirtableWebhookEventDto = AirtableWebhookEventDto;
//# sourceMappingURL=airtable-load.dto.js.map