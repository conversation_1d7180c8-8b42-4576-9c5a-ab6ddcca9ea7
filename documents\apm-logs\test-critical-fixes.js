// Test script for critical fixes P2-T0A and P2-T0B
// Run with: node test-critical-fixes.js

const https = require('https');

const API_BASE = 'https://api.fcp-portal.com/api/v1';

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test P2-T0A: Airtable Bid Integration Debug
async function testBidIntegration() {
  console.log('\n🔍 Testing P2-T0A: Airtable Bid Integration');
  console.log('==========================================');
  
  try {
    // First, get available loads to find a test load ID
    console.log('1. Fetching available loads...');
    const loadsResponse = await makeRequest({
      hostname: 'api.fcp-portal.com',
      path: '/api/v1/airtable-orders/available',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (loadsResponse.statusCode === 200 && loadsResponse.body && loadsResponse.body.length > 0) {
      const testLoad = loadsResponse.body[0];
      console.log(`✅ Found test load: ${testLoad.id} (${testLoad.proNumber})`);
      
      // Test the debug endpoint for bid field configuration
      console.log('2. Testing bid field configuration...');
      const debugResponse = await makeRequest({
        hostname: 'api.fcp-portal.com',
        path: `/api/v1/airtable-orders/${testLoad.id}/debug-bids`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Debug Response Status: ${debugResponse.statusCode}`);
      if (debugResponse.body) {
        console.log('Debug Response:', JSON.stringify(debugResponse.body, null, 2));
        
        if (debugResponse.body.recommendation) {
          console.log(`💡 Recommendation: ${debugResponse.body.recommendation}`);
        }
        
        if (debugResponse.body.availableFields) {
          console.log(`📋 Available Airtable fields: ${debugResponse.body.availableFields.join(', ')}`);
        }
      }
    } else {
      console.log('❌ No available loads found for testing');
    }
  } catch (error) {
    console.error('❌ Error testing bid integration:', error.message);
  }
}

// Test P2-T0B: Targeted Load Display
async function testTargetedLoads() {
  console.log('\n🎯 Testing P2-T0B: Targeted Load Display');
  console.log('======================================');
  
  try {
    // Get available loads and check for targeting metadata
    console.log('1. Checking loads for targeting metadata...');
    const loadsResponse = await makeRequest({
      hostname: 'api.fcp-portal.com',
      path: '/api/v1/airtable-orders/available',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (loadsResponse.statusCode === 200 && loadsResponse.body) {
      const loads = loadsResponse.body;
      console.log(`✅ Found ${loads.length} loads`);
      
      // Check for targeting fields
      const targetedLoads = loads.filter(load => 
        load.hasOwnProperty('isPublic') || 
        load.hasOwnProperty('isTargeted') || 
        load.hasOwnProperty('targetOrganizations')
      );
      
      const publicLoads = loads.filter(load => load.isPublic === true);
      const privateLoads = loads.filter(load => load.isPublic === false);
      const organizedLoads = loads.filter(load => load.targetOrganizations && load.targetOrganizations.length > 0);
      
      console.log(`📊 Load Analysis:`);
      console.log(`   - Total loads: ${loads.length}`);
      console.log(`   - Loads with targeting metadata: ${targetedLoads.length}`);
      console.log(`   - Public loads: ${publicLoads.length}`);
      console.log(`   - Private loads: ${privateLoads.length}`);
      console.log(`   - Organization-targeted loads: ${organizedLoads.length}`);
      
      if (targetedLoads.length > 0) {
        console.log('\n📋 Sample targeting data:');
        targetedLoads.slice(0, 3).forEach((load, index) => {
          console.log(`   Load ${index + 1}:`);
          console.log(`     - ID: ${load.id}`);
          console.log(`     - isPublic: ${load.isPublic}`);
          console.log(`     - isTargeted: ${load.isTargeted}`);
          console.log(`     - targetOrganizations: ${JSON.stringify(load.targetOrganizations)}`);
        });
      } else {
        console.log('⚠️  No loads found with targeting metadata - webhook fix may need testing');
      }
    } else {
      console.log('❌ Failed to fetch loads for targeting analysis');
    }
  } catch (error) {
    console.error('❌ Error testing targeted loads:', error.message);
  }
}

// Test API Health
async function testAPIHealth() {
  console.log('\n🏥 Testing API Health');
  console.log('====================');
  
  try {
    const healthResponse = await makeRequest({
      hostname: 'api.fcp-portal.com',
      path: '/api/v1/health',
      method: 'GET'
    });
    
    if (healthResponse.statusCode === 200) {
      console.log('✅ API is healthy');
      console.log(`   Status: ${healthResponse.body.status}`);
      console.log(`   Environment: ${healthResponse.body.environment}`);
      console.log(`   Version: ${healthResponse.body.version}`);
    } else {
      console.log(`❌ API health check failed: ${healthResponse.statusCode}`);
    }
  } catch (error) {
    console.error('❌ Error checking API health:', error.message);
  }
}

// Main test execution
async function runTests() {
  console.log('🚀 Starting Critical Fixes Test Suite');
  console.log('=====================================');
  console.log('Testing fixes for P2-T0A (Bid Integration) and P2-T0B (Targeted Loads)');
  
  await testAPIHealth();
  await testBidIntegration();
  await testTargetedLoads();
  
  console.log('\n✅ Test suite completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. If bid debug shows field issues, create "Bids" Multiple Select field in Airtable');
  console.log('2. If no targeting metadata found, test webhook with targeted load creation');
  console.log('3. Monitor logs during load creation/bidding for error details');
  console.log('4. Verify organization names match exactly between Clerk and Airtable');
}

// Run the tests
runTests().catch(console.error); 