{"name": "Counter Form Submission Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "admin/counter-submit", "responseMode": "responseNode", "options": {}}, "id": "webhook-counter-submit", "name": "Webhook - Counter Form Submit", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 300], "webhookId": "admin-counter-submit"}, {"parameters": {"functionCode": "// Counter Form Submission - Process Form Data Function Node\n\nconst formData = $json.body;\nconst bidId = formData.bidId;\nconst counterAmount = parseFloat(formData.counterAmount);\nconst notes = formData.notes || '';\n\n// Validate form data\nif (!bidId || !counterAmount || counterAmount <= 0) {\n  throw new Error('Invalid form data: Missing bidId or counterAmount');\n}\n\n// Generate admin JWT for internal operations\nconst jwt = require('jsonwebtoken');\nconst adminPayload = {\n  id: 'admin_system',\n  role: 'admin',\n  action: 'counter',\n  bidId: bidId,\n  timestamp: Date.now()\n};\n\nconst adminJWT = jwt.sign(adminPayload, process.env.N8N_JWT_SECRET, { expiresIn: '1h' });\n\nreturn {\n  bidId,\n  counterAmount,\n  adminNotes: notes,\n  responseTime: new Date().toISOString(),\n  adminJWT,\n  action: 'counter'\n};"}, "id": "process-counter-form", "name": "Process Counter Form", "type": "n8n-nodes-base.function", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"authentication": "airtableTokenApi", "operation": "list", "base": {"__rl": true, "value": "YOUR_AIRTABLE_BASE_ID", "mode": "id"}, "table": {"__rl": true, "value": "Bids", "mode": "id"}, "filterByFormula": "={Bid ID} = '{{ $json.bidId }}'", "options": {}}, "id": "get-bid-for-counter", "name": "Get Bid for Counter", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [600, 300], "credentials": {"airtableTokenApi": {"id": "airtable-credential", "name": "Airtable API"}}}, {"parameters": {"authentication": "airtableTokenApi", "operation": "update", "base": {"__rl": true, "value": "YOUR_AIRTABLE_BASE_ID", "mode": "id"}, "table": {"__rl": true, "value": "Bids", "mode": "id"}, "id": "={{ $json.records[0].id }}", "updateAllFields": false, "fieldsUi": {"fieldValues": [{"fieldId": "Admin Response", "fieldValue": "counter"}, {"fieldId": "Counter Amount", "fieldValue": "={{ $node['Process Counter Form'].json.counterAmount }}"}, {"fieldId": "Admin Notes", "fieldValue": "={{ $node['Process Counter Form'].json.adminNotes }}"}, {"fieldId": "Responded At", "fieldValue": "={{ $node['Process Counter Form'].json.responseTime }}"}, {"fieldId": "Bid Status", "fieldValue": "counter_offered"}]}, "options": {}}, "id": "update-bid-counter", "name": "Update Bid with Counter", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [800, 300], "credentials": {"airtableTokenApi": {"id": "airtable-credential", "name": "Airtable API"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.records[0].fields['Carrier Email'] }}", "subject": "💬 Counter Offer Received - Response Required", "emailType": "html", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #d97706; color: white; padding: 20px; border-radius: 8px 8px 0 0;\">\n    <h1 style=\"margin: 0; font-size: 24px;\">💬 Counter Offer Received</h1>\n    <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">We have a counter offer for your bid</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0;\">\n    <div style=\"background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h2 style=\"color: #1f2937; margin-top: 0;\">Counter Offer Details</h2>\n      <div style=\"margin: 15px 0;\">\n        <strong>Bid ID:</strong> {{ $node['Process Counter Form'].json.bidId }}<br>\n        <strong>Your Original Bid:</strong> ${{ $json.records[0].fields['Bid Amount'] }}<br>\n        <strong>Our Counter Offer:</strong> <span style=\"color: #d97706; font-size: 18px; font-weight: bold;\">${{ $node['Process Counter Form'].json.counterAmount }}</span><br>\n      </div>\n      {{ $node['Process Counter Form'].json.adminNotes ? `<div style=\"margin-top: 15px; padding: 15px; background: #fef3c7; border-radius: 6px;\"><strong>Admin Notes:</strong><br>${$node['Process Counter Form'].json.adminNotes}</div>` : '' }}\n    </div>\n\n    <div style=\"text-align: center; margin: 30px 0;\">\n      <h3 style=\"color: #1f2937;\">Your Response Options</h3>\n      <div style=\"display: inline-block; margin: 10px;\">\n        <a href=\"https://firstcutproduce.app.n8n.cloud/webhook/carrier/counter-accept?bidId={{ $node['Process Counter Form'].json.bidId }}\" style=\"background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;\">\n          ✅ Accept Counter Offer\n        </a>\n      </div>\n      <div style=\"display: inline-block; margin: 10px;\">\n        <a href=\"https://firstcutproduce.app.n8n.cloud/webhook/carrier/counter-decline?bidId={{ $node['Process Counter Form'].json.bidId }}\" style=\"background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;\">\n          ❌ Decline Counter Offer\n        </a>\n      </div>\n    </div>\n\n    <div style=\"background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #fbbf24;\">\n      <strong>⏰ Time Sensitive:</strong> Please respond within 4 hours to secure this counter offer.\n    </div>\n  </div>\n</div>", "options": {}}, "id": "email-counter-offer", "name": "Email Counter Offer to Carrier", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1000, 300], "credentials": {"smtp": {"id": "EMAIL_CREDENTIAL_ID", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "html", "responseBody": "=<div style=\"font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 30px; border: 1px solid #e2e8f0; border-radius: 8px; text-align: center;\">\n  <div style=\"background: #059669; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n    <h2 style=\"margin: 0;\">✅ Counter Offer Sent Successfully!</h2>\n  </div>\n  \n  <div style=\"margin: 20px 0;\">\n    <p><strong>Bid ID:</strong> {{ $node['Process Counter Form'].json.bidId }}</p>\n    <p><strong>Counter Amount:</strong> <span style=\"color: #059669; font-size: 18px; font-weight: bold;\">${{ $node['Process Counter Form'].json.counterAmount }}</span></p>\n  </div>\n  \n  <div style=\"background: #dcfdf7; padding: 15px; border-radius: 8px; border-left: 4px solid #059669;\">\n    <p><strong>Next Steps:</strong></p>\n    <ul style=\"text-align: left; margin: 10px 0;\">\n      <li><PERSON> has been notified via email</li>\n      <li>They will respond within 4 hours</li>\n      <li>You'll receive notification of their decision</li>\n    </ul>\n  </div>\n  \n  <div style=\"margin-top: 20px;\">\n    <a href=\"javascript:window.close()\" style=\"background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;\">Close Window</a>\n  </div>\n</div>", "options": {}}, "id": "respond-counter-success", "name": "Respond Counter Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 2, "position": [1200, 300]}], "connections": {"Webhook - Counter Form Submit": {"main": [[{"node": "Process Counter Form", "type": "main", "index": 0}]]}, "Process Counter Form": {"main": [[{"node": "Get Bid for Counter", "type": "main", "index": 0}]]}, "Get Bid for Counter": {"main": [[{"node": "Update Bid with Counter", "type": "main", "index": 0}]]}, "Update Bid with Counter": {"main": [[{"node": "Email Counter Offer to Carrier", "type": "main", "index": 0}]]}, "Email Counter Offer to Carrier": {"main": [[{"node": "Respond Counter Success", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "id": "counter-form-submission-workflow", "meta": {"instanceId": "n8n-instance"}}