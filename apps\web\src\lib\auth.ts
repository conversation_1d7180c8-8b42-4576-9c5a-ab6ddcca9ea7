import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

interface N8NJwtPayload {
  id: string;
  email: string;
  role: string;
  isAdmin?: boolean;
  iat?: number;
  exp?: number;
}

export async function getAuthenticatedUser(request: NextRequest): Promise<N8NJwtPayload | null> {
  try {
    // Get N8N JWT token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!token) {
      return null;
    }

    // Validate JWT token format (basic check)
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      return null;
    }

    // Decode and verify the JWT token
    const jwtSecret = process.env.N8N_JWT_SECRET || 'fallback-secret';
    const payload = jwt.verify(token, jwtSecret) as N8NJwtPayload;
    
    // Validate required fields from N8N JWT
    if (!payload.id || !payload.email || !payload.role) {
      return null;
    }

    return payload;
  } catch (error) {
    console.error('Authentication failed:', error);
    return null;
  }
}

export async function requireAuth(request: NextRequest): Promise<N8NJwtPayload> {
  const user = await getAuthenticatedUser(request);
  if (!user) {
    throw new Error('Unauthorized');
  }
  return user;
}

export async function requireAdmin(request: NextRequest): Promise<N8NJwtPayload> {
  const user = await requireAuth(request);
  
  // Check if user has admin role
  if (user.role !== 'ADMIN' && !user.isAdmin) {
    throw new Error('Admin access required');
  }
  
  return user;
}
