import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

interface N8NJwtPayload {
  id: string;
  email: string;
  role: string;
  isAdmin?: boolean;
  iat?: number;
  exp?: number;
}

export async function getAuthenticatedUser(request: NextRequest): Promise<N8NJwtPayload | null> {
  try {
    // Get N8N JWT token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    console.log('Auth Debug: Token found:', !!token);
    console.log('Auth Debug: Token source:', authHeader ? 'header' : cookieToken ? 'cookie' : 'none');

    if (!token) {
      console.log('Auth Debug: No token found');
      return null;
    }

    // Validate JWT token format (basic check)
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      console.log('Auth Debug: Invalid token format');
      return null;
    }

    // Decode and verify the JWT token
    const jwtSecret = process.env.N8N_JWT_SECRET || 'fallback-secret';
    console.log('Auth Debug: JWT Secret configured:', !!process.env.N8N_JWT_SECRET);
    console.log('Auth Debug: Using secret:', jwtSecret === 'fallback-secret' ? 'fallback' : 'configured');
    
    const payload = jwt.verify(token, jwtSecret) as N8NJwtPayload;
    console.log('Auth Debug: JWT verification successful, payload:', { id: payload.id, email: payload.email, role: payload.role });
    
    // Validate required fields from N8N JWT
    if (!payload.id || !payload.email || !payload.role) {
      console.log('Auth Debug: Missing required fields in payload');
      return null;
    }

    return payload;
  } catch (error) {
    console.error('Authentication failed:', error);
    console.error('Auth Debug: JWT verification error:', error instanceof Error ? error.message : 'Unknown error');
    return null;
  }
}

export async function requireAuth(request: NextRequest): Promise<N8NJwtPayload> {
  const user = await getAuthenticatedUser(request);
  if (!user) {
    throw new Error('Unauthorized');
  }
  return user;
}

export async function requireAdmin(request: NextRequest): Promise<N8NJwtPayload> {
  const user = await requireAuth(request);

  // Check if user has admin role
  if (user.role !== 'ADMIN' && !user.isAdmin) {
    throw new Error('Admin access required');
  }

  return user;
}

export async function requireOperations(request: NextRequest): Promise<N8NJwtPayload> {
  const user = await requireAuth(request);

  console.log('Operations Access Debug: User:', { id: user.id, email: user.email, role: user.role });

  // Check if user has operations access (admin or First Cut Produce team)
  const hasOperationsAccess = user.role === 'ADMIN' ||
                              user.isAdmin ||
                              (user as any).companyName === 'First Cut Produce' ||
                              (user as any).companyName === 'FIRST CUT PRODUCE' ||
                              user.email?.includes('@firstcutproduce.com') ||
                              user.email === '<EMAIL>';

  console.log('Operations Access Debug: Access checks:', {
    isAdmin: user.role === 'ADMIN',
    hasIsAdminFlag: user.isAdmin,
    hasFirstCutCompany: (user as any).companyName === 'First Cut Produce' || (user as any).companyName === 'FIRST CUT PRODUCE',
    hasFirstCutEmail: user.email?.includes('@firstcutproduce.com'),
    isAmerEmail: user.email === '<EMAIL>',
    finalAccess: hasOperationsAccess
  });

  if (!hasOperationsAccess) {
    throw new Error('Operations access required - First Cut Produce team members and admins only');
  }

  return user;
}
