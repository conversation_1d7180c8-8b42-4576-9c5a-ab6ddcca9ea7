import Airtable from 'airtable';

// Initialize Airtable client
const airtable = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY,
});

const base = airtable.base(process.env.AIRTABLE_BASE_ID!);

export interface DocumentInfo {
  url: string;
  name: string;
  type: string;
  size: number;
}

/**
 * Update an Airtable record with new field values
 */
export async function updateAirtableRecord(
  recordId: string, 
  fields: Record<string, any>
): Promise<void> {
  try {
    await base('Loads').update(recordId, fields);
    console.log(`Updated Airtable record ${recordId}:`, fields);
  } catch (error) {
    console.error('Airtable update failed:', error);
    throw new Error(`Failed to update Airtable record: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Sync document information to Airtable for a specific load
 */
export async function syncLoadDocuments(
  airtableRecordId: string,
  documents: DocumentInfo[]
): Promise<void> {
  if (!documents || documents.length === 0) {
    console.log('No documents to sync');
    return;
  }

  try {
    const documentUrls = documents.map(doc => doc.url).join('\n');
    const documentNames = documents.map(doc => doc.name).join('\n');
    const totalSize = documents.reduce((sum, doc) => sum + doc.size, 0);

    const fields = {
      'Documents': documentUrls,
      'Document Names': documentNames,
      'Document Count': documents.length,
      'Total Document Size': Math.round(totalSize / 1024), // Convert to KB
      'Last Document Upload': new Date().toISOString()
    };

    await updateAirtableRecord(airtableRecordId, fields);
    console.log(`Successfully synced ${documents.length} documents to Airtable record ${airtableRecordId}`);
  } catch (error) {
    console.error('Failed to sync documents to Airtable:', error);
    throw error;
  }
}

/**
 * Validate environment variables are present
 */
export function validateAirtableConfig(): boolean {
  const apiKey = process.env.AIRTABLE_API_KEY;
  const baseId = process.env.AIRTABLE_BASE_ID;
  
  if (!apiKey || !baseId) {
    console.error('Missing Airtable configuration - AIRTABLE_API_KEY and AIRTABLE_BASE_ID are required');
    return false;
  }
  
  return true;
} 