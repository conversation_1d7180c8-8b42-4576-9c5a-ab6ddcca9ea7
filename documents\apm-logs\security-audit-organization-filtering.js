// SECURITY AUDIT: Organization Filtering Vulnerability
// This script tests the critical organization boundary breach

const { PrismaClient } = require('@prisma/client');

async function auditOrganizationFiltering() {
  const prisma = new PrismaClient();
  
  console.log('🚨 SECURITY AUDIT: Organization Filtering Vulnerability\n');
  
  try {
    // 1. Check all loads and their organization targeting
    const allLoads = await prisma.load.findMany({
      select: {
        id: true,
        airtableRecordId: true,
        proNumber: true,
        rate: true,
        originCity: true,
        originState: true,
        destinationCity: true,
        destinationState: true,
        status: true,
        isPublic: true,
        isTargeted: true,
        targetOrganizations: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    });
    
    console.log(`📊 TOTAL LOADS IN DATABASE: ${allLoads.length}`);
    
    // Analyze organization targeting patterns
    let publicLoads = 0;
    let privateLoads = 0;
    let unsetPublicFlag = 0;
    let targetedLoads = 0;
    let untargetedLoads = 0;
    
    console.log('\n🔍 ORGANIZATION TARGETING ANALYSIS:');
    console.log('===================================');
    
    allLoads.forEach((load, index) => {
      // Count visibility patterns
      if (load.isPublic === true) publicLoads++;
      else if (load.isPublic === false) privateLoads++;
      else if (load.isPublic === null || load.isPublic === undefined) unsetPublicFlag++;
      
      if (load.isTargeted === true) targetedLoads++;
      else untargetedLoads++;
      
      // Show detailed info for first few loads
      if (index < 10) {
        console.log(`\n--- Load ${index + 1}: ${load.proNumber || 'No Pro#'} ---`);
        console.log(`  Airtable ID: ${load.airtableRecordId}`);
        console.log(`  Route: ${load.originCity}, ${load.originState} → ${load.destinationCity}, ${load.destinationState}`);
        console.log(`  Rate: $${load.rate || 'Not set'}`);
        console.log(`  Status: ${load.status}`);
        console.log(`  🚨 Is Public: ${load.isPublic} (${typeof load.isPublic})`);
        console.log(`  🎯 Is Targeted: ${load.isTargeted}`);
        console.log(`  🏢 Target Organizations: ${JSON.stringify(load.targetOrganizations)}`);
        console.log(`  📅 Created: ${load.createdAt.toISOString()}`);
        
        // SECURITY FLAG: Identify vulnerability patterns
        if (load.isPublic === null || load.isPublic === undefined) {
          console.log(`  🚨 SECURITY RISK: isPublic is NULL/undefined - defaults to PUBLIC!`);
        }
        if (load.targetOrganizations === null && load.isPublic !== false) {
          console.log(`  🚨 SECURITY RISK: No target orgs + public = visible to ALL!`);
        }
      }
    });
    
    console.log('\n📈 VISIBILITY STATISTICS:');
    console.log(`  Public loads (isPublic = true): ${publicLoads}`);
    console.log(`  Private loads (isPublic = false): ${privateLoads}`);
    console.log(`  🚨 UNSET public flag (NULL/undefined): ${unsetPublicFlag}`);
    console.log(`  Targeted loads: ${targetedLoads}`);
    console.log(`  Untargeted loads: ${untargetedLoads}`);
    
    // 2. Check user organizations for context
    const users = await prisma.user.findMany({
      select: {
        clerkUserId: true,
        orgName: true,
        role: true,
        email: true
      }
    });
    
    console.log('\n👥 USER ORGANIZATIONS:');
    const orgGroups = users.reduce((groups, user) => {
      const org = user.orgName || 'NO_ORGANIZATION';
      if (!groups[org]) groups[org] = [];
      groups[org].push(user);
      return groups;
    }, {});
    
    Object.entries(orgGroups).forEach(([org, orgUsers]) => {
      console.log(`  ${org}: ${orgUsers.length} users`);
      if (orgUsers.length <= 3) {
        orgUsers.forEach(user => {
          console.log(`    - ${user.email || 'No email'} (${user.role})`);
        });
      }
    });
    
    // 3. CRITICAL: Simulate organization filtering logic
    console.log('\n🔥 SIMULATING ORGANIZATION FILTERING VULNERABILITY:');
    console.log('====================================================');
    
    const testOrganizations = ['First Cut Produce', 'MVT Logistics', 'TEST_ORG'];
    
    for (const testOrg of testOrganizations) {
      console.log(`\n🏢 Testing as user from: "${testOrg}"`);
      
      const visibleLoads = allLoads.filter(load => {
        // REPLICATE THE VULNERABLE LOGIC FROM AIRTABLE SERVICE
        const isPublic = load.isPublic !== false; // 🚨 THE SECURITY FLAW!
        const targetOrganizations = load.targetOrganizations;
        let isTargetedToUser = false;
        let canUserSeeLoad = true;
        
        // Check if this load is targeted to the user's organization
        if (targetOrganizations && testOrg) {
          const orgArray = Array.isArray(targetOrganizations) 
            ? targetOrganizations 
            : [targetOrganizations];
          
          isTargetedToUser = orgArray.some(org => 
            org.toLowerCase().trim() === testOrg.toLowerCase().trim()
          );
        }
        
        // Determine if user can see this load
        if (!isPublic) {
          // Load is not public - only show to targeted organizations
          if (targetOrganizations && testOrg) {
            canUserSeeLoad = isTargetedToUser;
          } else {
            // Load is private but user has no organization - can't see it
            canUserSeeLoad = false;
          }
        }
        // If isPublic is true, canUserSeeLoad remains true (default)
        
        return canUserSeeLoad;
      });
      
      console.log(`  📊 ${testOrg} can see ${visibleLoads.length} loads:`);
      
      // Show details of what they can see
      visibleLoads.slice(0, 5).forEach(load => {
        const isPublicDisplay = load.isPublic !== false ? 'PUBLIC' : 'PRIVATE';
        const targetDisplay = load.targetOrganizations ? 
          `Target: ${JSON.stringify(load.targetOrganizations)}` : 
          'No targeting';
        
        console.log(`    - ${load.proNumber || 'No Pro#'}: $${load.rate || 'N/A'} (${isPublicDisplay}) ${targetDisplay}`);
      });
    }
    
    // 4. SECURITY RECOMMENDATIONS
    console.log('\n🛡️  SECURITY RECOMMENDATIONS:');
    console.log('=============================');
    
    if (unsetPublicFlag > 0) {
      console.log('🚨 CRITICAL: Fix isPublic default behavior');
      console.log('   - Change default from TRUE to FALSE');
      console.log('   - Require explicit public flag setting');
    }
    
    if (publicLoads > privateLoads) {
      console.log('⚠️  WARNING: More public than private loads');
      console.log('   - Review if all public loads should actually be public');
    }
    
    console.log('🔒 IMMEDIATE ACTIONS REQUIRED:');
    console.log('   1. Fix the isPublic default logic');
    console.log('   2. Add organization validation guards');
    console.log('   3. Audit all existing loads');
    console.log('   4. Add security logging');
    
  } catch (error) {
    console.error('❌ Security audit failed:', error);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

auditOrganizationFiltering().catch(console.error); 