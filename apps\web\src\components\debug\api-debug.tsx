"use client";

import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';

interface ApiDebugProps {
  showInProduction?: boolean;
}

export function ApiDebug({ showInProduction = false }: ApiDebugProps) {
  const { getToken, user, isLoading } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Don't show in production unless explicitly enabled
  if (process.env.NODE_ENV === 'production' && !showInProduction) {
    return null;
  }

  const runDiagnostics = async () => {
    const results: any = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
      authLoaded: !isLoading,
      userId: user?.id,
      tests: {}
    };

    try {
      // Test 1: Get Clerk token
      try {
        const token = await getToken();
        results.tests.clerkToken = {
          success: !!token,
          message: token ? 'Token obtained successfully' : 'No token received',
          tokenLength: token?.length || 0
        };
      } catch (error) {
        results.tests.clerkToken = {
          success: false,
          message: `Error getting token: ${error}`,
          error: error
        };
      }

      // Test 2: Test API proxy health
      try {
        const healthResponse = await fetch('/api/v1/health');
        results.tests.apiHealth = {
          success: healthResponse.ok,
          status: healthResponse.status,
          statusText: healthResponse.statusText,
          message: healthResponse.ok ? 'API proxy is working' : 'API proxy failed'
        };
      } catch (error) {
        results.tests.apiHealth = {
          success: false,
          message: `API health check failed: ${error}`,
          error: error
        };
      }

      // Test 3: Test authenticated endpoint
      try {
        const authResponse = await fetch('/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${await getToken()}`
          }
        });
        results.tests.authEndpoint = {
          success: authResponse.ok,
          status: authResponse.status,
          statusText: authResponse.statusText,
          message: authResponse.ok ? 'Authentication working' : 'Authentication failed'
        };
      } catch (error) {
        results.tests.authEndpoint = {
          success: false,
          message: `Auth endpoint test failed: ${error}`,
          error: error
        };
      }

      // Test 4: Test loadboard endpoint
      try {
        const loadboardResponse = await fetch('/api/v1/airtable-orders/available', {
          headers: {
            'Authorization': `Bearer ${await getToken()}`
          }
        });
        results.tests.loadboard = {
          success: loadboardResponse.ok,
          status: loadboardResponse.status,
          statusText: loadboardResponse.statusText,
          message: loadboardResponse.ok ? 'Loadboard API working' : 'Loadboard API failed'
        };
      } catch (error) {
        results.tests.loadboard = {
          success: false,
          message: `Loadboard test failed: ${error}`,
          error: error
        };
      }

    } catch (error) {
      results.error = `Diagnostics failed: ${error}`;
    }

    setDebugInfo(results);
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <AlertCircle className="h-4 w-4 text-red-500" />
    );
  };

  return (
    <Card className="mt-4 border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-sm">
          <AlertCircle className="h-4 w-4" />
          API Debug Panel
          <Badge variant="outline" className="text-xs">
            {process.env.NODE_ENV}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runDiagnostics} 
          size="sm"
          variant="outline"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Run Diagnostics
        </Button>

        {debugInfo && (
          <div className="space-y-3">
            <div className="text-xs text-muted-foreground">
              Last run: {new Date(debugInfo.timestamp).toLocaleString()}
            </div>
            
            <div className="grid gap-2">
              <div className="text-sm font-medium">Environment Info:</div>
              <div className="text-xs space-y-1 pl-4">
                <div>Environment: {debugInfo.environment}</div>
                <div>API Base URL: {debugInfo.apiBaseUrl || 'Not set'}</div>
                <div>Clerk Loaded: {debugInfo.authLoaded ? 'Yes' : 'No'}</div>
                <div>User ID: {debugInfo.userId || 'None'}</div>
              </div>
            </div>

            <div className="grid gap-2">
              <div className="text-sm font-medium">Test Results:</div>
              <div className="space-y-2 pl-4">
                {Object.entries(debugInfo.tests).map(([testName, result]: [string, any]) => (
                  <div key={testName} className="flex items-center gap-2 text-xs">
                    {getStatusIcon(result.success)}
                    <span className="font-medium">{testName}:</span>
                    <span>{result.message}</span>
                    {result.status && (
                      <Badge variant={result.success ? "default" : "destructive"} className="text-xs">
                        {result.status}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {debugInfo.error && (
              <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                Error: {debugInfo.error}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
