// Simple Database Environment Check
console.log('🗄️  ENVIRONMENT CHECK');
console.log('===================\n');

console.log('📋 Environment Variables:');
console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? 'SET (' + process.env.DATABASE_URL.substring(0, 20) + '...)' : 'NOT SET'}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'NOT SET'}`);
console.log(`VERCEL_ENV: ${process.env.VERCEL_ENV || 'NOT SET'}`);
console.log(`PWD: ${process.env.PWD || process.cwd()}`);

// List all environment variables that might be database related
console.log('\n🔍 Database-related environment variables:');
Object.keys(process.env).forEach(key => {
  if (key.toLowerCase().includes('database') || key.toLowerCase().includes('postgres') || key.toLowerCase().includes('db')) {
    console.log(`${key}: ${process.env[key] ? 'SET' : 'NOT SET'}`);
  }
});

console.log('\n📁 Current working directory:', process.cwd());
console.log('📁 Script location:', __dirname);

// Try to find env files
const fs = require('fs');
const path = require('path');

const possibleEnvFiles = [
  '.env',
  '.env.local', 
  '.env.production',
  '.env.development',
  'apps/api/.env',
  'apps/api/.env.local',
  'apps/web/.env.local'
];

console.log('\n📄 Looking for environment files:');
possibleEnvFiles.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  const exists = fs.existsSync(fullPath);
  console.log(`${file}: ${exists ? '✅ FOUND' : '❌ NOT FOUND'}`);
  
  if (exists) {
    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      console.log(`   Content preview (first 200 chars):`);
      console.log(`   ${content.substring(0, 200).replace(/\n/g, '\\n')}`);
      
      // Look for DATABASE_URL specifically
      const lines = content.split('\n');
      const dbLine = lines.find(line => line.trim().startsWith('DATABASE_URL'));
      if (dbLine) {
        console.log(`   📊 DATABASE_URL found: ${dbLine.substring(0, 50)}...`);
      }
    } catch (error) {
      console.log(`   ❌ Error reading file: ${error.message}`);
    }
    console.log('');
  }
}); 