import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { N8NJwtPayload } from './authenticated-request.interface';
interface UserProfile {
    airtableUserId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    companyName?: string;
    mcNumber?: string;
    dotNumber?: string;
    role: string;
    verificationStatus: string;
}
export declare class AuthService {
    private prisma;
    private configService;
    private readonly logger;
    private readonly jwtSecret;
    private readonly airtableBaseId;
    private readonly airtableApiKey;
    constructor(prisma: PrismaService, configService: ConfigService);
    verifyToken(token: string): Promise<N8NJwtPayload>;
    getUserProfile(airtableUserId: string, forceRefresh?: boolean): Promise<UserProfile | null>;
    private fetchAndCacheUserProfile;
    validateUser(jwtPayload: N8NJwtPayload, isLoginRequest?: boolean): Promise<{
        airtableUserId: string;
        email: string;
        mcNumber?: string;
        role: string;
        isAdmin: boolean;
    } | null>;
    updateCachedProfile(airtableUserId: string, updates: Partial<UserProfile>): Promise<void>;
    clearCachedProfile(airtableUserId: string): Promise<void>;
    refreshUserProfile(airtableUserId: string): Promise<UserProfile | null>;
    syncAllCachedProfiles(maxAge?: number): Promise<{
        updated: number;
        errors: string[];
    }>;
    findUserByClerkUserId(clerkUserId: string): Promise<any>;
    findOrCreateUserByClerkPayload(clerkPayload: any): Promise<any>;
    findUserByAirtableId(airtableUserId: string): Promise<UserProfile | null>;
    findOrCreateUserByN8NPayload(payload: N8NJwtPayload): Promise<UserProfile | null>;
    findUserByAirtableUserId(airtableUserId: string): Promise<any>;
    updateUserProfile(airtableUserId: string, updateData: {
        firstName?: string;
        lastName?: string;
        email?: string;
    }): Promise<any>;
}
export {};
