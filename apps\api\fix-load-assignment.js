const { PrismaClient } = require('../../../packages/db/generated/client');

const prisma = new PrismaClient();

async function fixLoadAssignment() {
  try {
    console.log('🔍 Checking load and carrier profile...');
    
    // Find the load
    const load = await prisma.load.findUnique({
      where: { airtableRecordId: 'recEcwEdJ52afFmSq' },
      include: {
        awardedToCarrierProfile: {
          select: {
            companyName: true,
            id: true
          }
        }
      }
    });
    
    console.log('📦 Load found:', {
      id: load?.id,
      airtableRecordId: load?.airtableRecordId,
      status: load?.status,
      currentlyAssignedTo: load?.awardedToCarrierProfile?.companyName || 'Not assigned',
      awardedToCarrierProfileId: load?.awardedToCarrierProfileId
    });
    
    // Find the carrier profile
    const carrierProfile = await prisma.carrierProfile.findFirst({
      where: { companyName: 'First Cut Produce' },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });
    
    console.log('🚛 Carrier profile found:', {
      id: carrierProfile?.id,
      companyName: carrierProfile?.companyName,
      userName: carrierProfile?.user?.name,
      userEmail: carrierProfile?.user?.email
    });
    
    // Fix the assignment if needed
    if (load && carrierProfile && !load.awardedToCarrierProfileId) {
      console.log('🔧 Fixing assignment...');
      
      const updatedLoad = await prisma.load.update({
        where: { id: load.id },
        data: {
          awardedToCarrierProfileId: carrierProfile.id
        }
      });
      
      console.log('✅ Load successfully assigned to carrier!');
      console.log('Updated load:', {
        id: updatedLoad.id,
        airtableRecordId: updatedLoad.airtableRecordId,
        awardedToCarrierProfileId: updatedLoad.awardedToCarrierProfileId
      });
    } else if (load?.awardedToCarrierProfileId) {
      console.log('ℹ️ Load is already assigned.');
    } else {
      console.log('❌ Missing load or carrier profile.');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixLoadAssignment(); 