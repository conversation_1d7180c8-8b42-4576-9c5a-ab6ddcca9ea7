import { IsString, IsOptional, IsBoolean, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SavedSearchDto {
  @ApiProperty({ description: 'Name of the saved search' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Search criteria as JSON object' })
  @IsOptional()
  @IsObject()
  criteria?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Whether this is the default search', default: false })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
} 