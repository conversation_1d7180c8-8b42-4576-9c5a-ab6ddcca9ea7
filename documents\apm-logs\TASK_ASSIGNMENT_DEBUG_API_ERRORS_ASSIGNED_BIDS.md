# TASK ASSIGNMENT: Debug API Errors - Assigned Bids Not Working

## PRIORITY: 🚨 CRITICAL - CORE FUNCTIONALITY BROKEN

**Estimated Timeline**: 1-2 hours  
**Complexity**: High - Multi-Organization Authentication  
**Impact**: Critical - User cannot access assigned bids or any API endpoints

## PROBLEM ANALYSIS

### **Current Issue Status**
Multiple API calls are failing with 401 errors, including assigned bids endpoint. Network console shows red 401 errors across the board.

### **Same Persistent Error:**
```
ERROR [AuthService] Failed to fetch user details from Clerk for user ID user_2xZWyA8oVI2bhQOrZ5vgJNRonLE: 
Multiple organizations found (MVT Logistics, MNM Transport, US Freight Lines, First Cut Produce). 
Please set your active organization in Clerk profile settings.
```

### **Failing API Endpoints (from Network Console):**
- `/api/v1/auth/me` - 401 Unauthorized
- `/api/v1/airtable-orders/assigned` - 401 Unauthorized  
- `/api/v1/airtable-orders/bids` - 401 Unauthorized
- Multiple other endpoints - All 401 Unauthorized

### **Root Cause Analysis**
Despite multiple previous attempts to fix this, the core issue persists:
1. **JWT Token Missing Organization Claims** - Token verified but no org context
2. **Organization Selector Not Working** - Either not implemented or not functioning
3. **Clerk Context Not Updating** - setActive() calls not taking effect

## CRITICAL INVESTIGATION TASKS

### **Task 1: IMMEDIATE - Check Organization Selector Status**

**Verify what user actually sees:**
```bash
# Check if any organization selector components exist
find src/ -name "*Org*" -name "*.tsx" -type f
find src/ -name "*Organization*" -type f

# Check what's actually being rendered in layout
grep -r "OrgSelector\|OrganizationSelector" src/ --include="*.tsx"
```

**Manual UI Check:**
1. **Load the application in browser**
2. **Look for ANY organization selection UI:**
   - Red banner at top of page
   - Dropdown in navigation
   - Modal/popup for organization selection
   - Any buttons or controls mentioning organizations
3. **Check browser console for errors**
4. **Check browser console for organization-related logs**

### **Task 2: Add Emergency Organization Detection & Selection**

**Create immediate organization selector that MUST work:**

```typescript
// src/components/EmergencyOrgFixer.tsx
'use client';

import { useOrganization, useOrganizationList, useClerk, useAuth } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

export function EmergencyOrgFixer() {
  const { organization, isLoaded: orgLoaded } = useOrganization();
  const { organizationList, isLoaded: listLoaded } = useOrganizationList();
  const { setActive } = useClerk();
  const { getToken } = useAuth();
  const [switching, setSwitching] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Debug current state
  useEffect(() => {
    const debugCurrentState = async () => {
      if (!orgLoaded || !listLoaded) return;
      
      console.log('🔍 EMERGENCY DEBUG - CURRENT STATE:');
      console.log('Current Organization:', organization);
      console.log('Organization List:', organizationList);
      console.log('List Length:', organizationList?.length);
      
      try {
        const token = await getToken();
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          console.log('JWT Payload:', payload);
          console.log('Org Claims:', {
            org_id: payload.org_id || 'MISSING ❌',
            org_role: payload.org_role || 'MISSING ❌',
            org_slug: payload.org_slug || 'MISSING ❌'
          });
          setDebugInfo(payload);
        }
      } catch (error) {
        console.error('Token debug error:', error);
      }
    };

    debugCurrentState();
  }, [organization, organizationList, orgLoaded, listLoaded, getToken]);

  // Determine if we need to show selector
  const needsSelection = orgLoaded && listLoaded && 
                        organizationList && organizationList.length > 1 && 
                        !organization;

  const handleOrgSelect = async (orgId: string, orgName: string) => {
    if (switching) return;
    
    setSwitching(orgId);
    console.log(`🔄 EMERGENCY: Setting organization ${orgName} (${orgId})`);
    
    try {
      await setActive({ organization: orgId });
      console.log('✅ setActive() completed');
      
      // Wait for token to update
      setTimeout(async () => {
        try {
          const newToken = await getToken();
          if (newToken) {
            const newPayload = JSON.parse(atob(newToken.split('.')[1]));
            console.log('🔍 NEW JWT AFTER SELECTION:', newPayload);
            
            if (newPayload.org_id) {
              console.log('✅ SUCCESS: JWT now includes organization!');
              // Force full page reload to update all API contexts
              window.location.reload();
            } else {
              console.error('❌ FAILED: JWT still missing organization claims');
            }
          }
        } catch (error) {
          console.error('Token check error:', error);
        }
      }, 2000);
      
    } catch (error) {
      console.error('❌ setActive() failed:', error);
      setSwitching(null);
    }
  };

  // Always show debug info in top corner
  const showDebug = process.env.NODE_ENV === 'development';

  return (
    <>
      {/* Debug info - always visible in development */}
      {showDebug && (
        <div className="fixed top-16 right-4 bg-yellow-100 border border-yellow-400 p-3 rounded-lg shadow-lg z-50 text-xs max-w-xs">
          <h4 className="font-bold text-yellow-800 mb-2">🔍 Auth Debug</h4>
          <div className="space-y-1 text-yellow-700">
            <div>User: {debugInfo?.sub ? '✅' : '❌'}</div>
            <div>Current Org: {organization?.id || '❌ NONE'}</div>
            <div>Available: {organizationList?.length || 0} orgs</div>
            <div>JWT org_id: {debugInfo?.org_id || '❌ MISSING'}</div>
            <div>Needs Selection: {needsSelection ? '✅' : '❌'}</div>
          </div>
        </div>
      )}

      {/* Emergency organization selector */}
      {needsSelection && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-2xl max-w-2xl w-full mx-4 p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-red-600 mb-4">
                🚨 EMERGENCY: Organization Required
              </h1>
              <p className="text-gray-700 text-lg">
                API access is blocked. You must select an organization to continue.
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Available: {organizationList?.map(org => org.organization.name).join(', ')}
              </p>
            </div>

            <div className="space-y-4">
              {organizationList?.map((org) => (
                <button
                  key={org.organization.id}
                  onClick={() => handleOrgSelect(org.organization.id, org.organization.name)}
                  disabled={!!switching}
                  className={`
                    w-full p-6 border-2 rounded-lg text-left transition-all
                    ${switching === org.organization.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-300 hover:border-blue-400 hover:bg-blue-25'
                    }
                    ${switching && switching !== org.organization.id ? 'opacity-50' : ''}
                    disabled:cursor-not-allowed
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">
                        {org.organization.name}
                      </h3>
                      <p className="text-gray-600">Role: {org.membership.role}</p>
                      <p className="text-xs text-gray-500">ID: {org.organization.id}</p>
                    </div>
                    {switching === org.organization.id && (
                      <div className="flex items-center text-blue-600">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                        <span>Setting...</span>
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-8 text-center text-sm text-gray-500">
              <p>This will set your active organization and reload the page.</p>
              <p>All API calls should work after selection.</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
```

### **Task 3: Add to Layout Immediately**

**Update main layout to include emergency fixer:**

```typescript
// In src/app/layout.tsx or main layout file
import { EmergencyOrgFixer } from '@/components/EmergencyOrgFixer';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <EmergencyOrgFixer />
          {/* ... rest of layout */}
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
```

### **Task 4: Test Complete Flow**

**Manual Testing Protocol:**
1. **Clear browser cache and reload**
2. **Should see emergency organization selector modal**
3. **Check browser console for debug logs**
4. **Select an organization (e.g., "MVT Logistics")**
5. **Monitor console logs during selection**
6. **Page should reload after selection**
7. **Check if API calls now return 200 instead of 401**
8. **Test assigned bids endpoint specifically**

### **Task 5: Verify API Access Restoration**

**After organization selection, test these endpoints:**
- `/api/v1/auth/me` - Should return 200
- `/api/v1/airtable-orders/assigned` - Should return 200  
- `/api/v1/airtable-orders/bids` - Should return 200

**Check JWT token content:**
```javascript
// In browser console after organization selection
const token = await window.clerk?.session?.getToken();
const payload = JSON.parse(atob(token.split('.')[1]));
console.log('JWT after selection:', payload);
// Should include: org_id, org_role, org_slug
```

## IMMEDIATE ACTION PLAN

### **Step 1: Emergency Implementation (20 minutes)**
1. Create `EmergencyOrgFixer.tsx` component
2. Add to main layout
3. Test that modal appears on page load

### **Step 2: Organization Selection Test (15 minutes)**
1. Select an organization from the modal
2. Monitor console logs for success/failure
3. Check if page reloads with working authentication

### **Step 3: API Verification (15 minutes)**
1. Test assigned bids endpoint in browser
2. Check network tab for 200 responses
3. Verify JWT token includes organization claims

### **Step 4: Production Cleanup (30 minutes)**
1. Remove debug components if not needed
2. Implement proper organization selector in navigation
3. Test complete user flow

## SUCCESS CRITERIA

### **Organization Selection Working**
- [ ] Emergency modal appears when user has no active org
- [ ] User can select organization successfully
- [ ] Clerk context updates with selected organization
- [ ] JWT token includes org_id, org_role, org_slug claims

### **API Access Restored**
- [ ] `/api/v1/airtable-orders/assigned` returns 200 status
- [ ] `/api/v1/airtable-orders/bids` returns 200 status
- [ ] `/api/v1/auth/me` returns 200 status
- [ ] All API endpoints return data instead of 401 errors

### **Assigned Bids Functional**
- [ ] User can access assigned bids page
- [ ] Bids data loads successfully
- [ ] No more authentication errors in network console
- [ ] All bid-related functionality works

## TROUBLESHOOTING

### **If Organization Selection Doesn't Work:**
1. Check if `setActive()` is being called correctly
2. Verify Clerk SDK version compatibility
3. Check browser console for JavaScript errors
4. Test if manual page reload helps

### **If JWT Still Missing Organization Claims:**
1. This indicates Clerk Dashboard configuration issue
2. May need to modify JWT template in Clerk Dashboard
3. Check if organization context is properly set in Clerk
4. Verify Clerk organization settings

## ESCALATION CRITERIA

**Escalate Immediately if:**
- Organization selector doesn't appear at all
- `setActive()` calls consistently fail
- JWT tokens never include organization claims after selection
- Need Clerk Dashboard admin access to modify JWT templates

This emergency approach should definitively resolve the assigned bids access issue by forcing organization selection and ensuring proper JWT token generation. 