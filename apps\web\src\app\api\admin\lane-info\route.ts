import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here when user roles are implemented
    
    // Fetch lane info from Airtable
    const lanes: any[] = [];
    
    await base('Lane Info').select({
      view: 'Grid view', // Use default view or specify your view name
      // Adjust field names based on your Airtable structure
      fields: [
        'Lane Name', 
        'Origin City', 
        'Origin State', 
        'Destination City', 
        'Destination State',
        'Distance (Miles)',
        'Rate'
      ]
    }).eachPage((records, fetchNextPage) => {
      records.forEach((record) => {
        lanes.push({
          id: record.id,
          name: record.get('Lane Name') || '',
          originCity: record.get('Origin City') || '',
          originState: record.get('Origin State') || '',
          destinationCity: record.get('Destination City') || '',
          destinationState: record.get('Destination State') || '',
          distance: record.get('Distance (Miles)') || 0,
          rate: record.get('Rate') || 0
        });
      });
      fetchNextPage();
    });

    return NextResponse.json({ 
      success: true, 
      lanes: lanes.sort((a, b) => a.name.localeCompare(b.name))
    });

  } catch (error) {
    console.error('Error fetching lane info:', error);
    return NextResponse.json(
      { error: 'Failed to fetch lane info' },
      { status: 500 }
    );
  }
}
