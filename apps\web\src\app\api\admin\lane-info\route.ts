import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    await requireAdmin(request);

    // Fetch lane info from Airtable
    const lanes: any[] = [];
    
    await base('Lane Info').select({
      view: 'Grid view' // Use default view or specify your view name
    }).eachPage((records, fetchNextPage) => {
      records.forEach((record) => {
        // Get all fields from the record to see what's available
        const fields = record.fields;

        lanes.push({
          id: record.id,
          name: fields['Lane Name'] || fields['Name'] || `${fields['Origin City']} to ${fields['Destination City']}` || '',
          originCity: fields['Origin City'] || '',
          originState: fields['Origin State'] || '',
          destinationCity: fields['Destination City'] || '',
          destinationState: fields['Destination State'] || '',
          distance: fields['Distance (Miles)'] || fields['Distance'] || 0,
          rate: fields['Rate'] || 0
        });
      });
      fetchNextPage();
    });

    return NextResponse.json({ 
      success: true, 
      lanes: lanes.sort((a, b) => a.name.localeCompare(b.name))
    });

  } catch (error) {
    console.error('Error fetching lane info:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to fetch lane info' },
      { status: 500 }
    );
  }
}
