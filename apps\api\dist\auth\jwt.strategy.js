"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const auth_service_1 = require("./auth.service");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy, 'jwt') {
    authService;
    constructor(authService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            secretOrKeyProvider: async (request, rawJwtToken, done) => {
                try {
                    const secret = process.env.N8N_JWT_SECRET || 'fallback-secret';
                    done(null, secret);
                }
                catch (error) {
                    done(error, undefined);
                }
            },
            issuer: process.env.N8N_JWT_ISSUER || 'n8n-auth',
            algorithms: ['HS256'],
        });
        this.authService = authService;
    }
    async validate(payload) {
        try {
            const userProfile = await this.authService.findOrCreateUserByN8NPayload(payload);
            if (!userProfile) {
                throw new common_1.UnauthorizedException('User could not be processed or found after token validation.');
            }
            return {
                id: userProfile.airtableUserId,
                email: userProfile.email,
                firstName: userProfile.firstName,
                lastName: userProfile.lastName,
                role: userProfile.role,
                mcNumber: userProfile.mcNumber,
                companyName: userProfile.companyName,
            };
        }
        catch (error) {
            console.error("Error during JWT strategy user processing:", error);
            throw new common_1.UnauthorizedException('Error processing user from token.');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map