// Serverless function wrapper for NestJS application
console.log('[WRAPPER] Starting to load wrapper function...');

// Check for required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'N8N_JWT_SECRET', 
  'N8N_JWT_ISSUER'
];

console.log('[WRAPPER] Checking required environment variables...');
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('[WRAPPER] Missing required environment variables:', missingVars);
  
  // Return a function that will always respond with the missing env vars error
  module.exports = async (req, res) => {
    return res.status(500).json({
      error: 'Configuration Error',
      message: 'Missing required environment variables',
      missingVariables: missingVars,
      available: Object.keys(process.env).filter(key => 
        key.startsWith('N8N_') || 
        key.startsWith('DATABASE_') || 
        key.includes('URL') ||
        key.startsWith('VERCEL_')
      )
    });
  };
  return;
}

console.log('[WRAPPER] All required environment variables present');

let mainHandler;

try {
  console.log('[WRAPPER] Attempting to require ../dist/main.js...');
  mainHandler = require('../dist/main.js');
  console.log('[WRAPPER] Successfully loaded main handler. Keys:', Object.keys(mainHandler));
  console.log('[WRAPPER] Default export type:', typeof mainHandler.default);
} catch (error) {
  console.error('[WRAPPER] Error loading main handler:', error);
  
  // Return a function that will always respond with the module loading error
  module.exports = async (req, res) => {
    return res.status(500).json({
      error: 'Module Loading Error',
      message: error.message,
      stack: error.stack,
      cwd: process.cwd()
    });
  };
  return;
}

// Export the default handler from the built NestJS app
module.exports = async (req, res) => {
  console.log('[WRAPPER] Function invoked');
  console.log('[WRAPPER] Request method:', req.method);
  console.log('[WRAPPER] Request URL:', req.url);
  
  try {
    console.log('[WRAPPER] About to call mainHandler.default...');
    
    if (typeof mainHandler.default !== 'function') {
      console.error('[WRAPPER] mainHandler.default is not a function:', typeof mainHandler.default);
      return res.status(500).json({ error: 'Handler is not a function' });
    }
    
    // Call the NestJS handler
    const result = await mainHandler.default(req, res);
    console.log('[WRAPPER] Handler completed successfully');
    return result;
  } catch (error) {
    console.error('[WRAPPER] Error calling main handler:', error);
    console.error('[WRAPPER] Error stack:', error.stack);
    
    // Send error response if res hasn't been sent yet
    if (!res.headersSent) {
      return res.status(500).json({ 
        error: 'Runtime Error',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }
}; 