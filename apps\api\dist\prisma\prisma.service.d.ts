import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@repo/db';
export declare class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
    private readonly logger;
    constructor();
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    healthCheck(): Promise<boolean>;
    verifyConnection(): Promise<{
        connected: boolean;
        error?: string;
    }>;
    executeWithRetry<T>(operation: () => Promise<T>, operationName: string, maxRetries?: number): Promise<T>;
    private isRetryableError;
    private enhanceError;
    transaction<T>(fn: (prisma: PrismaClient) => Promise<T>, operationName?: string): Promise<T>;
    getDetailedHealthCheck(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        database: {
            connected: boolean;
            responseTime?: number;
            error?: string;
        };
        timestamp: string;
    }>;
}
