"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PatternAnalysisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatternAnalysisService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let PatternAnalysisService = PatternAnalysisService_1 = class PatternAnalysisService {
    prisma;
    logger = new common_1.Logger(PatternAnalysisService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async analyzeLanePatterns(originCity, originState, destinationCity, destinationState) {
        this.logger.log(`Analyzing patterns for lane: ${originCity}, ${originState} → ${destinationCity}, ${destinationState}`);
        try {
            const historicalOrders = await this.prisma.load.findMany({
                where: {
                    originCity,
                    originState,
                    destinationCity,
                    destinationState,
                    rate: { not: null },
                    createdAt: {
                        gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
                    }
                },
                orderBy: { createdAt: 'desc' },
                take: 200
            });
            if (historicalOrders.length < 3) {
                this.logger.log(`Insufficient data for lane analysis (${historicalOrders.length} orders)`);
                return null;
            }
            const laneId = `${originState}_${destinationState}_${originCity}_${destinationCity}`.replace(/\s+/g, '_');
            const rates = historicalOrders.filter(o => o.rate).map(o => o.rate);
            const rateAnalysis = this.analyzeRates(rates);
            const equipmentAnalysis = this.analyzeEquipment(historicalOrders);
            const weights = historicalOrders.filter(o => o.weightLbs).map(o => o.weightLbs);
            const weightAnalysis = this.analyzeWeights(weights);
            const timingAnalysis = this.analyzeTimings(historicalOrders);
            const successfulOrders = historicalOrders.filter(o => o.status === 'DELIVERED_EMPTY' || o.status === 'ASSIGNED' || o.status === 'INVOICED').length;
            const successRate = historicalOrders.length > 0 ? successfulOrders / historicalOrders.length : 0;
            const pattern = {
                laneId,
                originCity,
                originState,
                destinationCity,
                destinationState,
                averageRate: rateAnalysis.average,
                medianRate: rateAnalysis.median,
                rateRange: rateAnalysis.range,
                rateConfidence: rateAnalysis.confidence,
                mostCommonEquipment: equipmentAnalysis.mostCommon,
                equipmentDistribution: equipmentAnalysis.distribution,
                averageWeight: weightAnalysis.average,
                typicalWeightRange: weightAnalysis.range,
                averageTransitDays: timingAnalysis.averageDays,
                seasonalFactors: timingAnalysis.seasonalFactors,
                orderCount: historicalOrders.length,
                successRate,
                lastUpdated: new Date()
            };
            this.logger.log(`Lane pattern analysis complete: ${pattern.orderCount} orders, ${(pattern.successRate * 100).toFixed(1)}% success rate`);
            return pattern;
        }
        catch (error) {
            this.logger.error('Error analyzing lane patterns:', error);
            return null;
        }
    }
    async analyzeUserPreferences(userId) {
        this.logger.log(`Analyzing user preferences for: ${userId}`);
        try {
            const userOrders = await this.prisma.load.findMany({
                where: {
                    rawAirtableData: {
                        path: ['createdBy'],
                        equals: userId
                    },
                    createdAt: {
                        gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
                    }
                },
                orderBy: { createdAt: 'desc' },
                take: 100
            });
            if (userOrders.length < 5) {
                this.logger.log(`Insufficient data for user preference analysis (${userOrders.length} orders)`);
                return null;
            }
            const equipmentPrefs = this.analyzeUserEquipmentPreferences(userOrders);
            const rateAdjustment = await this.analyzeUserRateAdjustments(userOrders);
            const weightPrefs = this.analyzeUserWeightPreferences(userOrders);
            const confidenceScore = Math.min(userOrders.length / 20, 1) *
                this.calculateConsistencyScore(userOrders);
            const preference = {
                userId,
                preferredEquipment: equipmentPrefs,
                preferredRateAdjustment: rateAdjustment,
                preferredWeightRanges: weightPrefs,
                rateModificationFrequency: this.calculateRateModificationFrequency(userOrders),
                equipmentOverrideFrequency: this.calculateEquipmentOverrideFrequency(userOrders),
                dateAdjustmentPattern: this.calculateDateAdjustmentPattern(userOrders),
                orderCount: userOrders.length,
                lastActivity: new Date(),
                confidenceScore
            };
            this.logger.log(`User preference analysis complete: ${preference.orderCount} orders, ${(preference.confidenceScore * 100).toFixed(1)}% confidence`);
            return preference;
        }
        catch (error) {
            this.logger.error('Error analyzing user preferences:', error);
            return null;
        }
    }
    async generateSmartSuggestions(originCity, originState, destinationCity, destinationState, userId, currentValues) {
        this.logger.log(`[PATTERN-DEBUG] Generating suggestions for lane: ${originCity}, ${originState} → ${destinationCity}, ${destinationState}`);
        this.logger.log(`[PATTERN-DEBUG] User ID: ${userId}, Current values: ${JSON.stringify(currentValues)}`);
        const suggestions = [];
        try {
            this.logger.log('[PATTERN-DEBUG] Fetching lane patterns and user preferences...');
            const [lanePattern, userPreference] = await Promise.all([
                this.analyzeLanePatterns(originCity, originState, destinationCity, destinationState),
                this.analyzeUserPreferences(userId)
            ]);
            this.logger.log(`[PATTERN-DEBUG] Lane pattern found: ${!!lanePattern}, User preference found: ${!!userPreference}`);
            if (lanePattern) {
                this.logger.log(`[PATTERN-DEBUG] Lane pattern - Orders: ${lanePattern.orderCount}, Rate confidence: ${lanePattern.rateConfidence}`);
            }
            if (userPreference) {
                this.logger.log(`[PATTERN-DEBUG] User preference - Orders: ${userPreference.orderCount}, Confidence: ${userPreference.confidenceScore}`);
            }
            this.logger.log('[PATTERN-DEBUG] Generating rate suggestions...');
            if (lanePattern && lanePattern.rateConfidence > 0.6) {
                let suggestedRate = lanePattern.medianRate;
                if (userPreference && userPreference.confidenceScore > 0.5) {
                    suggestedRate *= (1 + userPreference.preferredRateAdjustment);
                    this.logger.log(`[PATTERN-DEBUG] Rate adjusted by user preference: ${lanePattern.medianRate} → ${suggestedRate}`);
                }
                const rateSuggestion = {
                    type: 'rate',
                    confidence: lanePattern.rateConfidence * (userPreference?.confidenceScore || 0.7),
                    suggestion: {
                        rate: Math.round(suggestedRate),
                        range: lanePattern.rateRange,
                        reasoning: `Based on ${lanePattern.orderCount} similar orders`
                    },
                    reasoning: `Historical median: $${lanePattern.medianRate}, typical range: $${lanePattern.rateRange.min}-$${lanePattern.rateRange.max}`,
                    source: userPreference ? 'user_preference' : 'historical'
                };
                suggestions.push(rateSuggestion);
                this.logger.log(`[PATTERN-DEBUG] Added rate suggestion: $${rateSuggestion.suggestion.rate} (confidence: ${rateSuggestion.confidence})`);
            }
            else {
                this.logger.log(`[PATTERN-DEBUG] Skipping rate suggestion - Pattern: ${!!lanePattern}, Confidence: ${lanePattern?.rateConfidence || 0}`);
            }
            if (lanePattern && lanePattern.mostCommonEquipment) {
                let suggestedEquipment = lanePattern.mostCommonEquipment;
                if (userPreference && userPreference.confidenceScore > 0.7) {
                    const userFavorite = Object.entries(userPreference.preferredEquipment)
                        .sort(([, a], [, b]) => b - a)[0];
                    if (userFavorite && userFavorite[1] > 5) {
                        suggestedEquipment = userFavorite[0];
                    }
                }
                suggestions.push({
                    type: 'equipment',
                    confidence: 0.8,
                    suggestion: {
                        equipment: suggestedEquipment,
                        alternatives: Object.keys(lanePattern.equipmentDistribution)
                            .filter(eq => eq !== suggestedEquipment)
                            .slice(0, 2)
                    },
                    reasoning: `${Math.round(lanePattern.equipmentDistribution[suggestedEquipment] * 100)}% of similar orders use ${suggestedEquipment}`,
                    source: (userPreference?.confidenceScore || 0) > 0.7 ? 'user_preference' : 'historical'
                });
            }
            if (lanePattern && lanePattern.averageWeight > 0) {
                suggestions.push({
                    type: 'weight',
                    confidence: 0.7,
                    suggestion: {
                        weight: Math.round(lanePattern.averageWeight),
                        range: lanePattern.typicalWeightRange
                    },
                    reasoning: `Typical weight for this route: ${Math.round(lanePattern.averageWeight)} lbs`,
                    source: 'historical'
                });
            }
            if (lanePattern && lanePattern.averageTransitDays > 0) {
                const currentMonth = new Date().getMonth() + 1;
                const seasonalFactor = lanePattern.seasonalFactors[currentMonth] || 1;
                const adjustedTransitDays = Math.ceil(lanePattern.averageTransitDays * seasonalFactor);
                suggestions.push({
                    type: 'timing',
                    confidence: 0.8,
                    suggestion: {
                        transitDays: adjustedTransitDays,
                        seasonalNote: seasonalFactor > 1.1 ? 'Expect delays due to seasonal factors' :
                            seasonalFactor < 0.9 ? 'Faster transit expected for this time of year' : null
                    },
                    reasoning: `Historical average: ${lanePattern.averageTransitDays} days, seasonal adjustment applied`,
                    source: 'historical'
                });
            }
            this.logger.log('[PATTERN-DEBUG] Generating validation warnings...');
            const warnings = await this.generateValidationWarnings(currentValues, lanePattern, userPreference);
            suggestions.push(...warnings);
            this.logger.log(`[PATTERN-DEBUG] Added ${warnings.length} validation warnings`);
            this.logger.log(`[PATTERN-DEBUG] SUCCESS: Generated ${suggestions.length} smart suggestions for lane`);
            this.logger.log(`[PATTERN-DEBUG] Suggestion types: ${suggestions.map(s => s.type).join(', ')}`);
            return suggestions;
        }
        catch (error) {
            this.logger.error('[PATTERN-ERROR] Error generating smart suggestions:', error);
            this.logger.error('[PATTERN-ERROR] Stack trace:', error.stack);
            return [];
        }
    }
    analyzeRates(rates) {
        if (rates.length === 0)
            return { average: 0, median: 0, range: { min: 0, max: 0 }, confidence: 0 };
        const sorted = rates.sort((a, b) => a - b);
        const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
        const median = sorted[Math.floor(sorted.length / 2)];
        const confidence = Math.min(rates.length / 10, 1);
        return {
            average: Math.round(average),
            median: Math.round(median),
            range: { min: sorted[0], max: sorted[sorted.length - 1] },
            confidence
        };
    }
    analyzeEquipment(orders) {
        const equipmentCounts = {};
        orders.forEach(order => {
            if (order.equipmentRequired) {
                equipmentCounts[order.equipmentRequired] = (equipmentCounts[order.equipmentRequired] || 0) + 1;
            }
        });
        const total = orders.length;
        const distribution = {};
        Object.entries(equipmentCounts).forEach(([equipment, count]) => {
            distribution[equipment] = count / total;
        });
        const mostCommon = Object.entries(equipmentCounts)
            .sort(([, a], [, b]) => b - a)[0]?.[0] || 'Dry Van';
        return { mostCommon, distribution };
    }
    analyzeWeights(weights) {
        if (weights.length === 0)
            return { average: 0, range: { min: 0, max: 0 } };
        const average = weights.reduce((sum, weight) => sum + weight, 0) / weights.length;
        const sorted = weights.sort((a, b) => a - b);
        return {
            average: Math.round(average),
            range: {
                min: sorted[Math.floor(sorted.length * 0.1)],
                max: sorted[Math.floor(sorted.length * 0.9)]
            }
        };
    }
    analyzeTimings(orders) {
        const transitTimes = [];
        const monthlyFactors = {};
        orders.forEach(order => {
            if (order.pickupDateUtc && order.deliveryDateUtc) {
                const pickup = new Date(order.pickupDateUtc);
                const delivery = new Date(order.deliveryDateUtc);
                const days = Math.ceil((delivery.getTime() - pickup.getTime()) / (1000 * 60 * 60 * 24));
                if (days > 0 && days < 30) {
                    transitTimes.push(days);
                    const month = pickup.getMonth() + 1;
                    if (!monthlyFactors[month])
                        monthlyFactors[month] = [];
                    monthlyFactors[month].push(days);
                }
            }
        });
        const averageDays = transitTimes.length > 0
            ? transitTimes.reduce((sum, days) => sum + days, 0) / transitTimes.length
            : 2;
        const seasonalFactors = {};
        Object.entries(monthlyFactors).forEach(([month, days]) => {
            const monthAverage = days.reduce((sum, d) => sum + d, 0) / days.length;
            seasonalFactors[month] = monthAverage / averageDays;
        });
        return { averageDays, seasonalFactors };
    }
    analyzeUserEquipmentPreferences(orders) {
        const equipmentCounts = {};
        orders.forEach(order => {
            if (order.equipmentRequired) {
                equipmentCounts[order.equipmentRequired] = (equipmentCounts[order.equipmentRequired] || 0) + 1;
            }
        });
        return equipmentCounts;
    }
    async analyzeUserRateAdjustments(userOrders) {
        return 0;
    }
    analyzeUserWeightPreferences(orders) {
        const weightsByEquipment = {};
        orders.forEach(order => {
            if (order.equipmentRequired && order.weightLbs) {
                if (!weightsByEquipment[order.equipmentRequired]) {
                    weightsByEquipment[order.equipmentRequired] = [];
                }
                weightsByEquipment[order.equipmentRequired].push(order.weightLbs);
            }
        });
        const preferences = {};
        Object.entries(weightsByEquipment).forEach(([equipment, weights]) => {
            if (weights.length >= 3) {
                const sorted = weights.sort((a, b) => a - b);
                preferences[equipment] = {
                    min: sorted[0],
                    max: sorted[sorted.length - 1]
                };
            }
        });
        return preferences;
    }
    calculateConsistencyScore(orders) {
        return 0.8;
    }
    calculateRateModificationFrequency(orders) {
        return 0.3;
    }
    calculateEquipmentOverrideFrequency(orders) {
        return 0.2;
    }
    calculateDateAdjustmentPattern(orders) {
        return 0;
    }
    async generateValidationWarnings(currentValues, lanePattern, userPreference) {
        const warnings = [];
        if (currentValues?.rate && lanePattern) {
            const rate = parseFloat(currentValues.rate);
            if (rate < lanePattern.rateRange.min * 0.7) {
                warnings.push({
                    type: 'warning',
                    confidence: 0.9,
                    suggestion: {
                        message: 'Rate appears unusually low for this route',
                        recommendedAction: `Consider rates between $${lanePattern.rateRange.min} - $${lanePattern.rateRange.max}`
                    },
                    reasoning: `Historical rates for this route range from $${lanePattern.rateRange.min} to $${lanePattern.rateRange.max}`,
                    source: 'business_rule'
                });
            }
            else if (rate > lanePattern.rateRange.max * 1.3) {
                warnings.push({
                    type: 'warning',
                    confidence: 0.8,
                    suggestion: {
                        message: 'Rate appears unusually high for this route',
                        recommendedAction: 'Double-check rate calculation'
                    },
                    reasoning: `Rate is significantly above historical maximum of $${lanePattern.rateRange.max}`,
                    source: 'business_rule'
                });
            }
        }
        return warnings;
    }
};
exports.PatternAnalysisService = PatternAnalysisService;
exports.PatternAnalysisService = PatternAnalysisService = PatternAnalysisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PatternAnalysisService);
//# sourceMappingURL=pattern-analysis.service.js.map