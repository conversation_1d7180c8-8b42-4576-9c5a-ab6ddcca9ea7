# APM Task Assignment: Collapsible Sidebar Implementation

## 1. Agent Role & APM Context

**Introduction:** You are activated as an **Implementation Agent** within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As an Implementation Agent, you are responsible for implementing new features, enhancing existing functionality, and ensuring robust integration with the current system architecture.

**Workflow:** You will work directly with the Manager Agent (via the User) to implement a collapsible sidebar feature that enhances user experience by providing more screen real estate when needed.

## 2. Context from Prior Work

**UI/UX Enhancement Phase (Phase 2.5) Context:**
- ✅ **P2.5-T1:** UI/UX improvements completed (FAQ, onboarding, profile completion)
- ✅ **Debug Tasks:** Profile completion banner and onboarding tour fixes resolved
- ✅ **Loadboard Scrolling:** Horizontal scroll issues fixed
- ✅ **Dependencies:** All Radix UI dependencies resolved

**Current System State:** The dashboard features a fixed sidebar navigation with organization-specific pages. All recent UI improvements are functional and ready for additional enhancements.

## 3. Task Assignment

**Reference Implementation Plan:** This is Phase 2.5-T3 - Collapsible Sidebar Implementation

**Objective:** Implement a smooth, accessible collapsible sidebar feature that allows users to toggle sidebar visibility to maximize content area when needed.

### Detailed Requirements:

#### A. Core Functionality Requirements
1. **Toggle Mechanism:**
   - Clean toggle button positioned in sidebar header or main content area
   - Keyboard shortcut support (e.g., Ctrl/Cmd + B)
   - State persistence across page navigation and browser sessions

2. **Collapse Behavior:**
   - **Collapsed State:** Sidebar reduces to icon-only strip (~60px width)
   - **Expanded State:** Full sidebar with text labels and navigation
   - Smooth CSS transitions (300ms duration recommended)
   - Icons remain visible and functional in collapsed state

3. **Content Adaptation:**
   - Main content area expands smoothly to fill available space
   - No layout shift or content jumping during transitions
   - Tables and components adjust width appropriately

#### B. Visual Design Requirements
1. **Collapsed Sidebar Design:**
   ```
   [≡] <- Toggle button
   [🏠] <- Dashboard icon
   [📋] <- Loadboard icon  
   [📦] <- My Loads icon
   [⚙️] <- Settings icon
   [👥] <- Admin icon (if applicable)
   ```

2. **Hover Behavior (Collapsed State):**
   - Tooltip labels appear on hover for each icon
   - Optional: Quick peek expansion on hover
   - Clear visual feedback for interactive elements

3. **Animation & Transitions:**
   - Smooth width transition for sidebar container
   - Fade in/out for text labels
   - Icon positioning adjustments during transition
   - Easing functions for professional animation feel

#### C. Technical Implementation Requirements
1. **State Management:**
   - Use React context or local storage for sidebar state
   - Consistent state across all dashboard pages
   - Proper state initialization on app load

2. **Responsive Behavior:**
   - **Desktop:** Full collapsible functionality
   - **Tablet:** Consider auto-collapse below certain width
   - **Mobile:** Maintain existing mobile navigation behavior

3. **Accessibility:**
   - ARIA labels for collapsed icons
   - Keyboard navigation support
   - Screen reader announcements for state changes
   - Focus management during state transitions

### Detailed Action Steps:

#### A. Architecture Analysis & Planning
1. **Current Sidebar Investigation:**
   - **Primary File:** `apps/web/src/components/layout/` (likely navigation component)
   - **Dashboard Layout:** `apps/web/src/app/org/[orgId]/layout.tsx`
   - **Navigation Components:** Identify current sidebar structure and routing

2. **State Management Setup:**
   - Choose between React Context or Zustand store for sidebar state
   - Implement localStorage persistence for user preference
   - Consider server-side rendering compatibility

3. **Component Architecture Planning:**
   ```typescript
   // Proposed structure
   interface SidebarState {
     isCollapsed: boolean;
     toggleSidebar: () => void;
     setCollapsed: (collapsed: boolean) => void;
   }
   ```

#### B. Core Implementation
1. **Sidebar Component Enhancement:**
   - **File to Modify:** Main sidebar/navigation component
   - **Add Features:**
     - Toggle button with appropriate icon (hamburger or chevron)
     - Collapse/expand animation logic
     - Icon-only view for collapsed state
     - Tooltip system for collapsed icons

2. **Layout Integration:**
   - **File to Modify:** `apps/web/src/app/org/[orgId]/layout.tsx`
   - **Ensure:** Main content area adjusts width dynamically
   - **Add:** CSS classes for collapsed/expanded states
   - **Implement:** Smooth transition animations

3. **CSS Implementation:**
   ```css
   /* Sidebar States */
   .sidebar {
     width: 240px; /* Expanded width */
     transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
   }
   
   .sidebar.collapsed {
     width: 60px; /* Collapsed width */
   }
   
   /* Content Area Adjustment */
   .main-content {
     margin-left: 240px;
     transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
   }
   
   .main-content.sidebar-collapsed {
     margin-left: 60px;
   }
   
   /* Text/Icon Transitions */
   .sidebar-text {
     opacity: 1;
     transition: opacity 200ms ease-in-out;
   }
   
   .sidebar.collapsed .sidebar-text {
     opacity: 0;
     pointer-events: none;
   }
   ```

#### C. Advanced Features Implementation
1. **Keyboard Shortcut Support:**
   ```typescript
   // Global keyboard handler
   useEffect(() => {
     const handleKeyDown = (e: KeyboardEvent) => {
       if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
         e.preventDefault();
         toggleSidebar();
       }
     };
     
     window.addEventListener('keydown', handleKeyDown);
     return () => window.removeEventListener('keydown', handleKeyDown);
   }, [toggleSidebar]);
   ```

2. **Tooltip System (Collapsed State):**
   - Implement using Radix UI Tooltip components
   - Show navigation labels on icon hover
   - Position tooltips appropriately
   - Ensure accessibility compliance

3. **State Persistence:**
   ```typescript
   // localStorage integration
   const [isCollapsed, setIsCollapsed] = useState(() => {
     if (typeof window !== 'undefined') {
       return localStorage.getItem('sidebar-collapsed') === 'true';
     }
     return false;
   });
   
   useEffect(() => {
     localStorage.setItem('sidebar-collapsed', isCollapsed.toString());
   }, [isCollapsed]);
   ```

#### D. Mobile & Responsive Considerations
1. **Breakpoint Strategy:**
   - **Desktop (>= 1024px):** Full collapsible functionality
   - **Tablet (768px - 1023px):** Auto-collapse or maintain mobile behavior
   - **Mobile (< 768px):** Keep existing mobile navigation (likely overlay)

2. **Touch Interaction:**
   - Ensure toggle button is touch-friendly (minimum 44px hit area)
   - Consider swipe gestures for mobile devices
   - Test interaction on various screen sizes

#### E. Integration Testing & Verification
1. **Navigation Testing:**
   - Verify all navigation links work in both collapsed and expanded states
   - Test active state indicators in both modes
   - Ensure proper highlighting of current page

2. **Layout Testing:**
   - Test content area expansion/contraction
   - Verify no content overflow or layout breaks
   - Test with various content lengths and page types

3. **Performance Testing:**
   - Ensure smooth 60fps animations
   - Test state persistence across page navigation
   - Verify no memory leaks from event listeners

## 4. Technical Implementation Guidelines

**Key Files to Focus On:**
- Main navigation/sidebar component (`apps/web/src/components/layout/`)
- Dashboard layout file (`apps/web/src/app/org/[orgId]/layout.tsx`)
- Global CSS for animations (`apps/web/src/app/globals.css`)
- Potentially create new sidebar context file

**Implementation Patterns:**
1. **Context Provider Pattern:**
   ```typescript
   const SidebarContext = createContext<SidebarState | undefined>(undefined);
   
   export const SidebarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
     const [isCollapsed, setIsCollapsed] = useState(false);
     
     const toggleSidebar = useCallback(() => {
       setIsCollapsed(prev => !prev);
     }, []);
     
     return (
       <SidebarContext.Provider value={{ isCollapsed, toggleSidebar, setCollapsed: setIsCollapsed }}>
         {children}
       </SidebarContext.Provider>
     );
   };
   ```

2. **Animation Component Pattern:**
   ```typescript
   const AnimatedSidebar: React.FC = ({ children }) => {
     const { isCollapsed } = useSidebar();
     
     return (
       <aside className={cn(
         "sidebar",
         isCollapsed && "collapsed"
       )}>
         {children}
       </aside>
     );
   };
   ```

3. **Icon Navigation Pattern:**
   ```typescript
   const NavItem: React.FC<NavItemProps> = ({ icon, label, href, isActive }) => {
     const { isCollapsed } = useSidebar();
     
     return (
       <TooltipProvider>
         <Tooltip delayDuration={0}>
           <TooltipTrigger asChild>
             <Link 
               href={href}
               className={cn(
                 "nav-item",
                 isActive && "active",
                 isCollapsed && "collapsed"
               )}
             >
               {icon}
               {!isCollapsed && <span className="nav-label">{label}</span>}
             </Link>
           </TooltipTrigger>
           {isCollapsed && (
             <TooltipContent side="right">
               <p>{label}</p>
             </TooltipContent>
           )}
         </Tooltip>
       </TooltipProvider>
     );
   };
   ```

## 5. Expected Output & Deliverables

**Define Success:** Successful completion means:
- ✅ Sidebar toggles smoothly between collapsed and expanded states
- ✅ Icons remain visible and functional in collapsed state
- ✅ Main content area adjusts width appropriately during transitions
- ✅ State persists across page navigation and browser sessions
- ✅ Keyboard shortcut (Ctrl/Cmd + B) works consistently
- ✅ Tooltips display navigation labels in collapsed state
- ✅ Responsive behavior works correctly across all device sizes
- ✅ Accessibility standards maintained for all interactions

**Specific Deliverables:**
1. **Functional Collapsible Sidebar:** Complete toggle functionality with smooth animations
2. **State Management System:** Persistent sidebar state across sessions
3. **Icon Navigation:** Clear, accessible icon-only navigation in collapsed state
4. **Keyboard Support:** Working keyboard shortcuts for power users
5. **Responsive Design:** Appropriate behavior across all device sizes
6. **Documentation:** Clear code comments and implementation notes

**Critical Success Criteria:**
- **Animation Quality:** Smooth 300ms transitions without jank
- **Content Adaptation:** Main area expands/contracts without layout issues
- **User Experience:** Intuitive interaction with clear visual feedback
- **Performance:** No impact on page load times or interaction responsiveness
- **Accessibility:** Full keyboard navigation and screen reader support

## 6. UI/UX Considerations

**Visual Design Guidelines:**
- Use consistent icon set (Lucide React or current icon library)
- Maintain brand colors and styling consistency
- Ensure adequate contrast for collapsed state icons
- Implement hover states that provide clear interaction feedback

**User Experience Requirements:**
- Clear visual indication of sidebar state (expanded vs collapsed)
- Smooth, non-jarring transitions that feel natural
- Maintain navigation efficiency in both states
- Provide discoverability hint for new users (subtle animation or tooltip)

**Interaction Design:**
- Toggle button should be easily discoverable and clickable
- Collapsed icons should have sufficient touch target size (44px minimum)
- State changes should feel responsive and immediate
- Consider brief animation on first load to demonstrate functionality

## 7. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Ensure your log includes:
- Reference to **P2.5-T3: Collapsible Sidebar Implementation**
- **Implementation Approach:** State management strategy and component architecture
- **Technical Solutions:** CSS animations, React patterns, accessibility implementations
- **Files Modified:** Complete list of files changed and new files created
- **Testing Results:** Verification across different devices and interaction methods
- **Performance Impact:** Animation performance and state management efficiency
- **User Experience Notes:** Behavior patterns and interaction feedback

**Special Instructions:**
- Mark this as **UI/UX ENHANCEMENT**
- Include before/after screenshots or descriptions of behavior
- Document any reusable patterns created for future navigation enhancements
- Provide usage guidelines for maintaining the collapsible sidebar system

## 8. Clarification Instructions

If any part of this task assignment is unclear, please state your specific questions before proceeding. Key areas to clarify if needed:
- Specific navigation items that should be included in collapsed state
- Preferred icon library or existing icon system to use
- Any existing design system constraints or brand guidelines
- Specific keyboard shortcuts or interaction patterns preferred
- Mobile navigation behavior and responsive breakpoints

---

**Priority:** 🟡 **MEDIUM** - UI/UX enhancement to improve user experience

**Estimated Duration:** 4-6 hours

**Success Metric:** Professional, smooth collapsible sidebar that enhances user workflow efficiency and screen real estate utilization.

**Dependencies:** None - can begin immediately

**Impact:** Improves user experience by providing more flexible workspace management and modern navigation UX patterns. 