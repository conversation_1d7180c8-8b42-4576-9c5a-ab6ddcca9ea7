# Using Monorepos

Vercel provides support for monorepos. Learn how to deploy a monorepo here.

Monorepos allow you to manage multiple projects in a single directory. They are a great way to organize your projects and make them easier to work with.

## [Deploy a template monorepo](#deploy-a-template-monorepo)

Get started with monorepos on Vercel in a few minutes by using one of our monorepo quickstart templates.

  

[![](https://assets.vercel.com/image/upload/v1689795055/docs-assets/static/topics/icons/turborepo-dark.svg)

### Turborepo

Read the Turborepo docs, or start from an example.](/docs/monorepos/turborepo)

[Deploy](https://vercel.com/new/clone?repository-url=https://github.com/vercel/turbo/tree/main/examples/basic&project-name=turbo-monorepo&repository-name=turbo-monorepo&root-directory=apps/web&install-command=pnpm%20install&build-command=turbo%20build&skip-unaffected=true)[Live Example](https://examples-basic-web.vercel.sh/)

[![](https://assets.vercel.com/image/upload/v1689795055/docs-assets/static/topics/icons/nx.svg)

### Nx

Read the Nx docs, or start from an example.](/docs/monorepos/nx)

[Deploy](https://vercel.com/new/clone?build-command=cd%20..%2F..%2F%20%26%26%20npx%20nx%20build%20app%20--prod&demo-description=Learn%20to%20implement%20a%20monorepo%20with%20a%20single%20Next.js%20site%20using%20Nx.&demo-image=%2F%2Fimages.ctfassets.net%2Fe5382hct74si%2F4w8MJqkgHvXlKgBMglBHsB%2F6cd4b35af6024e08c9a8b7ded092af2d%2Fsolutions-nx-monorepo.vercel.sh_.png&demo-title=Monorepo%20with%20Nx&demo-url=https%3A%2F%2Fsolutions-nx-monorepo.vercel.sh&output-directory=out%2F.next&project-name=nx-monorepo&repository-name=nx-monorepo&repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fexamples%2Ftree%2Fmain%2Fsolutions%2Fnx-monorepo&root-directory=apps%2Fapp&teamSlug=vercel)[Live Example](https://examples-nx-monorepo.vercel.app/)

## [Add a monorepo through the Vercel Dashboard](#add-a-monorepo-through-the-vercel-dashboard)

1.  Go to the [Vercel Dashboard](https://vercel.com/dashboard) and ensure your team is selected from the [scope selector](/docs/dashboard-features#scope-selector).
2.  Select the Add New… button, and then choose Project from the list. You'll create a new [project](/docs/projects/overview) for each directory in your monorepo that you wish to import.
3.  From the Import Git Repository section, select the Import button next to the repository you want to import.
4.  Before you deploy, you'll need to specify the directory within your monorepo that you want to deploy. Click the Edit button next to the [Root Directory setting](/docs/deployments/configure-a-build#root-directory) to select the directory, or project, you want to deploy. This will configure the root directory of each project to its relevant directory in the repository:
    
    ![Selecting a Root Directory for one of your new Projects.](/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1729190708%2Fdocs-assets%2Fstatic%2Fdocs%2Fconcepts%2Fprojects%2Fmonorepo-import-light.png&w=1080&q=75)![Selecting a Root Directory for one of your new Projects.](/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1729190708%2Fdocs-assets%2Fstatic%2Fdocs%2Fconcepts%2Fprojects%2Fmonorepo-import-dark.png&w=1080&q=75)
    
    Selecting a Root Directory for one of your new Projects.
    
5.  Configure any necessary settings and click the Deploy button to deploy that project.
6.  Repeat steps 2-5 to [import each directory](/docs/git#deploying-a-git-repository) from your monorepo that you want to deploy.

Once you've created a separate project for each of the directories within your Git repository, every commit will issue a deployment for all connected projects and display the resulting URLs on your pull requests and commits:

![An example of Deployment URLs provided for a Deployment through Git.](/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1729191499%2Fdocs-assets%2Fstatic%2Fdocs%2Fconcepts%2Fprojects%2Fgithub-comment-light.png&w=1920&q=75)![An example of Deployment URLs provided for a Deployment through Git.](/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1729191499%2Fdocs-assets%2Fstatic%2Fdocs%2Fconcepts%2Fprojects%2Fgithub-comment-dark.png&w=1920&q=75)

An example of Deployment URLs provided for a Deployment through Git.

The number of Vercel Projects connected with the same Git repository is [limited depending on your plan](/docs/limits#general-limits).

## [Add a monorepo through Vercel CLI](#add-a-monorepo-through-vercel-cli)

You should use [Vercel CLI 20.1.0](/docs/cli#updating-vercel-cli) or newer.

1.  Ensure you're in the root directory of your monorepo. Vercel CLI should not be invoked from the subdirectory.
2.  Run `vercel link` to link multiple Vercel projects at once. To learn more, see the [CLI documentation](/docs/cli/link#repo-alpha):
    
    ```
    vercel link --repo
    ```
    
3.  Once linked, subsequent commands such as `vercel dev` will use the selected Vercel Project. To switch to a different Project in the same monorepo, run `vercel link` again and select the new Project.

Alternatively, you can use `git clone` to create multiple copies of your monorepo in different directories and link each one to a different Vercel Project.

See this [example](https://github.com/vercel-support/yarn-ws-monorepo) of a monorepo with Yarn Workspaces.

## [When does a monorepo build occur?](#when-does-a-monorepo-build-occur)

By default, pushing a commit to your monorepo will create a deployment for each of the connected Vercel projects. However, you can choose to:

*   [Skip unaffected projects](#skipping-unaffected-projects) by only building projects whose files have changed.
*   [Ignore the build step](#ignoring-the-build-step) for projects whose files have not changed.

### [Skipping unaffected projects](#skipping-unaffected-projects)

A project in a monorepo is considered to be changed if any of the following conditions are true:

1.  The project source code has changed
2.  Any of the project's internal dependencies have changed.
3.  A change to a package manager lockfile has occurred, that _only_ impacts the dependencies of the project.

Vercel automatically skips builds for projects in a monorepo that are unchanged by the commit.

This setting does not occupy [concurrent build slots](/docs/deployments/concurrent-builds), unlike the [Ignored Build Step](/docs/project-configuration/git-settings#ignored-build-step) feature, reducing build queue times.

#### [Requirements](#requirements)

*   This feature is only available for projects connected to GitHub repositories.
*   The monorepo must be using `npm`, `yarn`, or `pnpm` workspaces. We detect your package manager by the lockfile present at the repository root. You can also specify the package manager with the `packageManager` field in root `package.json` file.
*   All packages within the workspace must have a _unique_ `name` field in their `package.json` file.
*   Dependencies between packages in the monorepo must be explicitly stated in each package's `package.json` file. This is necessary to determine the dependency graph between packages.
    *   For example, an end-to-end tests package (`package-e2e`) tests must depend on the package it tests (`package-core`) in the `package.json` of `package-e2e`.

#### [Disable the skipping unaffected projects feature](#disable-the-skipping-unaffected-projects-feature)

To disable this behavior, [visit the project's Root Directory settings](https://vercel.com/d?to=%2F%5Bteam%5D%2F%5Bproject%5D%2Fsettings%2Fbuild-and-deployment%23root-directory&title=Disable+unaffected+project+skipping).

1.  From the [Dashboard](https://vercel.com/dashboard), select the project you want to configure and navigate to the Settings tab.
2.  Go to the Build and Deployment page of the project's Settings.
3.  Scroll down to Root Directory
4.  Toggle the Skip deployment switch to Disabled.
5.  Click Save to apply the changes.

### [Ignoring the build step](#ignoring-the-build-step)

If you want to cancel the Build Step for projects if their files didn't change, you can do so with the [Ignored Build Step](/docs/project-configuration/git-settings#ignored-build-step) project setting. Canceled builds initiated using the ignore build step do count towards your deployment and concurrent build limits and so [skipping unaffected projects](#skipping-unaffected-projects) may be a better option for monorepos with many projects.

If you have created a script to ignore the build step, you can skip the [the script](/guides/how-do-i-use-the-ignored-build-step-field-on-vercel) when redeploying or promoting your app to production. This can be done through the dashboard when you click on the Redeploy button, and unchecking the Use project's Ignore Build Step checkbox.

## [How to link projects together in a monorepo](#how-to-link-projects-together-in-a-monorepo)

When working in a monorepo with multiple applications—such as a frontend and a backend—it can be challenging to manage the connection strings between environments to ensure a seamless experience. Traditionally, referencing one project from another requires manually setting URLs or environment variables for each deployment, in _every_ environment.

With Related Projects, this process is streamlined, enabling teams to:

*   Verify changes in pre-production environments without manually updating URLs or environment variables.
*   Eliminate misconfigurations when referencing internal services across multiple deployments, and environments.

For example, if your monorepo contains:

1.  A frontend project that fetches data from an API
2.  A backend API project that serves the data

Related Projects can ensure that each preview deployment of the frontend automatically references the corresponding preview deployment of the backend, avoiding the need for hardcoded environment variables when testing changes that span both projects.

### [Requirements](#requirements)

*   A maximum of 3 projects can be linked together
*   Only supports projects within the same repository
*   CLI deployments are not supported

### [Getting started](#getting-started)

1.  #### [Define Related Projects](#define-related-projects)
    
    Specify the projects your app needs to reference in a `vercel.json` configuration file at the root of the app. While every app in your monorepo can list related projects in their own `vercel.json`, you can only specify up to three related projects per app.
    
    ```
    {
      "relatedProjects": ["prj_123"]
    }
    ```
    
    This will make the preview, and production hosts of `prj_123` available as an environment variable in the deployment of the `frontend` project.
    
    You can [find your project ID](https://vercel.com/d?to=%2F%5Bteam%5D%2F%5Bproject%5D%2Fsettings%23project-id&title=Find+your+Vercel+project+ID) in the project Settings page in the Vercel dashboard.
    
2.  #### [Retrieve Related Project Information](#retrieve-related-project-information)
    
    The next deployment will have the `VERCEL_RELATED_PROJECTS` environment variable set containing the urls of the related projects for use.
    
    View the data provided for each project in the [`@vercel/related-projects`](https://github.com/vercel/vercel/blob/main/packages/related-projects/src/types.ts#L9-L58) package.
    
    For easy access to this information, you can use the [`@vercel/related-projects`](https://github.com/vercel/vercel/tree/main/packages/related-projects) npm package:
    
    ```
    pnpm add @vercel/related-projects
    ```
    
    1.  Easily reference hosts of related projects
    
    ```
    import { withRelatedProject } from '@vercel/related-projects';
     
    const apiHost = withRelatedProject({
      projectName: 'my-api-project',
      /**
       * Specify a default host that will be used for my-api-project if the related project
       * data cannot be parsed or is missing.
       */
      defaultHost: process.env.API_HOST,
    });
    ```
    
    2.  Retrieve just the related project data:
    
    ```
    import {
      relatedProjects,
      type VercelRelatedProject,
    } from '@vercel/related-projects';
     
    // fully typed project data
    const projects: VercelRelatedProject[] = relatedProjects();
    ```
    

---------------------
# Monorepos FAQ

Learn the answer to common questions about deploying monorepos on Vercel.

## [How can I speed up builds?](#how-can-i-speed-up-builds)

Whether or not your deployments are queued depends on the amount of Concurrent Builds you have available. Hobby plans are limited to 1 Concurrent Build, while Pro or Enterprise plans can customize the amount on the "Billing" page in the team settings.

Learn more about [Concurrent Builds](/docs/deployments/concurrent-builds).

## [How can I make my projects available on different paths under the same domain?](#how-can-i-make-my-projects-available-on-different-paths-under-the-same-domain)

After having set up your monorepo as described above, each of the directories will be a separate Vercel project, and therefore be available on a separate domain.

If you'd like to host multiple projects under a single domain, you can create a new project, assign the domain in the project settings, and proxy requests to the other upstream projects. The proxy can be implemented using a `vercel.json` file with the [rewrites](/docs/project-configuration#rewrites) property, where each `source` is the path under the main domain and each `destination` is the upstream project domain.

## [How are projects built after I push?](#how-are-projects-built-after-i-push)

Pushing a commit to a Git repository that is connected with multiple Vercel projects will result in multiple deployments being created and built in parallel for each.

## [Can I share source files between projects? Are shared packages supported?](#can-i-share-source-files-between-projects-are-shared-packages-supported)

To access source files outside the Root Directory, enable the Include source files outside of the Root Directory in the Build Step option in the Root Directory section within the project settings.

For information on using Yarn workspaces, see [Deploying a Monorepo Using Yarn Workspaces to Vercel](/guides/deploying-yarn-monorepos-to-vercel).

Vercel projects created after August 27th 2020 23:50 UTC have this option enabled by default. If you're using Vercel CLI, at least version 20.1.0 is required.

## [How can I use Vercel CLI without Project Linking?](#how-can-i-use-vercel-cli-without-project-linking)

Vercel CLI will accept Environment Variables instead of Project Linking, which can be useful for deployments from CI providers. For example:

```
VERCEL_ORG_ID=team_123 VERCEL_PROJECT_ID=prj_456 vercel
```

Learn more about [Vercel CLI for custom workflows](/guides/using-vercel-cli-for-custom-workflows).

## [Can I use Turborepo on the Hobby plan?](#can-i-use-turborepo-on-the-hobby-plan)

Yes. Turborepo is available on all plans.

## [Can I use Nx with environment variables on Vercel?](#can-i-use-nx-with-environment-variables-on-vercel)

When using [Nx](https://nx.dev/getting-started/intro) on Vercel with [environment variables](/docs/environment-variables), you may encounter an issue where some of your environment variables are not being assigned the correct value in a specific deployment.

This can happen if the environment variable is not initialized or defined in that deployment. If that's the case, the system will look for a value in an existing cache which may or may not be the value you would like to use. It is a recommended practice to define all environment variables in each deployment for all monorepos.

With Nx, you also have the ability to prevent the environment variable from using a cached value. You can do that by using [Runtime Hash Inputs](https://nx.dev/using-nx/caching#runtime-hash-inputs). For example, if you have an environment variable `MY_VERCEL_ENV` in your project, you will add the following line to your `nx.json` configuration file:

```
"runtimeCacheInputs": ["echo $MY_VERCEL_ENV"]
```


Last updated on March 12, 2025