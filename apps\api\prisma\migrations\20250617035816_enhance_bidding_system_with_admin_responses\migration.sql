-- CreateEnum
CREATE TYPE "InvStatus" AS ENUM ('Not Sent', 'Sent', 'Paid');

-- CreateEnum
CREATE TYPE "AdminResponse" AS ENUM ('PENDING', 'ACCEPTED', 'COUNTERED', 'DECLINED');

-- CreateEnum
CREATE TYPE "NegotiationStatus" AS ENUM ('OPEN', 'CLOSED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "BidResponseType" AS ENUM ('INITIAL_BID', 'ADMIN_RESPONSE', 'CARRIER_RESPONSE');

-- AlterTable
ALTER TABLE "bids" ADD COLUMN     "admin_notes" TEXT,
ADD COLUMN     "admin_response" "AdminResponse" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "counter_offer_amount" DOUBLE PRECISION,
ADD COLUMN     "expires_at" TIMESTAMP(3),
ADD COLUMN     "negotiation_status" "NegotiationStatus" NOT NULL DEFAULT 'OPEN',
ADD COLUMN     "response_timestamp" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "loads" ADD COLUMN     "carrier" TEXT,
ADD COLUMN     "cases" INTEGER,
ADD COLUMN     "delivery_number" TEXT,
ADD COLUMN     "inv_status" "InvStatus",
ADD COLUMN     "pallets" INTEGER,
ADD COLUMN     "pickup_number" TEXT,
ADD COLUMN     "po_number" TEXT,
ADD COLUMN     "receiver_address" TEXT,
ADD COLUMN     "receiver_name" TEXT,
ADD COLUMN     "shipper_address" TEXT,
ADD COLUMN     "so_number" TEXT;

-- CreateTable
CREATE TABLE "distance_cache" (
    "id" TEXT NOT NULL,
    "origin_address" TEXT NOT NULL,
    "destination_address" TEXT NOT NULL,
    "distance_miles" DOUBLE PRECISION NOT NULL,
    "duration_hours" DOUBLE PRECISION NOT NULL,
    "calculated_by" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "distance_cache_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bid_responses" (
    "id" TEXT NOT NULL,
    "bid_id" TEXT NOT NULL,
    "response_type" "BidResponseType" NOT NULL,
    "amount" DOUBLE PRECISION,
    "notes" TEXT,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "bid_responses_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "distance_cache_origin_address_destination_address_key" ON "distance_cache"("origin_address", "destination_address");

-- CreateIndex
CREATE INDEX "bid_responses_bid_id_idx" ON "bid_responses"("bid_id");

-- CreateIndex
CREATE INDEX "bid_responses_created_at_idx" ON "bid_responses"("created_at");

-- CreateIndex
CREATE INDEX "bid_responses_created_by_idx" ON "bid_responses"("created_by");

-- CreateIndex
CREATE INDEX "bids_load_id_admin_response_idx" ON "bids"("load_id", "admin_response");

-- CreateIndex
CREATE INDEX "bids_carrier_profile_id_admin_response_idx" ON "bids"("carrier_profile_id", "admin_response");

-- CreateIndex
CREATE INDEX "bids_response_timestamp_idx" ON "bids"("response_timestamp");

-- CreateIndex
CREATE INDEX "bids_expires_at_idx" ON "bids"("expires_at");

-- CreateIndex
CREATE INDEX "bids_negotiation_status_idx" ON "bids"("negotiation_status");

-- AddForeignKey
ALTER TABLE "bid_responses" ADD CONSTRAINT "bid_responses_bid_id_fkey" FOREIGN KEY ("bid_id") REFERENCES "bids"("id") ON DELETE CASCADE ON UPDATE CASCADE;
