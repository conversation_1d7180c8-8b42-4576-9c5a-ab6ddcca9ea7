import { NextRequest, NextResponse } from 'next/server';

const publicRoutes = [
  '/',
  '/sign-in',
  '/sign-up',
  '/api/health',
];

function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => {
    if (route.endsWith('(.*)')) {
      return pathname.startsWith(route.replace('(.*)', ''));
    }
    return pathname === route || pathname.startsWith(route + '/');
  });
}

function validateJWTToken(token: string): boolean {
  try {
    // Basic JWT structure validation
    const parts = token.split('.');
    if (parts.length !== 3 || !parts[1]) return false;
    
    // Decode payload to check expiration
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);
    
    // Check if token is expired
    if (payload.exp && payload.exp < now) {
      return false;
    }
    
    // Check if token has required fields
    return !!(payload.id && payload.email);
  } catch {
    return false;
  }
}

export default function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  
  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/_next/') ||
    pathname.includes('.') // Skip files with extensions
  ) {
    return NextResponse.next();
  }

  // Check if route is public
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }

  // Check for authentication token
  const token = req.cookies.get('n8n_auth_token')?.value || 
               req.headers.get('authorization')?.replace('Bearer ', '');

  // If no token, redirect to sign-in
  if (!token) {
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Validate token
  if (!validateJWTToken(token)) {
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(signInUrl);
  }

  // If we reach here, user is authenticated
  // Proceed to construct and apply security headers
  const nonce = Buffer.from(crypto.randomUUID()).toString('base64');
  const isDevelopment = process.env.NODE_ENV === 'development';
  const appDomain = "*.fcp-portal.com";

  const commonScriptDomains = [
    `https://cdn.jsdelivr.net`,
    `https://js.sentry-cdn.com`,
    `https://browser.sentry-cdn.com`,
    `https://*.sentry.io`,
    `https://challenges.cloudflare.com`,
    `https://js.stripe.com`,
    `https://*.js.stripe.com`,
    `https://maps.googleapis.com`,
    `https://*.googletagmanager.com`,
    `https://*.google-analytics.com`,
    `https://*.vercel.live`,
    `https://vercel.live`,
    `https://*.hotjar.com`,
    `https://static.hotjar.com`,
    `https://*.intercom.io`,
    `https://widget.intercom.io`,
  ];

  const scriptSrcDirectives = [
    `'self'`,
    `'nonce-${nonce}'`,
    `'unsafe-eval'`,
    `'unsafe-inline'`,
    ...commonScriptDomains,
  ];
  
  if (isDevelopment) {
    scriptSrcDirectives.push(`http:`, `https:`, `ws:`, `wss:`);
  } else {
    scriptSrcDirectives.push(`https:`);
  }

  const connectSrcDirectives = [
    `'self'`,
    `https://${appDomain}`,
    `https://api.stripe.com`,
    `https://maps.googleapis.com`,
    `https://*.sentry.io`,
    `https://*.vercel.live`,
    `https://vercel.live`,
    process.env.NEXT_PUBLIC_N8N_BASE_URL || '',
  ].filter(Boolean);
  
  if (isDevelopment) {
    connectSrcDirectives.push(`ws:`, `wss:`);
  }

  const cspDirectives = [
    `default-src 'self' https://${appDomain};`,
    `script-src ${scriptSrcDirectives.join(' ')};`,
    `style-src 'self' 'unsafe-inline' https://${appDomain} https://fonts.googleapis.com;`,
    `img-src 'self' data: https: blob: https://${appDomain} https://*.stripe.com https://www.google-analytics.com https://www.googletagmanager.com;`,
    `font-src 'self' https://fonts.gstatic.com https://${appDomain};`,
    `connect-src ${connectSrcDirectives.join(' ')};`,
    `frame-src 'self' https://${appDomain} https://challenges.cloudflare.com https://js.stripe.com https://hooks.stripe.com https://*.vercel.live https://vercel.live;`,
    `worker-src 'self' blob:;`,
    `object-src 'none';`,
    `base-uri 'self';`,
    `form-action 'self';`,
    `frame-ancestors 'none';`,
    `media-src 'self';`,
    `manifest-src 'self';`
  ];

  if (!isDevelopment) {
    cspDirectives.push(`upgrade-insecure-requests;`);
  }

  const cspHeader = cspDirectives.join(' ').replace(/\s{2,}/g, ' ').trim();
  const response = NextResponse.next();
  response.headers.set('Content-Security-Policy', cspHeader);
  response.headers.set('x-nonce', nonce);

  // Additional security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=()');

  return response;
}

export const config = {
  matcher: [
    // Skip all Next.js internals and static files
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
    // Include API routes that need authentication
    '/(api|trpc)(.*)',
  ],
}; 