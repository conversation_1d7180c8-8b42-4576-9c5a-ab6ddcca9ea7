"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AdminGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const auth_service_1 = require("./auth.service");
let AdminGuard = AdminGuard_1 = class AdminGuard {
    reflector;
    authService;
    logger = new common_1.Logger(AdminGuard_1.name);
    constructor(reflector, authService) {
        this.reflector = reflector;
        this.authService = authService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        try {
            const authorizationHeader = request.headers['authorization'];
            if (!authorizationHeader || !authorizationHeader.startsWith('Bearer ')) {
                throw new common_1.UnauthorizedException('Authorization token is required');
            }
            const token = authorizationHeader.substring(7);
            const jwtPayload = await this.authService.verifyToken(token);
            const user = await this.authService.validateUser(jwtPayload);
            if (!user) {
                throw new common_1.ForbiddenException('User not found or invalid');
            }
            if (!user.isAdmin) {
                throw new common_1.ForbiddenException('Admin access required');
            }
            request.user = {
                airtableUserId: user.airtableUserId,
                email: user.email,
                mcNumber: user.mcNumber,
                role: user.role,
                isAdmin: user.isAdmin,
            };
            this.logger.log(`Admin access granted to user ${user.email} (${user.airtableUserId})`);
            return true;
        }
        catch (error) {
            if (error instanceof common_1.ForbiddenException || error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            this.logger.error(`Authorization failed: ${error.message}`);
            throw new common_1.ForbiddenException('Authorization failed');
        }
    }
};
exports.AdminGuard = AdminGuard;
exports.AdminGuard = AdminGuard = AdminGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        auth_service_1.AuthService])
], AdminGuard);
//# sourceMappingURL=admin.guard.js.map