"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SavedSearchDto = exports.AdvancedFiltersDto = exports.SortOrder = exports.SortField = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
var SortField;
(function (SortField) {
    SortField["PICKUP_DATE"] = "pickupDate";
    SortField["DELIVERY_DATE"] = "deliveryDate";
    SortField["RATE"] = "rate";
    SortField["DISTANCE"] = "distance";
    SortField["CREATED_AT"] = "createdAt";
    SortField["WEIGHT"] = "weight";
    SortField["EQUIPMENT"] = "equipment";
})(SortField || (exports.SortField = SortField = {}));
var SortOrder;
(function (SortOrder) {
    SortOrder["ASC"] = "asc";
    SortOrder["DESC"] = "desc";
})(SortOrder || (exports.SortOrder = SortOrder = {}));
class AdvancedFiltersDto {
    origin;
    destination;
    equipment;
    equipmentTypes;
    minRate;
    maxRate;
    minWeight;
    maxWeight;
    pickupDateStart;
    pickupDateEnd;
    deliveryDateStart;
    deliveryDateEnd;
    geoCenter;
    geoRadius;
    geoFilterOrigin = true;
    search;
    sortBy;
    sortOrder = SortOrder.ASC;
    page = 1;
    pageSize = 25;
}
exports.AdvancedFiltersDto = AdvancedFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by origin city/state' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "origin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by destination city/state' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "destination", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by equipment type (R, V, F, etc.)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "equipment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Equipment types array for multiple selection' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AdvancedFiltersDto.prototype, "equipmentTypes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Minimum rate' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "minRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Maximum rate' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "maxRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Minimum weight in lbs' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "minWeight", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Maximum weight in lbs' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "maxWeight", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pickup date start (ISO 8601 format)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], AdvancedFiltersDto.prototype, "pickupDateStart", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pickup date end (ISO 8601 format)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], AdvancedFiltersDto.prototype, "pickupDateEnd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Delivery date start (ISO 8601 format)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], AdvancedFiltersDto.prototype, "deliveryDateStart", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Delivery date end (ISO 8601 format)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], AdvancedFiltersDto.prototype, "deliveryDateEnd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Geographic search center point (lat,lng format: "40.7128,-74.0060")' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "geoCenter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search radius in miles from geo center', minimum: 1, maximum: 3000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(3000),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "geoRadius", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Use geographic filtering for origin (true) or destination (false)', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdvancedFiltersDto.prototype, "geoFilterOrigin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Free text search across multiple fields' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: SortField, description: 'Field to sort by' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortField),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: SortOrder, description: 'Sort order', default: SortOrder.ASC }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], AdvancedFiltersDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Page number for pagination', minimum: 1, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Page size for pagination', minimum: 1, maximum: 100, default: 25 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AdvancedFiltersDto.prototype, "pageSize", void 0);
class SavedSearchDto {
    name;
    criteria;
    isDefault = false;
}
exports.SavedSearchDto = SavedSearchDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name for the saved search' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SavedSearchDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search criteria as JSON' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", AdvancedFiltersDto)
], SavedSearchDto.prototype, "criteria", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Whether this search should be set as default' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], SavedSearchDto.prototype, "isDefault", void 0);
//# sourceMappingURL=advanced-filters.dto.js.map