{"version": 3, "file": "performance.middleware.js", "sourceRoot": "", "sources": ["../../src/middleware/performance.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoE;AAI7D,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IACf,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAEjE,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC;QAGpC,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACxE,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;YAG3B,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,iBAAiB,MAAM,IAAI,WAAW,MAAM,UAAU,MAAM,QAAQ,IAAI,CACzE,CAAC;YACJ,CAAC;iBAEI,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,mBAAmB,MAAM,IAAI,WAAW,MAAM,UAAU,MAAM,QAAQ,IAAI,CAC3E,CAAC;YACJ,CAAC;iBAEI,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,MAAM,IAAI,WAAW,MAAM,UAAU,MAAM,QAAQ,IAAI,CAC3D,CAAC;YACJ,CAAC;YAGD,IAAI,WAAW,CAAC,QAAQ,CAAC,4BAA4B,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,QAAQ,IAAI,CAAC,CAAC;YAC1D,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,2BAA2B,CAAC,EAAE,CAAC;gBAC7D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,QAAQ,IAAI,CAAC,CAAC;YAC/D,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,QAAQ,IAAI,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AA/CY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;GACA,qBAAqB,CA+CjC"}