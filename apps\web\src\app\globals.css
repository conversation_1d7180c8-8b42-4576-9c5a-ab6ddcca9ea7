@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {

  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  /* FCP Brand Colors - Light Theme - FIXED CONTRAST */
  --primary: 222.2 47.4% 11.2%; /* Dark blue for primary */
  --primary-foreground: 210 40% 98%; /* White for good contrast */

  --secondary: 210 40% 96%; 
  --secondary-foreground: 222.2 84% 4.9%; /* Dark text on light background */

  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 35%; /* DARKENED for better visibility */

  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;

  --radius: 0.5rem;

  /* Sidebar Animation Variables */
  --sidebar-width-expanded: 16rem; /* 64 * 0.25rem = 16rem */
  --sidebar-width-collapsed: 4rem; /* 16 * 0.25rem = 4rem */
  --sidebar-transition-duration: 300ms;
  --sidebar-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);

  /* Shadcn Default Theme Variables (using oklch as per their docs for these) */
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --sidebar: 222.2 84% 4.9%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-primary: 222.2 84% 4.9%;
  --sidebar-primary-foreground: 222.2 84% 4.9%;
  --sidebar-accent: 210 40% 96%;
  --sidebar-accent-foreground: 222.2 84% 4.9%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 222.2 84% 4.9%;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%; /* Dark text on light background - GOOD CONTRAST */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;

  --card: 217.2 32.6% 17.5%; 
  --card-foreground: 210 40% 98%;

  --popover: 217.2 32.6% 17.5%;
  --popover-foreground: 210 40% 98%;

  /* FCP Brand Colors - Dark Theme */
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;

  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215.4 16.3% 56.9%; 

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;

  /* Shadcn Default Theme Variables (dark) */
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 27 87% 67%;
  --chart-4: 43 74% 66%;
  --chart-5: 12 76% 61%;
  --sidebar: 222.2 84% 4.9%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 12 76% 61%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 215.4 16.3% 46.9%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 222.2 84% 4.9%;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* font-family: var(--font-geist-sans); Ensure this is uncommented if you want to use Geist Sans */
    /* Ensure minimum contrast for accessibility */
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
  }
  
  /* ENHANCED DARK MODE TEXT VISIBILITY */
  
  /* Ensure all text elements inherit proper theme colors */
  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground)) !important;
  }
  
  p, span, div {
    color: inherit;
  }
  
  /* Enhanced form element visibility */
  input, textarea, select, [data-slot="input"], [data-slot="textarea"] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  input:focus, textarea:focus, select:focus, [data-slot="input"]:focus, [data-slot="textarea"]:focus {
    border-color: hsl(var(--ring)) !important;
    box-shadow: 0 0 0 3px hsl(var(--ring) / 0.2) !important;
  }
  
  /* Label visibility */
  label, [data-slot="label"] {
    color: hsl(var(--foreground)) !important;
  }
  
  /* Placeholder text visibility */
  input::placeholder, textarea::placeholder {
    color: hsl(var(--muted-foreground)) !important;
  }
  
  /* Select component visibility */
  [data-radix-select-trigger], [role="combobox"] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  [data-radix-select-content], [data-radix-popper-content-wrapper] {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  [data-radix-select-item] {
    color: hsl(var(--popover-foreground)) !important;
  }
  
  [data-radix-select-item]:hover,
  [data-radix-select-item][data-highlighted] {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
  }
  
  /* Card content visibility */
  .text-card-foreground {
    color: hsl(var(--card-foreground)) !important;
  }
  
  .text-muted-foreground {
    color: hsl(var(--muted-foreground)) !important;
  }
  
  /* Enhanced table visibility for dark mode */
  table th {
    background-color: hsl(var(--muted) / 0.5) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  table td {
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  /* Fix button text visibility */
  .btn, button {
    color: inherit !important;
  }
  
  /* Enhanced badge visibility */
  .badge, [data-badge] {
    color: inherit !important;
  }
  
  /* Ensure proper contrast for all interactive elements */
  .dark input,
  .dark textarea,
  .dark select {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  .dark .text-white {
    color: hsl(var(--foreground)) !important;
  }
  
  .dark .text-black {
    color: hsl(var(--background)) !important;
  }
  
  /* Statistics card fixes for dark mode */
  .dark .bg-blue-100 {
    background-color: rgb(30, 58, 138, 0.2) !important;
  }
  
  .dark .bg-green-100 {
    background-color: rgb(20, 83, 45, 0.2) !important;
  }
  
  .dark .bg-orange-100 {
    background-color: rgb(154, 52, 18, 0.2) !important;
  }
  
  .dark .text-blue-600 {
    color: rgb(96, 165, 250) !important;
  }
  
  .dark .text-green-600 {
    color: rgb(74, 222, 128) !important;
  }
  
  .dark .text-orange-600 {
    color: rgb(251, 146, 60) !important;
  }
  
  /* Remove the old Tailwind directives if @import "tailwindcss"; is used */
  /* @tailwind base; */
  /* @tailwind components; */
  /* @tailwind utilities; */
}

/* Remove duplicated body styles if they exist outside @layer base */
/* 
body {
}
*/

/* Collapsible Sidebar Animations */
.sidebar-text {
  opacity: 1;
  transition: opacity 200ms ease-in-out;
}

.sidebar.collapsed .sidebar-text {
  opacity: 0;
  pointer-events: none;
}

/* Sidebar width transitions */
.sidebar {
  width: var(--sidebar-width-expanded);
  transition: width var(--sidebar-transition-duration) var(--sidebar-transition-easing);
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

/* Main content transitions */
.main-content {
  margin-left: var(--sidebar-width-expanded);
  transition: margin-left var(--sidebar-transition-duration) var(--sidebar-transition-easing);
}

.main-content.sidebar-collapsed {
  margin-left: var(--sidebar-width-collapsed);
}

/* Icon transitions for better visual feedback */
.sidebar-icon {
  transition: transform 150ms ease-in-out;
}

.sidebar-icon:hover {
  transform: scale(1.05);
}

/* Toggle button animations */
.sidebar-toggle {
  transition: all 200ms ease-in-out;
}

.sidebar-toggle:hover {
  background-color: var(--accent);
  transform: scale(1.05);
}

/* Tooltip positioning adjustments for collapsed sidebar */
.sidebar-tooltip {
  z-index: 50;
}

/* Enhanced UI Consistency Styles */
.ui-card-hover {
  @apply transition-all duration-200 hover:shadow-md hover:scale-[1.01];
}

.ui-button-primary {
  @apply bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md;
}

.ui-input-focus {
  @apply transition-all duration-200 focus:scale-[1.02] hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-primary/20;
}

.ui-text-gradient {
  @apply bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent;
}

.ui-section-spacing {
  @apply space-y-6;
}

.ui-card-section {
  @apply bg-card border border-border rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
}

/* Micro-animations and enhanced transitions */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

@keyframes bounce-in {
  0% { 
    transform: scale(0.95) translateY(10px); 
    opacity: 0;
  }
  50% { 
    transform: scale(1.02) translateY(-2px); 
    opacity: 0.8;
  }
  100% { 
    transform: scale(1) translateY(0); 
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  0% { 
    transform: translateY(-20px); 
    opacity: 0;
  }
  100% { 
    transform: translateY(0); 
    opacity: 1;
  }
}

@keyframes fade-in {
  0% { 
    opacity: 0;
    transform: translateY(5px);
  }
  100% { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% { 
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

/* Animation classes */
.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-bounce-in {
  animation: bounce-in 0.3s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Enhanced component styles */
.gradient-border {
  position: relative;
  background: linear-gradient(var(--background), var(--background)) padding-box,
              linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary))) border-box;
  border: 2px solid transparent;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Enhanced hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .hover-lift:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Progress bar enhancements */
.progress-bar {
  background: linear-gradient(90deg, 
    hsl(var(--primary)) 0%, 
    hsl(var(--secondary)) 50%, 
    hsl(var(--primary)) 100%);
  background-size: 200% 100%;
  animation: progress-flow 3s ease-in-out infinite;
}

@keyframes progress-flow {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

/* Table row hover enhancements */
.table-row-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-row-hover:hover {
  background-color: hsl(var(--muted) / 0.3);
  transform: scale(1.002);
}

/* Dark mode specific animations */
.dark .table-row-hover:hover {
  background-color: hsl(var(--muted) / 0.2);
  box-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
}

/* Card enhancements */
.modern-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
}

.modern-card:hover {
  border-color: hsl(var(--primary) / 0.2);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.dark .modern-card:hover {
  border-color: hsl(var(--primary) / 0.4);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.3);
}

/* Badge pulse animation */
.badge-pulse {
  animation: badge-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes badge-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--muted) / 0.5) 50%, 
    hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Focus enhancements */
.focus-ring:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}

/* Button enhancements */
.button-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
  background-size: 200% 200%;
  transition: all 0.3s ease;
}

.button-gradient:hover {
  background-position: right center;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .button-gradient:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Tab transitions */
.tab-content {
  animation: fade-in-up 0.3s ease-out;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ==========================================
   MODAL & DIALOG THEME FIXES
   ========================================== */

/* Ensure dialogs are always visible and properly styled */
[data-slot="dialog-content"] {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

[data-slot="dialog-overlay"] {
  background-color: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  z-index: 100 !important;
}

.dark [data-slot="dialog-content"] {
  background-color: hsl(var(--card)) !important;
  border-color: hsl(var(--border)) !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5) !important;
}

/* Enhanced form visibility in modals */
[data-slot="dialog-content"] input,
[data-slot="dialog-content"] textarea {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--input)) !important;
}

.dark [data-slot="dialog-content"] input,
.dark [data-slot="dialog-content"] textarea {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

/* ==========================================
   CARD & COMPONENT THEME FIXES
   ========================================== */

/* Ensure all cards have proper theme colors */
.ui-card-section {
  background: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Fix button contrast in all themes */
.ui-button-primary {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.ui-button-primary:hover {
  background: hsl(var(--primary) / 0.9) !important;
}

/* Ensure label visibility in all themes */
[data-slot="label"] {
  color: hsl(var(--foreground)) !important;
}

/* Fix navigation active states */
.nav-item-active {
  background-color: hsl(var(--primary) / 0.1) !important;
  color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary) / 0.2) !important;
}

/* Improve form element visibility */
input[data-slot="input"],
textarea[data-slot="textarea"] {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

input[data-slot="input"]:focus,
textarea[data-slot="textarea"]:focus {
  border-color: hsl(var(--ring)) !important;
  outline: none !important;
  box-shadow: 0 0 0 3px hsl(var(--ring) / 0.2) !important;
}

/* Enhanced dark mode form styling */
.dark input[data-slot="input"],
.dark textarea[data-slot="textarea"] {
  background-color: hsl(var(--background)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Improved hover states for cards */
.ui-card-section:hover,
[data-slot="card"]:hover {
  border-color: hsl(var(--border)) !important;
  box-shadow: 0 4px 12px -2px hsl(var(--foreground) / 0.1) !important;
}

.dark .ui-card-section:hover,
.dark [data-slot="card"]:hover {
  box-shadow: 0 4px 12px -2px hsl(var(--foreground) / 0.2) !important;
}

/* Fix any remaining theme inconsistencies */
* {
  border-color: hsl(var(--border));
}

/* Ensure proper text contrast everywhere */
p, span, div, h1, h2, h3, h4, h5, h6 {
  color: inherit;
}

/* Mobile responsive dialog improvements */
@media (max-width: 640px) {
  [data-slot="dialog-content"] {
    width: calc(100vw - 2rem) !important;
    max-width: calc(100vw - 2rem) !important;
    margin: 1rem !important;
  }
}

/* Custom scrollbar styles for loadboard table */
@layer utilities {
  .loadboard-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.4) transparent;
    scroll-behavior: smooth;
    will-change: scroll-position;
    
    /* Force stable scroll bar */
    scrollbar-gutter: stable;
    
    /* Ensure scroll bar is always interactive */
    -webkit-overflow-scrolling: touch;
  }
  
  /* Webkit browsers - stable scroll bar implementation */
  .loadboard-scroll::-webkit-scrollbar {
    height: 12px;
    background-color: hsl(var(--muted) / 0.1);
    border-radius: 6px;
  }
  
  .loadboard-scroll::-webkit-scrollbar-track {
    background-color: hsl(var(--muted) / 0.1);
    border-radius: 6px;
    margin: 2px;
  }
  
  .loadboard-scroll::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.4);
    border-radius: 6px;
    border: 2px solid hsl(var(--muted) / 0.1);
    background-clip: content-box;
    min-width: 30px; /* Ensure draggable area */
    
    /* Remove transitions that cause flashing */
    transition: none;
  }
  
  .loadboard-scroll::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.6);
  }
  
  .loadboard-scroll::-webkit-scrollbar-thumb:active {
    background-color: hsl(var(--muted-foreground) / 0.8);
  }
  
  /* Additional table styling for better layout */
  .loadboard-scroll table {
    table-layout: fixed;
    width: 100%;
  }
  
  .loadboard-scroll th,
  .loadboard-scroll td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* Prevent layout shifts during loading */
  .loadboard-scroll th {
    position: sticky;
    top: 0;
    background-color: hsl(var(--muted) / 0.8);
    backdrop-filter: blur(8px);
    z-index: 10;
  }
  
  /* Enhanced Table Row Styling for Professional Appearance */
  .loadboard-scroll tbody tr {
    background-color: hsl(var(--card));
    border-bottom: 1px solid hsl(var(--border));
    transition: all 0.15s ease-in-out;
  }
  
  .loadboard-scroll tbody tr:hover {
    background-color: hsl(var(--muted) / 0.3);
    transform: translateX(2px);
    box-shadow: 0 2px 4px hsl(var(--foreground) / 0.05);
  }
  
  .loadboard-scroll tbody tr:nth-child(even) {
    background-color: hsl(var(--muted) / 0.1);
  }
  
  .loadboard-scroll tbody tr:nth-child(even):hover {
    background-color: hsl(var(--muted) / 0.4);
  }
  
  /* Enhanced Cell Styling */
  .loadboard-scroll td {
    background-color: inherit;
    vertical-align: middle;
  }
  
  /* Targeted Load Row Enhancement */
  .loadboard-scroll tbody tr.targeted-load {
    background: linear-gradient(to right, 
      hsl(var(--blue-50)), 
      hsl(var(--card))
    );
    border-left: 4px solid hsl(var(--blue-500));
    box-shadow: 0 1px 3px hsl(var(--blue-200) / 0.3);
  }
  
  .loadboard-scroll tbody tr.targeted-load:hover {
    background: linear-gradient(to right, 
      hsl(var(--blue-100)), 
      hsl(var(--muted) / 0.2)
    );
    box-shadow: 0 4px 6px hsl(var(--blue-200) / 0.4);
  }
  
  /* Dark mode table styling */
  .dark .loadboard-scroll tbody tr {
    background-color: hsl(var(--card));
    border-bottom-color: hsl(var(--border));
  }
  
  .dark .loadboard-scroll tbody tr:hover {
    background-color: hsl(var(--muted) / 0.2);
  }
  
  .dark .loadboard-scroll tbody tr:nth-child(even) {
    background-color: hsl(var(--muted) / 0.05);
  }
  
  .dark .loadboard-scroll tbody tr:nth-child(even):hover {
    background-color: hsl(var(--muted) / 0.3);
  }
  
  /* Dark mode targeted loads */
  .dark .loadboard-scroll tbody tr.targeted-load {
    background: linear-gradient(to right, 
      hsl(var(--blue-950) / 0.5), 
      hsl(var(--card))
    );
    border-left-color: hsl(var(--blue-400));
  }
  
  .dark .loadboard-scroll tbody tr.targeted-load:hover {
    background: linear-gradient(to right, 
      hsl(var(--blue-900) / 0.6), 
      hsl(var(--muted) / 0.2)
    );
  }
  
  /* Dark mode adjustments */
  .dark .loadboard-scroll {
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--background));
  }
  
  .dark .loadboard-scroll::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-color: hsl(var(--background));
  }
  
  .dark .loadboard-scroll::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.5);
  }
  
  /* Table layout stabilization */
  .loadboard-scroll table {
    table-layout: fixed;
    width: 100%;
    /* Prevent table from changing size during scroll */
    min-width: 900px;
  }
  
  /* Prevent horizontal scroll bar jumping by ensuring consistent container width */
  .loadboard-scroll {
    /* Use hardware acceleration for smooth scrolling */
    will-change: scroll-position;
    /* Ensure container doesn't change size */
    box-sizing: border-box;
    /* Stable container dimensions */
    position: relative;
    /* Prevent scrollbar from affecting layout */
    scrollbar-gutter: stable;
  }
  
  /* Optimize scroll performance and prevent flicker */
  .loadboard-scroll * {
    /* Prevent layout changes during scroll */
    contain: layout style;
  }
  
  /* Performance optimization for smooth scrolling */
  @media (hover: hover) {
    .loadboard-scroll::-webkit-scrollbar-thumb {
      transition: background-color 0.15s ease-in-out;
    }
  }
}

/* Full-Width Loadboard Table Optimization */
.loadboard-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
}

/* Remove horizontal scrolling - table must fit in viewport */
.loadboard-scroll {
  width: 100%;
  overflow-x: visible; /* Changed from auto to prevent horizontal scroll */
  max-width: none; /* Remove any width constraints */
}

/* Optimized Column Widths for Full-Width No-Scroll Experience */
.loadboard-table th:nth-child(1),  /* Pro Number */
.loadboard-table td:nth-child(1) {
  width: 100px;
  min-width: 100px;
}

.loadboard-table th:nth-child(2),  /* Origin */
.loadboard-table td:nth-child(2) {
  width: 150px;
  min-width: 150px;
}

.loadboard-table th:nth-child(3),  /* Destination */
.loadboard-table td:nth-child(3) {
  width: 150px;
  min-width: 150px;
}

.loadboard-table th:nth-child(4),  /* Miles */
.loadboard-table td:nth-child(4) {
  width: 80px;
  min-width: 80px;
}



.loadboard-table th:nth-child(5),  /* Pickup */
.loadboard-table td:nth-child(5) {
  width: 170px;
  min-width: 170px;
}

.loadboard-table th:nth-child(6),  /* Delivery */
.loadboard-table td:nth-child(6) {
  width: 170px;
  min-width: 170px;
}

.loadboard-table th:nth-child(7),  /* Equipment */
.loadboard-table td:nth-child(7) {
  width: 120px; /* Increased to prevent "Equipm" truncation */
  min-width: 120px;
}

.loadboard-table th:nth-child(8),  /* Temperature */
.loadboard-table td:nth-child(8) {
  width: 90px;
  min-width: 90px;
}

.loadboard-table th:nth-child(9), /* Weight */
.loadboard-table td:nth-child(9) {
  width: 100px;
  min-width: 100px;
}

.loadboard-table th:nth-child(10), /* Commodity */
.loadboard-table td:nth-child(10) {
  width: 120px;
  min-width: 120px;
}

.loadboard-table th:nth-child(11), /* Status */
.loadboard-table td:nth-child(11) {
  width: 130px;
  min-width: 130px;
}

.loadboard-table th:nth-child(12), /* Rate */
.loadboard-table td:nth-child(12) {
  width: 100px;
  min-width: 100px;
}

.loadboard-table th:nth-child(13), /* Actions */
.loadboard-table td:nth-child(13) {
  width: 220px; /* Increased for better action button spacing */
  min-width: 220px;
}

/* Enhanced Cell Content Handling */
.loadboard-table th,
.loadboard-table td {
  padding: 12px 8px; /* Better vertical padding for full-width table */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  border-bottom: 1px solid hsl(var(--border));
  font-size: 0.875rem; /* Optimal font size for readability */
}

/* Header-specific styling for better visibility */
.loadboard-table th {
  font-weight: 600;
  background-color: hsl(var(--muted) / 0.5);
  border-bottom: 2px solid hsl(var(--border));
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Enhanced hover effects for better UX */
.loadboard-table tbody tr:hover {
  background-color: hsl(var(--muted) / 0.2);
}

/* Ensure table takes full container width */
.loadboard-table {
  width: 100% !important;
  min-width: 1750px; /* Minimum width for all columns to display properly */
  table-layout: fixed;
}

/* Responsive Column Adjustments for Sidebar States */
@media (min-width: 1440px) {
  /* When sidebar is collapsed, expand key columns */
  body:not(.sidebar-expanded) .loadboard-table th:nth-child(2),
  body:not(.sidebar-expanded) .loadboard-table td:nth-child(2),
  body:not(.sidebar-expanded) .loadboard-table th:nth-child(3),
  body:not(.sidebar-expanded) .loadboard-table td:nth-child(3) {
    width: 160px;
    min-width: 160px;
  }
  
  body:not(.sidebar-expanded) .loadboard-table th:nth-child(5),
  body:not(.sidebar-expanded) .loadboard-table td:nth-child(5),
  body:not(.sidebar-expanded) .loadboard-table th:nth-child(6),
  body:not(.sidebar-expanded) .loadboard-table td:nth-child(6) {
    width: 180px;
    min-width: 180px;
  }
  
  body:not(.sidebar-expanded) .loadboard-table th:nth-child(10),
  body:not(.sidebar-expanded) .loadboard-table td:nth-child(10) {
    width: 140px;
    min-width: 140px;
  }
  
  body:not(.sidebar-expanded) .loadboard-table th:nth-child(13),
  body:not(.sidebar-expanded) .loadboard-table td:nth-child(13) {
    width: 200px;
    min-width: 200px;
  }
}

/* Compact Content Display Helpers */
.loadboard-compact-date {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.loadboard-compact-date .date {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  font-weight: 500;
}

.loadboard-compact-date .time {
  font-size: 0.875rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.loadboard-location {
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.loadboard-rate {
  font-family: monospace;
  font-weight: 600;
  color: hsl(var(--foreground));
}

/* Enhanced Actions Column */
.loadboard-actions {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  justify-content: flex-start;
}

.loadboard-actions button {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  height: auto;
  min-height: 28px;
}

/* Professional Table Styling Maintained */

/* ==========================================
   TOP NAVIGATION STYLES
   ========================================== */

/* Top Navigation Backdrop Styling */
.top-nav-backdrop {
  backdrop-filter: blur(16px) saturate(200%);
  -webkit-backdrop-filter: blur(16px) saturate(200%);
  background-color: hsl(var(--background) / 0.98);
  border-bottom: 1px solid hsl(var(--border) / 0.8);
  box-shadow: 0 1px 6px hsl(var(--foreground) / 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.top-nav-backdrop.scrolled {
  backdrop-filter: blur(20px) saturate(250%);
  -webkit-backdrop-filter: blur(20px) saturate(250%);
  background-color: hsl(var(--background) / 0.95);
  border-bottom: 1px solid hsl(var(--border));
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.15);
}

/* Navigation item hover animations */
.nav-item-hover {
  position: relative;
  overflow: hidden;
}

.nav-item-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-item-hover:hover::before {
  left: 100%;
}

/* Active navigation item indicator */
.nav-item-active {
  position: relative;
}

.nav-item-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: hsl(var(--primary));
  border-radius: 1px;
}

/* Mobile menu animations */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 200ms ease-out, transform 200ms ease-out;
}

.mobile-menu-exit {
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Smooth page transitions for top navigation */
.page-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states for accessibility */
.nav-item:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 8px;
}

/* Top navigation brand logo styling */
.nav-brand-logo {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--primary) / 0.2));
  border: 1px solid hsl(var(--primary) / 0.2);
  transition: all 0.2s ease;
}

.nav-brand-logo:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.15), hsl(var(--primary) / 0.25));
  transform: scale(1.05);
}

/* Smooth micro-interactions for navigation items */
.nav-item-micro {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item-micro:hover {
  transform: translateY(-1px);
}

.nav-item-micro:active {
  transform: translateY(0);
}

/* Top navigation layout optimizations */
.top-nav-layout {
  /* Remove any sidebar-related margins/padding */
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Top navigation table optimizations for maximum width */
.top-nav-layout .loadboard-table {
  min-width: 1600px; /* Ensure table uses full available width */
}

/* Enhanced column widths for top navigation layout */
.top-nav-layout .loadboard-table th:nth-child(1),
.top-nav-layout .loadboard-table td:nth-child(1) { width: 100px; min-width: 100px; }  /* Status */

.top-nav-layout .loadboard-table th:nth-child(2),
.top-nav-layout .loadboard-table td:nth-child(2) { width: 150px; min-width: 150px; }  /* Origin */

.top-nav-layout .loadboard-table th:nth-child(3),
.top-nav-layout .loadboard-table td:nth-child(3) { width: 150px; min-width: 150px; }  /* Destination */

.top-nav-layout .loadboard-table th:nth-child(4),
.top-nav-layout .loadboard-table td:nth-child(4) { width: 80px; min-width: 80px; }   /* Miles */

.top-nav-layout .loadboard-table th:nth-child(5),
.top-nav-layout .loadboard-table td:nth-child(5) { width: 180px; min-width: 180px; } /* Pickup */

.top-nav-layout .loadboard-table th:nth-child(6),
.top-nav-layout .loadboard-table td:nth-child(6) { width: 180px; min-width: 180px; } /* Delivery */

.top-nav-layout .loadboard-table th:nth-child(7),
.top-nav-layout .loadboard-table td:nth-child(7) { width: 100px; min-width: 100px; } /* Equipment */

.top-nav-layout .loadboard-table th:nth-child(8),
.top-nav-layout .loadboard-table td:nth-child(8) { width: 90px; min-width: 90px; }   /* Temperature */

.top-nav-layout .loadboard-table th:nth-child(9),
.top-nav-layout .loadboard-table td:nth-child(9) { width: 100px; min-width: 100px; }  /* Weight */

.top-nav-layout .loadboard-table th:nth-child(10),
.top-nav-layout .loadboard-table td:nth-child(10) { width: 120px; min-width: 120px; } /* Commodity */

.top-nav-layout .loadboard-table th:nth-child(11),
.top-nav-layout .loadboard-table td:nth-child(11) { width: 100px; min-width: 100px; } /* Status */

.top-nav-layout .loadboard-table th:nth-child(12),
.top-nav-layout .loadboard-table td:nth-child(12) { width: 110px; min-width: 110px; } /* Rate */

.top-nav-layout .loadboard-table th:nth-child(13),
.top-nav-layout .loadboard-table td:nth-child(13) { width: 220px; min-width: 220px; } /* Actions */

/* Dark mode enhancements for top navigation */
.dark .top-nav-backdrop {
  background-color: hsl(var(--background) / 0.95);
  border-bottom: 1px solid hsl(var(--border) / 0.6);
}

.dark .top-nav-backdrop.scrolled {
  background-color: hsl(var(--background) / 0.9);
  border-bottom: 1px solid hsl(var(--border) / 0.8);
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.1);
}

.dark .nav-brand-logo {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--primary) / 0.2));
  border: 1px solid hsl(var(--primary) / 0.2);
}

.dark .nav-brand-logo:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.15), hsl(var(--primary) / 0.25));
}

/* ==========================================
   LOADBOARD UI SPACING & LAYOUT ENHANCEMENTS
   ========================================== */

/* Enhanced Private Offer load styling with improved glow effect */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.95;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

/* Consistent row heights for loadboard */
.loadboard-table tbody tr {
  min-height: 80px;
}

.loadboard-table tbody td {
  padding: 0.75rem 0.5rem;
  vertical-align: middle;
}

/* Enhanced Private Offer glow effect */
.private-offer-glow {
  position: relative;
}

.private-offer-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 8px;
  z-index: -1;
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.4), 0 0 10px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.6), 0 0 20px rgba(59, 130, 246, 0.4);
  }
}

/* Compact status column with consistent heights */
.loadboard-table th:nth-child(12), /* Status column */
.loadboard-table td:nth-child(12) {
  min-height: 80px;
  display: table-cell;
  vertical-align: middle;
}

/* Badge flexbox improvements for consistent spacing */
.status-column-badges {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-height: 60px;
  justify-content: center;
  align-items: flex-start;
}

.status-column-badges .badge-item {
  flex-shrink: 0;
  white-space: nowrap;
}

/* Compact filter and stats sections */
.compact-stats-card {
  height: auto;
  min-height: 80px;
}

.compact-filter-section {
  padding: 1rem 1.5rem;
}

.compact-filter-input {
  height: 2rem;
  font-size: 0.875rem;
}

/* Enhanced visibility for Private Offer loads */
.loadboard-table tbody tr.private-offer-row {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.05) 0%, 
    rgba(59, 130, 246, 0.1) 50%, 
    rgba(59, 130, 246, 0.05) 100%);
  border-left: 4px solid #3b82f6;
  box-shadow: 
    0 0 10px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.loadboard-table tbody tr.private-offer-row:hover {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(59, 130, 246, 0.15) 50%, 
    rgba(59, 130, 246, 0.1) 100%);
  box-shadow: 
    0 0 15px rgba(59, 130, 246, 0.3),
    0 4px 8px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Dark mode enhancements for Private Offer loads */
.dark .loadboard-table tbody tr.private-offer-row {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(59, 130, 246, 0.15) 50%, 
    rgba(59, 130, 246, 0.1) 100%);
  border-left: 4px solid #60a5fa;
  box-shadow: 
    0 0 10px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dark .loadboard-table tbody tr.private-offer-row:hover {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.15) 0%, 
    rgba(59, 130, 246, 0.2) 50%, 
    rgba(59, 130, 246, 0.15) 100%);
  box-shadow: 
    0 0 15px rgba(59, 130, 246, 0.4),
    0 4px 8px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* ==========================================
   HELPER TEXT & TOOLTIP DARK MODE FIXES
   ========================================== */

/* Fix all tooltip content visibility */
[data-radix-tooltip-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.15) !important;
}

.dark [data-radix-tooltip-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border-color: hsl(var(--border)) !important;
  box-shadow: 0 4px 20px hsl(var(--background) / 0.3) !important;
}

/* Helper text components with colored backgrounds */
.help-tooltip-info {
  background-color: hsl(210 100% 95%) !important;
  color: hsl(210 100% 15%) !important;
  border-color: hsl(210 100% 80%) !important;
}

.dark .help-tooltip-info {
  background-color: hsl(210 100% 5%) !important;
  color: hsl(210 100% 90%) !important;
  border-color: hsl(210 100% 20%) !important;
}

.help-tooltip-warning {
  background-color: hsl(45 100% 95%) !important;
  color: hsl(45 100% 15%) !important;
  border-color: hsl(45 100% 80%) !important;
}

.dark .help-tooltip-warning {
  background-color: hsl(45 100% 5%) !important;
  color: hsl(45 100% 90%) !important;
  border-color: hsl(45 100% 25%) !important;
}

.help-tooltip-success {
  background-color: hsl(140 100% 95%) !important;
  color: hsl(140 100% 15%) !important;
  border-color: hsl(140 100% 80%) !important;
}

.dark .help-tooltip-success {
  background-color: hsl(140 100% 5%) !important;
  color: hsl(140 100% 90%) !important;
  border-color: hsl(140 100% 25%) !important;
}

/* Info boxes and alert components */
.info-box,
.alert-info {
  background-color: hsl(210 100% 95%) !important;
  color: hsl(210 100% 20%) !important;
  border-color: hsl(210 100% 80%) !important;
}

.dark .info-box,
.dark .alert-info {
  background-color: hsl(210 100% 5%) !important;
  color: hsl(210 100% 85%) !important;
  border-color: hsl(210 100% 25%) !important;
}

/* Profile completion and helper cards */
.profile-helper-card {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark .profile-helper-card {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Form help text visibility */
.form-help-text {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .form-help-text {
  color: hsl(var(--muted-foreground)) !important;
}

/* Badge and status indicators */
.status-badge {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
}

.dark .status-badge {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
}

/* Progress indicators */
.progress-description {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .progress-description {
  color: hsl(var(--muted-foreground)) !important;
}

/* Ensure all small text is readable */
.text-xs,
.text-sm {
  color: inherit !important;
}

/* Override any remaining hardcoded colors */
.bg-blue-50 {
  background-color: hsl(210 100% 95%) !important;
}

.dark .bg-blue-50 {
  background-color: hsl(210 100% 5%) !important;
}

.text-blue-800 {
  color: hsl(210 100% 20%) !important;
}

.dark .text-blue-800 {
  color: hsl(210 100% 85%) !important;
}

.text-blue-600 {
  color: hsl(210 100% 30%) !important;
}

.dark .text-blue-600 {
  color: hsl(210 100% 75%) !important;
}

.border-blue-200 {
  border-color: hsl(210 100% 80%) !important;
}

.dark .border-blue-200 {
  border-color: hsl(210 100% 25%) !important;
}

/* Orange theme helpers */
.text-orange-800 {
  color: hsl(25 100% 20%) !important;
}

.dark .text-orange-800 {
  color: hsl(25 100% 85%) !important;
}

.text-orange-600 {
  color: hsl(25 100% 30%) !important;
}

.dark .text-orange-600 {
  color: hsl(25 100% 75%) !important;
}

.border-orange-200 {
  border-color: hsl(25 100% 80%) !important;
}

.dark .border-orange-200 {
  border-color: hsl(25 100% 25%) !important;
}

/* Green theme helpers */
.text-green-800 {
  color: hsl(140 100% 20%) !important;
}

.dark .text-green-800 {
  color: hsl(140 100% 85%) !important;
}

.text-green-600 {
  color: hsl(140 100% 30%) !important;
}

.dark .text-green-600 {
  color: hsl(140 100% 75%) !important;
}
