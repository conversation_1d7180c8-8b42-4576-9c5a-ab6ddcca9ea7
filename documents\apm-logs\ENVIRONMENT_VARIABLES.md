# Environment Variables for File Upload Feature

This document lists the environment variables required for the file upload and Airtable sync feature.

## Required Environment Variables

### Airtable Configuration
```bash
# Airtable API Key - Get from https://airtable.com/account
AIRTABLE_API_KEY=your_airtable_api_key

# Airtable Base ID - Found in your Airtable base URL
AIRTABLE_BASE_ID=your_airtable_base_id
```

### Vercel Blob Storage
```bash
# Vercel Blob Storage Token - Automatically configured in Vercel deployments
# For local development, create a blob store in Vercel dashboard
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token
```

### Backend API Configuration
```bash
# Backend API URL - Already configured
NEXT_PUBLIC_API_BASE_URL=https://api.fcp-portal.com
```

## Setup Instructions

### 1. Airtable Setup
1. Go to https://airtable.com/account
2. Generate a new API key
3. Copy your base ID from the Airtable base URL (starts with `app`)
4. Add both values to your environment variables

### 2. Vercel Blob Setup
1. Go to your Vercel dashboard
2. Navigate to Storage → Blob
3. Create a new blob store if you don't have one
4. Copy the read/write token
5. Add to your environment variables

### 3. Local Development
Create a `.env.local` file in the `apps/web` directory:
```bash
AIRTABLE_API_KEY=your_airtable_api_key
AIRTABLE_BASE_ID=your_base_id
BLOB_READ_WRITE_TOKEN=your_blob_token
```

### 4. Production Deployment
Add the environment variables to your Vercel project settings:
1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable with the appropriate values

## Testing the Configuration

You can test if the environment variables are properly configured by:

1. **Airtable Connection**: The upload API will log warnings if Airtable config is missing
2. **Vercel Blob**: File uploads will fail if the blob token is invalid
3. **Backend API**: Load verification will fail if the API URL is incorrect

## Security Notes

- Never commit environment variables to version control
- Use different Airtable bases for development and production
- Rotate API keys regularly
- Ensure blob storage has appropriate access controls 