import { PrismaService } from '../../prisma/prisma.service';
interface LanePattern {
    laneId: string;
    originCity: string;
    originState: string;
    destinationCity: string;
    destinationState: string;
    averageRate: number;
    medianRate: number;
    rateRange: {
        min: number;
        max: number;
    };
    rateConfidence: number;
    mostCommonEquipment: string;
    equipmentDistribution: Record<string, number>;
    averageWeight: number;
    typicalWeightRange: {
        min: number;
        max: number;
    };
    averageTransitDays: number;
    seasonalFactors: Record<string, number>;
    orderCount: number;
    successRate: number;
    lastUpdated: Date;
}
interface UserPreference {
    userId: string;
    preferredEquipment: Record<string, number>;
    preferredRateAdjustment: number;
    preferredWeightRanges: Record<string, {
        min: number;
        max: number;
    }>;
    rateModificationFrequency: number;
    equipmentOverrideFrequency: number;
    dateAdjustmentPattern: number;
    orderCount: number;
    lastActivity: Date;
    confidenceScore: number;
}
interface SmartSuggestion {
    type: 'rate' | 'equipment' | 'weight' | 'timing' | 'warning';
    confidence: number;
    suggestion: any;
    reasoning: string;
    source: 'historical' | 'user_preference' | 'market_data' | 'business_rule';
}
export declare class PatternAnalysisService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    analyzeLanePatterns(originCity: string, originState: string, destinationCity: string, destinationState: string): Promise<LanePattern | null>;
    analyzeUserPreferences(userId: string): Promise<UserPreference | null>;
    generateSmartSuggestions(originCity: string, originState: string, destinationCity: string, destinationState: string, userId: string, currentValues?: any): Promise<SmartSuggestion[]>;
    private analyzeRates;
    private analyzeEquipment;
    private analyzeWeights;
    private analyzeTimings;
    private analyzeUserEquipmentPreferences;
    private analyzeUserRateAdjustments;
    private analyzeUserWeightPreferences;
    private calculateConsistencyScore;
    private calculateRateModificationFrequency;
    private calculateEquipmentOverrideFrequency;
    private calculateDateAdjustmentPattern;
    private generateValidationWarnings;
}
export {};
