'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  AlertCircle,
  User,
  Building,
  Phone,
  Mail,
  Truck,
  FileText,
  Shield,
  ArrowRight,
  X
} from 'lucide-react';
import Link from 'next/link';

interface ProfileField {
  id: string;
  label: string;
  description: string;
  icon: React.ComponentType<any>;
  isRequired: boolean;
  isCompleted: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface ProfileCompletionProps {
  profileData: any;
  onDismiss?: () => void;
  showCompact?: boolean;
}

export function ProfileCompletion({ profileData, onDismiss, showCompact = false }: ProfileCompletionProps) {
  const [isDismissed, setIsDismissed] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('fcp-profile-completion-dismissed') === 'true';
    }
    return false;
  });

  // Debug logging
  console.log('[PROFILE_COMPLETION] Received profileData:', profileData);

  const profileFields: ProfileField[] = [
    {
      id: 'companyName',
      label: 'Company Name',
      description: 'Legal business name as it appears on your MC authority',
      icon: Building,
      isRequired: true,
      isCompleted: !!(profileData?.companyName && profileData.companyName !== 'Not provided'),
      priority: 'high'
    },
    {
      id: 'dotNumber',
      label: 'DOT Number',
      description: 'Department of Transportation identification number',
      icon: Shield,
      isRequired: true,
      isCompleted: !!(profileData?.dotNumber && profileData.dotNumber !== 'Not provided'),
      priority: 'high'
    },
    {
      id: 'mcNumber',
      label: 'MC Number',
      description: 'Motor Carrier authority number from FMCSA',
      icon: Shield,
      isRequired: true,
      isCompleted: !!(profileData?.mcNumber && profileData.mcNumber !== 'Not provided'),
      priority: 'high'
    },
    {
      id: 'contactName',
      label: 'Contact Name',
      description: 'Primary contact person for load coordination',
      icon: User,
      isRequired: true,
      isCompleted: !!(
        (profileData?.contact_name && profileData.contact_name !== 'Not provided') ||
        (profileData?.firstName && profileData.firstName !== 'Not provided') ||
        (profileData?.lastName && profileData.lastName !== 'Not provided')
      ),
      priority: 'high'
    },
    {
      id: 'contactPhone',
      label: 'Contact Phone',
      description: 'Primary phone number for urgent communications',
      icon: Phone,
      isRequired: true,
      isCompleted: !!(
        (profileData?.contact_phone && profileData.contact_phone !== 'Not provided') ||
        (profileData?.phoneNumber && profileData.phoneNumber !== 'Not provided')
      ),
      priority: 'high'
    },
    {
      id: 'contactEmail',
      label: 'Contact Email',
      description: 'Email address for load notifications and documents',
      icon: Mail,
      isRequired: true,
      isCompleted: !!(
        (profileData?.contact_email && profileData.contact_email !== 'Not provided') ||
        (profileData?.email && profileData.email !== 'Not provided')
      ),
      priority: 'high'
    },
    {
      id: 'equipmentTypes',
      label: 'Equipment Types',
      description: 'Types of trailers and equipment you operate',
      icon: Truck,
      isRequired: true,
      isCompleted: !!(
        profileData?.equipmentTypes && 
        Array.isArray(profileData.equipmentTypes) && 
        profileData.equipmentTypes.length > 0 &&
        profileData.equipmentTypes[0] !== 'Not provided'
      ),
      priority: 'medium'
    }
  ];

  const completedFields = profileFields.filter(field => field.isCompleted);
  const requiredFields = profileFields.filter(field => field.isRequired);
  const incompleteRequiredFields = requiredFields.filter(field => !field.isCompleted);
  
  const completionPercentage = Math.round((completedFields.length / profileFields.length) * 100);
  const requiredCompletionPercentage = Math.round((requiredFields.filter(f => f.isCompleted).length / requiredFields.length) * 100);
  
  const isProfileComplete = incompleteRequiredFields.length === 0;

  // Debug logging for field completion status
  console.log('[PROFILE_COMPLETION] Field completion status:', 
    profileFields.map(field => ({
      id: field.id,
      label: field.label,
      value: profileData?.[field.id],
      isCompleted: field.isCompleted,
      isRequired: field.isRequired
    }))
  );
  
  console.log('[PROFILE_COMPLETION] Completion stats:', {
    completedFields: completedFields.length,
    totalFields: profileFields.length,
    requiredFields: requiredFields.length,
    incompleteRequiredFields: incompleteRequiredFields.length,
    completionPercentage,
    requiredCompletionPercentage,
    isProfileComplete
  });

  // Reset dismissed state if profile becomes incomplete after being complete
  useEffect(() => {
    if (!isProfileComplete && isDismissed) {
      // Only reset if profile was previously complete
      const wasComplete = localStorage.getItem('fcp-profile-was-complete') === 'true';
      if (wasComplete) {
        setIsDismissed(false);
        localStorage.removeItem('fcp-profile-completion-dismissed');
        localStorage.removeItem('fcp-profile-was-complete');
      }
    } else if (isProfileComplete) {
      // Mark as complete when all required fields are filled
      localStorage.setItem('fcp-profile-was-complete', 'true');
    }
  }, [isProfileComplete, isDismissed]);

  const handleDismiss = () => {
    setIsDismissed(true);
    localStorage.setItem('fcp-profile-completion-dismissed', 'true');
    onDismiss?.();
  };

  // Don't show if dismissed or profile is complete
  if (isDismissed || isProfileComplete) {
    return null;
  }

  // Compact version for dashboard
  if (showCompact) {
    return (
      <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
        <CardContent className="pt-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2 flex-1">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                <span className="font-medium text-sm text-red-900 dark:text-red-100">Complete Your Profile</span>
                <Badge variant="destructive" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                  {requiredCompletionPercentage}% Complete
                </Badge>
              </div>
              <p className="text-xs text-red-700 dark:text-red-300">
                {incompleteRequiredFields.length} required {incompleteRequiredFields.length === 1 ? 'field' : 'fields'} remaining
              </p>
              <Progress value={requiredCompletionPercentage} className="h-2" />
            </div>
            <div className="flex items-center space-x-2 ml-4">
              <Button asChild size="sm" variant="outline" className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900">
                <Link href="/settings">
                  Complete
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Link>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-8 w-8 p-0 text-red-600 hover:bg-red-100 dark:text-red-400 dark:hover:bg-red-900"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Full version for settings page
  return (
    <Card className="border-orange-200 dark:border-orange-800 bg-orange-50/50 dark:bg-orange-950/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="flex items-center text-lg">
              <AlertCircle className="h-5 w-5 mr-2 text-orange-600 dark:text-orange-400" />
              Complete Your Carrier Profile
            </CardTitle>
            <CardDescription>
              Complete all required fields to start bidding on loads
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Overview */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <Badge variant="secondary" className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200">
              {completionPercentage}% Complete
            </Badge>
          </div>
          <Progress value={completionPercentage} className="h-3" />
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{completedFields.length} of {profileFields.length} fields completed</span>
            <span>{incompleteRequiredFields.length} required fields remaining</span>
          </div>
        </div>

        {/* Required Fields */}
        {incompleteRequiredFields.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-orange-800 dark:text-orange-200">Required Fields</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {incompleteRequiredFields.map((field) => {
                const Icon = field.icon;
                return (
                  <div
                    key={field.id}
                    className="flex items-start space-x-3 p-3 bg-white dark:bg-slate-900 rounded-lg border border-orange-200 dark:border-orange-800"
                  >
                    <Icon className="h-4 w-4 mt-0.5 text-orange-600 dark:text-orange-400" />
                    <div className="flex-1 space-y-1">
                      <div className="font-medium text-sm">{field.label}</div>
                      <div className="text-xs text-muted-foreground">{field.description}</div>
                    </div>
                    <AlertCircle className="h-4 w-4 text-orange-500 dark:text-orange-400" />
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Completed Fields */}
        {completedFields.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-green-800 dark:text-green-200">Completed Fields</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {completedFields.map((field) => {
                const Icon = field.icon;
                return (
                  <div
                    key={field.id}
                    className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-950/30 rounded-lg border border-green-200 dark:border-green-800"
                  >
                                          <Icon className="h-4 w-4 mt-0.5 text-green-600 dark:text-green-400" />
                    <div className="flex-1 space-y-1">
                      <div className="font-medium text-sm">{field.label}</div>
                      <div className="text-xs text-muted-foreground">{field.description}</div>
                    </div>
                                          <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-orange-200 dark:border-orange-800">
          <div className="text-sm text-muted-foreground">
            Complete your profile to unlock all portal features
          </div>
          <div className="space-x-2">
            <Button variant="outline" onClick={handleDismiss}>
              Remind Me Later
            </Button>
            <Button asChild>
              <Link href="/settings">
                Complete Profile
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </div>
        </div>

        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">
            No carrier profile information found. Click &quot;Edit Profile&quot; to add details.
          </p>
          <div className="p-3 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Having trouble accessing your profile?</strong>
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
              If you&apos;re seeing authentication errors, try refreshing the page or signing out and back in.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Hook for profile completion logic
export function useProfileCompletion(profileData: any) {
  const [completionStatus, setCompletionStatus] = useState({
    isComplete: false,
    completionPercentage: 0,
    incompleteFields: [] as string[]
  });

  useEffect(() => {
    const requiredFields = [
      'companyName',
      'dotNumber', 
      'mcNumber',
      'contactName',
      'contactPhone',
      'contactEmail',
      'equipmentTypes'
    ];

    const completedFields = requiredFields.filter(field => {
      // Use the same logic as the ProfileField components above
      switch (field) {
        case 'contactName':
          return !!(
            (profileData?.contact_name && profileData.contact_name !== 'Not provided') ||
            (profileData?.firstName && profileData.firstName !== 'Not provided') ||
            (profileData?.lastName && profileData.lastName !== 'Not provided')
          );
        case 'contactPhone':
          return !!(
            (profileData?.contact_phone && profileData.contact_phone !== 'Not provided') ||
            (profileData?.phoneNumber && profileData.phoneNumber !== 'Not provided')
          );
        case 'contactEmail':
          return !!(
            (profileData?.contact_email && profileData.contact_email !== 'Not provided') ||
            (profileData?.email && profileData.email !== 'Not provided')
          );
        case 'equipmentTypes':
          return !!(
            profileData?.equipmentTypes && 
            Array.isArray(profileData.equipmentTypes) && 
            profileData.equipmentTypes.length > 0 &&
            profileData.equipmentTypes[0] !== 'Not provided'
          );
        default:
          const value = profileData?.[field];
          return value && value !== 'Not provided' && value !== '';
      }
    });

    const incompleteFields = requiredFields.filter(field => {
      const value = profileData?.[field];
      return !value || value === 'Not provided' || value === '';
    });

    setCompletionStatus({
      isComplete: incompleteFields.length === 0,
      completionPercentage: Math.round((completedFields.length / requiredFields.length) * 100),
      incompleteFields
    });
  }, [profileData]);

  return completionStatus;
} 