"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderDto = exports.PriorityLevel = exports.EquipmentType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var EquipmentType;
(function (EquipmentType) {
    EquipmentType["DRY_VAN"] = "Dry Van";
    EquipmentType["REEFER"] = "Reefer";
    EquipmentType["FLATBED"] = "Flatbed";
    EquipmentType["STEP_DECK"] = "Step Deck";
    EquipmentType["LOWBOY"] = "Lowboy";
    EquipmentType["TANKER"] = "Tanker";
    EquipmentType["BOX_TRUCK"] = "Box Truck";
    EquipmentType["STRAIGHT_TRUCK"] = "Straight Truck";
})(EquipmentType || (exports.EquipmentType = EquipmentType = {}));
var PriorityLevel;
(function (PriorityLevel) {
    PriorityLevel["NORMAL"] = "normal";
    PriorityLevel["HIGH"] = "high";
    PriorityLevel["URGENT"] = "urgent";
})(PriorityLevel || (exports.PriorityLevel = PriorityLevel = {}));
class CreateOrderDto {
    laneId;
    originCity;
    originState;
    destinationCity;
    destinationState;
    estimatedMiles;
    poNumber;
    soNumber;
    pickupDate;
    deliveryDate;
    equipmentRequired;
    weightLbs;
    rate;
    temperature;
    notes;
    priority;
}
exports.CreateOrderDto = CreateOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lane ID from the selected lane',
        example: 'lane_123_CA_TX'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "laneId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Origin city',
        example: 'Los Angeles'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "originCity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Origin state',
        example: 'CA'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(2),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "originState", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Destination city',
        example: 'Houston'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "destinationCity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Destination state',
        example: 'TX'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(2),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "destinationState", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Estimated miles for the route',
        example: 1547
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "estimatedMiles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Purchase Order number (must be unique)',
        example: 'PO-20240127-001',
        pattern: '^[A-Za-z0-9-_]+$'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(50),
    (0, class_validator_1.Matches)(/^[A-Za-z0-9-_]+$/, {
        message: 'PO Number can only contain letters, numbers, hyphens, and underscores'
    }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "poNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sales Order number (optional)',
        example: 'SO-2024-456',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "soNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pickup date in ISO format',
        example: '2024-01-28T00:00:00.000Z'
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "pickupDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Delivery date in ISO format (optional, can be auto-calculated)',
        example: '2024-01-30T00:00:00.000Z',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "deliveryDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Required equipment type',
        enum: EquipmentType,
        example: EquipmentType.DRY_VAN
    }),
    (0, class_validator_1.IsEnum)(EquipmentType),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "equipmentRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Weight in pounds',
        example: 45000,
        minimum: 1,
        maximum: 80000,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.Max)(80000),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "weightLbs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Rate in USD',
        example: 2500.00,
        minimum: 1,
        maximum: 50000,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.Max)(50000),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "rate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Temperature requirements (for reefer loads)',
        example: 'Fresh (32°F to 40°F)',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "temperature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional notes and special instructions',
        example: 'Handle with care, fragile cargo',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Priority level for the order',
        enum: PriorityLevel,
        example: PriorityLevel.NORMAL,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PriorityLevel),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "priority", void 0);
//# sourceMappingURL=create-order.dto.js.map