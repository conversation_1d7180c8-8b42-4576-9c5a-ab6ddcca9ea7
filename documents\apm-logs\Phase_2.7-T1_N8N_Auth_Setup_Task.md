# APM Task Assignment: N8N Authentication Workflow Setup

## 1. Agent Role & APM Context

**Introduction:** You are activated as an Implementation Agent within the Agentic Project Management (APM) framework for the **Carrier Portal Authentication Migration** project.

**Your Role:** You are **Agent_N8N_Specialist**, responsible for executing the N8N workflow configuration and deployment tasks with meticulous attention to detail and comprehensive logging of all activities.

**APM Workflow:** You interact with the Manager Agent via the User communication conduit. Upon completion, you must log your work comprehensively to the project's Memory Bank following the established format.

## 2. Task Assignment

**Reference Implementation Plan:** This assignment corresponds to **Phase 2.7, Task T1** in the Implementation Plan - "N8N Authentication Workflow Setup".

**Strategic Context:** This task is part of a critical architectural migration from Clerk authentication to Airtable + N8N JWT system, which will unify data management, eliminate org-based routing complexity, and create the foundation for a simplified bidding system implementation.

**Objective:** Deploy and configure the N8N authentication workflow using the provided template, establish Airtable integration, and validate all authentication endpoints for production readiness.

## 3. Detailed Action Steps

### **Step 1: N8N Workflow Template Import & Configuration**

**Actions:**
1. **Import Workflow Template**
   - Import the provided N8N workflow template JSON file (`User_Authentication_Service_Template.json`)
   - Verify all workflow nodes are properly connected and functional
   - Ensure workflow structure matches expected authentication flows

2. **Configure Airtable Integration**
   - Create Airtable Personal Access Token with required permissions:
     - Scope: `data.records:read`, `data.records:write`
     - Base Access: Carrier portal Airtable base
   - Configure N8N Airtable credential with the personal access token
   - Update workflow nodes to point to your specific Airtable base and table IDs

3. **JWT Authentication Setup**
   - Create JWT signing secret (strong, random 32+ character string)
   - Configure N8N JWT Auth credential with the signing secret
   - Verify JWT token generation and validation functionality

**Guidance Notes from Implementation Plan:**
- **Base Configuration**: Use existing Airtable base for load management, create new "UserManagement" table
- **Security Standards**: Implement proper JWT expiration (24-48 hours) and token refresh logic
- **Error Handling**: Ensure comprehensive error handling for all authentication scenarios

### **Step 2: Airtable User Management Schema Setup**

**Actions:**
1. **Create User Management Table Structure**
   - **Required Base Fields:**
     - Email (Single Line Text, Primary field)
     - First Name (Single Line Text)
     - Last Name (Single Line Text) 
     - Password (Single Line Text - for hashed passwords)
     - Date (Date - signup timestamp)
   
   - **Carrier-Specific Extension Fields:**
     - Company Name (Single Line Text)
     - Contact Phone (Phone Number)
     - Role (Single Select: Carrier, Admin, Super Admin)
     - Verification Status (Single Select: Pending, Verified, Rejected)
     - MC Number (Single Line Text)
     - DOT Number (Single Line Text)
     - Notes (Long Text - for admin notes)

2. **Configure Table Settings**
   - Set Email as primary field with unique constraint
   - Create views for different user types and verification statuses
   - Set up appropriate field permissions and validation rules

**Guidance Notes from Implementation Plan:**
- **Schema Consistency**: Ensure field names match exactly with N8N workflow expectations
- **Data Validation**: Implement proper field validation (email format, phone format)
- **Admin Interface**: Create views that facilitate user management for administrators

### **Step 3: Authentication Endpoint Configuration & Testing**

**Actions:**
1. **Configure Webhook Endpoints**
   - **POST /auth/signup** (webhook: 30ad296d-9b18-49e1-a443-9f2f20d468f9)
     - Configure CORS settings for frontend integration
     - Set up proper request validation
     - Test email uniqueness checking
     - Validate SHA256 password hashing with Base64 encoding
   
   - **POST /auth/login** (webhook: 190cad91-8dde-4baa-8c72-2d587b2f7f3f)
     - Configure CORS settings for frontend integration  
     - Test user lookup and password verification
     - Validate JWT token generation with correct payload structure
   
   - **GET /auth/user** (webhook: 190cad91-8dde-4baa-8c72-2d587b2f7f3f + JWT auth)
     - Configure JWT authentication middleware
     - Test authenticated user data retrieval using jwtPayload.id
   
   - **POST /auth/user** (webhook: 190cad91-8dde-4baa-8c72-2d587b2f7f3f + JWT auth)
     - Configure JWT authentication middleware
     - Test authenticated user profile updates

2. **Comprehensive Endpoint Testing**
   - Test all success scenarios for each endpoint
   - Test all error scenarios (invalid credentials, missing fields, etc.)
   - Validate response formats match expected API contract
   - Test JWT token validation and expiration handling
   - Document actual webhook URLs for frontend integration

3. **Security & Performance Validation**
   - Verify password hashing security (SHA256 + Base64)
   - Test JWT token structure and validation
   - Implement and test rate limiting for authentication endpoints
   - Validate CORS configuration for production domains

**Guidance Notes from Implementation Plan:**
- **Production URLs**: Document the actual webhook URLs generated by N8N for integration
- **Security Testing**: Verify no plaintext passwords are stored or transmitted
- **Integration Ready**: Ensure endpoints are immediately usable by frontend applications

### **Step 4: Production Deployment Preparation**

**Actions:**
1. **Environment Configuration**
   - Set up production N8N environment variables
   - Configure production Airtable credentials
   - Set up monitoring and logging for authentication workflows
   - Create backup and rollback procedures

2. **Documentation Creation**
   - Document all webhook endpoints with request/response examples
   - Create integration guide for frontend developers
   - Document Airtable schema and field requirements
   - Create troubleshooting guide for common authentication issues

3. **Load Testing & Performance Optimization**
   - Test authentication endpoints under load
   - Optimize N8N workflow performance
   - Validate Airtable API rate limit handling
   - Test concurrent user authentication scenarios

## 4. Expected Output & Deliverables

**Define Success:** Successful completion requires:
- ✅ Fully functional N8N authentication workflow deployed and tested
- ✅ Airtable UserManagement table created with proper schema
- ✅ All 4 authentication endpoints operational and validated
- ✅ Comprehensive testing completed for all success/error scenarios
- ✅ Production-ready configuration with proper security measures
- ✅ Integration documentation completed for development team

**Specific Deliverables:**
1. **Configured N8N Workflow**
   - Working authentication workflow in N8N
   - All credentials properly configured and secured
   - Webhook URLs documented for integration

2. **Airtable Schema**
   - UserManagement table with all required fields
   - Proper data validation and field configurations
   - Admin views for user management

3. **API Documentation**
   - Complete endpoint documentation with examples
   - Authentication flow diagrams
   - Error handling documentation
   - Integration guide for frontend team

4. **Test Results**
   - Comprehensive test results for all endpoints
   - Performance test results and optimizations
   - Security validation confirmation

## 5. Memory Bank Logging Instructions (Mandatory)

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Adhere strictly to the logging format defined in `cursor-agent-management/prompts/02_Utility_Prompts_And_Format_Definitions/Memory_Bank_Log_Format.md`. Ensure your log includes:

- **Task Reference:** Phase 2.7 / Task T1 (N8N Authentication Workflow Setup)
- **Clear Summary:** High-level overview of N8N workflow deployment and configuration
- **Detailed Actions:** Step-by-step summary of configuration, schema setup, and testing
- **Critical Outputs:** Webhook URLs, Airtable schema details, test results
- **Key Decisions:** Any configuration choices made during implementation
- **Issues & Resolutions:** Any challenges encountered and how they were resolved
- **Integration Notes:** Important details for subsequent Phase 2.7 tasks

**Logging Priority:** Focus on information critical for **Agent_API_Backend** (P2.7-T2) who will need to integrate these endpoints into the existing system.

## 6. Clarification Instruction

If any part of this task assignment is unclear, please state your specific questions before proceeding. This includes:
- Airtable base access or permission issues
- N8N environment configuration questions  
- Specific security requirements or compliance needs
- Integration requirements with existing system components

---

**Critical Success Factor:** This task establishes the authentication foundation for the entire Phase 2.7 migration. Thorough testing and documentation are essential for successful integration in subsequent tasks.

**Ready to Begin:** Confirm you understand the task scope and have access to the necessary N8N and Airtable environments before starting implementation. 