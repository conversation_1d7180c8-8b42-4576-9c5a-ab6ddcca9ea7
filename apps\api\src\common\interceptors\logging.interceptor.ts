import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { randomUUID } from 'crypto';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    // Generate or get correlation ID
    const correlationId = (request as any).correlationId || randomUUID();
    (request as any).correlationId = correlationId;
    
    // Add correlation ID to response headers for client-side tracking
    response.setHeader('X-Correlation-Id', correlationId);

    const { method, url, headers, body, query, params } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = (request as any).auth?.sub;
    const startTime = Date.now();

    // Log incoming request
    this.logger.log(
      `Incoming Request [${correlationId}]`,
      {
        correlationId,
        method,
        url,
        userAgent,
        userId,
        timestamp: new Date().toISOString(),
        ip: request.ip,
        query: this.sanitizeObject(query),
        params: this.sanitizeObject(params),
        bodySize: body ? JSON.stringify(body).length : 0,
        hasBody: !!body,
      }
    );

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Log successful response
        this.logger.log(
          `Outgoing Response [${correlationId}]`,
          {
            correlationId,
            method,
            url,
            statusCode: response.statusCode,
            duration: `${duration}ms`,
            userId,
            timestamp: new Date().toISOString(),
            responseSize: data ? JSON.stringify(data).length : 0,
            hasResponse: !!data,
          }
        );
      }),
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Log error response
        this.logger.error(
          `Request Error [${correlationId}]`,
          {
            correlationId,
            method,
            url,
            error: error.message,
            errorName: error.constructor.name,
            duration: `${duration}ms`,
            userId,
            timestamp: new Date().toISOString(),
            stack: error.stack,
          }
        );
        
        // Re-throw the error to be handled by the exception filter
        throw error;
      })
    );
  }

  private sanitizeObject(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;
    
    const sanitized = { ...obj };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'authorization', 'auth', 'secret', 'key'];
    
    for (const key in sanitized) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }
} 