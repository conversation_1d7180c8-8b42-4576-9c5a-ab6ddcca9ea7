TITLE: Configuring Airtable via Environment Variable (Shell)
DESCRIPTION: Set the Airtable API key using a shell environment variable. This is a common practice in cloud deployments and 12-factor applications for securely managing sensitive configuration like API keys.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_3

LANGUAGE: Shell
CODE:
```
export AIRTABLE_API_KEY=YOUR_SECRET_API_TOKEN
```

----------------------------------------

TITLE: Installing Airtable Library Node.js (Shell)
DESCRIPTION: Use this command in your project's terminal to install the official Airtable JavaScript library via npm. This adds 'airtable' as a dependency to your Node.js project, making it available for use.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install airtable
```

----------------------------------------

TITLE: Using Airtable Promise Pattern (JavaScript)
DESCRIPTION: Demonstrates the modern Promise-based approach for asynchronous operations. The `firstPage` method returns a Promise, allowing you to chain `.then()` to handle the result when it becomes available, providing a cleaner syntax than nested callbacks.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
table.select().firstPage().then(result => { ... })
```

----------------------------------------

TITLE: Configuring Airtable Globally (JavaScript)
DESCRIPTION: This JavaScript snippet demonstrates how to set global configuration options for the Airtable library, such as your secret API token. Using `Airtable.configure` sets these options for all subsequent Airtable instance creations unless specifically overridden per instance.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
Airtable.configure({ apiKey: 'YOUR_SECRET_API_TOKEN' })
```

----------------------------------------

TITLE: Configuring Airtable Per Connection (JavaScript)
DESCRIPTION: Create a new Airtable instance with specific configuration options that override any globally set values. This example shows how to set a custom endpoint URL for a particular connection, which can be useful for debugging with proxies.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
const airtable = new Airtable({endpointUrl: 'https://api-airtable-com-8hw7i1oz63iz.runscope.net/'})
```

----------------------------------------

TITLE: Using Airtable Callback Pattern (JavaScript)
DESCRIPTION: This example shows the traditional callback pattern for handling the result of an asynchronous operation like fetching the first page of records from a table. A function is passed as an argument to `firstPage` and executed upon completion.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
table.select().firstPage(result => { ... })
```

----------------------------------------

TITLE: Running Airtable Browser Demo Server (Shell)
DESCRIPTION: Navigate to the specified test directory and start a simple HTTP server using Python's built-in module. This is necessary to serve the `index.html` file locally, allowing you to run and test the Airtable.js library directly in a web browser.
SOURCE: https://github.com/airtable/airtable.js/blob/master/README.md#_snippet_1

LANGUAGE: Shell
CODE:
```
cd test/test_files
python -m SimpleHTTPServer
```