-- Manual migration to add additional Airtable fields
-- Run this on the production database when ready
-- CORRECTED VERSION - properly references public schema

-- Create the InvStatus enum first (check if it exists to avoid errors)
DO $$ BEGIN
    CREATE TYPE "InvStatus" AS ENUM ('Not Sent', 'Sent', 'Paid');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add new columns to the loads table (one by one with error handling)
DO $$ 
BEGIN
    -- Add po_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='po_number') THEN
        ALTER TABLE public.loads ADD COLUMN po_number TEXT;
        RAISE NOTICE 'Added po_number column';
    ELSE
        RAISE NOTICE 'po_number column already exists';
    END IF;
    
    -- Add so_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='so_number') THEN
        ALTER TABLE public.loads ADD COLUMN so_number TEXT;
        RAISE NOTICE 'Added so_number column';
    ELSE
        RAISE NOTICE 'so_number column already exists';
    END IF;
    
    -- Add pickup_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='pickup_number') THEN
        ALTER TABLE public.loads ADD COLUMN pickup_number TEXT;
        RAISE NOTICE 'Added pickup_number column';
    ELSE
        RAISE NOTICE 'pickup_number column already exists';
    END IF;
    
    -- Add delivery_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='delivery_number') THEN
        ALTER TABLE public.loads ADD COLUMN delivery_number TEXT;
        RAISE NOTICE 'Added delivery_number column';
    ELSE
        RAISE NOTICE 'delivery_number column already exists';
    END IF;
    
    -- Add shipper_address column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='shipper_address') THEN
        ALTER TABLE public.loads ADD COLUMN shipper_address TEXT;
        RAISE NOTICE 'Added shipper_address column';
    ELSE
        RAISE NOTICE 'shipper_address column already exists';
    END IF;
    
    -- Add receiver_name column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='receiver_name') THEN
        ALTER TABLE public.loads ADD COLUMN receiver_name TEXT;
        RAISE NOTICE 'Added receiver_name column';
    ELSE
        RAISE NOTICE 'receiver_name column already exists';
    END IF;
    
    -- Add receiver_address column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='receiver_address') THEN
        ALTER TABLE public.loads ADD COLUMN receiver_address TEXT;
        RAISE NOTICE 'Added receiver_address column';
    ELSE
        RAISE NOTICE 'receiver_address column already exists';
    END IF;
    
    -- Add inv_status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='inv_status') THEN
        ALTER TABLE public.loads ADD COLUMN inv_status "InvStatus";
        RAISE NOTICE 'Added inv_status column';
    ELSE
        RAISE NOTICE 'inv_status column already exists';
    END IF;
    
    -- Add carrier column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='carrier') THEN
        ALTER TABLE public.loads ADD COLUMN carrier TEXT;
        RAISE NOTICE 'Added carrier column';
    ELSE
        RAISE NOTICE 'carrier column already exists';
    END IF;
    
    -- Add cases column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='cases') THEN
        ALTER TABLE public.loads ADD COLUMN cases INTEGER;
        RAISE NOTICE 'Added cases column';
    ELSE
        RAISE NOTICE 'cases column already exists';
    END IF;
    
    -- Add pallets column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='loads' AND column_name='pallets') THEN
        ALTER TABLE public.loads ADD COLUMN pallets INTEGER;
        RAISE NOTICE 'Added pallets column';
    ELSE
        RAISE NOTICE 'pallets column already exists';
    END IF;
    
END $$;

-- Verify the columns were added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'loads' 
AND column_name IN ('po_number', 'so_number', 'pickup_number', 'delivery_number', 'shipper_address', 'receiver_name', 'receiver_address', 'inv_status', 'carrier', 'cases', 'pallets')
ORDER BY column_name; 