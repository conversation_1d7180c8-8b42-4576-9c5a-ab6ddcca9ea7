import { put } from '@vercel/blob';
import sharp from 'sharp';
import { NextRequest, NextResponse } from 'next/server';
import { syncLoadDocuments, validateAirtableConfig, type DocumentInfo } from '@/lib/airtable';

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.fcp-portal.com';
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILES = 5;
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];

interface UploadedFileResult {
  id?: string;
  name: string;
  url: string;
  size: number;
  type: string;
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ loadId: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    
    // Get N8N JWT token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const n8nToken = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!n8nToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Decode JWT token to get user info
    let userId;
    try {
      const tokenParts = n8nToken.split('.');
      if (tokenParts.length !== 3 || !tokenParts[1]) {
        throw new Error('Invalid token format');
      }
      const tokenPayload = tokenParts[1];
      if (!tokenPayload) {
        throw new Error('Invalid token format - missing payload');
      }
      const payload = JSON.parse(atob(tokenPayload));
      userId = payload.id;
    } catch (error) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Validate Airtable configuration
    if (!validateAirtableConfig()) {
      console.warn('Airtable configuration invalid - sync will be skipped');
    }

    // Parse multipart form data
    const formData = await request.formData();
    const files = formData.getAll('documents') as File[];

    if (files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 });
    }

    if (files.length > MAX_FILES) {
      return NextResponse.json({ error: `Maximum ${MAX_FILES} files allowed` }, { status: 400 });
    }

    // Validate files
    for (const file of files) {
      if (!ALLOWED_TYPES.includes(file.type)) {
        return NextResponse.json({ 
          error: `Unsupported file type: ${file.type}. Allowed types: ${ALLOWED_TYPES.join(', ')}` 
        }, { status: 400 });
      }

      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json({ 
          error: `File too large: ${file.name}. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB` 
        }, { status: 400 });
      }
    }

    // Verify load ownership via backend API
    const loadValidation = await verifyLoadOwnership(params.loadId, userId);
    if (!loadValidation.success) {
      return NextResponse.json({ error: loadValidation.error }, { status: loadValidation.status });
    }

    const uploadedFiles: UploadedFileResult[] = [];
    const documentInfos: DocumentInfo[] = [];

    // Process and upload each file
    for (const file of files) {
      try {
        let buffer = Buffer.from(await file.arrayBuffer());
        let processedSize = buffer.length;
        let finalContentType = file.type;
        
        // Optimize images with Sharp
        if (file.type.startsWith('image/')) {
          const optimized = await sharp(buffer)
            .resize(2048, 2048, { 
              fit: 'inside', 
              withoutEnlargement: true 
            })
            .jpeg({ quality: 85 })
            .toBuffer();
          
          buffer = optimized;
          processedSize = buffer.length;
          finalContentType = 'image/jpeg';
        }

        // Generate unique filename
        const timestamp = Date.now();
        const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        const filename = `${timestamp}-${sanitizedName}`;

        // Upload to Vercel Blob
        const blob = await put(
          `loads/${params.loadId}/${filename}`, 
          buffer, 
          {
            access: 'public',
            contentType: finalContentType,
          }
        );

        // Save document metadata via backend API
        const documentData = {
          loadId: params.loadId,
          filename: file.name,
          url: blob.url,
          type: finalContentType,
          size: processedSize,
        };

        const saveResult = await saveDocumentToDatabase(documentData, userId);
        
        const uploadedFile: UploadedFileResult = {
          id: saveResult.id,
          name: file.name,
          url: blob.url,
          size: processedSize,
          type: finalContentType,
        };

        uploadedFiles.push(uploadedFile);
        documentInfos.push({
          url: blob.url,
          name: file.name,
          type: finalContentType,
          size: processedSize,
        });

      } catch (error) {
        console.error(`Failed to process file ${file.name}:`, error);
        return NextResponse.json({ 
          error: `Failed to process file: ${file.name}` 
        }, { status: 500 });
      }
    }

    // Sync with Airtable (non-blocking)
    if (validateAirtableConfig() && loadValidation.load?.airtableRecordId) {
      try {
        await syncLoadDocuments(loadValidation.load.airtableRecordId, documentInfos);
      } catch (airtableError) {
        console.error('Airtable sync failed:', airtableError);
        // Continue - don't fail the upload if Airtable sync fails
      }
    }

    return NextResponse.json({ 
      success: true, 
      files: uploadedFiles,
      message: `${uploadedFiles.length} file(s) uploaded successfully`
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Verify that the user owns the load and can upload documents
 */
async function verifyLoadOwnership(loadId: string, userId: string): Promise<{
  success: boolean;
  error?: string;
  status?: number;
  load?: { airtableRecordId: string };
}> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/airtable-orders/${loadId}/verify-ownership`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': userId,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return { success: false, error: 'Load not found', status: 404 };
      } else if (response.status === 403) {
        return { success: false, error: 'Access denied - load not assigned to you', status: 403 };
      } else {
        return { success: false, error: 'Failed to verify load ownership', status: 500 };
      }
    }

    const loadData = await response.json();
    return { 
      success: true, 
      load: { airtableRecordId: loadData.airtableRecordId }
    };

  } catch (error) {
    console.error('Error verifying load ownership:', error);
    return { success: false, error: 'Internal server error', status: 500 };
  }
}

/**
 * Save document metadata to database via backend API
 */
async function saveDocumentToDatabase(
  documentData: {
    loadId: string;
    filename: string;
    url: string;
    type: string;
    size: number;
  },
  userId: string
): Promise<{ id: string }> {
  const response = await fetch(`${API_BASE_URL}/api/v1/airtable-orders/${documentData.loadId}/documents`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-User-ID': userId,
    },
    body: JSON.stringify(documentData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(`Failed to save document: ${errorData.error || response.statusText}`);
  }

  const result = await response.json();
  return { id: result.id };
} 