# Configuring projects with vercel.json

The `vercel.json` configuration file lets you configure, and override the default behavior of <PERSON>er<PERSON> from within your project. This includes settings for:

*   [schema autocomplete](#schema-autocomplete)
*   [buildCommand](#buildcommand)
*   [cleanUrls](#cleanurls)
*   [crons](#crons)
*   [devCommand](#devcommand)
*   [framework](#framework)
*   [functions](#functions)
*   [headers](#headers)
*   [ignoreCommand](#ignorecommand)
*   [images](#images)
*   [installCommand](#installcommand)
*   [outputDirectory](#outputdirectory)
*   [public](#public)
*   [redirects](#redirects)
*   [regions](#regions)
*   [functionFailoverRegion](#functionfailoverregions)
*   [rewrites](#rewrites)
*   [trailingSlash](#trailingslash)

To get started, create a `vercel.json` file in your project's root directory.

## [Schema autocomplete](#schema-autocomplete)

To add autocompletion, type checking, and schema validation to your `vercel.json` file, add the following to the top of your file:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json"
}
```

## [buildCommand](#buildcommand)

Type: `string | null`

The `buildCommand` property can be used to override the Build Command in the Project Settings dashboard, and the `build` script from the `package.json` file for a given deployment. For more information on the default behavior of the Build Command, visit the [Configure a Build - Build Command](/docs/deployments/configure-a-build#build-command) section.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "buildCommand": "next build"
}
```

This value overrides the [Build Command](/docs/deployments/configure-a-build#build-command) in Project Settings.

## [cleanUrls](#cleanurls)

Type: `Boolean`.

Default Value: `false`.

When set to `true`, all HTML files and Serverless Functions will have their extension removed. When visiting a path that ends with the extension, a 308 response will redirect the client to the extensionless path.

For example, a static file named `about.html` will be served when visiting the `/about` path. Visiting `/about.html` will redirect to `/about`.

Similarly, a Serverless Function named `api/user.go` will be served when visiting `/api/user`. Visiting `/api/user.go` will redirect to `/api/user`.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "cleanUrls": true
}
```

If you are using Next.js and running `vercel dev`, you will get a 404 error when visiting a route configured with `cleanUrls` locally. It does however work fine when deployed to Vercel. In the example above, visiting `/about` locally will give you a 404 with `vercel dev` but `/about` will render correctly on Vercel.

## [crons](#crons)

Used to configure [cron jobs](/docs/cron-jobs) for the production deployment of a project.

Type: `Array` of cron `Object`.

Limits:

*   A maximum of string length of 512 for the `path` value.
*   A maximum of string length of 256 for the `schedule` value.

### [Cron object definition](#cron-object-definition)

*   `path` - Required - The path to invoke when the cron job is triggered. Must start with `/`.
*   `schedule` - Required - The [cron schedule expression](/docs/cron-jobs#cron-expressions) to use for the cron job.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "crons": [
    {
      "path": "/api/every-minute",
      "schedule": "* * * * *"
    },
    {
      "path": "/api/every-hour",
      "schedule": "0 * * * *"
    },
    {
      "path": "/api/every-day",
      "schedule": "0 0 * * *"
    }
  ]
}
```

## [devCommand](#devcommand)

This value overrides the [Development Command](/docs/deployments/configure-a-build#development-command) in Project Settings.

Type: `string | null`

The `devCommand` property can be used to override the Development Command in the Project Settings dashboard. For more information on the default behavior of the Development Command, visit the [Configure a Build - Development Command](/docs/deployments/configure-a-build#development-command) section.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "devCommand": "next dev"
}
```

## [framework](#framework)

This value overrides the [Framework](/docs/deployments/configure-a-build#framework-preset) in Project Settings.

Type: `string | null`

Available framework slugs:

[nextjs](https://nextjs.org)[nuxtjs](https://nuxtjs.org)[svelte](https://svelte.dev)[create-react-app](https://create-react-app.dev)[gatsby](https://gatsbyjs.org)[remix](https://remix.run)[react-router](https://reactrouter.com)[solidstart](https://solidjs.com)[sveltekit](https://kit.svelte.dev)[blitzjs](https://blitzjs.com)[astro](https://astro.build)[hexo](https://hexo.io)[eleventy](https://www.11ty.dev)[docusaurus-2](https://v2.docusaurus.io)[docusaurus](https://docusaurus.io/)[preact](https://preactjs.com)[solidstart-1](https://start.solidjs.com)[dojo](https://dojo.io)[ember](https://emberjs.com/)[vue](https://vuejs.org)[scully](https://github.com/scullyio/scully)[ionic-angular](https://ionicframework.com)[angular](https://angular.io)[polymer](https://www.polymer-project.org/)[sveltekit-1](https://kit.svelte.dev)[ionic-react](https://ionicframework.com)[gridsome](https://gridsome.org/)[umijs](https://umijs.org)[sapper](https://sapper.svelte.dev)[saber](https://saber.egoist.dev)[stencil](https://stenciljs.com/)[redwoodjs](https://redwoodjs.com)[hugo](https://gohugo.io)[jekyll](https://jekyllrb.com/)[brunch](https://brunch.io/)[middleman](https://middlemanapp.com/)[zola](https://www.getzola.org)[hydrogen](https://hydrogen.shopify.dev)[vite](https://vitejs.dev)[vitepress](https://vitepress.vuejs.org/)[vuepress](https://vuepress.vuejs.org/)[parcel](https://parceljs.org)[fasthtml](https://fastht.ml)[sanity-v3](https://www.sanity.io)[sanity](https://www.sanity.io)[storybook](https://storybook.js.org)

The `framework` property can be used to override the Framework Preset in the Project Settings dashboard. The value must be a valid framework slug. For more information on the default behavior of the Framework Preset, visit the [Configure a Build - Framework Preset](/docs/deployments/configure-a-build#framework-preset) section.

To select "Other" as the Framework Preset, use `null`.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "framework": "nextjs"
}
```

## [functions](#functions)

Type: `Object` of key `String` and value `Object`.

### [Key definition](#key-definition)

A [glob](https://github.com/isaacs/node-glob#glob-primer) pattern that matches the paths of the Serverless Functions you would like to customize:

*   `api/*.js` (matches one level e.g. `api/hello.js` but not `api/hello/world.js`)
*   `api/**/*.ts` (matches all levels `api/hello.ts` and `api/hello/world.ts`)
*   `src/pages/**/*` (matches all functions from `src/pages`)
*   `api/test.js`

### [Value definition](#value-definition)

*   `runtime` (optional): The npm package name of a [Runtime](/docs/functions/serverless-functions/runtimes), including its version.
*   `memory` (optional): An integer defining the memory in MB for your Serverless Function (between `128` and `3009`).
*   `maxDuration` (optional): An integer defining how long your Serverless Function should be allowed to run on every request in seconds (between `1` and the maximum limit of your plan, as mentioned below).
*   `includeFiles` (optional): A [glob](https://github.com/isaacs/node-glob#glob-primer) pattern to match files that should be included in your Serverless Function. If you’re using a Community Runtime, the behavior might vary. Please consult its documentation for more details. (Not supported in Next.js, instead use [`outputFileTracingIncludes`](https://nextjs.org/docs/app/api-reference/next-config-js/output) in `next.config.js` )
*   `excludeFiles` (optional): A [glob](https://github.com/isaacs/node-glob#glob-primer) pattern to match files that should be excluded from your Serverless Function. If you’re using a Community Runtime, the behavior might vary. Please consult its documentation for more details. (Not supported in Next.js, instead use [`outputFileTracingIncludes`](https://nextjs.org/docs/app/api-reference/next-config-js/output) in `next.config.js` )

### [Description](#description)

By default, no configuration is needed to deploy Serverless Functions to Vercel.

For all [officially supported runtimes](/docs/functions/serverless-functions/runtimes), the only requirement is to create an `api` directory at the root of your project directory, placing your Serverless Functions inside.

The `functions` property cannot be used in combination with `builds`. Since the latter is a legacy configuration property, we recommend dropping it in favor of the new one.

Because [Incremental Static Regeneration (ISR)](/docs/incremental-static-regeneration) uses Serverless Functions, the same configurations apply. The ISR route can be defined using a glob pattern, and accepts the same properties as when using Serverless Functions.

When deployed, each Serverless Function receives the following properties:

*   Memory: 1024 MB (1 GB) - (Optional)
*   Maximum Duration: 10s default - 60s (Hobby), 15s default - 300s (Pro), or 15s default - 900s (Enterprise). This [can be configured](/docs/functions/serverless-functions/runtimes#maxduration) up to the respective plan limit) - (Optional)

To configure them, you can add the `functions` property.

#### [`functions` property with Serverless Functions](#functions-property-with-serverless-functions)

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "functions": {
    "api/test.js": {
      "memory": 3009,
      "maxDuration": 30
    },
    "api/*.js": {
      "memory": 3009,
      "maxDuration": 30
    }
  }
}
```

#### [`functions` property with ISR](#functions-property-with-isr)

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "functions": {
    "pages/blog/[hello].tsx": {
      "memory": 1024
    },
    "src/pages/isr/**/*": {
      "maxDuration": 10
    }
  }
}
```

### [Using unsupported runtimes](#using-unsupported-runtimes)

In order to use a runtime that is not [officially supported](/docs/functions/serverless-functions/runtimes), you can add a `runtime` property to the definition:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "functions": {
    "api/test.php": {
      "runtime": "vercel-php@0.5.2"
    }
  }
}
```

In the example above, the `api/test.php` Serverless Function does not use one of the [officially supported runtimes](/docs/functions/serverless-functions/runtimes). In turn, a `runtime` property was added in order to invoke the [vercel-php](https://www.npmjs.com/package/vercel-php) community runtime.

For more information on Runtimes, see the [Runtimes documentation](/docs/functions/serverless-functions/runtimes):

## [headers](#headers)

Type: `Array` of header `Object`.

Valid values: a list of header definitions.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "headers": [
    {
      "source": "/service-worker.js",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=0, must-revalidate"
        }
      ]
    },
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    },
    {
      "source": "/:path*",
      "has": [
        {
          "type": "query",
          "key": "authorized"
        }
      ],
      "headers": [
        {
          "key": "x-authorized",
          "value": "true"
        }
      ]
    }
  ]
}
```

This example configures custom response headers for static files, [Serverless Functions](/docs/functions/serverless-functions), and a wildcard that matches all routes.

### [Header object definition](#header-object-definition)

| Property | Description |
| --- | --- |
| `source` | A pattern that matches each incoming pathname (excluding querystring). |
| `headers` | A non-empty array of key/value pairs representing each response header. |
| `has` | An optional array of `has` objects with the `type`, `key` and `value` properties. Used for conditional path matching based on the presence of specified properties. |
| `missing` | An optional array of `missing` objects with the `type`, `key` and `value` properties. Used for conditional path matching based on the absence of specified properties. |

### [Header `has` or `missing` object definition](#header-has-or-missing-object-definition)

| Property | Type | Description |
| --- | --- | --- |
| `type` | `String` | Must be either `header`, `cookie`, `host`, or `query`. The `type` property only applies to request headers sent by clients, not response headers sent by your functions or backends. |
| `key` | `String` | The key from the selected type to match against. |
| `value` | `String` or not defined | The value to check for, if `undefined` any value will match. A regex like string can be used to capture a specific part of the value, e.g. if the value `first-(?<paramName>.*)` is used for `first-second` then `second` will be usable in the destination with `:paramName`. |

Learn more about [rewrites](/docs/headers) on Vercel and see [limitations](/docs/edge-network/caching#limits).

## [ignoreCommand](#ignorecommand)

This value overrides the [Ignored Build Step](/docs/project-configuration/git-settings#ignored-build-step) in Project Settings.

Type: `string | null`

This `ignoreCommand` property will override the Command for Ignoring the Build Step for a given deployment. When the command exits with code 1, the build will continue. When the command exits with 0, the build is ignored. For more information on the default behavior of the Ignore Command, visit the [Ignored Build Step](/docs/project-configuration/git-settings#ignored-build-step) section.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "ignoreCommand": "git diff --quiet HEAD^ HEAD ./"
}
```

## [installCommand](#installcommand)

This value overrides the [Install Command](/docs/deployments/configure-a-build#install-command) in Project Settings.

Type: `string | null`

The `installCommand` property can be used to override the Install Command in the Project Settings dashboard for a given deployment. This setting is useful for trying out a new package manager for the project. An empty string value will cause the Install Command to be skipped. For more information on the default behavior of the install command visit the [Configure a Build - Install Command](/docs/deployments/configure-a-build#install-command) section.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "installCommand": "npm install"
}
```

## [images](#images)

The `images` property defines the behavior of [Vercel's native Image Optimization API](/docs/image-optimization), which allows on-demand optimization of images at runtime.

Type: `Object`

### [Value definition](#value-definition)

*   `sizes` - Required - Array of allowed image widths. The Image Optimization API will return an error if the `w` parameter is not defined in this list.
*   `localPatterns` - Allow-list of local image paths which can be used with the Image Optimization API.
*   `remotePatterns` - Allow-list of external domains which can be used with the Image Optimization API.
*   `minimumCacheTTL` - Cache duration (in seconds) for the optimized images.
*   `qualities` - Array of allowed image qualities. The Image Optimization API will return an error if the `q` parameter is not defined in this list.
*   `formats` - Supported output image formats. Allowed values are either `"image/avif"` and/or `"image/webp"`.
*   `dangerouslyAllowSVG` - Allow SVG input image URLs. This is disabled by default for security purposes.
*   `contentSecurityPolicy` - Specifies the [Content Security Policy](https://developer.mozilla.org/docs/Web/HTTP/CSP) of the optimized images.
*   `contentDispositionType` - Specifies the value of the `"Content-Disposition"` response header. Allowed values are `"inline"` or `"attachment"`.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "images": {
    "sizes": [256, 640, 1080, 2048, 3840],
    "localPatterns": [{
      "pathname": "^/assets/.*$",
      "search": ""
    }]
    "remotePatterns": [
      {
        "protocol": "https",
        "hostname": "example.com",
        "port": "",
        "pathname": "^/account123/.*$",
        "search": "?v=1"
      }
    ],
    "minimumCacheTTL": 60,
    "qualities": [25, 50, 75],
    "formats": ["image/webp"],
    "dangerouslyAllowSVG": false,
    "contentSecurityPolicy": "script-src 'none'; frame-src 'none'; sandbox;",
    "contentDispositionType": "inline"
  }
}
```

## [outputDirectory](#outputdirectory)

This value overrides the [Output Directory](/docs/deployments/configure-a-build#output-directory) in Project Settings.

Type: `string | null`

The `outputDirectory` property can be used to override the Output Directory in the Project Settings dashboard for a given deployment.

In the following example, the deployment will look for the `build` directory rather than the default `public` or `.` root directory. For more information on the default behavior of the Output Directory see the [Configure a Build - Output Directory](/docs/deployments/configure-a-build#output-directory) section. The following example is a `vercel.json` file that overrides the `outputDirectory` to `build`:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "outputDirectory": "build"
}
```

## [public](#public)

Type: `Boolean`.

Default Value: `false`.

When set to `true`, both the [source view](/docs/deployments/build-features#source-view) and [logs view](/docs/deployments/build-features#logs-view) will be publicly accessible.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "public": true
}
```

## [redirects](#redirects)

Type: `Array` of redirect `Object`.

Valid values: a list of redirect definitions.

### [Redirects examples](#redirects-examples)

Some redirects and rewrites configurations can accidentally become gateways for semantic attacks. Learn how to check and protect your configurations with the [Enhancing Security for Redirects and Rewrites guide](/guides/enhancing-security-for-redirects-and-rewrites).

This example redirects requests to the path `/me` from your site's root to the `profile.html` file relative to your site's root with a [307 Temporary Redirect](https://developer.mozilla.org/docs/Web/HTTP/Status/307):

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    { "source": "/me", "destination": "/profile.html", "permanent": false }
  ]
}
```

This example redirects requests to the path `/me` from your site's root to the `profile.html` file relative to your site's root with a [308 Permanent Redirect](https://developer.mozilla.org/docs/Web/HTTP/Status/308):

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    { "source": "/me", "destination": "/profile.html", "permanent": true }
  ]
}
```

This example redirects requests to the path `/user` from your site's root to the api route `/api/user` relative to your site's root with a [301 Moved Permanently](https://developer.mozilla.org/docs/Web/HTTP/Status/301):

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    { "source": "/user", "destination": "/api/user", "statusCode": 301 }
  ]
}
```

This example redirects requests to the path `/view-source` from your site's root to the absolute path `https://github.com/vercel/vercel` of an external site with a redirect status of 308:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    {
      "source": "/view-source",
      "destination": "https://github.com/vercel/vercel"
    }
  ]
}
```

This example redirects requests to all the paths (including all sub-directories and pages) from your site's root to the absolute path `https://vercel.com/docs` of an external site with a redirect status of 308:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    {
      "source": "/(.*)",
      "destination": "https://vercel.com/docs"
    }
  ]
}
```

This example uses wildcard path matching to redirect requests to any path (including subdirectories) under `/blog/` from your site's root to a corresponding path under `/news/` relative to your site's root with a redirect status of 308:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    {
      "source": "/blog/:path*",
      "destination": "/news/:path*"
    }
  ]
}
```

This example uses regex path matching to redirect requests to any path under `/posts/` that only contain numerical digits from your site's root to a corresponding path under `/news/` relative to your site's root with a redirect status of 308:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    {
      "source": "/post/:path(\\d{1,})",
      "destination": "/news/:path*"
    }
  ]
}
```

This example redirects requests to any path from your site's root that does not start with `/uk/` and has `x-vercel-ip-country` header value of `GB` to a corresponding path under `/uk/` relative to your site's root with a redirect status of 307:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    {
      "source": "/:path((?!uk/).*)",
      "has": [
        {
          "type": "header",
          "key": "x-vercel-ip-country",
          "value": "GB"
        }
      ],
      "destination": "/uk/:path*",
      "permanent": false
    }
  ]
}
```

Using `has` does not yet work locally while using `vercel dev`, but does work when deployed.

### [Redirect object definition](#redirect-object-definition)

| Property | Description |
| --- | --- |
| `source` | A pattern that matches each incoming pathname (excluding querystring). |
| `destination` | A location destination defined as an absolute pathname or external URL. |
| `permanent` | An optional boolean to toggle between permanent and temporary redirect (default `true`). When `true`, the status code is [308](https://developer.mozilla.org/docs/Web/HTTP/Status/308). When `false` the status code is [307](https://developer.mozilla.org/docs/Web/HTTP/Status/307). |
| `statusCode` | An optional integer to define the status code of the redirect. Used when you need a value other than 307/308 from `permanent`, and therefore cannot be used with `permanent` boolean. |
| `has` | An optional array of `has` objects with the `type`, `key` and `value` properties. Used for conditional redirects based on the presence of specified properties. |
| `missing` | An optional array of `missing` objects with the `type`, `key` and `value` properties. Used for conditional redirects based on the absence of specified properties. |

### [Redirect `has` or `missing` object definition](#redirect-has-or-missing-object-definition)

| Property | Type | Description |
| --- | --- | --- |
| `type` | `String` | Must be either `header`, `cookie`, `host`, or `query`. |
| `key` | `String` | The key from the selected type to match against. |
| `value` | `String` or not defined | The value to check for, if `undefined` any value will match. A regex like string can be used to capture a specific part of the value. See example. |

Learn more about [redirects on Vercel](/docs/redirects) and see [limitations](/docs/redirects#limits).

## [regions](#regions)

This value overrides the [Serverless Function Region](/docs/functions/serverless-functions/regions) in Project Settings.

Type: `Array` of region identifier `String`.

Valid values: List of [regions](/docs/edge-network/regions), defaults to `iad1`.

You can define the regions where your [Serverless Functions](/docs/functions/serverless-functions) are executed. Users on Pro and Enterprise can deploy to multiple regions. Hobby plans can select any single region. To learn more, see [Configuring Regions](/docs/functions/configuring-functions/region#project-configuration).

Function responses [can be cached](/docs/edge-network/caching) in the requested regions. Selecting a Serverless Function region does not impact static files, which are deployed to every region by default.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "regions": ["sfo1"]
}
```

## [functionFailoverRegions](#functionfailoverregions)

Setting failover regions for Serverless Functions are available on [Enterprise plans](/docs/plans/enterprise)

Set this property to specify the [region](/docs/functions/serverless-functions/regions) to which a Serverless Function should fallback when the default region(s) are unavailable.

Type: `Array` of region identifier `String`.

Valid values: List of [regions](/docs/edge-network/regions).

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "functionFailoverRegions": ["iad1", "sfo1"]
}
```

These regions serve as a fallback to any regions specified in the [`regions` configuration](/docs/project-configuration#regions). The region Vercel selects to invoke your function depends on availability and ingress. For instance:

*   Vercel always attempts to invoke the function in the primary region. If you specify more than one primary region in the `regions` property, Vercel selects the region geographically closest to the request
*   If all primary regions are unavailable, Vercel automatically fails over to the regions specified in `functionFailoverRegions`, selecting the region geographically closest to the request
*   The order of the regions in `functionFailoverRegions` does not matter as Vercel automatically selects the region geographically closest to the request

To learn more about automatic failover for Serverless Functions, see [Automatic failover](/docs/functions/configuring-functions/region#serverless-functions-failover). Edge Functions will [automatically failover](/docs/functions/configuring-functions/region#edge-functions-failover) with no configuration required.

Region failover is supported with Secure Compute, see [Region Failover](/docs/secure-compute#region-failover) to learn more.

## [rewrites](#rewrites)

Type: `Array` of rewrite `Object`.

Valid values: a list of rewrite definitions.

### [Rewrites examples](#rewrites-examples)

Some redirects and rewrites configurations can accidentally become gateways for semantic attacks. Learn how to check and protect your configurations with the [Enhancing Security for Redirects and Rewrites guide](/guides/enhancing-security-for-redirects-and-rewrites).

This example rewrites requests to the path `/about` from your site's root to the `/about-our-company.html` file relative to your site's root:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [{ "source": "/about", "destination": "/about-our-company.html" }]
}
```

This example rewrites requests to the paths under `/resize` that with 2 paths levels (defined as variables `width` and `height` that can be used in the destination value) to the api route `/api/sharp` relative to your site's root:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [
    { "source": "/resize/:width/:height", "destination": "/api/sharp" }
  ]
}
```

This example uses wildcard path matching to rewrite requests to any path (including subdirectories) under `/proxy/` from your site's root to a corresponding path under the root of an external site `https://example.com/`:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [
    {
      "source": "/proxy/:match*",
      "destination": "https://example.com/:match*"
    }
  ]
}
```

This example rewrites requests to any path from your site's root that does not start with /uk/ and has x-vercel-ip-country header value of GB to a corresponding path under /uk/ relative to your site's root:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [
    {
      "source": "/:path((?!uk/).*)",
      "has": [
        {
          "type": "header",
          "key": "x-vercel-ip-country",
          "value": "GB"
        }
      ],
      "destination": "/uk/:path*"
    }
  ]
}
```

This example rewrites requests to the path `/dashboard` from your site's root that does not have a cookie with key `auth_token` to the path `/login` relative to your site's root:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [
    {
      "source": "/dashboard",
      "missing": [
        {
          "type": "cookie",
          "key": "auth_token"
        }
      ],
      "destination": "/login"
    }
  ]
}
```

### [Rewrite object definition](#rewrite-object-definition)

| Property | Description |
| --- | --- |
| `source` | A pattern that matches each incoming pathname (excluding querystring). |
| `destination` | A location destination defined as an absolute pathname or external URL. |
| `permanent` | A boolean to toggle between permanent and temporary redirect (default true). When `true`, the status code is [308](https://developer.mozilla.org/docs/Web/HTTP/Status/308). When `false` the status code is [307](https://developer.mozilla.org/docs/Web/HTTP/Status/307). |
| `has` | An optional array of `has` objects with the `type`, `key` and `value` properties. Used for conditional rewrites based on the presence of specified properties. |
| `missing` | An optional array of `missing` objects with the `type`, `key` and `value` properties. Used for conditional rewrites based on the absence of specified properties. |

### [Rewrite `has` or `missing` object definition](#rewrite-has-or-missing-object-definition)

| Property | Type | Description |
| --- | --- | --- |
| `type` | `String` | Must be either `header`, `cookie`, `host`, or `query`. |
| `key` | `String` | The key from the selected type to match against. |
| `value` | `String` or not defined | The value to check for, if `undefined` any value will match. A regex like string can be used to capture a specific part of the value, e.g. if the value `first-(?<paramName>.*)` is used for `first-second` then `second` will be usable in the destination with `:paramName`. |

The `source` property should NOT be a file because precedence is given to the filesystem prior to rewrites being applied. Instead, you should rename your static file or Serverless Function.

Using `has` does not yet work locally while using `vercel dev`, but does work when deployed.

Learn more about [rewrites](/docs/rewrites) on Vercel.

## [trailingSlash](#trailingslash)

Type: `Boolean`.

Default Value: `undefined`.

### [false](#false)

When `trailingSlash: false`, visiting a path that ends with a forward slash will respond with a 308 status code and redirect to the path without the trailing slash.

For example, the `/about/` path will redirect to `/about`.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "trailingSlash": false
}
```

### [true](#true)

When `trailingSlash: true`, visiting a path that does not end with a forward slash will respond with a 308 status code and redirect to the path with a trailing slash.

For example, the `/about` path will redirect to `/about/`.

However, paths with a file extension will not redirect to a trailing slash.

For example, the `/about/styles.css` path will not redirect, but the `/about/styles` path will redirect to `/about/styles/`.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "trailingSlash": true
}
```

### [undefined](#undefined)

When `trailingSlash: undefined`, visiting a path with or without a trailing slash will not redirect.

For example, both `/about` and `/about/` will serve the same content without redirecting.

This is not recommended because it could lead to search engines indexing two different pages with duplicate content.

## [Legacy](#legacy)

Legacy properties are still supported for backwards compatibility, but are deprecated.

### [name](#name)

The `name` property has been deprecated in favor of [Project Linking](/docs/cli/project-linking), which allows you to link a Vercel project to your local codebase when you run `vercel`.

Type: `String`.

Valid values: string name for the deployment.

Limits:

*   A maximum length of 52 characters
*   Only lower case alphanumeric characters or hyphens are allowed
*   Cannot begin or end with a hyphen, or contain multiple consecutive hyphens

The prefix for all new deployment instances. Vercel CLI usually generates this field automatically based on the name of the directory. But if you'd like to define it explicitly, this is the way to go.

The defined name is also used to organize the deployment into [a project](/docs/projects/overview).

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "name": "example-app"
}
```

### [version](#version)

The `version` property should not be used anymore.

Type: `Number`.

Valid values: `1`, `2`.

Specifies the Vercel Platform version the deployment should use.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "version": 2
}
```

### [alias](#alias)

The `alias` property should not be used anymore. To assign a custom Domain to your project, please [define it in the Project Settings](/docs/domains/add-a-domain) instead. Once your domains are, they will take precedence over the configuration property.

Type: `Array` or `String`.

Valid values: [domain names](/docs/domains/add-a-domain) (optionally including subdomains) added to the account, or a string for a suffixed URL using `.vercel.app` or a Custom Deployment Suffix ([available on the Enterprise plan](/pricing)).

Limit: A maximum of 64 aliases in the array.

The alias or aliases are applied automatically using [Vercel for GitHub](/docs/git/vercel-for-github), [Vercel for GitLab](/docs/git/vercel-for-gitlab), or [Vercel for Bitbucket](/docs/git/vercel-for-bitbucket) when merging or pushing to the [Production Branch](/docs/git#production-branch).

You can deploy to the defined aliases using [Vercel CLI](/docs/cli) by setting the [production deployment environment target](/docs/domains/deploying-and-redirecting).

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "alias": ["my-domain.com", "my-alias"]
}
```

### [scope](#scope)

The `scope` property has been deprecated in favor of [Project Linking](/docs/cli/project-linking), which allows you to link a Vercel project to your local codebase when you run `vercel`.

Type: `String`.

Valid values: For teams, either an ID or slug. For users, either a email address, username, or ID.

This property determines the scope ([Hobby team](/docs/accounts/create-an-account#creating-a-hobby-account) or [team](/docs/accounts/create-a-team)) under which the project will be deployed by [Vercel CLI](/cli).

It also affects any other actions that the user takes within the directory that contains this configuration (e.g. listing [environment variables](/docs/environment-variables) using `vercel secrets ls`).

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "scope": "my-team"
}
```

Deployments made through [Git](/docs/git) will ignore the `scope` property because the repository is already connected to [project](/docs/projects/overview).

### [env](#env)

We recommend against using this property. To add custom environment variables to your project [define them in the Project Settings](/docs/environment-variables).

Type: `Object` of `String` keys and values.

Valid values: environment keys and values.

Environment variables passed to the invoked [Serverless Functions](/docs/functions/serverless-functions).

This example will pass the `MY_KEY` static env to all [Serverless Functions](/docs/functions/serverless-functions) and the `SECRET` resolved from the `my-secret-name` [secret](/docs/environment-variables/reserved-environment-variables) dynamically.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "env": {
    "MY_KEY": "this is the value",
    "SECRET": "@my-secret-name"
  }
}
```

### [build.env](#build.env)

We recommend against using this property. To add custom environment variables to your project [define them in the Project Settings](/docs/environment-variables).

Type: `Object` of `String` keys and values inside the `build` `Object`.

Valid values: environment keys and values.

[Environment variables](/docs/environment-variables) passed to the [Build](/docs/deployments/configure-a-build) processes.

The following example will pass the `MY_KEY` environment variable to all [Builds](/docs/deployments/configure-a-build) and the `SECRET` resolved from the `my-secret-name` [secret](/docs/environment-variables/reserved-environment-variables) dynamically.

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "env": {
    "MY_KEY": "this is the value",
    "SECRET": "@my-secret-name"
  }
}
```

### [builds](#builds)

We recommend against using this property. To customize Serverless Functions, please use the [functions](#functions) property instead. If you'd like to deploy a monorepo, see the [Monorepo docs](/docs/monorepos).

Type: `Array` of build `Object`.

Valid values: a list of build descriptions whose `src` references valid source files.

#### [Build object definition](#build-object-definition)

*   `src` (`String`): A glob expression or pathname. If more than one file is resolved, one build will be created per matched file. It can include `*` and `**`.
*   `use` (`String`): An npm module to be installed by the build process. It can include a semver compatible version (e.g.: `@org/proj@1`).
*   `config` (`Object`): Optionally, an object including arbitrary metadata to be passed to the Builder.

The following will include all HTML files as-is (to be served statically), and build all Python files and JS files into [Serverless Functions](/docs/functions/serverless-functions):

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "builds": [
    { "src": "*.html", "use": "@vercel/static" },
    { "src": "*.py", "use": "@vercel/python" },
    { "src": "*.js", "use": "@vercel/node" }
  ]
}
```

When at least one `builds` item is specified, only the outputs of the build processes will be included in the resulting deployment as a security precaution. This is why we need to allowlist static files explicitly with `@vercel/static`.

### [routes](#routes)

We recommend using [cleanUrls](#cleanurls), [trailingSlash](#trailingslash), [redirects](#redirects), [rewrites](#rewrites), and/or [headers](#headers) instead.

The `routes` property is only meant to be used for advanced integration purposes, such as the [Build Output API](/docs/build-output-api/v3), and cannot be used in conjunction with any of the properties mentioned above.

See the [upgrading routes section](#upgrading-legacy-routes) to learn how to migrate away from this property.

Type: `Array` of route `Object`.

Valid values: a list of route definitions.

#### [Route object definition](#route-object-definition)

*   `src`: A [PCRE-compatible regular expression](https://www.pcre.org/original/doc/html/pcrepattern.html) that matches each incoming pathname (excluding querystring).
*   `methods`: A set of HTTP method types. If no method is provided, requests with any HTTP method will be a candidate for the route.
*   `dest`: A destination pathname or full URL, including querystring, with the ability to embed capture groups as $1, $2…
*   `headers`: A set of headers to apply for responses.
*   `status`: A status code to respond with. Can be used in tandem with `Location:` header to implement redirects.
*   `continue`: A boolean to change matching behavior. If `true`, routing will continue even when the `src` is matched.
*   `has`: An optional array of `has` objects with the `type`, `key` and `value` properties. Used for conditional path matching based on the presence of specified properties
*   `missing`: An optional array of `missing` objects with the `type`, `key` and `value` properties. Used for conditional path matching based on the absence of specified properties

Routes are processed in the order they are defined in the array, so wildcard/catch-all patterns should usually be last.

This example configures custom routes that map to static files and [Serverless Functions](/docs/functions/serverless-functions):

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [
    {
      "src": "/redirect",
      "status": 308,
      "headers": { "Location": "https://example.com/" }
    },
    {
      "src": "/custom-page",
      "headers": { "cache-control": "s-maxage=1000" },
      "dest": "/index.html"
    },
    { "src": "/api", "dest": "/my-api.js" },
    { "src": "/users", "methods": ["POST"], "dest": "/users-api.js" },
    { "src": "/users/(?<id>[^/]*)", "dest": "/users-api.js?id=$id" },
    { "src": "/legacy", "status": 404 },
    { "src": "/.*", "dest": "https://my-old-site.com" }
  ]
}
```

### [Upgrading legacy routes](#upgrading-legacy-routes)

In most cases, you can upgrade legacy `routes` usage to the newer [`rewrites`](/docs/project-configuration#rewrites), [`redirects`](/docs/project-configuration#redirects), [`headers`](/docs/project-configuration#headers), [`cleanUrls`](/docs/project-configuration#cleanurls) or [`trailingSlash`](/docs/project-configuration#trailingslash) properties.

Here are some examples that show how to upgrade legacy `routes` to the equivalent new property.

#### [Route Parameters](#route-parameters)

With `routes`, you use a [PCRE Regex](https://en.wikipedia.org/wiki/Perl_Compatible_Regular_Expressions) named group to match the ID and then pass that parameter in the query string. The following example matches a URL like `/product/532004` and proxies to `/api/product?id=532004`:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [{ "src": "/product/(?<id>[^/]+)", "dest": "/api/product?id=$id" }]
}
```

With [`rewrites`](/docs/project-configuration#rewrites), named parameters are automatically passed in the query string. The following example is equivalent to the legacy `routes` usage above, but uses `rewrites` instead:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [{ "source": "/product/:id", "destination": "/api/product" }]
}
```

#### [Legacy redirects](#legacy-redirects)

With `routes`, you specify the status code to use a 307 Temporary Redirect. Also, this redirect needs to be defined before other routes. The following example redirects all paths in the `posts` directory to the `blog` directory, but keeps the path in the new location:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [
    {
      "src": "/posts/(.*)",
      "headers": { "Location": "/blog/$1" },
      "status": 307
    }
  ]
}
```

With [`redirects`](/docs/project-configuration#redirects), you disable the `permanent` property to use a 307 Temporary Redirect. Also, `redirects` are always processed before `rewrites`. The following example is equivalent to the legacy `routes` usage above, but uses `redirects` instead:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "redirects": [
    {
      "source": "/posts/:id",
      "destination": "/blog/:id",
      "permanent": false
    }
  ]
}
```

#### [Legacy SPA Fallback](#legacy-spa-fallback)

With `routes`, you use `"handle": "filesystem"` to give precedence to the filesystem and exit early if the requested path matched a file. The following example will serve the `index.html` file for all paths that do not match a file in the filesystem:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [
    { "handle": "filesystem" },
    { "src": "/(.*)", "dest": "/index.html" }
  ]
}
```

With [`rewrites`](/docs/project-configuration#rewrites), the filesystem check is the default behavior. If you want to change the name of files at the filesystem level, file renames can be performed during the [Build Step](/docs/deployments/configure-a-build), but not with `rewrites`. The following example is equivalent to the legacy `routes` usage above, but uses `rewrites` instead:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [{ "source": "/(.*)", "destination": "/index.html" }]
}
```

#### [Legacy Headers](#legacy-headers)

With `routes`, you use `"continue": true` to prevent stopping at the first match. The following example adds `Cache-Control` headers to the favicon and other static assets:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [
    {
      "src": "/favicon.ico",
      "headers": { "Cache-Control": "public, max-age=3600" },
      "continue": true
    },
    {
      "src": "/assets/(.*)",
      "headers": { "Cache-Control": "public, max-age=31556952, immutable" },
      "continue": true
    }
  ]
}
```

With [`headers`](/docs/project-configuration#headers), this is no longer necessary since that is the default behavior. The following example is equivalent to the legacy `routes` usage above, but uses `headers` instead:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "headers": [
    {
      "source": "/favicon.ico",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=3600"
        }
      ]
    },
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31556952, immutable"
        }
      ]
    }
  ]
}
```

#### [Legacy Pattern Matching](#legacy-pattern-matching)

With `routes`, you need to escape a dot with two backslashes, otherwise it would match any character [PCRE Regex](https://en.wikipedia.org/wiki/Perl_Compatible_Regular_Expressions). The following example matches the literal `atom.xml` and proxies to `/api/rss` to dynamically generate RSS:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [{ "src": "/atom\\.xml", "dest": "/api/rss" }]
}
```

With [`rewrites`](/docs/project-configuration#rewrites), the `.` is not a special character so it does not need to be escaped. The following example is equivalent to the legacy `routes` usage above, but instead uses `rewrites`:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [{ "source": "/atom.xml", "destination": "/api/rss" }]
}
```

#### [Legacy Negative Lookahead](#legacy-negative-lookahead)

With `routes`, you use [PCRE Regex](https://en.wikipedia.org/wiki/Perl_Compatible_Regular_Expressions) negative lookahead. The following example proxies all requests to the `/maintenance` page except for `/maintenance` itself to avoid infinite loop:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "routes": [{ "src": "/(?!maintenance)", "dest": "/maintenance" }]
}
```

With [`rewrites`](/docs/project-configuration#rewrites), the Regex needs to be wrapped. The following example is equivalent to the legacy `routes` usage above, but instead uses `rewrites`:

```
{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "rewrites": [
    { "source": "/((?!maintenance).*)", "destination": "/maintenance" }
  ]
}
```

#### [Legacy Case Sensitivity](#legacy-case-sensitivity)

With `routes`, the `src` property is case-insensitive leading to duplicate content, where multiple request paths with difference cases serve the same page.

With [`rewrites`](/docs/project-configuration#rewrites) / [`redirects`](/docs/project-configuration#redirects) / [`headers`](/docs/project-configuration#headers), the `source` property is case-sensitive so you don't accidentally create duplicate content.

Last updated on March 12, 2025