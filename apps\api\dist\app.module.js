"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const throttler_1 = require("@nestjs/throttler");
const cache_manager_1 = require("@nestjs/cache-manager");
const core_1 = require("@nestjs/core");
const performance_middleware_1 = require("./middleware/performance.middleware");
const logging_interceptor_1 = require("./common/interceptors/logging.interceptor");
const circuit_breaker_service_1 = require("./common/services/circuit-breaker.service");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const prisma_module_1 = require("./prisma/prisma.module");
const auth_module_1 = require("./auth/auth.module");
const carrier_profiles_module_1 = require("./carrier-profiles/carrier-profiles.module");
const airtable_orders_module_1 = require("./airtable-orders/airtable-orders.module");
const admin_module_1 = require("./admin/admin.module");
const operations_module_1 = require("./operations/operations.module");
const notifications_module_1 = require("./notifications/notifications.module");
const bids_module_1 = require("./bids/bids.module");
const path_1 = require("path");
const compression_1 = __importDefault(require("compression"));
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply((0, compression_1.default)()).forRoutes({ path: '*', method: common_1.RequestMethod.ALL });
        consumer.apply(performance_middleware_1.PerformanceMiddleware).forRoutes({ path: '*', method: common_1.RequestMethod.ALL });
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: process.env.NODE_ENV === 'test'
                    ? (0, path_1.join)(__dirname, '..', '.env.test')
                    : (0, path_1.join)(__dirname, '..', '.env'),
            }),
            cache_manager_1.CacheModule.register({
                isGlobal: true,
                ttl: 300000,
                max: 100,
            }),
            throttler_1.ThrottlerModule.forRoot([
                {
                    name: 'short',
                    ttl: 60000,
                    limit: process.env.NODE_ENV === 'production' ? 20 : 100,
                },
                {
                    name: 'medium',
                    ttl: 60000 * 10,
                    limit: process.env.NODE_ENV === 'production' ? 100 : 500,
                },
                {
                    name: 'long',
                    ttl: 60000 * 60,
                    limit: process.env.NODE_ENV === 'production' ? 500 : 2000,
                }
            ]),
            prisma_module_1.PrismaModule,
            airtable_orders_module_1.AirtableOrdersModule,
            auth_module_1.AuthModule,
            carrier_profiles_module_1.CarrierProfilesModule,
            admin_module_1.AdminModule,
            operations_module_1.OperationsModule,
            notifications_module_1.NotificationsModule,
            bids_module_1.BidsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            circuit_breaker_service_1.CircuitBreakerService,
            {
                provide: core_1.APP_GUARD,
                useClass: throttler_1.ThrottlerGuard,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logging_interceptor_1.LoggingInterceptor,
            },
            common_1.Logger,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map