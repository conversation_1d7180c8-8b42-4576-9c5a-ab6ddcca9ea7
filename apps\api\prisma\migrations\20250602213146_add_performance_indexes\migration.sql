-- CreateIndex
CREATE INDEX "bids_load_id_idx" ON "bids"("load_id");

-- CreateIndex
CREATE INDEX "bids_carrier_profile_id_idx" ON "bids"("carrier_profile_id");

-- CreateIndex
CREATE INDEX "bids_load_id_carrier_profile_id_idx" ON "bids"("load_id", "carrier_profile_id");

-- CreateIndex
CREATE INDEX "bids_created_at_idx" ON "bids"("created_at");

-- CreateIndex
CREATE INDEX "carrier_profiles_user_id_idx" ON "carrier_profiles"("user_id");

-- CreateIndex
CREATE INDEX "carrier_profiles_company_name_idx" ON "carrier_profiles"("company_name");

-- CreateIndex
CREATE INDEX "carrier_profiles_contact_email_idx" ON "carrier_profiles"("contact_email");

-- CreateIndex
CREATE INDEX "carrier_profiles_dot_number_idx" ON "carrier_profiles"("dot_number");

-- CreateIndex
CREATE INDEX "carrier_profiles_mc_number_idx" ON "carrier_profiles"("mc_number");

-- CreateIndex
CREATE INDEX "carrier_profiles_is_verified_by_admin_idx" ON "carrier_profiles"("is_verified_by_admin");

-- CreateIndex
CREATE INDEX "loads_airtable_record_id_idx" ON "loads"("airtable_record_id");

-- CreateIndex
CREATE INDEX "loads_status_idx" ON "loads"("status");

-- CreateIndex
CREATE INDEX "loads_awarded_to_carrier_profile_id_idx" ON "loads"("awarded_to_carrier_profile_id");

-- CreateIndex
CREATE INDEX "loads_is_public_idx" ON "loads"("is_public");

-- CreateIndex
CREATE INDEX "loads_is_targeted_idx" ON "loads"("is_targeted");

-- CreateIndex
CREATE INDEX "loads_pickup_date_utc_idx" ON "loads"("pickup_date_utc");

-- CreateIndex
CREATE INDEX "loads_delivery_date_utc_idx" ON "loads"("delivery_date_utc");

-- CreateIndex
CREATE INDEX "loads_origin_state_idx" ON "loads"("origin_state");

-- CreateIndex
CREATE INDEX "loads_destination_state_idx" ON "loads"("destination_state");

-- CreateIndex
CREATE INDEX "loads_equipment_required_idx" ON "loads"("equipment_required");

-- CreateIndex
CREATE INDEX "loads_status_is_public_idx" ON "loads"("status", "is_public");

-- CreateIndex
CREATE INDEX "loads_status_awarded_to_carrier_profile_id_idx" ON "loads"("status", "awarded_to_carrier_profile_id");

-- CreateIndex
CREATE INDEX "loads_payment_status_idx" ON "loads"("payment_status");

-- CreateIndex
CREATE INDEX "loads_created_at_idx" ON "loads"("created_at");

-- CreateIndex
CREATE INDEX "users_clerk_user_id_idx" ON "users"("clerk_user_id");

-- CreateIndex
CREATE INDEX "users_email_idx" ON "users"("email");

-- CreateIndex
CREATE INDEX "users_clerk_org_id_idx" ON "users"("clerk_org_id");

-- CreateIndex
CREATE INDEX "users_org_name_idx" ON "users"("org_name");

-- CreateIndex
CREATE INDEX "users_role_idx" ON "users"("role");
