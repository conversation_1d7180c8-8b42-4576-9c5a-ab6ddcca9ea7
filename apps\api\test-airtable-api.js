// Test Airtable API connection to debug the discrepancy
const Airtable = require('airtable');

async function testAirtableAPI() {
  console.log('🧪 TESTING AIRTABLE API CONNECTION');
  console.log('===================================\n');
  
  try {
    // Get environment variables (assuming they're set)
    const apiKey = process.env.AIRTABLE_API_KEY;
    const baseId = process.env.AIRTABLE_BASE_ID;
    const tableName = process.env.AIRTABLE_TABLE_NAME || 'Orders';
    
    console.log('📋 Configuration:');
    console.log(`API Key: ${apiKey ? 'SET (' + apiKey.substring(0, 8) + '...)' : 'NOT SET'}`);
    console.log(`Base ID: ${baseId || 'NOT SET'}`);
    console.log(`Table Name: ${tableName}`);
    console.log('');
    
    if (!apiKey || !baseId) {
      console.log('❌ Missing Airtable configuration');
      return;
    }
    
    // Initialize Airtable connection
    const airtable = new Airtable({ apiKey });
    const base = airtable.base(baseId);
    
    console.log('✅ Airtable connection initialized');
    
    // Test 1: Get total records
    console.log('\n🔍 Test 1: Fetching ALL records from Airtable...');
    const allRecords = await base(tableName)
      .select({
        fields: ['Order ID.', 'Status', 'Synced to API', 'Is Public'],
        view: 'Grid view'
      })
      .all();
    
    console.log(`📊 Total records in Airtable: ${allRecords.length}`);
    
    // Test 2: Count by status
    const statusCounts = {};
    const syncedCounts = {};
    allRecords.forEach(record => {
      const status = record.get('Status') || 'NULL';
      const synced = record.get('Synced to API');
      
      statusCounts[status] = (statusCounts[status] || 0) + 1;
      syncedCounts[synced ? 'Synced' : 'Not Synced'] = (syncedCounts[synced ? 'Synced' : 'Not Synced'] || 0) + 1;
    });
    
    console.log('\n📋 Airtable Status Breakdown:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`${status}: ${count} records`);
    });
    
    console.log('\n📋 Airtable Sync Status:');
    Object.entries(syncedCounts).forEach(([synced, count]) => {
      console.log(`${synced}: ${count} records`);
    });
    
    // Test 3: Test the exact filter used by getAvailableLoads
    console.log('\n🔍 Test 3: Testing exact API filter...');
    const filteredRecords = await base(tableName)
      .select({
        view: 'Grid view',
        fields: ['Order ID.', 'Status', 'Synced to API', 'Is Public', 'Rate to Carrier'],
        filterByFormula: `AND({Synced to API}, OR({Status} = "Available", {Status} = "Booking Requested"))`
      })
      .all();
    
    console.log(`🎯 Filtered records (exact API query): ${filteredRecords.length}`);
    
    if (filteredRecords.length > 0) {
      console.log('\n📋 Sample filtered records:');
      filteredRecords.slice(0, 3).forEach((record, index) => {
        console.log(`\nRecord ${index + 1}:`);
        console.log(`- Airtable ID: ${record.id}`);
        console.log(`- Order ID: ${record.get('Order ID.')}`);
        console.log(`- Status: ${record.get('Status')}`);
        console.log(`- Synced to API: ${record.get('Synced to API')}`);
        console.log(`- Is Public: ${record.get('Is Public')}`);
        console.log(`- Rate: ${record.get('Rate to Carrier')}`);
      });
    }
    
    // Test 4: Test individual conditions
    console.log('\n🔍 Test 4: Testing individual filter conditions...');
    
    const syncedRecords = await base(tableName)
      .select({
        fields: ['Order ID.', 'Status', 'Synced to API'],
        filterByFormula: `{Synced to API}`
      })
      .all();
    
    console.log(`📊 Records with 'Synced to API' = true: ${syncedRecords.length}`);
    
    const availableRecords = await base(tableName)
      .select({
        fields: ['Order ID.', 'Status'],
        filterByFormula: `{Status} = "Available"`
      })
      .all();
    
    console.log(`📊 Records with Status = "Available": ${availableRecords.length}`);
    
    console.log('\n🎯 DIAGNOSIS:');
    if (filteredRecords.length === 0 && availableRecords.length > 0) {
      console.log('❌ Issue: Records exist with Status="Available" but none have "Synced to API"=true');
      console.log('🔧 Solution: Check Airtable "Synced to API" field - it may not be checked for available loads');
    } else if (filteredRecords.length > 0) {
      console.log('✅ Airtable filter is working - issue may be elsewhere');
    } else {
      console.log('❓ No available records found in Airtable - need to investigate further');
    }
    
  } catch (error) {
    console.error('❌ Airtable API test failed:', error.message);
    console.error('Full error:', error);
  }
}

testAirtableAPI(); 