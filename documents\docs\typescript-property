TITLE: TypeScript Class with Arrow Functions Accessing Parameter Property
DESCRIPTION: This class demonstrates arrow function properties that access a property from a parameter property. The arrow functions can safely access properties of the parameter property object.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignParameterPropertyToPropertyDeclarationES2022.errors.txt#2025-04-21_snippet_5

LANGUAGE: typescript
CODE:
```
class H {
    constructor(public p1: C) {}

    public p2 = () => {
        return this.p1.foo;
    }

    public p3 = () => this.p1.foo;
}
```

----------------------------------------

TITLE: Defining Module Properties with Object.defineProperty in JavaScript
DESCRIPTION: This snippet defines module properties using Object.defineProperty. It sets up various properties with different access levels, including read-only properties and getter/setter accessors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkExportsObjectAssignProperty.errors.txt#2025-04-21_snippet_1

LANGUAGE: JavaScript
CODE:
```
Object.defineProperty(exports, "thing", { value: 42, writable: true });
Object.defineProperty(exports, "readonlyProp", { value: "Smith", writable: false });
Object.defineProperty(exports, "rwAccessors", { get() { return 98122 }, set(_) { /*ignore*/ } });
Object.defineProperty(exports, "readonlyAccessor", { get() { return 21.75 } });
Object.defineProperty(exports, "setonlyAccessor", {
    /** @param {string} str */
    set(str) {
        this.rwAccessors = Number(str) 
    }
});
```

----------------------------------------

TITLE: TypeScript Class with Arrow Function Property Using Other Properties
DESCRIPTION: This class demonstrates how arrow function properties can safely access other properties in a class. The initialization order is correctly handled without generating errors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignParameterPropertyToPropertyDeclarationES2022.errors.txt#2025-04-21_snippet_2

LANGUAGE: typescript
CODE:
```
class E {
    bar = () => this.foo1 + this.foo2; // both ok
    foo1 = '';
    constructor(public foo2: string) {}
}
```

----------------------------------------

TITLE: Valid Parameter Properties in TypeScript Class Constructors
DESCRIPTION: Shows valid usage of parameter properties with accessibility modifiers in class constructor implementations. Parameter properties are a TypeScript feature that allows declaring a class property and assigning a constructor parameter to it in a single statement.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    constructor(public x, private y) { }
}

class C2 {
    constructor(public x) { }
}

class C3 {
    constructor(private x) { }
}
```

----------------------------------------

TITLE: TypeScript Class with Inner Class and Parameter Property
DESCRIPTION: This class shows a variation with an inner class and a parameter property in the constructor. The inner class can access the parameter property from the outer class.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignParameterPropertyToPropertyDeclarationES2022.errors.txt#2025-04-21_snippet_4

LANGUAGE: typescript
CODE:
```
class G {
    Inner = class extends G {
        p2 = this.p1
    }
    constructor(public p1: number) {}
}
```

----------------------------------------

TITLE: Invalid Parameter Properties in Declared Constructor
DESCRIPTION: Demonstrates incorrect usage of parameter properties (public/private modifiers) in a declared class constructor. Parameter properties are only allowed in constructor implementations, not in declarations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ParameterList8.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
declare class C2 {
    constructor(public p1:string);
    constructor(private p2:number);
    constructor(public p3:any);
}
```

----------------------------------------

TITLE: TypeScript Property Access and Validation
DESCRIPTION: TypeScript file showing property access patterns and validation errors when attempting invalid assignments to readonly properties and type mismatches.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkExportsObjectAssignPrototypeProperty.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
import "./";

import Person = require("./mod1");

const m1 = new Person("Name")

m1.thing;
m1.readonlyProp;
m1.rwAccessors;
m1.readonlyAccessor;
m1.setonlyAccessor;

// allowed assignments
m1.thing = 10;
m1.rwAccessors = 11;
m1.setonlyAccessor = "yes";

// disallowed assignments
m1.readonlyProp = "name";
m1.readonlyAccessor = 12;
m1.thing = "no";
m1.rwAccessors = "no";
m1.setonlyAccessor = 0;
```

----------------------------------------

TITLE: Static Property Access Error Examples in TypeScript
DESCRIPTION: Demonstrates various error cases when accessing static and instance properties in a TypeScript class. Shows errors when trying to access static properties through instance, accessing private static members, and accessing non-existent properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classStaticPropertyAccess.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class A {
    public static "\""() {}
    public static x: number = 1;
    public static y: number = 1;
    private static _b: number = 2;
}

const a = new A();

a["\""] // Error
a['y']  // Error
a.y     // Error
A._b    // Error
A.a
```

----------------------------------------

TITLE: Exporting and Assigning Properties in JavaScript
DESCRIPTION: This snippet demonstrates exporting properties and assigning void 0 to a property, which leads to TypeScript errors due to undefined properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentToVoidZero2.errors.txt#2025-04-21_snippet_0

LANGUAGE: JavaScript
CODE:
```
exports.j = 1;
exports.k = void 0;
```

----------------------------------------

TITLE: Exploring Subtyping with Static Properties in TypeScript
DESCRIPTION: This snippet expands on subtyping by adding static properties to demonstrate prototype inheritance. The Base2 class introduces a static property s of type String. Derived2 extends Base2 and has an instance property x of type U, constrained to extend String. Despite an apparent type match for instances, a TypeScript error arises due to prototype differences in static properties. This showcases a limitation of subtyping when static properties differ. Dependencies include TypeScript's class and static property syntax.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/apparentTypeSubtyping.errors.txt#2025-04-21_snippet_1

LANGUAGE: TypeScript
CODE:
```
class Base2 {
    x: String;
    static s: String;
}

class Derived2<U extends String> extends Base2 { // error because of the prototype's not matching, not because of the instance side
    x: U;
}
```

----------------------------------------

TITLE: TypeScript Class Inheritance with Property Access
DESCRIPTION: This class demonstrates how property access works in class inheritance. The derived class D can access properties from the base class C without initialization errors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignParameterPropertyToPropertyDeclarationES2022.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
class D extends C {
    quill = this.foo // ok
}
```

----------------------------------------

TITLE: Demonstrating Property Initialization Order Errors in TypeScript Classes
DESCRIPTION: This class demonstrates various scenarios of property initialization order issues in TypeScript. It shows how accessing properties before their declaration points results in 'Property is used before its initialization' errors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignParameterPropertyToPropertyDeclarationES2022.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    qux = this.bar // should error
    bar = this.foo // should error
    quiz = this.bar // ok
    quench = this.m1() // ok
    quanch = this.m3() // should error
    m1() {
        this.foo // ok
    }
    m3 = function() { }
    constructor(public foo: string) {}
    quim = this.baz // should error
    baz = this.foo; // should error
    quid = this.baz // ok
    m2() {
        this.foo // ok
    }
}
```

----------------------------------------

TITLE: Demonstrating Property Mismatch Errors in TypeScript Assignment Compatibility
DESCRIPTION: This code snippet shows how TypeScript enforces structural typing by checking property presence when assigning between different types. The example includes a class C with a 'foo' property and an interface I with a 'fooo' property, demonstrating how assignments in both directions fail due to missing required properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatWithObjectMembers5.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    foo: string;
}

var c: C;

interface I {
    fooo: string;
}

var i: I;

c = i; // error
i = c; // error
```

----------------------------------------

TITLE: Invalid Class Property Declaration in TypeScript
DESCRIPTION: Example showing incorrect property declarations in a TypeScript class that results in multiple compilation errors. The code attempts to use properties 'foo' and 'bar' without properly declaring them as class members.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/autoLift2.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class A
{
    constructor() {
        this.foo: any;
        this.bar: any;
    }


  baz() {

     this.foo = "foo";

     this.bar = "bar";

     [1, 2].forEach((p) => this.foo);

     [1, 2].forEach((p) => this.bar);

  }

}



var a = new A();

a.baz();



```

----------------------------------------

TITLE: Importing and Accessing Module Properties in TypeScript
DESCRIPTION: This snippet shows the import of two modules and various property access attempts. It demonstrates both valid and invalid assignments, highlighting TypeScript's type checking and read-only property enforcement.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkExportsObjectAssignProperty.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
import "./";

import m1 = require("./mod1");

m1.thing;
m1.readonlyProp;
m1.rwAccessors;
m1.readonlyAccessor;
m1.setonlyAccessor;

// allowed assignments
m1.thing = 10;
m1.rwAccessors = 11;
m1.setonlyAccessor = "yes";

// disallowed assignments
m1.readonlyProp = "name";
m1.readonlyAccessor = 12;
m1.thing = "no";
m1.rwAccessors = "no";
m1.setonlyAccessor = 0;

import m2 = require("./mod2");

m2.thing;
m2.readonlyProp;
m2.rwAccessors;
m2.readonlyAccessor;
m2.setonlyAccessor;

// allowed assignments
m2.thing = "ok";
m2.rwAccessors = 11;
m2.setonlyAccessor = "yes";

// disallowed assignments
m2.readonlyProp = "name";
m2.readonlyAccessor = 12;
m2.thing = 0;
m2.rwAccessors = "no";
m2.setonlyAccessor = 0;
```

----------------------------------------

TITLE: JavaScript Person Class Implementation
DESCRIPTION: JavaScript implementation of Person class with various property definitions using Object.defineProperty, including readonly properties, getters, and setters.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkExportsObjectAssignPrototypeProperty.errors.txt#2025-04-21_snippet_1

LANGUAGE: javascript
CODE:
```
/**
 * @constructor
 * @param {string} name
 */
function Person(name) {
    this.name = name;
}
Person.prototype.describe = function () {
    return "Person called " + this.name;
};
Object.defineProperty(Person.prototype, "thing", { value: 42, writable: true });
Object.defineProperty(Person.prototype, "readonlyProp", { value: "Smith", writable: false });
Object.defineProperty(Person.prototype, "rwAccessors", { get() { return 98122 }, set(_) { /*ignore*/ } });
Object.defineProperty(Person.prototype, "readonlyAccessor", { get() { return 21.75 } });
Object.defineProperty(Person.prototype, "setonlyAccessor", {
    /** @param {string} str */
    set(str) {
        this.rwAccessors = Number(str)
    }
});
module.exports = Person;
```

----------------------------------------

TITLE: Defining Properties with Object.defineProperty in JavaScript
DESCRIPTION: This JavaScript code defines an object `x` and uses `Object.defineProperty` to define various properties with specific attributes like `writable`, `get`, and `set`. This shows how to control the mutability and access behavior of object properties in JavaScript. It exports the object `x` using `module.exports = x;` to make it available to other modules.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkObjectDefineProperty.errors.txt#2025-04-21_snippet_1

LANGUAGE: javascript
CODE:
```
"const x = {};
    Object.defineProperty(x, \"name\", { value: \"Charles\", writable: true });
    Object.defineProperty(x, \"middleInit\", { value: \"H\" });
    Object.defineProperty(x, \"lastName\", { value: \"Smith\", writable: false });
    Object.defineProperty(x, \"zip\", { get() { return 98122 }, set(_) { /*ignore*/ } });
    Object.defineProperty(x, \"houseNumber\", { get() { return 21.75 } });
    Object.defineProperty(x, \"zipStr\", {
        /** @param {string} str */
        set(str) {
            this.zip = Number(str) 
        }
    });
    
    /**
     * @param {{name: string}} named
     */
    function takeName(named) { return named.name; }
    
    takeName(x);
    /**
     * @type {number}
     */
    var a = x.zip;
    
    /**
     * @type {number}
     */
    var b = x.houseNumber;
    
    const returnExemplar = () => x;
    const needsExemplar = (_ = x) => void 0;
    
    const expected = /** @type {{name: string, readonly middleInit: string, readonly lastName: string, zip: number, readonly houseNumber: number, zipStr: string}} */(/** @type {*} */(null));
    
    /**
     * 
     * @param {typeof returnExemplar} a 
     * @param {typeof needsExemplar} b 
     */
    function match(a, b) {}
    
    match(() => expected, (x = expected) => void 0);
    
    module.exports = x;"
```

----------------------------------------

TITLE: Demonstrating Property Initialization Order Errors in TypeScript Classes
DESCRIPTION: This code demonstrates TypeScript's property initialization order rules. It shows cases where accessing properties before initialization causes TS2729 errors, and cases where the access is valid. It includes examples of parameter properties, method definitions, and property initializers.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignParameterPropertyToPropertyDeclarationESNext.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    qux = this.bar // should error
    bar = this.foo // should error
    quiz = this.bar // ok
    quench = this.m1() // ok
    quanch = this.m3() // should error
    m1() {
        this.foo // ok
    }
    m3 = function() { }
    constructor(public foo: string) {}
    quim = this.baz // should error
    baz = this.foo; // should error
    quid = this.baz // ok
    m2() {
        this.foo // ok
    }
}

class D extends C {
    quill = this.foo // ok
}

class E {
    bar = () => this.foo1 + this.foo2; // both ok
    foo1 = '';
    constructor(public foo2: string) {}
}

class F {
    Inner = class extends F {
        p2 = this.p1
    }
    p1 = 0
}
class G {
    Inner = class extends G {
        p2 = this.p1
    }
    constructor(public p1: number) {}
}
class H {
    constructor(public p1: C) {}

    public p2 = () => {
        return this.p1.foo;
    }

    public p3 = () => this.p1.foo;
}
```

----------------------------------------

TITLE: Exporting Properties in TypeScript Modules - TypeScript
DESCRIPTION: This snippet provides the definitions of properties in a TypeScript module, including writable and read-only settings for each property via Object.defineProperty.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkOtherObjectAssignProperty.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
// Property exports in mod1.js
const obj = { value: 42, writable: true };
Object.defineProperty(exports, "thing", obj);

/**
 * @type {string}
 */
let str = /** @type {string} */("other");
Object.defineProperty(exports, str, { value: 42, writable: true });

const propName = "prop"
Object.defineProperty(exports, propName, { value: 42, writable: true });

Object.defineProperty(exports, "bad1", { });
Object.defineProperty(exports, "bad2", { get() { return 12 }, value: "no" });
Object.defineProperty(exports, "bad3", { writable: true });
```

----------------------------------------

TITLE: TypeScript Property Override Error Example
DESCRIPTION: Demonstrates an error case where the 'sound' property from the Animal class is incorrectly overridden with get/set accessors in the Lion class. This causes TypeScript error TS2611 since properties and accessors cannot be mixed in inheritance.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/accessorsOverrideProperty4.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
declare class Animal {
    sound: string;
}
class Lion extends Animal {
    _sound = 'roar'
    get sound(): string { return this._sound }
    set sound(val: string) { this._sound = val }
}
```

----------------------------------------

TITLE: Defining Module Properties for module.exports in JavaScript
DESCRIPTION: This snippet is similar to the previous one but uses module.exports instead of exports. It defines various properties with different access levels using Object.defineProperty.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkExportsObjectAssignProperty.errors.txt#2025-04-21_snippet_2

LANGUAGE: JavaScript
CODE:
```
Object.defineProperty(module.exports, "thing", { value: "yes", writable: true });
Object.defineProperty(module.exports, "readonlyProp", { value: "Smith", writable: false });
Object.defineProperty(module.exports, "rwAccessors", { get() { return 98122 }, set(_) { /*ignore*/ } });
Object.defineProperty(module.exports, "readonlyAccessor", { get() { return 21.75 } });
Object.defineProperty(module.exports, "setonlyAccessor", {
    /** @param {string} str */
    set(str) {
        this.rwAccessors = Number(str) 
    }
});
```

----------------------------------------

TITLE: TypeScript Validation of Read-Only Properties
DESCRIPTION: This TypeScript code imports a JavaScript module and attempts to assign values to properties, triggering TypeScript errors when assigning to read-only properties. It demonstrates TypeScript's compile-time checks and type safety features. The `require("./")` imports the JavaScript module, which defines the object with properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkObjectDefineProperty.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
"// Validate in TS as simple validations would usually be interpreted as more special assignments
    import x = require("./");
    x.name;
    x.middleInit;
    x.lastName;
    x.zip;
    x.houseNumber;
    x.zipStr;
    
    x.name = \"Another\";
    x.zip = 98123;
    x.zipStr = \"OK\";
    
    x.lastName = \"should fail\";
    x.houseNumber = 12; // should also fail
    x.zipStr = 12; // should fail
    x.middleInit = \"R\"; // should also fail"
```

----------------------------------------

TITLE: Class with property named same as type parameter
DESCRIPTION: Example showing a class Foo with a type parameter 't' and a property with the same name. It demonstrates how TypeScript handles property access when the property name matches the type parameter name.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/conflictingTypeParameterSymbolTransfer.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
// via #56689
class Leg { }
class Foo<t> extends Leg {
    t = {} as t

    // should allow this access since t was declared as a property on Foo
    foo = this.t
}
```

----------------------------------------

TITLE: Invalid Parameter Property in Setter Method - TypeScript
DESCRIPTION: Example showing incorrect usage of parameter property modifier 'public' in a class setter method. Parameter properties are only allowed in constructor parameters, not in other methods or accessors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/MemberAccessorDeclaration15.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
   set Foo(public a: number) { }
}
```

----------------------------------------

TITLE: Demonstrating Object Literal Property Mismatch in TypeScript
DESCRIPTION: This snippet shows a function expecting an object with a number property 'a', but receives an object with an incorrect property 'b'. TypeScript raises an error for the unknown property.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatBug5.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
function foo1(x: { a: number; }) { }
foo1({ b: 5 });
```

----------------------------------------

TITLE: Invalid Parameter Property Usage in TypeScript Function
DESCRIPTION: Demonstrates an incorrect attempt to use a parameter property modifier 'public' in a regular function declaration. Parameter properties are only allowed within class constructor methods in TypeScript.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ParameterList4.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
function F(public A) {
}
```

----------------------------------------

TITLE: Private Parameter Properties in Interface Constructor Signatures
DESCRIPTION: Example showing invalid usage of private parameter properties in interface constructor signatures. Parameter properties are not allowed in interface declarations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters2.errors.txt#2025-04-21_snippet_4

LANGUAGE: typescript
CODE:
```
interface I2 {
    new (private x);
    new (private x);
}
```

----------------------------------------

TITLE: Computed Property Names with Static Properties in TypeScript Classes
DESCRIPTION: Demonstrates incorrect usage of class references in computed property names, where the class is referenced before its declaration. Contains two classes (C1 and C2) with similar patterns of errors in getters, setters, and methods using static properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNamesWithStaticProperty.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C1 {
    static staticProp = 10;
    get [C1.staticProp]() {
        return "hello";
    }
    set [C1.staticProp](x: string) {
        var y = x;
    }
    [C1.staticProp]() { }
}

(class C2 {
    static staticProp = 10;
    get [C2.staticProp]() {
        return "hello";
    }
    set [C2.staticProp](x: string) {
        var y = x;
    }
    [C2.staticProp]() { }
})
```

----------------------------------------

TITLE: Parameter Properties in Interface Constructor Signatures
DESCRIPTION: Example showing invalid usage of parameter properties in interface constructor signatures. Parameter properties are not allowed in interface declarations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters2.errors.txt#2025-04-21_snippet_3

LANGUAGE: typescript
CODE:
```
interface I {
    new (public x);
    new (public x);
}
```

----------------------------------------

TITLE: Invalid Computed Property Names in TypeScript Class
DESCRIPTION: Demonstrates incorrect usage of computed property names where the property expressions have union types that don't satisfy TypeScript's requirements. Property names must be of type 'string', 'number', 'symbol', or 'any'.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames15_ES6.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
var p1: number | string;
var p2: number | number[];
var p3: string | boolean;
class C {
    [p1]() { }
    [p2]() { }
    [p3]() { }
}
```

----------------------------------------

TITLE: TypeScript Class Constructor Parameter Properties Error Cases
DESCRIPTION: Demonstrates multiple classes with constructor parameter properties that show common error patterns including duplicate identifier declarations and mismatched access modifiers. Shows errors with public, private, and protected parameter properties conflicting with class field declarations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructorParameterProperties2.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    y: number;
    constructor(y: number) { } // ok
}

var c: C;
var r = c.y;

class D {
    y: number;
    constructor(public y: number) { } // error
}

var d: D;
var r2 = d.y;

class E {
    y: number;
    constructor(private y: number) { } // error
}

var e: E;
var r3 = e.y; // error

class F {
    y: number;
    constructor(protected y: number) { } // error
}

var f: F;
var r4 = f.y; // error
```

----------------------------------------

TITLE: Defining Class with Private Properties in TypeScript
DESCRIPTION: This snippet defines a TypeScript class 'C' with various private properties and methods, including instance and static members. It demonstrates different ways of declaring private members in a class.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classWithPrivateProperty.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
class C {
    private x;
    private a = '';
    private b: string = '';
    private c() { return '' }
    private d = () => '';
    private static e;
    private static f() { return '' }
    private static g = () => '';
}
```

----------------------------------------

TITLE: Parameter Properties in Anonymous Type Constructor Signatures
DESCRIPTION: Example showing invalid usage of public parameter properties in anonymous type constructor signatures. Parameter properties are not allowed in type declarations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters2.errors.txt#2025-04-21_snippet_5

LANGUAGE: typescript
CODE:
```
var a: {
    new (public x);
    new (public y);
}
```

----------------------------------------

TITLE: Assignment Compatibility with Optional Target Properties in TypeScript
DESCRIPTION: This module demonstrates assignment compatibility when the target type has optional properties. It shows various scenarios of assigning objects with required properties to targets with optional properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatWithObjectMembersOptionality.errors.txt#2025-04-21_snippet_1

LANGUAGE: TypeScript
CODE:
```
module TargetHasOptional {
    // targets
    interface C {
        opt?: Base
    }
    var c: C;

    var a: { opt?: Base; }
    var b: typeof a = { opt: new Base() }

    // sources
    interface D {
        opt: Base;
    }
    interface E {
        opt: Derived;
    }
    interface F {
        opt?: Derived;
    }
    var d: D;
    var e: E;
    var f: F;

    // all ok
    c = d;
    c = e;
    c = f;
    c = a;

    a = d;
    a = e;
    a = f;
    a = c;

    b = d;
    b = e;
    b = f;
    b = a;
    b = c;
}
```

----------------------------------------

TITLE: Variable Declarations and Class with Property Initialization - TypeScript
DESCRIPTION: This snippet declares two global variables `x` and `y`, and then defines a class `C3`. The class `C3` includes a property `x` initialized using the value of the globally declared variable `y`. This demonstrates accessing variables from the surrounding scope during property initialization.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classBodyWithStatements.errors.txt#_snippet_2

LANGUAGE: TypeScript
CODE:
```
var x = 1;
var y = 2;
class C3 {
    x: number = y + 1;
}
```

----------------------------------------

TITLE: Incorrect Usage of Parameter Property in Arrow Function
DESCRIPTION: This code demonstrates an incorrect attempt to use a parameter property modifier (public) in an arrow function expression. In TypeScript, parameter properties can only be declared in constructor implementations of a class, not in regular functions or arrow functions.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ArrowFunctionExpression1.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
var v = (public x: string) => { };
```

----------------------------------------

TITLE: Class Property Type Mismatch in TypeScript
DESCRIPTION: Demonstrates a type error where computed property names in a derived class are not assignable to the string index signature type. The base class Foo lacks property 'y' required by Foo2, causing type incompatibility.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames45_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class Foo { x }
class Foo2 { x; y }

class C {
    get ["get1"]() { return new Foo }
}

class D extends C {
    // No error when the indexer is in a class more derived than the computed property
    [s: string]: Foo2;
    set ["set1"](p: Foo) { }
}
```

----------------------------------------

TITLE: Defining Class with Required Properties - TypeScript
DESCRIPTION: This snippet defines a class constructor that requires two properties: 'one' of type T and 'two' of type U. An object of this class is instantiated with valid arguments, demonstrating how required properties work in TypeScript classes.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatability39.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
module __test2__ {
    export class classWithTwoPublic<T,U> { constructor(public one: T, public two: U) {} }
    var x2 = new classWithTwoPublic(1, "a");
    export var __val__x2 = x2;
}
```

----------------------------------------

TITLE: Object Property Assignment and Access in JavaScript
DESCRIPTION: This code creates an object, assigns properties, and attempts to access an undefined property, resulting in TypeScript errors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentToVoidZero2.errors.txt#2025-04-21_snippet_1

LANGUAGE: JavaScript
CODE:
```
var o = {}
o.x = 1
o.y = void 0
o.x + o.y
```

----------------------------------------

TITLE: Private Parameter Properties in Anonymous Type Constructor Signatures
DESCRIPTION: Example showing invalid usage of private parameter properties in anonymous type constructor signatures. Parameter properties are not allowed in type declarations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters2.errors.txt#2025-04-21_snippet_6

LANGUAGE: typescript
CODE:
```
var b: {
    new (private x);
    new (private y);
}
```

----------------------------------------

TITLE: Demonstrating TypeScript Computed Property Name Errors in Class Definition
DESCRIPTION: This code snippet defines a class with computed property names, resulting in TypeScript errors. The errors occur because the computed property names use union types that are not compatible with the required types for computed properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames15_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
var p1: number | string;
var p2: number | number[];
var p3: string | boolean;
class C {
    [p1]() { }
    [p2]() { }
    [p3]() { }
}
```

----------------------------------------

TITLE: Invalid Parameter Property in Interface Constructor
DESCRIPTION: Demonstrates a TypeScript error case where a parameter property modifier (public) is incorrectly used in an interface constructor signature. Parameter properties are only allowed in actual class constructor implementations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ParameterList13.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
interface I {
    new (public x);
}
```

----------------------------------------

TITLE: Constructor Parameter Properties in Class Overloads in TypeScript
DESCRIPTION: Example showing invalid usage of parameter properties in constructor overloads. Parameter properties (with public/private modifiers) are only allowed in constructor implementations, not in overload signatures.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters2.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    constructor(public x, private y);
    constructor(public x, private y) { }
}
```

----------------------------------------

TITLE: Invalid Parameter Properties in Interface Constructor Signatures
DESCRIPTION: Demonstrates invalid usage of parameter properties with accessibility modifiers in interface constructor signatures. This produces TypeScript errors because parameter properties are only allowed in constructor implementations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
interface I {
    new (public x);
}

interface I2 {
    new (private x);
}
```

----------------------------------------

TITLE: Invalid Computed Property Name Using 'this' in TypeScript Class
DESCRIPTION: Example showing a TypeScript compilation error where 'this' is incorrectly used in a computed property name. The error occurs because 'this' cannot be referenced in computed property names as it's not available during property definition.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames21_ES6.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    bar() {
        return 0;
    }
    [this.bar()]() { }
}
```

----------------------------------------

TITLE: TypeScript Abstract Property Implementation Errors
DESCRIPTION: Demonstrates various error cases with abstract properties including missing implementations, type mismatches, and incorrect accessor declarations. Shows errors when trying to use abstract properties in non-abstract classes and attempting to modify readonly properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/abstractPropertyNegative.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
interface A {
    prop: string;
    m(): string;
}
abstract class B implements A {
    abstract prop: string;
    public abstract readonly ro: string;
    abstract get readonlyProp(): string;
    abstract m(): string;
    abstract get mismatch(): string;
    abstract set mismatch(val: number);
}
class C extends B {
    readonly ro = "readonly please";
    abstract notAllowed: string;
    get concreteWithNoBody(): string;
}
let c = new C();
c.ro = "error: lhs of assignment can't be readonly";

abstract class WrongTypeProperty {
    abstract num: number;
}
class WrongTypePropertyImpl extends WrongTypeProperty {
    num = "nope, wrong";
}
abstract class WrongTypeAccessor {
    abstract get num(): number;
}
class WrongTypeAccessorImpl extends WrongTypeAccessor {
    get num() { return "nope, wrong"; }
}
class WrongTypeAccessorImpl2 extends WrongTypeAccessor {
    num = "nope, wrong";
}

abstract class AbstractAccessorMismatch {
    abstract get p1(): string;
    set p1(val: string) { };
    get p2(): string { return "should work"; }
    abstract set p2(val: string);
}
```

----------------------------------------

TITLE: Invalid Parameter Properties in Object Literal Function Property - TypeScript
DESCRIPTION: This snippet shows an object literal property assigned a function definition attempting to use a typed `private` modifier on a parameter. Parameter properties are only allowed in class constructors, resulting in TypeScript error TS2369.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/callSignaturesWithAccessibilityModifiersOnParameters.errors.txt#_snippet_21

LANGUAGE: typescript
CODE:
```
function foo(x: number, private y: string) { }
```

----------------------------------------

TITLE: Invalid Parameter Properties in Object Literal Arrow Function Property - TypeScript
DESCRIPTION: This snippet shows an object literal property assigned an arrow function definition attempting to use typed `public` and `private` modifiers on parameters. Parameter properties are only allowed in class constructors, resulting in TypeScript error TS2369.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/callSignaturesWithAccessibilityModifiersOnParameters.errors.txt#_snippet_22

LANGUAGE: typescript
CODE:
```
<T>(public x: T, private y: T) => { }
```

----------------------------------------

TITLE: Invalid Generator Function Property with Type Parameter in TypeScript
DESCRIPTION: Example of invalid syntax when attempting to define a generator function property with generic type parameter. The compiler expects an identifier before the type parameter definition.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/FunctionPropertyAssignments6_es6.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
var v = { *<T>() { } }
```

----------------------------------------

TITLE: Invalid Abstract Property and Accessor Override in TypeScript
DESCRIPTION: Example showing two common TypeScript errors: initializing an abstract property (which is not allowed) and incorrectly overriding a property with a getter accessor. The abstract property 'p' in class A cannot have an initializer, and class B incorrectly tries to override it with a getter.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/accessorsOverrideProperty7.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
abstract class A {
    abstract p = 'yep'
}
class B extends A {
    get p() { return 'oh no' } // error
}
```

----------------------------------------

TITLE: Assignment Compatibility with Optional Source Properties in TypeScript
DESCRIPTION: This module demonstrates assignment compatibility when the source type has optional properties. It shows various scenarios of assigning objects with optional properties to targets with required properties, highlighting type errors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatWithObjectMembersOptionality.errors.txt#2025-04-21_snippet_2

LANGUAGE: TypeScript
CODE:
```
module SourceHasOptional {
    // targets
    interface C {
        opt: Base
    }
    var c: C;

    var a: { opt: Base; }
    var b = { opt: new Base() }

    // sources
    interface D {
        opt?: Base;
    }
    interface E {
        opt?: Derived;
    }
    interface F {
        opt: Derived;
    }
    var d: D;
    var e: E;
    var f: F;

    c = d; // error
    c = e; // error
    c = f; // ok
    c = a; // ok

    a = d; // error
    a = e; // error
    a = f; // ok
    a = c; // ok

    b = d; // error
    b = e; // error
    b = f; // ok
    b = a; // ok
    b = c; // ok
}
```

----------------------------------------

TITLE: Demonstrating TypeScript Computed Property Error
DESCRIPTION: This code snippet shows a TypeScript error where a computed property named with an empty string is not assignable to the string index type of the class. The error occurs because the property type 'Foo' is not compatible with the index signature type 'Foo2'.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames42_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
class Foo { x }
class Foo2 { x; y }

class C {
    [s: string]: Foo2;

    // Computed properties
    [""]: Foo;
}
```

----------------------------------------

TITLE: Type Assignment Error with Different Private Property Visibility
DESCRIPTION: Shows a type assignment error where type D has a private property not matching the private property constraints of type E
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatWithObjectMembersAccessibility.errors.txt#2025-04-21_snippet_3

LANGUAGE: typescript
CODE:
```
e = e;
```

----------------------------------------

TITLE: Invalid Computed Property Names in TypeScript Object Literal
DESCRIPTION: This code snippet demonstrates various invalid computed property names in a TypeScript object literal. The compiler throws errors for each invalid property name, stating that computed property names must be of type 'string', 'number', 'symbol', or 'any'.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames5_ES6.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
var b: boolean;
var v = {
    [b]: 0,
    [true]: 1,
    [[]]: 0,
    [{}]: 0,
    [undefined]: undefined,
    [null]: null
}
```

----------------------------------------

TITLE: Invalid Generator Function Property Assignment in TypeScript
DESCRIPTION: Shows an erroneous attempt to define a generator function as an object property using invalid syntax. The code is missing the required identifier (property name) after the generator asterisk, causing a TS1003 compilation error.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/FunctionPropertyAssignments3_es6.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
var v = { *{ } }
```

----------------------------------------

TITLE: Defining Interface with Public and Optional Properties - TypeScript
DESCRIPTION: This snippet defines an interface named 'interfaceWithPublicAndOptional' with a required public property 'one' and an optional public property 'two'. It also creates an instance of this interface named 'obj4' initialized with the 'one' property.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatability42.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
module __test1__ {
    export interface interfaceWithPublicAndOptional<T,U> { one: T; two?: U; };  var obj4: interfaceWithPublicAndOptional<number,string> = { one: 1 };;
    export var __val__obj4 = obj4;
}
```

----------------------------------------

TITLE: Defining a TypeScript Class with Private Members
DESCRIPTION: This snippet defines a TypeScript class 'C' with various private instance and static members, including properties, getters, setters, and methods.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classPropertyAsPrivate.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
    private x: string;
    private get y() { return null; }
    private set y(x) { }
    private foo() { }

    private static a: string;
    private static get b() { return null; }
    private static set b(x) { }
    private static foo() { }
}
```

----------------------------------------

TITLE: Type Predicate with Duplicate Properties in TypeScript
DESCRIPTION: This code snippet demonstrates a TypeScript function `addProp2` that uses a type predicate. The type predicate attempts to define a type with duplicate properties ('a'), leading to TypeScript compiler errors TS2300. The function's purpose is to highlight the error that occurs when redundant properties are defined within a type predicate's type definition.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkTypePredicateForRedundantProperties.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
function addProp2(x: any): x is { a: string; a: string; } {
        return true;
    }
```

----------------------------------------

TITLE: Invalid Computed Property Names in TypeScript
DESCRIPTION: Demonstrates a function overload pattern and object creation with computed property names. The code fails because one computed property uses boolean type which is not allowed - only string, number, symbol, or any types are valid for computed property names.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames9_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
function f(s: string): string;
function f(n: number): number;
function f<T>(x: T): T;
function f(x): any { }

var v = {
    [f("")]: 0,
    [f(0)]: 0,
    [f(true)]: 0
}
```

----------------------------------------

TITLE: Generic Object Property Function Assignment
DESCRIPTION: Tests assignment compatibility for functions with generic object parameters containing multiple properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatWithCallSignatures4.errors.txt#2025-04-21_snippet_6

LANGUAGE: typescript
CODE:
```
var b15: <T>(x: { a: T; b: T }) => T; 
a15 = b15;
b15 = a15;
```

----------------------------------------

TITLE: Creating Interface Instance with Partial Properties
DESCRIPTION: Demonstrates creating an instance of a generic interface with only the required property
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatability37.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
var obj4: interfaceWithPublicAndOptional<number,string> = { one: 1 };;
```

----------------------------------------

TITLE: Invalid Abstract Property Initialization in TypeScript
DESCRIPTION: Demonstrates a compilation error that occurs when trying to initialize an abstract property with a value. Abstract properties cannot have initializers as they are meant to be implemented by derived classes.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/abstractPropertyInitializer.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
abstract class C {
    abstract prop = 1
}
```

----------------------------------------

TITLE: Destructuring Assignment with Invalid Computed Property
DESCRIPTION: Example showing a TypeScript error when attempting to use a computed property [k] in an object literal during destructuring assignment. The error indicates that computed properties are not allowed in this context.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkDestructuringShorthandAssigment2.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
let o: any, k: any;
let { x } = { x: 1, ...o, [k]: 1 };
```

----------------------------------------

TITLE: Abstract Class Property Access Examples in TypeScript
DESCRIPTION: Demonstrates error cases when accessing abstract properties in constructors and valid usage patterns. Shows different scenarios including direct access, destructuring, and property assignments within abstract and concrete classes.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/abstractPropertyInConstructor.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
abstract class AbstractClass {
    constructor(str: string, other: AbstractClass) {
        this.method(parseInt(str));
        let val = this.prop.toLowerCase();

        if (!str) {
            this.prop = "Hello World";
        }
        this.cb(str);

        // OK, reference is inside function
        const innerFunction = () => {
            return this.prop;
        }

        // OK, references are to another instance
        other.cb(other.prop);
    }

    abstract prop: string;
    abstract cb: (s: string) => void;

    abstract method(num: number): void;

    other = this.prop;
    fn = () => this.prop;

    method2() {
        this.prop = this.prop + "!";
    }
}

abstract class DerivedAbstractClass extends AbstractClass {
    cb = (s: string) => {};

    constructor(str: string, other: AbstractClass, yetAnother: DerivedAbstractClass) {
        super(str, other);
        // there is no implementation of 'prop' in any base class
        this.cb(this.prop.toLowerCase());

        this.method(1);

        // OK, references are to another instance
        other.cb(other.prop);
        yetAnother.cb(yetAnother.prop);
    }
}

class Implementation extends DerivedAbstractClass {
    prop = "";
    cb = (s: string) => {};

    constructor(str: string, other: AbstractClass, yetAnother: DerivedAbstractClass) {
        super(str, other, yetAnother);
        this.cb(this.prop);
    }

    method(n: number) {
        this.cb(this.prop + n);
    }
}

class User {
    constructor(a: AbstractClass) {
        a.prop;
        a.cb("hi");
        a.method(12);
        a.method2();
    }
}

abstract class C1 {
    abstract x: string;
    abstract y: string;

    constructor() {
        let self = this;                // ok
        let { x, y: y1 } = this;        // error
        ({ x, y: y1, "y": y1 } = this); // error
    }
}

class C2 {
    x: string;
    y: string;

    constructor() {
        let self = this;                // ok
        let { x, y: y1 } = this;        // ok
        ({ x, y: y1, "y": y1 } = this); // ok
    }
}
```

----------------------------------------

TITLE: Invalid Parameter Properties in Object Type Constructor Signatures
DESCRIPTION: Shows invalid usage of parameter properties with accessibility modifiers in object type constructor signatures. This code produces TypeScript errors because parameter properties can only be used in actual class constructor implementations.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constructSignatureWithAccessibilityModifiersOnParameters.errors.txt#2025-04-21_snippet_2

LANGUAGE: typescript
CODE:
```
var a: {
    new (public x);
}

var b: {
    new (private x);
}
```

----------------------------------------

TITLE: Attempting to Access Private Properties in TypeScript
DESCRIPTION: This snippet shows attempts to access private properties and methods of class 'C' from outside the class. Each attempt results in a TypeScript compilation error, as private members are only accessible within the class they are defined in.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classWithPrivateProperty.errors.txt#2025-04-21_snippet_1

LANGUAGE: TypeScript
CODE:
```
var c = new C();
var r1: string = c.x;
var r2: string = c.a;
var r3: string = c.b;
var r4: string = c.c();
var r5: string = c.d();
var r6: string = C.e;
var r7: string = C.f();
var r8: string = C.g();
```

----------------------------------------

TITLE: Demonstrating Invalid Computed Property Names in TypeScript Class
DESCRIPTION: This code snippet shows a TypeScript class with multiple methods using invalid computed property names. Each invalid property name triggers a TS2464 error, indicating that computed property names must be of specific types.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames14_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
var b: boolean;
class C {
    [b]() {}
    static [true]() { }
    [[]]() { }
    static [{}]() { }
    [undefined]() { }
    static [null]() { }
}
```

----------------------------------------

TITLE: Creating Class with Private Properties in TypeScript
DESCRIPTION: This snippet defines a class 'classWithTwoPrivate' with two private properties. The constructor requires two parameters to initialize these private properties. This demonstrates how TypeScript enforces encapsulation through access modifiers.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatability41.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
module __test2__ {
        export        class classWithTwoPrivate<T,U> { constructor(private one: T, private two: U) {} }  var x6 = new classWithTwoPrivate(1, "a");;
        export var __val__x6 = x6;
    }
```

----------------------------------------

TITLE: Defining Class with Public and Private Properties - TypeScript
DESCRIPTION: This snippet defines a class named 'classWithPublicPrivate' which has a constructor accepting a public property 'one' and a private property 'two'. An instance of this class is created and assigned to the variable 'x7', which is then exported.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentCompatability42.errors.txt#2025-04-21_snippet_1

LANGUAGE: TypeScript
CODE:
```
module __test2__ {
        export     class classWithPublicPrivate<T,U> { constructor(public one: T, private two: U) {} }   var x7 = new classWithPublicPrivate(1, "a");;
        export var __val__x7 = x7;
    }
```

----------------------------------------

TITLE: Invalid Parameter Property in Constructor
DESCRIPTION: Example demonstrating an error when attempting to use the 'public' accessor on a parameter within a function type parameter of a constructor. Parameter properties can only be used directly on constructor parameters, not within function type definitions.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ParameterList6.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
  constructor(C: (public A) => any) {
  }
}
```

----------------------------------------

TITLE: Assigning to Generic Function Property in TypeScript
DESCRIPTION: Attempt to assign a function to a generic property of an object, resulting in a compilation error due to invalid left-hand side of the assignment.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/assignmentToInstantiationExpression.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
let obj: { fn?: <T>() => T } = {};
obj.fn<number> = () => 1234;
```

----------------------------------------

TITLE: Invalid Super Reference in Computed Property Name
DESCRIPTION: Demonstrates a TypeScript error where 'super' is used in a computed property name, which is not allowed. The error TS2466 indicates that 'super' cannot be referenced in computed property names.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames24_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class Base {
    bar() {
        return 0;
    }
}
class C extends Base {
    [super.bar()]() { }
}
```

----------------------------------------

TITLE: TypeScript Class with Invalid Public Property Declaration in Constructor
DESCRIPTION: Shows incorrect syntax where a public property is declared inside a constructor body instead of in the class body, resulting in a syntax error.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classUpdateTests.errors.txt#2025-04-21_snippet_13

LANGUAGE: typescript
CODE:
```
class O {
	constructor() {
		public p1 = 0; // ERROR
	}
}
```

----------------------------------------

TITLE: Demonstrating TypeScript Computed Property Name Errors in Class Definitions
DESCRIPTION: This code defines classes with computed property names that are causing type errors. The 'get1' and 'set1' properties of type 'Foo' are not assignable to the 'string' index type 'Foo2' in the base class.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames44_ES6.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
class Foo { x }
class Foo2 { x; y }

class C {
    [s: string]: Foo2;
    get ["get1"]() { return new Foo }
}

class D extends C {
    set ["set1"](p: Foo) { }
}
```

----------------------------------------

TITLE: Importing and Type Annotating Module Properties in JavaScript
DESCRIPTION: This snippet demonstrates importing properties from modules and annotating their types using JSDoc comments. It shows how to specify types for imported values in a JavaScript file.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkExportsObjectAssignProperty.errors.txt#2025-04-21_snippet_3

LANGUAGE: JavaScript
CODE:
```
/**
 * @type {number}
 */
const q = require("./mod1").thing;

/**
 * @type {string}
 */
const u = require("./mod2").thing;
```

----------------------------------------

TITLE: Property Override with Accessor Error in TypeScript
DESCRIPTION: Demonstrates a TypeScript error (TS2611) that occurs when attempting to override a regular property from a base class with getter/setter accessors in a derived class. The base class 'Animal' defines 'sound' as a property, while 'Lion' tries to override it with accessors.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/accessorsOverrideProperty3.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
declare class Animal {
    sound: string
}
class Lion extends Animal {
    _sound = 'grrr'
    get sound() { return this._sound } // error here
    set sound(val) { this._sound = val }
}
```

----------------------------------------

TITLE: Demonstrating TypeScript Error in Computed Property Names
DESCRIPTION: This snippet shows an object literal with a computed property name that results in a TypeScript error. The error indicates that the expression "" || 0 is always falsy, which is not a valid property name.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames46_ES6.errors.txt#2025-04-21_snippet_0

LANGUAGE: TypeScript
CODE:
```
var o = {
    ["" || 0]: 0
};
```

----------------------------------------

TITLE: Accessing Invalid Const Enum Property in TypeScript
DESCRIPTION: Shows invalid property access on a const enum that results in a compilation error. The code attempts to access property 'B' which is not defined in enum E that only contains 'A'.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/constEnumBadPropertyNames.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
const enum E { A }
var x = E["B"]
```

----------------------------------------

TITLE: Class Property Declaration with Function Type
DESCRIPTION: Example of class C1T5 with a function property declaration and implementation
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/contextualTyping.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
class C1T5 {
    foo: (i: number, s: string) => number = function(i) {
        return i;
    }
}
```

----------------------------------------

TITLE: Using Object Properties in TypeScript
DESCRIPTION: This snippet demonstrates accessing properties of an object 'p' that utilizes TypeScript's type inference. It illustrates safe property access for 'a' and 'b', while attempting to access 'd' results in an error since it is not defined in the object. This highlights TypeScript's behavior with inferred types and missing properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkJsdocSatisfiesTag10.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
    // Should be OK -- retain info that a is number and b is string
    let a = p.a.toFixed();
    let b = p.b.substring(1);
    
    // Should error even though 'd' is in 'Keys'
    let d = p.d;
              ~
!!! error TS2339: Property 'd' does not exist on type '{ a: number; b: string; x: number; }'.
```

----------------------------------------

TITLE: Invalid Class Property Declaration with const Keyword in TypeScript
DESCRIPTION: Shows an incorrect attempt to declare a static class property using the 'const' keyword, which is not allowed in TypeScript class members. Results in compiler error TS1248.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ClassDeclarationWithInvalidConstOnPropertyDeclaration.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class AtomicNumbers {
  static const H = 1;
}
```

----------------------------------------

TITLE: Declaring Ambient Class A in TypeScript
DESCRIPTION: This snippet declares an ambient class A with a getter for the length property, which returns a number. It is designed to demonstrate the syntax for ambient class declarations in TypeScript. No specific dependencies are required.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/ambientGetters.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
declare class A {
    get length() : number;
}
```

----------------------------------------

TITLE: TypeScript Class with Parameter Properties in Constructor
DESCRIPTION: Demonstrates parameter properties in TypeScript, where constructor parameters are automatically assigned to class properties using accessibility modifiers.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classUpdateTests.errors.txt#2025-04-21_snippet_2

LANGUAGE: typescript
CODE:
```
class C {
	constructor(public p1=0, private p2=0, p3=0) {}
}
```

----------------------------------------

TITLE: Demonstrating Private Property Access Errors in TypeScript
DESCRIPTION: This snippet shows attempts to access private members of class 'C' from outside the class, which results in TypeScript compiler errors. It illustrates the protection of private members in TypeScript.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classPropertyAsPrivate.errors.txt#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
var c: C;
// all errors
c.x;
c.y;
c.y = 1;
c.foo();

C.a;
C.b();
C.b = 1;
C.foo();
```

----------------------------------------

TITLE: Invalid Computed Property Names in TypeScript
DESCRIPTION: Demonstrates invalid usage of computed property names in an object literal where logical/bitwise operators are used. TypeScript requires computed property names to be of type 'string', 'number', 'symbol', or 'any'.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNamesDeclarationEmit6_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
var v = {
  [-1]: {},
  [+1]: {},
  [~1]: {},
  [!1]: {}
}
```

----------------------------------------

TITLE: Static Property Access Error in TypeScript Class Inheritance
DESCRIPTION: Example showing how TypeScript catches an error when trying to access a static property 'blah2' via super before it is assigned. The code demonstrates class inheritance with static initialization blocks and property access.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classFieldSuperAccessibleJs1.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class C {
  static blah1 = 123;
}
C.blah2 = 456;

class D extends C {
  static {
    console.log(super.blah1);
    console.log(super.blah2);
  }
}
```

----------------------------------------

TITLE: Handling TypeScript Errors for Import Properties - TypeScript
DESCRIPTION: This snippet details TypeScript errors encountered when trying to access and assign properties on an imported module, highlighting issues with non-existent and read-only properties.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkOtherObjectAssignProperty.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
// Error logs from TypeScript compiler
importer.js(3,5): error TS2339: Property 'other' does not exist on type 'typeof import("mod1")'.
importer.js(4,5): error TS2339: Property 'prop' does not exist on type 'typeof import("mod1")'.
importer.js(11,5): error TS2339: Property 'other' does not exist on type 'typeof import("mod1")'.
importer.js(12,5): error TS2339: Property 'prop' does not exist on type 'typeof import("mod1")'.
importer.js(13,5): error TS2540: Cannot assign to 'bad1' because it is a read-only property.
importer.js(14,5): error TS2540: Cannot assign to 'bad2' because it is a read-only property.
importer.js(15,5): error TS2540: Cannot assign to 'bad3' because it is a read-only property.
```

LANGUAGE: typescript
CODE:
```
// Code attempting to access and assign properties
const mod = require("./mod1");
mod.thing;
mod.other;
    ~~~~~
!!! error TS2339: Property 'other' does not exist on type 'typeof import("mod1")'.
mod.prop;
    ~~~~
!!! error TS2339: Property 'prop' does not exist on type 'typeof import("mod1")'.
mod.bad1;
mod.bad2;
mod.bad3;

mod.thing = 0;
mod.other = 0;
    ~~~~~
!!! error TS2339: Property 'other' does not exist on type 'typeof import("mod1")'.
mod.prop = 0;
    ~~~~
!!! error TS2339: Property 'prop' does not exist on type 'typeof import("mod1")'.
mod.bad1 = 0;
    ~~~~
!!! error TS2540: Cannot assign to 'bad1' because it is a read-only property.
mod.bad2 = 0;
    ~~~~
!!! error TS2540: Cannot assign to 'bad2' because it is a read-only property.
mod.bad3 = 0;
    ~~~~
!!! error TS2540: Cannot assign to 'bad3' because it is a read-only property.
```

----------------------------------------

TITLE: Demonstrating Getter Property Call Error in TypeScript
DESCRIPTION: Shows a common TypeScript error where a getter property is incorrectly called as a function. The code defines a class with a getter property that returns a number, and demonstrates the error when attempting to call the property as a method.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/accessorAccidentalCallDiagnostic.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
// https://github.com/microsoft/TypeScript/issues/24554
class Test24554 {
    get property(): number { return 1; }
}
function test24554(x: Test24554) {
    return x.property();
}
```

----------------------------------------

TITLE: Filtering Properties by Type in TypeScript
DESCRIPTION: Defines a utility type FilterPropsByType that extracts property keys of type TT from type T, and implements a select function using this type
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/conditionalTypeAssignabilityWhenDeferred.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
export type FilterPropsByType<T, TT> = {
  [K in keyof T]: T[K] extends TT ? K : never
}[keyof T];

function select<
  T extends string | number,
  TList extends object,
  TValueProp extends FilterPropsByType<TList, T>
>(property: T, list: TList[], valueProp: TValueProp) {}
```

----------------------------------------

TITLE: TypeScript Class with Incompatible Computed Property Type
DESCRIPTION: Demonstrates a type error where a computed property getter returns Foo type which is not assignable to the string index signature type Foo2. The error occurs because the getter's return type must match the index signature type.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/computedPropertyNames38_ES5.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
class Foo { x }
class Foo2 { x; y }

class C {
    [s: string]: Foo2;

    // Computed properties
    get [1 << 6]() { return new Foo }
    set [1 << 6](p: Foo2) { }
}
```

----------------------------------------

TITLE: Rendering UserName Component with Typo in Property
DESCRIPTION: This component demonstrates a TypeScript error where the property `NAme` is used instead of `Name`.  The error message TS2551 indicates that the property 'NAme' does not exist on type 'IUser', and suggests the correct property 'Name'.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/checkJsxChildrenProperty4.errors.txt#2025-04-21_snippet_3

LANGUAGE: tsx
CODE:
```
function UserName() {
    return (
        <FetchUser>
            { user => (
                <h1>{ user.NAme }</h1>
                           ~~~~
!!! error TS2551: Property 'NAme' does not exist on type 'IUser'. Did you mean 'Name'?
!!! related TS2728 file.tsx:4:5: 'Name' is declared here.
            ) }
        </FetchUser>
    );
}
```

----------------------------------------

TITLE: TypeScript Class with Property Reference Before Initialization Error
DESCRIPTION: This code snippet shows a TypeScript error (TS2729) that occurs when a property is referenced before its initialization. The class extends Foo, which is both a class and an interface that extends multiple bases (Bar, Baz), and tries to use the 'handleIntersection' property in the initialization of 'observer' before 'handleIntersection' is declared.
SOURCE: https://github.com/microsoft/typescript/blob/main/tests/baselines/reference/classMergedWithInterfaceMultipleBasesNoError.errors.txt#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
interface Bar { }
interface Baz { }
interface Q { }
interface Foo extends Bar, Baz { }
class Foo { }

export default class extends Foo {
    readonly observer = this.handleIntersection;
    readonly handleIntersection = () => { }
}
```