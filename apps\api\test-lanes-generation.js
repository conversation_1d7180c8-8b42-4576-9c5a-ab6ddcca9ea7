// Test Lanes Generation with Radar Distance Integration
// Validates that the operations service generates lanes with accurate distances
const fs = require('fs');
const path = require('path');

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          let value = valueParts.join('=');
          // Remove surrounding quotes if present
          if ((value.startsWith('"') && value.endsWith('"')) || 
              (value.startsWith("'") && value.endsWith("'"))) {
            value = value.slice(1, -1);
          }
          envVars[key] = value;
        }
      }
    });
    
    return envVars;
  } catch (error) {
    console.error('Error loading .env.local file:', error.message);
    return {};
  }
}

async function testLanesGeneration() {
  console.log('🛤️  TESTING LANES GENERATION WITH RADAR INTEGRATION');
  console.log('==================================================\n');
  
  try {
    // Load environment variables
    const envVars = loadEnvFile();
    
    // Set up environment for the API
    process.env.DATABASE_URL = envVars.DATABASE_URL;
    process.env.RADAR_SECRET_KEY = envVars.RADAR_SECRET_KEY;
    process.env.RADAR_PUBLISHABLE_KEY = envVars.RADAR_PUBLISHABLE_KEY;
    
    console.log('🔑 Configuration:');
    console.log(`- Database URL: ${process.env.DATABASE_URL ? 'Found' : 'Missing'}`);
    console.log(`- Radar Secret Key: ${process.env.RADAR_SECRET_KEY ? 'Found' : 'Missing'}`);
    console.log(`- Radar Publishable Key: ${process.env.RADAR_PUBLISHABLE_KEY ? 'Found' : 'Missing'}\n`);
    
    if (!process.env.DATABASE_URL || !process.env.RADAR_SECRET_KEY) {
      console.log('❌ Missing required environment variables');
      return;
    }
    
    // Dynamically import and test the services
    console.log('🏗️  Loading NestJS services...');
    
    // Mock the dependencies for testing
    const mockConfigService = {
      get: (key) => process.env[key]
    };
    
    const mockPrismaService = {
      load: {
        groupBy: async () => {
          // Mock some sample lane data for testing
          return [
            {
              originCity: 'Los Angeles',
              originState: 'CA',
              destinationCity: 'Houston',
              destinationState: 'TX',
              _count: { id: 5 },
              _max: { createdAt: new Date() }
            },
            {
              originCity: 'New York',
              originState: 'NY',
              destinationCity: 'Los Angeles',
              destinationState: 'CA',
              _count: { id: 3 },
              _max: { createdAt: new Date() }
            },
            {
              originCity: 'Chicago',
              originState: 'IL',
              destinationCity: 'Miami',
              destinationState: 'FL',
              _count: { id: 7 },
              _max: { createdAt: new Date() }
            }
          ];
        }
      }
    };
    
    // Import the RadarDistanceService
    const { RadarDistanceService } = await import('./dist/operations/services/radar-distance.service.js');
    
    console.log('✅ Services loaded successfully\n');
    
    // Test the RadarDistanceService directly
    console.log('🧪 Testing RadarDistanceService...');
    const radarService = new RadarDistanceService(mockConfigService);
    
    const testRoutes = [
      { from: 'Los Angeles, CA', to: 'Houston, TX', expected: 1550 },
      { from: 'New York, NY', to: 'Los Angeles, CA', expected: 2800 },
      { from: 'Chicago, IL', to: 'Miami, FL', expected: 1200 }
    ];
    
    console.log('📊 Testing distance calculations:');
    
    for (const route of testRoutes) {
      const [originCity, originState] = route.from.split(', ');
      const [destCity, destState] = route.to.split(', ');
      
      try {
        const result = await radarService.calculateDistance(originCity, originState, destCity, destState);
        
        const accuracy = Math.abs(result.distanceMiles - route.expected) / route.expected * 100;
        const accuracyPercentage = (100 - accuracy).toFixed(1);
        
        console.log(`   ${route.from} → ${route.to}`);
        console.log(`   Expected: ${route.expected} miles, Actual: ${result.distanceMiles} miles`);
        console.log(`   Accuracy: ${accuracyPercentage}%, Source: ${result.source}\n`);
        
      } catch (error) {
        console.log(`   ❌ Error testing ${route.from} → ${route.to}: ${error.message}\n`);
      }
    }
    
    console.log('🎯 LANES GENERATION SIMULATION:');
    console.log('================================');
    
    // Simulate what would happen in the actual getLanes() method
    const mockLanes = await mockPrismaService.load.groupBy();
    console.log(`✅ Found ${mockLanes.length} unique lane combinations from historical data\n`);
    
    const simulatedLanes = [];
    
    for (let index = 0; index < mockLanes.length; index++) {
      const rawLane = mockLanes[index];
      const frequency = rawLane._count.id;
      
      console.log(`Processing lane ${index + 1}/${mockLanes.length}: ${rawLane.originCity}, ${rawLane.originState} → ${rawLane.destinationCity}, ${rawLane.destinationState}`);
      
      try {
        const result = await radarService.calculateDistance(
          rawLane.originCity,
          rawLane.originState,
          rawLane.destinationCity,
          rawLane.destinationState
        );
        
        const estimatedDuration = Math.round((result.distanceMiles / 60) * 10) / 10; // 60 mph average
        const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 2), 1), 10);
        
        const lane = {
          id: `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`,
          originCity: rawLane.originCity,
          originState: rawLane.originState,
          destinationCity: rawLane.destinationCity,
          destinationState: rawLane.destinationState,
          estimatedMiles: result.distanceMiles,
          estimatedDuration: `${estimatedDuration} hours`,
          frequencyRank,
          lastUsed: rawLane._max.createdAt?.toISOString(),
          distanceSource: result.source
        };
        
        simulatedLanes.push(lane);
        
        console.log(`   ✅ Lane created: ${result.distanceMiles} miles (${result.source}), Frequency: ${frequencyRank}/10\n`);
        
      } catch (error) {
        console.log(`   ❌ Failed to create lane: ${error.message}\n`);
      }
    }
    
    console.log('📋 GENERATED LANES SUMMARY:');
    console.log('===========================');
    
    simulatedLanes.forEach((lane, index) => {
      console.log(`${index + 1}. ${lane.originCity}, ${lane.originState} → ${lane.destinationCity}, ${lane.destinationState}`);
      console.log(`   Distance: ${lane.estimatedMiles} miles`);
      console.log(`   Duration: ${lane.estimatedDuration}`);
      console.log(`   Frequency Rank: ${lane.frequencyRank}/10`);
      console.log(`   Distance Source: ${lane.distanceSource}`);
      console.log(`   Lane ID: ${lane.id}\n`);
    });
    
    const radarSources = simulatedLanes.filter(lane => lane.distanceSource === 'radar').length;
    const fallbackSources = simulatedLanes.filter(lane => lane.distanceSource === 'fallback').length;
    
    console.log('🎯 FINAL RESULTS:');
    console.log('=================');
    console.log(`✅ Total lanes generated: ${simulatedLanes.length}`);
    console.log(`📡 Using Radar API: ${radarSources} lanes`);
    console.log(`🔄 Using fallback: ${fallbackSources} lanes`);
    console.log(`📊 Success rate: ${((radarSources / simulatedLanes.length) * 100).toFixed(1)}%`);
    
    if (radarSources > 0) {
      console.log('\n✅ RADAR INTEGRATION SUCCESSFUL!');
      console.log('🚀 Lanes are being generated with commercial-grade distance accuracy');
    } else {
      console.log('\n⚠️  RADAR API NOT ACCESSIBLE');
      console.log('🔄 Lanes are using enhanced fallback distances');
      console.log('💡 Production system will work with or without Radar API');
    }
    
    return {
      totalLanes: simulatedLanes.length,
      radarLanes: radarSources,
      fallbackLanes: fallbackSources,
      successRate: (radarSources / simulatedLanes.length) * 100
    };
    
  } catch (error) {
    console.error('❌ Lanes generation test failed:', error.message);
    console.error('Full error:', error);
    return null;
  }
}

// Execute the test
testLanesGeneration().then(result => {
  if (result) {
    console.log('\n🎯 PHASE 2 INTEGRATION TEST COMPLETE');
    console.log('====================================');
    console.log('✅ RadarDistanceService integration validated');
    console.log('✅ Lanes generation working correctly');
    console.log('✅ Distance calculations operational');
    console.log('✅ Ready for production deployment');
  }
}).catch(error => {
  console.error('💥 Integration test failed:', error);
}); 