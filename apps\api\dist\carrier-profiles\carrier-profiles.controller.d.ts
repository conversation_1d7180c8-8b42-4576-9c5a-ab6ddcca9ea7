import { CarrierProfilesService } from './carrier-profiles.service';
import { CreateCarrierProfileDto, UpdateCarrierProfileDto, AdminUpdateCarrierProfileDto } from './dto';
import { AuthService } from '../auth/auth.service';
import { CarrierProfile } from '@repo/db';
import { AuthenticatedRequest } from '../auth/authenticated-request.interface';
export declare class CarrierProfilesController {
    private readonly carrierProfilesService;
    private readonly authService;
    private readonly logger;
    constructor(carrierProfilesService: CarrierProfilesService, authService: AuthService);
    create(createCarrierProfileDto: CreateCarrierProfileDto, req: AuthenticatedRequest): Promise<CarrierProfile>;
    findAll(): Promise<CarrierProfile[]>;
    findMyProfile(req: AuthenticatedRequest): Promise<CarrierProfile>;
    findOne(id: string): Promise<CarrierProfile>;
    updateMyProfile(updateCarrierProfileDto: UpdateCarrierProfileDto, req: AuthenticatedRequest): Promise<CarrierProfile>;
    update(id: string, updateCarrierProfileDto: AdminUpdateCarrierProfileDto): Promise<CarrierProfile>;
    refreshMyProfileFromAirtable(req: AuthenticatedRequest): Promise<CarrierProfile>;
    emergencyCreateMyProfile(req: AuthenticatedRequest): Promise<CarrierProfile>;
    remove(id: string): Promise<CarrierProfile>;
}
