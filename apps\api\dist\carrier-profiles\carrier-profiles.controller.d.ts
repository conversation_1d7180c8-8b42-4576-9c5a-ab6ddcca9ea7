import { CarrierProfilesService } from './carrier-profiles.service';
import { CreateCarrierProfileDto, UpdateCarrierProfileDto, AdminUpdateCarrierProfileDto } from './dto';
import { CarrierProfile } from '@repo/db';
import { AuthenticatedRequest } from '../auth/authenticated-request.interface';
export declare class CarrierProfilesController {
    private readonly carrierProfilesService;
    private readonly logger;
    constructor(carrierProfilesService: CarrierProfilesService);
    create(createCarrierProfileDto: CreateCarrierProfileDto, req: AuthenticatedRequest): Promise<CarrierProfile>;
    findAll(): Promise<CarrierProfile[]>;
    findMyProfile(req: AuthenticatedRequest): Promise<CarrierProfile>;
    findOne(id: string): Promise<CarrierProfile>;
    updateMyProfile(updateCarrierProfileDto: UpdateCarrierProfileDto, req: AuthenticatedRequest): Promise<CarrierProfile>;
    update(id: string, updateCarrierProfileDto: AdminUpdateCarrierProfileDto): Promise<CarrierProfile>;
    emergencyCreateMyProfile(req: AuthenticatedRequest): Promise<CarrierProfile>;
    remove(id: string): Promise<CarrierProfile>;
}
