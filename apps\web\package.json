{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3000", "prebuild": "cd ../../packages/db && pnpm exec prisma generate --schema=../../apps/api/prisma/schema.prisma", "build": "pnpm run prebuild && next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.201.1", "@opentelemetry/exporter-trace-otlp-http": "^0.201.1", "@opentelemetry/instrumentation": "^0.201.1", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-logs": "^0.201.1", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.34.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.0.7", "@repo/db": "workspace:*", "@repo/ui": "workspace:*", "@tailwindcss/cli": "^4.1.6", "@tanstack/react-table": "^8.21.3", "@vercel/blob": "^0.24.1", "@vercel/otel": "^1.12.0", "@vercel/related-projects": "^1.0.0", "airtable": "^0.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.509.0", "next": "^15.3.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.0", "react-hook-form": "^7.56.3", "sharp": "^0.33.5", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "zod": "^3.24.4"}, "devDependencies": {"@prisma/nextjs-monorepo-workaround-plugin": "^6.12.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.26.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.9", "typescript": "5.8.2"}}