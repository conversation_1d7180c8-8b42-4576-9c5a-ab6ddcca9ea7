const https = require('https');

console.log('🔴 CRITICAL SECURITY VULNERABILITY TEST');
console.log('Testing unauthorized load access prevention...\n');

// Test 1: Try to access loads without auth token (should fail)
console.log('TEST 1: No auth token');
const req1 = https.request({
  hostname: 'api.fcp-portal.com',
  port: 443,
  path: '/api/v1/airtable-orders/available',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
}, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log('✅ EXPECTED: 401 Unauthorized (No auth token)\n');
});

req1.on('error', (e) => {
  console.error(`Request error: ${e.message}\n`);
});

req1.end();

// Test 2: Try with valid JWT but no carrier profile (should fail with new guard)
console.log('TEST 2: Valid JWT but no carrier profile');
console.log('This test requires a fresh user token - manual verification needed');
console.log('Expected behavior: 401 Unauthorized with message about carrier profile required\n');

// Test 3: Verify admin users can still access
console.log('TEST 3: Admin user access');
console.log('This test requires admin token - manual verification needed');
console.log('Expected behavior: 200 OK (Admin bypass works)\n');

console.log('🔒 SECURITY TEST SUMMARY:');
console.log('1. Fresh users (no profile) should get 401 "Carrier profile required"');
console.log('2. Users without organization should get 401 "Organization assignment required"');
console.log('3. Users with incomplete profiles should get 401 "Incomplete carrier profile"');
console.log('4. Admin users should bypass all checks');
console.log('5. Complete carrier users should access organization-targeted loads only');

console.log('\n🚨 MANUAL VERIFICATION REQUIRED:');
console.log('1. Sign up as new user');
console.log('2. Try to access /org/[orgId]/loadboard');
console.log('3. Should redirect or show profile setup flow');
console.log('4. Should NOT see any loads until profile is complete'); 