import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  timestamp: Date;
  userId?: string;
  organizationId?: string;
}

export interface LoadNotificationData {
  loadId: string;
  airtableRecordId: string;
  originCity?: string;
  originState?: string;
  destinationCity?: string;
  destinationState?: string;
  rate?: number;
  equipmentRequired?: string;
  pickupDate?: Date;
  deliveryDate?: Date;
  status?: string;
  targetOrganizations?: string[];
  isPublic?: boolean;
}

export interface BidNotificationData {
  bidId: string;
  loadId: string;
  loadAirtableRecordId: string;
  carrierUserId: string;
  carrierCompanyName?: string;
  bidAmount: number;
  status: string;
  carrierNotes?: string;
}

export interface LoadStatusChangeData {
  loadId: string;
  airtableRecordId: string;
  oldStatus: string;
  newStatus: string;
  assignedCarrierUserId?: string;
  assignedCarrierCompanyName?: string;
}

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private readonly prisma: PrismaService,
  ) {}

  /**
   * Log new load posted to the loadboard
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastNewLoad(loadData: LoadNotificationData): Promise<void> {
    this.logger.log(`New load posted: ${loadData.loadId} - N8N handles notifications`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log load status changes
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastLoadStatusChange(statusData: LoadStatusChangeData): Promise<void> {
    this.logger.log(`Load status change: ${statusData.loadId} from ${statusData.oldStatus} to ${statusData.newStatus} - N8N handles notifications`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log bid submission updates
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastBidUpdate(bidData: BidNotificationData): Promise<void> {
    this.logger.log(`Bid update: ${bidData.bidId} status ${bidData.status} - N8N handles notifications`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log user notification
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastToUser(userId: string, notification: any): Promise<void> {
    this.logger.log(`User notification for ${userId}: ${notification.type} - N8N handles delivery`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log organization notification
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastToOrganization(organizationId: string, notification: Omit<NotificationData, 'id' | 'timestamp'>): Promise<void> {
    this.logger.log(`Organization notification for ${organizationId}: ${notification.type} - N8N handles delivery`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log booking confirmation
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastBookingConfirmation(loadId: string, carrierUserId: string, loadDetails: any): Promise<void> {
    this.logger.log(`Booking confirmation for load ${loadId} by carrier ${carrierUserId} - N8N handles notifications`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log load assignment
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastLoadAssignment(loadId: string, carrierUserId: string, loadDetails: any): Promise<void> {
    this.logger.log(`Load assignment: ${loadId} to carrier ${carrierUserId} - N8N handles notifications`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Log system announcement
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastSystemAnnouncement(title: string, message: string, data?: any): Promise<void> {
    this.logger.log(`System announcement: ${title} - N8N handles delivery`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Return mock connection stats since WebSocket is removed
   */
  getConnectionStats(): { connectedUsers: number } {
    // WebSocket removed - N8N handles all notifications
    return { connectedUsers: 0 };
  }

  /**
   * Return false since WebSocket connections are removed
   */
  isUserConnected(userId: string): boolean {
    // WebSocket removed - N8N handles all notifications
    return false;
  }

  /**
   * Log admin notification
   * Note: Real-time notifications now handled by N8N workflows via email/SMS
   */
  async broadcastToAdmins(notification: any): Promise<void> {
    this.logger.log(`Admin notification: ${notification.type} - N8N handles delivery`);
    
    // N8N workflows handle actual notifications via email/SMS
    // This method now serves as a logging/audit point
  }

  /**
   * Generate appropriate message for bid status
   */
  private getBidStatusMessage(status: string, bidAmount: number): string {
    switch (status) {
      case 'pending':
        return `Your bid of $${bidAmount.toLocaleString()} has been submitted and is pending review.`;
      case 'accepted':
        return `Congratulations! Your bid of $${bidAmount.toLocaleString()} has been accepted.`;
      case 'rejected':
        return `Your bid of $${bidAmount.toLocaleString()} was not accepted this time.`;
      case 'countered':
        return `Your bid of $${bidAmount.toLocaleString()} received a counter-offer.`;
      default:
        return `Your bid status has been updated to: ${status}`;
    }
  }
} 