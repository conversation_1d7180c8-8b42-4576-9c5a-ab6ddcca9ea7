import { Injectable, CanActivate, ExecutionContext, ForbiddenException, UnauthorizedException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from './auth.service';

@Injectable()
export class AdminGuard implements CanActivate {
  private readonly logger = new Logger(AdminGuard.name);

  constructor(
    private reflector: Reflector,
    private authService: AuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      // Extract JWT token from Authorization header
      const authorizationHeader = request.headers['authorization'];
      if (!authorizationHeader || !authorizationHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('Authorization token is required');
      }

      const token = authorizationHeader.substring(7);
      
      // Verify N8N JWT token
      const jwtPayload = await this.authService.verifyToken(token);
      
      // Validate user and get profile info
      const user = await this.authService.validateUser(jwtPayload);
      
      if (!user) {
        throw new ForbiddenException('User not found or invalid');
      }

      // Check if user has admin role
      if (!user.isAdmin) {
        throw new ForbiddenException('Admin access required');
      }

      // Attach user info to request for use in controllers
      request.user = {
        airtableUserId: user.airtableUserId,
        email: user.email,
        mcNumber: user.mcNumber,
        role: user.role,
        isAdmin: user.isAdmin,
      };
      
      this.logger.log(`Admin access granted to user ${user.email} (${user.airtableUserId})`);
      return true;

    } catch (error) {
      if (error instanceof ForbiddenException || error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error(`Authorization failed: ${error.message}`);
      throw new ForbiddenException('Authorization failed');
    }
  }
} 