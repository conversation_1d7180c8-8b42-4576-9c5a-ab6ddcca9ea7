generator client {
  provider      = "prisma-client-js"
  output        = "../../../packages/db/generated/client"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String          @id @default(cuid())
  airtableUserId String?         @unique // N8N/Airtable user ID
  email          String?
  firstName      String?
  lastName       String?
  role           Role            @default(CARRIER)
  mcNumber       String? // MC Number for carrier targeting
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  carrierProfile CarrierProfile?
  savedSearches  SavedSearch[]

  @@index([airtableUserId])
  @@index([mcNumber])
  @@index([email])
  @@index([role])
  @@map("users")
}

model UserProfile {
  airtableUserId     String   @id
  email              String   @unique
  firstName          String?
  lastName           String?
  companyName        String?
  mcNumber           String?
  dotNumber          String?
  role               String   @default("CARRIER")
  verificationStatus String   @default("PENDING")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now()) @updatedAt

  @@index([mcNumber])
  @@index([email])
  @@index([role])
  @@map("user_profiles")
}

model CachedLocation {
  id               String   @id @default(cuid())
  airtableRecordId String   @unique
  name             String?
  city             String
  state            String
  zipCode          String?
  address          String?
  lastSyncedAt     DateTime @default(now()) @map("last_synced_at")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  @@index([lastSyncedAt])
  @@index([city, state])
  @@map("cached_locations")
}

model CarrierProfile {
  id                 String   @id @default(cuid())
  userId             String   @unique @map("user_id")
  companyName        String?  @map("company_name")
  dotNumber          String?  @map("dot_number")
  mcNumber           String?  @map("mc_number")
  phoneNumber        String?  @map("phone_number")
  equipmentTypes     Json?    @map("equipment_types")
  serviceableRegions Json?    @map("serviceable_regions")
  isVerifiedByAdmin  Boolean  @default(false) @map("is_verified_by_admin")
  adminNotes         String?  @map("admin_notes")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  contact_email      String?  @unique
  contact_name       String?
  contact_phone      String?
  bids               Bid[]
  user               User     @relation(fields: [userId], references: [id])
  awardedLoads       Load[]   @relation("AwardedLoads")

  @@index([userId])
  @@index([companyName])
  @@index([contact_email])
  @@index([dotNumber])
  @@index([mcNumber])
  @@index([isVerifiedByAdmin])
  @@map("carrier_profiles")
}

model Load {
  id                        String          @id @default(cuid())
  airtableRecordId          String          @unique @map("airtable_record_id")
  originCity                String?         @map("origin_city")
  originState               String?         @map("origin_state")
  destinationCity           String?         @map("destination_city")
  destinationState          String?         @map("destination_state")
  pickupDateUtc             DateTime?       @map("pickup_date_utc")
  deliveryDateUtc           DateTime?       @map("delivery_date_utc")
  equipmentRequired         String?         @map("equipment_required")
  weightLbs                 Int?            @map("weight_lbs")
  rate                      Float?
  status                    LoadStatus?
  temperature               String?
  awardedToCarrierProfileId String?         @map("awarded_to_carrier_profile_id")
  rawAirtableData           Json?           @map("raw_airtable_data")
  createdAt                 DateTime        @default(now()) @map("created_at")
  updatedAt                 DateTime        @updatedAt @map("updated_at")
  proNumber                 String?         @map("pro_number")
  bolFileUrl                String?         @map("bol_file_url")
  bolUploadedAt             DateTime?       @map("bol_uploaded_at")
  invoiceFileUrl            String?         @map("invoice_file_url")
  invoiceUploadedAt         DateTime?       @map("invoice_uploaded_at")
  paidAt                    DateTime?       @map("paid_at")
  paymentAmount             Float?          @map("payment_amount")
  paymentDueDate            DateTime?       @map("payment_due_date")
  paymentNotes              String?         @map("payment_notes")
  paymentStatus             PaymentStatus?  @default(PENDING) @map("payment_status")
  podFileUrl                String?         @map("pod_file_url")
  podUploadedAt             DateTime?       @map("pod_uploaded_at")
  isPublic                  Boolean         @default(true) @map("is_public")
  isTargeted                Boolean         @default(false) @map("is_targeted")
  targetOrganizations       Json?           @map("target_organizations")
  carrier                   String?         @map("carrier")
  cases                     Int?            @map("cases")
  deliveryNumber            String?         @map("delivery_number")
  invStatus                 InvStatus?      @map("inv_status")
  pallets                   Int?            @map("pallets")
  pickupNumber              String?         @map("pickup_number")
  poNumber                  String?         @map("po_number")
  receiverAddress           String?         @map("receiver_address")
  receiverName              String?         @map("receiver_name")
  shipperAddress            String?         @map("shipper_address")
  soNumber                  String?         @map("so_number")
  bids                      Bid[]
  documents                 Document[]
  awardedToCarrierProfile   CarrierProfile? @relation("AwardedLoads", fields: [awardedToCarrierProfileId], references: [id])

  @@index([airtableRecordId])
  @@index([status])
  @@index([awardedToCarrierProfileId])
  @@index([isPublic])
  @@index([isTargeted])
  @@index([pickupDateUtc])
  @@index([deliveryDateUtc])
  @@index([originState])
  @@index([destinationState])
  @@index([equipmentRequired])
  @@index([status, isPublic])
  @@index([status, awardedToCarrierProfileId])
  @@index([paymentStatus])
  @@index([createdAt])
  @@map("loads")
}

model Bid {
  id                   String            @id @default(cuid())
  loadId               String            @map("load_id")
  carrierProfileId     String            @map("carrier_profile_id")
  bidAmount            Float             @map("bid_amount")
  carrierNotes         String?           @map("carrier_notes")
  status               String?
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")
  admin_notes          String?
  admin_response       AdminResponse     @default(PENDING)
  counter_offer_amount Float?
  expires_at           DateTime?
  negotiation_status   NegotiationStatus @default(OPEN)
  response_timestamp   DateTime?
  bid_responses        bid_responses[]
  carrierProfile       CarrierProfile    @relation(fields: [carrierProfileId], references: [id])
  load                 Load              @relation(fields: [loadId], references: [id])

  @@unique([loadId, carrierProfileId], name: "load_carrier_unique_bid")
  @@index([loadId])
  @@index([carrierProfileId])
  @@index([loadId, carrierProfileId])
  @@index([createdAt])
  @@index([carrierProfileId, admin_response])
  @@index([expires_at])
  @@index([loadId, admin_response])
  @@index([negotiation_status])
  @@index([response_timestamp])
  @@map("bids")
}

model SavedSearch {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  name      String
  criteria  Json
  isDefault Boolean  @default(false) @map("is_default")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, name], name: "user_search_name_unique")
  @@index([userId])
  @@index([userId, isDefault])
  @@index([createdAt])
  @@map("saved_searches")
}

model SystemSettings {
  id                           String   @id @default(cuid())
  platformName                 String?  @map("platform_name")
  supportEmail                 String?  @map("support_email")
  maintenanceMode              Boolean? @default(false) @map("maintenance_mode")
  autoAssignLoads              Boolean? @default(false) @map("auto_assign_loads")
  requireLoadApproval          Boolean? @default(true) @map("require_load_approval")
  maxLoadsPerCarrier           Int?     @default(10) @map("max_loads_per_carrier")
  loadExpirationHours          Int?     @default(72) @map("load_expiration_hours")
  requireInsuranceVerification Boolean? @default(true) @map("require_insurance_verification")
  requireDotVerification       Boolean? @default(true) @map("require_dot_verification")
  autoApproveCarriers          Boolean? @default(false) @map("auto_approve_carriers")
  verificationReminderDays     Int?     @default(30) @map("verification_reminder_days")
  enableEmailNotifications     Boolean? @default(true) @map("enable_email_notifications")
  enableSmsNotifications       Boolean? @default(false) @map("enable_sms_notifications")
  notificationFrequency        String?  @default("DAILY") @map("notification_frequency")
  requireTwoFactor             Boolean? @default(false) @map("require_two_factor")
  sessionTimeoutMinutes        Int?     @default(60) @map("session_timeout_minutes")
  maxLoginAttempts             Int?     @default(5) @map("max_login_attempts")
  passwordExpirationDays       Int?     @default(90) @map("password_expiration_days")
  defaultPaymentTerms          String?  @default("Net 30") @map("default_payment_terms")
  latePaymentFeePercent        Float?   @default(2.5) @map("late_payment_fee_percent")
  invoiceReminderDays          Int?     @default(7) @map("invoice_reminder_days")
  maxFileUploadSize            Int?     @default(10) @map("max_file_upload_size")
  rateLimitPerMinute           Int?     @default(100) @map("rate_limit_per_minute")
  enableLoadTracking           Boolean? @default(true) @map("enable_load_tracking")
  enableRealTimeUpdates        Boolean? @default(true) @map("enable_real_time_updates")
  enableAdvancedReporting      Boolean? @default(false) @map("enable_advanced_reporting")
  enableApiAccess              Boolean? @default(false) @map("enable_api_access")
  maintenanceWindowStart       String?  @default("02:00") @map("maintenance_window_start")
  maintenanceWindowEnd         String?  @default("04:00") @map("maintenance_window_end")
  backupFrequency              String?  @default("DAILY") @map("backup_frequency")
  createdAt                    DateTime @default(now()) @map("created_at")
  updatedAt                    DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}

model Document {
  id         String   @id @default(cuid())
  loadId     String   @map("load_id")
  filename   String
  url        String
  type       String
  size       Int
  uploadedAt DateTime @default(now()) @map("uploaded_at")
  load       Load     @relation(fields: [loadId], references: [id], onDelete: Cascade)

  @@index([loadId])
  @@index([uploadedAt])
  @@map("documents")
}

model DistanceCache {
  id                 String    @id @default(cuid())
  originAddress      String    @map("origin_address")
  destinationAddress String    @map("destination_address")
  distanceMiles      Float     @map("distance_miles")
  durationHours      Float     @map("duration_hours")
  calculatedBy       String    @map("calculated_by")
  createdAt          DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt          DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@unique([originAddress, destinationAddress])
  @@map("distance_cache")
}

model bid_responses {
  id            String          @id
  bid_id        String
  response_type BidResponseType
  amount        Float?
  notes         String?
  created_by    String
  created_at    DateTime        @default(now())
  bids          Bid             @relation(fields: [bid_id], references: [id], onDelete: Cascade)

  @@index([bid_id])
  @@index([created_at])
  @@index([created_by])
}

model OrderTemplate {
  id                 String   @id @default(cuid())
  name               String
  pickupLocationId   String   @map("pickup_location_id")
  deliveryLocationId String   @map("delivery_location_id")
  rate               Float
  daysToDelivery     Int      @map("days_to_delivery")
  createdBy          String   @map("created_by")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  @@index([createdBy])
  @@index([name])
  @@map("order_templates")
}

enum Role {
  CARRIER
  ADMIN
}

enum LoadStatus {
  NEW_ORDER       @map("New Order")
  AVAILABLE       @map("Available")
  ASSIGNED        @map("Assigned")
  INVOICED        @map("Invoiced")
  ON_HOLD         @map("On Hold")
  CANCELLED       @map("Cancelled")
  DELIVERED_EMPTY @map("Delivered / Empty")
}

enum PaymentStatus {
  PENDING
  PAID
  OVERDUE
}

enum InvStatus {
  NOT_SENT @map("Not Sent")
  SENT     @map("Sent")
  PAID     @map("Paid")
}

enum AdminResponse {
  PENDING
  ACCEPTED
  COUNTERED
  DECLINED
}

enum BidResponseType {
  INITIAL_BID
  ADMIN_RESPONSE
  CARRIER_RESPONSE
}

enum NegotiationStatus {
  OPEN
  CLOSED
  EXPIRED
}
