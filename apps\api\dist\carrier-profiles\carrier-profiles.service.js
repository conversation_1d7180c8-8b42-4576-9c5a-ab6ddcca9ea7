"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarrierProfilesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const db_1 = require("@repo/db");
let CarrierProfilesService = class CarrierProfilesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createCarrierProfileDto, airtableUserId) {
        let user = await this.prisma.user.findUnique({
            where: { airtableUserId: airtableUserId },
        });
        if (!user) {
            try {
                user = await this.prisma.user.create({
                    data: {
                        airtableUserId: airtableUserId,
                        email: createCarrierProfileDto.contact_email || `${airtableUserId}@placeholder.com`,
                        firstName: 'Unknown',
                        lastName: 'User',
                        role: db_1.Role.CARRIER,
                    },
                });
                console.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
            }
            catch (error) {
                throw new common_1.NotFoundException(`Unable to create User record for Airtable ID ${airtableUserId}: ${error.message}`);
            }
        }
        const existingProfile = await this.prisma.carrierProfile.findUnique({
            where: { userId: user.id },
        });
        if (existingProfile) {
            throw new common_1.ConflictException(`Carrier profile already exists for user ${user.id}`);
        }
        const data = {
            ...createCarrierProfileDto,
            user: {
                connect: { id: user.id },
            },
        };
        return this.prisma.carrierProfile.create({ data });
    }
    async findAll() {
        return this.prisma.carrierProfile.findMany();
    }
    async findOne(id) {
        const profile = await this.prisma.carrierProfile.findUnique({
            where: { id },
        });
        if (!profile) {
            throw new common_1.NotFoundException(`CarrierProfile with ID "${id}" not found`);
        }
        return profile;
    }
    async findOneByUserId(userId) {
        const profile = await this.prisma.carrierProfile.findUnique({
            where: { userId },
        });
        if (!profile) {
            throw new common_1.NotFoundException(`CarrierProfile for user ID "${userId}" not found`);
        }
        return profile;
    }
    async findMyProfileByAirtableUserId(airtableUserId) {
        let user = await this.prisma.user.findUnique({
            where: { airtableUserId: airtableUserId },
        });
        if (!user) {
            try {
                user = await this.prisma.user.create({
                    data: {
                        airtableUserId: airtableUserId,
                        email: `${airtableUserId}@placeholder.com`,
                        firstName: 'Unknown',
                        lastName: 'User',
                        role: db_1.Role.CARRIER,
                    },
                });
                console.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
            }
            catch (error) {
                throw new common_1.NotFoundException(`Unable to create User record for Airtable ID ${airtableUserId}: ${error.message}`);
            }
        }
        return this.findOneByUserId(user.id);
    }
    async update(id, updateCarrierProfileDto) {
        await this.findOne(id);
        const data = {
            ...updateCarrierProfileDto,
        };
        return this.prisma.carrierProfile.update({
            where: { id },
            data,
        });
    }
    async updateByUserId(userId, updateCarrierProfileDto) {
        const profile = await this.findOneByUserId(userId);
        const data = {
            ...updateCarrierProfileDto,
        };
        return this.prisma.carrierProfile.update({
            where: { id: profile.id },
            data,
        });
    }
    async updateMyProfileByAirtableUserId(airtableUserId, updateCarrierProfileDto) {
        let user = await this.prisma.user.findUnique({
            where: { airtableUserId: airtableUserId },
        });
        if (!user) {
            try {
                user = await this.prisma.user.create({
                    data: {
                        airtableUserId: airtableUserId,
                        email: updateCarrierProfileDto.contact_email || `${airtableUserId}@placeholder.com`,
                        firstName: 'Unknown',
                        lastName: 'User',
                        role: db_1.Role.CARRIER,
                    },
                });
                console.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
            }
            catch (error) {
                console.error(`Error creating User record for ${airtableUserId}:`, error);
                throw new common_1.NotFoundException(`Unable to create User record for Airtable ID ${airtableUserId}: ${error.message}`);
            }
        }
        let carrierProfile = await this.prisma.carrierProfile.findUnique({
            where: { userId: user.id },
        });
        if (!carrierProfile) {
            try {
                const createData = {
                    companyName: updateCarrierProfileDto.companyName || 'New Carrier',
                    user: {
                        connect: { id: user.id },
                    },
                };
                carrierProfile = await this.prisma.carrierProfile.create({ data: createData });
                console.log(`Created missing CarrierProfile for user: ${user.id}`);
            }
            catch (error) {
                console.error(`Error creating CarrierProfile for user ${user.id}:`, error);
                throw new common_1.NotFoundException(`Unable to create CarrierProfile for user ${user.id}: ${error.message}`);
            }
        }
        return this.updateByUserId(user.id, updateCarrierProfileDto);
    }
    async remove(id) {
        await this.findOne(id);
        return this.prisma.carrierProfile.delete({
            where: { id },
        });
    }
};
exports.CarrierProfilesService = CarrierProfilesService;
exports.CarrierProfilesService = CarrierProfilesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CarrierProfilesService);
//# sourceMappingURL=carrier-profiles.service.js.map