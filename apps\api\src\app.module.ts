import { Module, NestModule, MiddlewareConsumer, RequestMethod, Logger } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
// import { GlobalOptionsMiddleware } from './middleware/global-options.middleware'; // Commented out
import { PerformanceMiddleware } from './middleware/performance.middleware';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { CircuitBreakerService } from './common/services/circuit-breaker.service';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { CarrierProfilesModule } from './carrier-profiles/carrier-profiles.module';
import { AirtableOrdersModule } from './airtable-orders/airtable-orders.module';
import { AdminModule } from './admin/admin.module';
import { OperationsModule } from './operations/operations.module';
import { NotificationsModule } from './notifications/notifications.module';
import { BidsModule } from './bids/bids.module'; // Re-enabled with fixed implementation
import { join } from 'path';
import compression from 'compression';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: process.env.NODE_ENV === 'test' 
        ? join(__dirname, '..', '.env.test') 
        : join(__dirname, '..', '.env'),
    }),
    // Add caching module for performance
    CacheModule.register({
      isGlobal: true,
      ttl: 300000, // 5 minutes default TTL
      max: 100, // Maximum number of items in cache
    }),
    // Rate limiting with different limits for different environments
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: 60000, // 1 minute
        limit: process.env.NODE_ENV === 'production' ? 20 : 100, // 20 requests per minute in production
      },
      {
        name: 'medium', 
        ttl: 60000 * 10, // 10 minutes
        limit: process.env.NODE_ENV === 'production' ? 100 : 500, // 100 requests per 10 minutes in production
      },
      {
        name: 'long',
        ttl: 60000 * 60, // 1 hour  
        limit: process.env.NODE_ENV === 'production' ? 500 : 2000, // 500 requests per hour in production
      }
    ]),
    PrismaModule,
    AirtableOrdersModule,
    AuthModule,
    CarrierProfilesModule,
    AdminModule,
    OperationsModule,
    NotificationsModule,
    BidsModule, // Re-enabled with fixed implementation
  ],
  controllers: [AppController],
  providers: [
    AppService,
    CircuitBreakerService,
    // Apply throttling globally
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    // Apply structured logging globally
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    Logger,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Add compression middleware for all routes
    consumer.apply(compression()).forRoutes({ path: '*', method: RequestMethod.ALL });
    
    // Add performance monitoring middleware
    consumer.apply(PerformanceMiddleware).forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
