const fetch = require('node-fetch');

// Test configuration
const API_BASE_URL = 'http://localhost:3001'; // Adjust if your API runs on different port
const CLERK_USER_ID = 'user_2y03kIRH4nR6ZA2POA5xvnz3Xno';

// Mock JWT token for testing (you'll need a real token)
const TEST_TOKEN = 'YOUR_REAL_JWT_TOKEN_HERE';

async function testEmergencyProfileCreation() {
    console.log('🧪 Testing emergency CarrierProfile creation...');
    console.log(`👤 Testing for Clerk User ID: ${CLERK_USER_ID}`);
    
    try {
        // Test 1: Try to get existing profile (should trigger auto-creation)
        console.log('\n📋 Test 1: GET /api/v1/carrier-profiles/me');
        const getResponse = await fetch(`${API_BASE_URL}/api/v1/carrier-profiles/me`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`,
                'Content-Type': 'application/json',
            },
        });
        
        if (getResponse.ok) {
            const profile = await getResponse.json();
            console.log('✅ SUCCESS: Profile retrieved/created via GET /me');
            console.log(`   Profile ID: ${profile.id}`);
            console.log(`   Company Name: ${profile.companyName}`);
            return;
        } else {
            const error = await getResponse.text();
            console.log(`❌ GET /me failed: ${getResponse.status} - ${error}`);
        }

        // Test 2: Use emergency endpoint
        console.log('\n🚨 Test 2: POST /api/v1/carrier-profiles/emergency/create');
        const emergencyResponse = await fetch(`${API_BASE_URL}/api/v1/carrier-profiles/emergency/create`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`,
                'Content-Type': 'application/json',
            },
        });

        if (emergencyResponse.ok) {
            const profile = await emergencyResponse.json();
            console.log('✅ SUCCESS: Profile created via emergency endpoint');
            console.log(`   Profile ID: ${profile.id}`);
            console.log(`   Company Name: ${profile.companyName}`);
        } else {
            const error = await emergencyResponse.text();
            console.log(`❌ Emergency creation failed: ${emergencyResponse.status} - ${error}`);
        }

        // Test 3: Verify profile exists now
        console.log('\n✓ Test 3: Verify profile exists after creation');
        const verifyResponse = await fetch(`${API_BASE_URL}/api/v1/carrier-profiles/me`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`,
                'Content-Type': 'application/json',
            },
        });

        if (verifyResponse.ok) {
            const profile = await verifyResponse.json();
            console.log('✅ SUCCESS: Profile now accessible');
            console.log(`   Profile ID: ${profile.id}`);
            console.log(`   User ID: ${profile.userId}`);
        } else {
            const error = await verifyResponse.text();
            console.log(`❌ Verification failed: ${verifyResponse.status} - ${error}`);
        }

    } catch (error) {
        console.error('🔥 Test failed with error:', error.message);
    }
}

async function testWebhook() {
    console.log('\n🪝 Testing webhook endpoint...');
    
    const webhookPayload = {
        type: 'user.created',
        data: {
            id: CLERK_USER_ID,
            email_addresses: [{
                email_address: '<EMAIL>',
                id: 'email_123'
            }],
            first_name: 'Test',
            last_name: 'User',
            username: 'testuser'
        },
        object: 'event'
    };

    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/webhooks/clerk/user-created`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Webhook-Secret': process.env.CLERK_WEBHOOK_SECRET || 'test-secret',
            },
            body: JSON.stringify(webhookPayload),
        });

        if (response.ok) {
            const result = await response.json();
            console.log('✅ Webhook test successful:', result);
        } else {
            const error = await response.text();
            console.log(`❌ Webhook test failed: ${response.status} - ${error}`);
        }
    } catch (error) {
        console.error('🔥 Webhook test failed:', error.message);
    }
}

async function main() {
    console.log('🛠️ Carrier Portal - CarrierProfile Fix Test');
    console.log('==========================================');
    
    if (TEST_TOKEN === 'YOUR_REAL_JWT_TOKEN_HERE') {
        console.log('⚠️  WARNING: Please set a real JWT token in TEST_TOKEN variable');
        console.log('   You can get this from the browser dev tools when logged in');
        console.log('   Look for Authorization header in network requests');
    }
    
    await testEmergencyProfileCreation();
    await testWebhook();
    
    console.log('\n🏁 Test completed');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testEmergencyProfileCreation, testWebhook }; 