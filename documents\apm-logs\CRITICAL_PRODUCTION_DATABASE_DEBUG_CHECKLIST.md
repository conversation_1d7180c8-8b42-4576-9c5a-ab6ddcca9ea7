# CRITICAL PRODUCTION DATABASE DEBUG CHECKLIST

## 🚨 IMMEDIATE ACTIONS (Do these NOW)

### 1. **Check Database Server Status**
```bash
# Check if your database provider is having outages
# - Visit your database provider's status page
# - Check their Twitter/status updates
# - Look for maintenance windows
```

### 2. **Verify Vercel Environment Variables**
```bash
# In Vercel Dashboard:
1. Go to Project Settings → Environment Variables
2. Check DATABASE_URL is properly set for Production
3. Verify the connection string format:
   postgresql://username:password@host:port/database?sslmode=require
```

### 3. **Test Database Connection Directly**
```bash
# From your local machine, test if the database is reachable:
psql $DATABASE_URL -c "SELECT 1;"

# Or use any PostgreSQL client to connect with the same URL
```

### 4. **Check Vercel Function Logs**
```bash
# In Vercel Dashboard:
1. Go to Functions tab
2. Look for recent invocations
3. Check full error logs for additional details
```

## 🔧 DEBUGGING STEPS

### A. **Database Provider Specific Checks**

#### If using **PlanetScale**:
- Check connection count limits
- Verify branch is not paused
- Check for IP allowlist restrictions

#### If using **Supabase**:
- Check database is not paused
- Verify connection pooling settings
- Check for IP restrictions

#### If using **AWS RDS**:
- Check security groups allow Vercel IPs
- Verify database instance is running
- Check connection limits

#### If using **Railway/Render**:
- Check service status
- Verify database is not sleeping
- Check connection limits

### B. **Connection String Analysis**
```bash
# Your DATABASE_URL should look like:
********************************/dbname?sslmode=require&connection_limit=10

# Common issues:
- Missing sslmode=require
- No connection_limit specified (causes pool exhaustion)
- Wrong port (5432 for PostgreSQL)
- Special characters in password not URL encoded
```

### C. **Vercel-Specific Issues**
```bash
# Vercel serverless functions have limitations:
1. 30-second timeout (your functions are hitting this)
2. Cold starts can cause connection issues
3. No persistent connections between invocations
4. Need proper connection pooling
```

## 🛠️ IMMEDIATE FIXES TO TRY

### 1. **Add Connection Pool Parameters**
Update your DATABASE_URL in Vercel to include:
```
?sslmode=require&connection_limit=10&pool_timeout=20
```

### 2. **Enable Connection Pooling**
If using PlanetScale, Supabase, or similar, enable their connection pooling:
```
# PlanetScale: Use their connection pooling endpoint
# Supabase: Use the pooled connection string
# Others: Check their docs for pooled connections
```

### 3. **Update Prisma Configuration**
Add these to your Prisma client configuration:
```typescript
// In prisma.service.ts constructor
super({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  // Add these for serverless
  __internal: {
    engine: {
      maxConnections: 5,
      poolTimeout: 10
    }
  }
});
```

### 4. **Implement Connection Health Check**
Add a health check endpoint to test database connectivity:
```typescript
// Add to your API
@Get('/health/db')
async checkDatabase() {
  try {
    await this.prisma.$queryRaw`SELECT 1`;
    return { status: 'ok', database: 'connected' };
  } catch (error) {
    return { status: 'error', database: 'disconnected', error: error.message };
  }
}
```

## 📊 MONITORING & LOGS

### 1. **Check These Vercel Logs**
- Function execution logs
- Runtime errors
- Network timeouts
- Database connection attempts

### 2. **Database Provider Logs**
- Connection attempts
- Failed authentication
- Connection pool status
- Query performance

### 3. **Metrics to Monitor**
- Database connection count
- Function execution time
- Error rates
- Timeout frequency

## 🔥 EMERGENCY WORKAROUNDS

### 1. **Increase Function Timeout** (if possible)
```javascript
// In vercel.json
{
  "functions": {
    "apps/api/api/*.js": {
      "maxDuration": 60
    }
  }
}
```

### 2. **Implement Graceful Degradation**
```typescript
// Return cached data or basic responses when DB is down
@Get('/api/v1/auth/me')
async getMe() {
  try {
    // Try database connection with short timeout
    const user = await Promise.race([
      this.authService.findUser(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('DB timeout')), 5000)
      )
    ]);
    return user;
  } catch (error) {
    // Return minimal user data from JWT token instead
    return { 
      error: 'Database temporarily unavailable',
      degraded: true,
      user: this.extractUserFromToken(req)
    };
  }
}
```

### 3. **Database Connection Bypass** (temporary)
```typescript
// Temporarily disable database operations
if (process.env.DB_EMERGENCY_MODE === 'true') {
  return { error: 'Database maintenance mode' };
}
```

---
**Created**: ${new Date().toISOString()}  
**Severity**: CRITICAL - Production Down  
**Priority**: Fix database connectivity IMMEDIATELY 