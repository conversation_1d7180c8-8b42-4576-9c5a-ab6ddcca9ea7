import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AuthService } from '../auth/auth.service';
import { SystemSettingsDto, UpdateSystemSettingsDto } from './admin.controller';
import { Role } from '@prisma/client';
import { AdminResponse, NegotiationStatus, BidResponseType } from '@repo/db';
import { NotificationsService } from '../notifications/notifications.service';

// Define return types to avoid Prisma type inference issues
interface VerifyCarrierResponse {
  message: string;
  carrierProfile: {
    id: string;
    companyName: string | null;
    isVerifiedByAdmin: boolean;
  };
  success: boolean;
}

interface UserInfoResponse {
  clerkUserId?: string;
  user?: any;
  message?: string;
  error?: string;
  details?: string;
}

interface PromoteAdminResponse {
  message: string;
  user?: {
    id: string;
    email: string | null;
    firstName: string | null;
    lastName: string | null;
    role: string;
    createdAt: Date;
  };
  success: boolean;
  error?: string;
  details?: string;
}

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    private prisma: PrismaService,
    private authService: AuthService,
    private notificationsService?: NotificationsService  // Optional injection for notifications
  ) {}

  async getCurrentUserInfo(req: any): Promise<UserInfoResponse> {
    try {
      // Extract the authorization header
      const authorizationHeader = req.headers['authorization'];
      if (!authorizationHeader || !authorizationHeader.startsWith('Bearer ')) {
        return { error: 'No authorization token provided' };
      }

      const token = authorizationHeader.substring(7);
      const clerkUserId = await this.authService.verifyToken(token);
      
      if (!clerkUserId) {
        return { error: 'Invalid authorization token' };
      }

      // Find the user in our database
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: clerkUserId.toString() },
        include: {
          carrierProfile: {
            select: {
              id: true,
              companyName: true,
              isVerifiedByAdmin: true,
            },
          },
        },
      });

      return {
        clerkUserId: clerkUserId.toString(),
        user,
        message: user ? 'User found in database' : 'User not found in database',
      };
    } catch (error) {
      return {
        error: 'Failed to get user info',
        details: error.message,
      };
    }
  }

  async promoteFirstAdmin(): Promise<PromoteAdminResponse> {
    try {
      // Find the first user in the database
      const firstUser = await this.prisma.user.findFirst({
        orderBy: { createdAt: 'asc' },
        select: { id: true, email: true, firstName: true, lastName: true, role: true, createdAt: true },
      });

      if (!firstUser) {
        return {
          message: 'No users found in the database',
          success: false,
        };
      }

      if (firstUser.role === 'ADMIN') {
        return {
          message: `User ${firstUser.email} is already an admin`,
          user: firstUser,
          success: true,
        };
      }

      // Update the first user to admin
      const updatedUser = await this.prisma.user.update({
        where: { id: firstUser.id },
        data: { role: 'ADMIN' },
        select: { id: true, email: true, firstName: true, lastName: true, role: true, createdAt: true },
      });

      return {
        message: `Successfully promoted ${updatedUser.email} to admin`,
        user: updatedUser,
        success: true,
      };
    } catch (error) {
      return {
        message: 'Failed to promote first user to admin',
        error: 'Failed to promote first user to admin',
        details: error.message,
        success: false,
      };
    }
  }

  async getAllUsers() {
    try {
      const users = await this.prisma.user.findMany({
        include: {
          carrierProfile: {
            select: {
              id: true,
              companyName: true,
              isVerifiedByAdmin: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return users;
    } catch (error) {
      throw new BadRequestException('Failed to fetch users');
    }
  }

  async verifyCarrier(userId: string, isVerified: boolean): Promise<VerifyCarrierResponse> {
    try {
      // Check if user exists and has a carrier profile
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          carrierProfile: true,
        },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      if (!user.carrierProfile) {
        throw new BadRequestException('User does not have a carrier profile');
      }

      if (user.role !== 'CARRIER') {
        throw new BadRequestException('User is not a carrier');
      }

      // Update carrier profile verification status
      const updatedCarrierProfile = await this.prisma.carrierProfile.update({
        where: { id: user.carrierProfile.id },
        data: { isVerifiedByAdmin: isVerified },
        select: {
          id: true,
          companyName: true,
          isVerifiedByAdmin: true,
        },
      });

      return {
        message: `Carrier ${isVerified ? 'verified' : 'unverified'} successfully`,
        carrierProfile: updatedCarrierProfile,
        success: true,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to update carrier verification status');
    }
  }

  async updateUserRole(userId: string, role: 'CARRIER' | 'ADMIN') {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true, email: true, firstName: true, lastName: true },
      });

      if (!existingUser) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Validate role
      if (!['CARRIER', 'ADMIN'].includes(role)) {
        throw new BadRequestException('Invalid role. Must be CARRIER or ADMIN');
      }

      // If role is the same, no need to update
      if (existingUser.role === role) {
        return {
          message: `User role is already ${role}`,
          user: existingUser,
        };
      }

      // Update user role
      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: { role },
        include: {
          carrierProfile: {
            select: {
              id: true,
              companyName: true,
              isVerifiedByAdmin: true,
            },
          },
        },
      });

      return {
        message: `User role updated from ${existingUser.role} to ${role}`,
        user: updatedUser,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to update user role');
    }
  }

  async getSystemSettings(): Promise<SystemSettingsDto> {
    const settings = await this.prisma.systemSettings.findFirst();
    
    if (!settings) {
      // Return default settings if none exist
      return this.getDefaultSettings();
    }

    return {
      platformName: settings.platformName ?? undefined,
      supportEmail: settings.supportEmail ?? undefined,
      maintenanceMode: settings.maintenanceMode ?? undefined,
      // Load Management
      autoAssignLoads: settings.autoAssignLoads ?? undefined,
      requireLoadApproval: settings.requireLoadApproval ?? undefined,
      maxLoadsPerCarrier: settings.maxLoadsPerCarrier ?? undefined,
      loadExpirationHours: settings.loadExpirationHours ?? undefined,
      // Carrier Verification
      requireInsuranceVerification: settings.requireInsuranceVerification ?? undefined,
      requireDotVerification: settings.requireDotVerification ?? undefined,
      autoApproveCarriers: settings.autoApproveCarriers ?? undefined,
      verificationReminderDays: settings.verificationReminderDays ?? undefined,
      // Communications
      enableEmailNotifications: settings.enableEmailNotifications ?? undefined,
      enableSmsNotifications: settings.enableSmsNotifications ?? undefined,
      notificationFrequency: settings.notificationFrequency as 'REAL_TIME' | 'HOURLY' | 'DAILY' | undefined,
      // Security
      requireTwoFactor: settings.requireTwoFactor ?? undefined,
      sessionTimeoutMinutes: settings.sessionTimeoutMinutes ?? undefined,
      maxLoginAttempts: settings.maxLoginAttempts ?? undefined,
      passwordExpirationDays: settings.passwordExpirationDays ?? undefined,
      // Billing
      defaultPaymentTerms: settings.defaultPaymentTerms ?? undefined,
      latePaymentFeePercent: settings.latePaymentFeePercent ?? undefined,
      invoiceReminderDays: settings.invoiceReminderDays ?? undefined,
      // System Limits
      maxFileUploadSize: settings.maxFileUploadSize ?? undefined,
      rateLimitPerMinute: settings.rateLimitPerMinute ?? undefined,
      // Feature Flags
      enableLoadTracking: settings.enableLoadTracking ?? undefined,
      enableRealTimeUpdates: settings.enableRealTimeUpdates ?? undefined,
      enableAdvancedReporting: settings.enableAdvancedReporting ?? undefined,
      enableApiAccess: settings.enableApiAccess ?? undefined,
      // Maintenance
      maintenanceWindowStart: settings.maintenanceWindowStart ?? undefined,
      maintenanceWindowEnd: settings.maintenanceWindowEnd ?? undefined,
      backupFrequency: settings.backupFrequency as 'HOURLY' | 'DAILY' | 'WEEKLY' | undefined,
    };
  }

  async updateSystemSettings(updateData: UpdateSystemSettingsDto): Promise<SystemSettingsDto> {
    // Check if settings record exists
    const existingSettings = await this.prisma.systemSettings.findFirst();

    let updatedSettings;
    if (existingSettings) {
      // Update existing settings
      updatedSettings = await this.prisma.systemSettings.update({
        where: { id: existingSettings.id },
        data: updateData,
      });
    } else {
      // Create new settings record with defaults + updates
      const defaultSettings = this.getDefaultSettings();
      updatedSettings = await this.prisma.systemSettings.create({
        data: {
          ...defaultSettings,
          ...updateData,
        },
      });
    }

    return this.convertToDto(updatedSettings);
  }

  async resetSystemSettings(): Promise<SystemSettingsDto> {
    const defaultSettings = this.getDefaultSettings();
    
    // Delete existing settings if any
    await this.prisma.systemSettings.deleteMany({});
    
    // Create fresh settings with defaults
    const newSettings = await this.prisma.systemSettings.create({
      data: defaultSettings,
    });

    return this.convertToDto(newSettings);
  }

  private getDefaultSettings(): SystemSettingsDto {
    return {
      // General Settings
      platformName: 'Carrier Portal',
      supportEmail: '<EMAIL>',
      maintenanceMode: false,
      
      // Load Management Settings
      autoAssignLoads: false,
      requireLoadApproval: true,
      maxLoadsPerCarrier: 10,
      loadExpirationHours: 72,
      
      // Carrier Verification Settings
      requireInsuranceVerification: true,
      requireDotVerification: true,
      autoApproveCarriers: false,
      verificationReminderDays: 30,
      
      // Communication Settings
      enableEmailNotifications: true,
      enableSmsNotifications: false,
      notificationFrequency: 'DAILY',
      
      // Security Settings
      requireTwoFactor: false,
      sessionTimeoutMinutes: 60,
      maxLoginAttempts: 5,
      passwordExpirationDays: 90,
      
      // Billing Settings
      defaultPaymentTerms: 'Net 30',
      latePaymentFeePercent: 2.5,
      invoiceReminderDays: 7,
      
      // System Limits
      maxFileUploadSize: 10, // MB
      rateLimitPerMinute: 100,
      
      // Feature Flags
      enableLoadTracking: true,
      enableRealTimeUpdates: true,
      enableAdvancedReporting: false,
      enableApiAccess: false,
      
      // Maintenance Settings
      maintenanceWindowStart: '02:00',
      maintenanceWindowEnd: '04:00',
      backupFrequency: 'DAILY',
    };
  }

  private convertToDto(settings: any): SystemSettingsDto {
    return {
      platformName: settings.platformName,
      supportEmail: settings.supportEmail,
      maintenanceMode: settings.maintenanceMode,
      autoAssignLoads: settings.autoAssignLoads,
      requireLoadApproval: settings.requireLoadApproval,
      maxLoadsPerCarrier: settings.maxLoadsPerCarrier,
      loadExpirationHours: settings.loadExpirationHours,
      requireInsuranceVerification: settings.requireInsuranceVerification,
      requireDotVerification: settings.requireDotVerification,
      autoApproveCarriers: settings.autoApproveCarriers,
      verificationReminderDays: settings.verificationReminderDays,
      enableEmailNotifications: settings.enableEmailNotifications,
      enableSmsNotifications: settings.enableSmsNotifications,
      notificationFrequency: settings.notificationFrequency,
      requireTwoFactor: settings.requireTwoFactor,
      sessionTimeoutMinutes: settings.sessionTimeoutMinutes,
      maxLoginAttempts: settings.maxLoginAttempts,
      passwordExpirationDays: settings.passwordExpirationDays,
      defaultPaymentTerms: settings.defaultPaymentTerms,
      latePaymentFeePercent: settings.latePaymentFeePercent,
      invoiceReminderDays: settings.invoiceReminderDays,
      maxFileUploadSize: settings.maxFileUploadSize,
      rateLimitPerMinute: settings.rateLimitPerMinute,
      enableLoadTracking: settings.enableLoadTracking,
      enableRealTimeUpdates: settings.enableRealTimeUpdates,
      enableAdvancedReporting: settings.enableAdvancedReporting,
      enableApiAccess: settings.enableApiAccess,
      maintenanceWindowStart: settings.maintenanceWindowStart,
      maintenanceWindowEnd: settings.maintenanceWindowEnd,
      backupFrequency: settings.backupFrequency as 'HOURLY' | 'DAILY' | 'WEEKLY',
    };
  }

  // ===== BIDDING MANAGEMENT METHODS =====

  /**
   * Get all pending bids for admin review
   */
  async getPendingBids(): Promise<any[]> {
    try {
      const pendingBids = await this.prisma.bid.findMany({
        where: {
          admin_response: AdminResponse.PENDING,
          negotiation_status: NegotiationStatus.OPEN
        },
        include: {
          load: {
            select: {
              id: true,
              airtableRecordId: true,
              originCity: true,
              originState: true,
              destinationCity: true,
              destinationState: true,
              pickupDateUtc: true,
              deliveryDateUtc: true,
              equipmentRequired: true,
              rate: true,
              status: true
            }
          },
          carrierProfile: {
            select: {
              id: true,
              companyName: true,
              mcNumber: true,
              dotNumber: true,
              contact_name: true,
              contact_email: true,
              contact_phone: true
            }
          }
        },
        orderBy: [
          { expires_at: 'asc' },  // Most urgent first
          { createdAt: 'desc' }   // Newest first within same expiry
        ]
      });

      // Calculate time remaining for each bid
      const bidsWithTimeRemaining = pendingBids.map(bid => ({
        ...bid,
        timeRemaining: bid.expires_at ? this.calculateTimeRemaining(bid.expires_at) : null,
        isExpiringSoon: bid.expires_at ? this.isExpiringSoon(bid.expires_at) : false
      }));

      this.logger.log(`Retrieved ${bidsWithTimeRemaining.length} pending bids for admin review`);
      return bidsWithTimeRemaining;

    } catch (error) {
      this.logger.error(`Failed to retrieve pending bids: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve pending bids');
    }
  }

  /**
   * Get all bids with advanced filtering
   */
  async getAllBidsWithFilters(filters: {
    status?: string;
    carrierId?: string;
    loadId?: string;
    dateFrom?: Date;
    dateTo?: Date;
    page: number;
    limit: number;
  }): Promise<{ bids: any[]; totalCount: number; page: number; pageSize: number }> {
    try {
      const whereConditions: any = {};

      // Apply filters
      if (filters.status) {
        whereConditions.admin_response = filters.status.toUpperCase();
      }
      if (filters.carrierId) {
        whereConditions.carrierProfileId = filters.carrierId;
      }
      if (filters.loadId) {
        whereConditions.loadId = filters.loadId;
      }
      if (filters.dateFrom || filters.dateTo) {
        whereConditions.createdAt = {};
        if (filters.dateFrom) {
          whereConditions.createdAt.gte = filters.dateFrom;
        }
        if (filters.dateTo) {
          whereConditions.createdAt.lte = filters.dateTo;
        }
      }

      // Get total count for pagination
      const totalCount = await this.prisma.bid.count({
        where: whereConditions
      });

      // Get paginated results
      const bids = await this.prisma.bid.findMany({
        where: whereConditions,
        include: {
          load: {
            select: {
              id: true,
              airtableRecordId: true,
              originCity: true,
              originState: true,
              destinationCity: true,
              destinationState: true,
              pickupDateUtc: true,
              deliveryDateUtc: true,
              equipmentRequired: true,
              rate: true,
              status: true
            }
          },
          carrierProfile: {
            select: {
              id: true,
              companyName: true,
              mcNumber: true,
              dotNumber: true,
              contact_name: true,
              contact_email: true,
              contact_phone: true
            }
          },
          bid_responses: {
            orderBy: { created_at: 'desc' },
            take: 5  // Latest 5 responses for quick reference
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit
      });

      this.logger.log(`Retrieved ${bids.length} bids with filters for admin (page ${filters.page})`);
      
      return {
        bids,
        totalCount,
        page: filters.page,
        pageSize: filters.limit
      };

    } catch (error) {
      this.logger.error(`Failed to retrieve filtered bids: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve bids');
    }
  }

  /**
   * Respond to a bid (accept, counter, decline)
   */
  async respondToBid(
    bidId: string, 
    responseDto: { response: 'accepted' | 'countered' | 'declined'; counterOfferAmount?: number; notes?: string }, 
    adminUserId: string
  ): Promise<any> {
    try {
      // Validate the bid exists and is in a valid state
      const existingBid = await this.prisma.bid.findUnique({
        where: { id: bidId },
        include: {
          load: true,
          carrierProfile: {
            include: {
              user: true
            }
          }
        }
      });

      if (!existingBid) {
        throw new NotFoundException(`Bid ${bidId} not found`);
      }

      // Validate bid is still open for responses
      if (existingBid.negotiation_status !== NegotiationStatus.OPEN) {
        throw new BadRequestException(`Bid ${bidId} is no longer open for responses (status: ${existingBid.negotiation_status})`);
      }

      // Validate counter offer amount if countering
      if (responseDto.response === 'countered' && (!responseDto.counterOfferAmount || responseDto.counterOfferAmount <= 0)) {
        throw new BadRequestException('Counter offer amount is required and must be greater than 0 when countering a bid');
      }

      // Determine new negotiation status based on response
      let newNegotiationStatus: NegotiationStatus;
      let newAdminResponse: AdminResponse;
      
      switch (responseDto.response) {
        case 'accepted':
          newNegotiationStatus = NegotiationStatus.CLOSED;
          newAdminResponse = AdminResponse.ACCEPTED;
          break;
        case 'countered':
          newNegotiationStatus = NegotiationStatus.OPEN; // Still open for carrier response
          newAdminResponse = AdminResponse.COUNTERED;
          break;
        case 'declined':
          newNegotiationStatus = NegotiationStatus.CLOSED;
          newAdminResponse = AdminResponse.DECLINED;
          break;
      }

      // Use transaction to ensure data consistency
      const result = await this.prisma.$transaction(async (tx) => {
        // Update the bid
        const updatedBid = await tx.bid.update({
          where: { id: bidId },
          data: {
            admin_response: newAdminResponse,
            counter_offer_amount: responseDto.counterOfferAmount || null,
            response_timestamp: new Date(),
            negotiation_status: newNegotiationStatus,
            admin_notes: responseDto.notes || null
          },
          include: {
            load: true,
            carrierProfile: {
              include: {
                user: true
              }
            }
          }
        });

        // Create bid response record for history
        await tx.bid_responses.create({
          data: {
            id: `response_${bidId}_${Date.now()}`, // Generate unique ID
            bid_id: bidId,
            response_type: BidResponseType.ADMIN_RESPONSE,
            amount: responseDto.counterOfferAmount || null,
            notes: responseDto.notes || null,
            created_by: adminUserId
          }
        });

        // If bid is accepted, handle load assignment
        if (responseDto.response === 'accepted') {
          await tx.load.update({
            where: { id: existingBid.loadId },
            data: {
              awardedToCarrierProfileId: existingBid.carrierProfileId,
              status: 'ASSIGNED'
            }
          });

          // Close any other pending bids on this load
          await tx.bid.updateMany({
            where: {
              loadId: existingBid.loadId,
              id: { not: bidId },
              negotiation_status: NegotiationStatus.OPEN
            },
            data: {
              negotiation_status: NegotiationStatus.CLOSED,
              admin_response: AdminResponse.DECLINED,
              response_timestamp: new Date(),
              admin_notes: 'Load awarded to another carrier'
            }
          });
        }

        return updatedBid;
      });

      // Skip email notifications - N8N handles email communication
      // Real-time notifications for admin dashboard only (no emails)
      await this.sendAdminDashboardNotifications(result, responseDto.response, adminUserId);

      this.logger.log(`Admin ${adminUserId} responded to bid ${bidId} with: ${responseDto.response}`);
      
      return {
        success: true,
        message: `Bid ${responseDto.response} successfully`,
        bid: result,
        response: responseDto.response,
        counterOfferAmount: responseDto.counterOfferAmount
      };

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to respond to bid ${bidId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to process bid response');
    }
  }

  /**
   * Get complete bid negotiation history
   */
  async getBidHistory(bidId: string): Promise<any[]> {
    try {
      const bid = await this.prisma.bid.findUnique({
        where: { id: bidId },
        include: {
          load: {
            select: {
              airtableRecordId: true,
              originCity: true,
              originState: true,
              destinationCity: true,
              destinationState: true
            }
          },
          carrierProfile: {
            select: {
              companyName: true,
              mcNumber: true
            }
          }
        }
      });

      if (!bid) {
        throw new NotFoundException(`Bid ${bidId} not found`);
      }

      const bidResponses = await this.prisma.bid_responses.findMany({
        where: { bid_id: bidId },
        orderBy: { created_at: 'asc' }
      });

      // Build complete history including initial bid
      const history = [
        {
          id: `initial_${bidId}`,
          type: 'INITIAL_BID',
          amount: bid.bidAmount,
          notes: bid.carrierNotes,
          created_at: bid.createdAt,
          created_by: bid.carrierProfile.companyName,
          created_by_type: 'carrier'
        },
        ...bidResponses.map(response => ({
          ...response,
          created_by_type: response.response_type === BidResponseType.ADMIN_RESPONSE ? 'admin' : 'carrier'
        }))
      ];

      this.logger.log(`Retrieved ${history.length} history entries for bid ${bidId}`);
      return history;

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to retrieve bid history for ${bidId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve bid history');
    }
  }

  /**
   * Get bidding statistics for admin dashboard
   */
  async getBiddingStatistics(): Promise<any> {
    try {
      const [
        totalBids,
        pendingBids,
        acceptedBids,
        declinedBids,
        counteredBids,
        expiredBids,
        averageBidAmount,
        bidsLast24Hours,
        bidsLast7Days,
        topCarriers
      ] = await Promise.all([
        // Total bids
        this.prisma.bid.count(),
        
        // Pending bids
        this.prisma.bid.count({
          where: { admin_response: AdminResponse.PENDING }
        }),
        
        // Accepted bids
        this.prisma.bid.count({
          where: { admin_response: AdminResponse.ACCEPTED }
        }),
        
        // Declined bids
        this.prisma.bid.count({
          where: { admin_response: AdminResponse.DECLINED }
        }),
        
        // Countered bids
        this.prisma.bid.count({
          where: { admin_response: AdminResponse.COUNTERED }
        }),
        
        // Expired bids
        this.prisma.bid.count({
          where: { negotiation_status: NegotiationStatus.EXPIRED }
        }),
        
        // Average bid amount
        this.prisma.bid.aggregate({
          _avg: { bidAmount: true }
        }),
        
        // Bids in last 24 hours
        this.prisma.bid.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
          }
        }),
        
        // Bids in last 7 days
        this.prisma.bid.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        
        // Top carriers by bid count
        this.prisma.bid.groupBy({
          by: ['carrierProfileId'],
          _count: { id: true },
          orderBy: { _count: { id: 'desc' } },
          take: 5
        })
      ]);

      // Get carrier names for top carriers
      const topCarriersWithNames = await Promise.all(
        topCarriers.map(async (carrier) => {
          const profile = await this.prisma.carrierProfile.findUnique({
            where: { id: carrier.carrierProfileId },
            select: { companyName: true, mcNumber: true }
          });
          return {
            ...carrier,
            companyName: profile?.companyName || 'Unknown',
            mcNumber: profile?.mcNumber || 'N/A'
          };
        })
      );

      const stats = {
        overview: {
          totalBids,
          pendingBids,
          acceptedBids,
          declinedBids,
          counteredBids,
          expiredBids,
          averageBidAmount: averageBidAmount._avg.bidAmount || 0
        },
        activity: {
          bidsLast24Hours,
          bidsLast7Days,
          acceptanceRate: totalBids > 0 ? ((acceptedBids / totalBids) * 100).toFixed(1) : 0,
          responseRate: totalBids > 0 ? (((acceptedBids + declinedBids + counteredBids) / totalBids) * 100).toFixed(1) : 0
        },
        topCarriers: topCarriersWithNames
      };

      this.logger.log('Retrieved bidding statistics for admin dashboard');
      return stats;

    } catch (error) {
      this.logger.error(`Failed to retrieve bidding statistics: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve bidding statistics');
    }
  }

  /**
   * Process expired bids - should be called periodically
   */
  async processExpiredBids(): Promise<{ expiredCount: number; notifiedCount: number }> {
    this.logger.log('Processing expired bids...');
    
    try {
      // Find all bids that have expired but are still in OPEN status
      const expiredBids = await this.prisma.bid.findMany({
        where: {
          expires_at: {
            lt: new Date()
          },
          negotiation_status: NegotiationStatus.OPEN
        },
        include: {
          load: {
            select: {
              airtableRecordId: true,
              originCity: true,
              originState: true,
              destinationCity: true,
              destinationState: true
            }
          },
          carrierProfile: {
            include: {
              user: true
            }
          }
        }
      });

      if (expiredBids.length === 0) {
        this.logger.log('No expired bids found');
        return { expiredCount: 0, notifiedCount: 0 };
      }

      this.logger.log(`Found ${expiredBids.length} expired bids to process`);

      // Update all expired bids to EXPIRED status
      await this.prisma.bid.updateMany({
        where: {
          id: {
            in: expiredBids.map(bid => bid.id)
          }
        },
        data: {
          negotiation_status: NegotiationStatus.EXPIRED,
          response_timestamp: new Date(),
          admin_notes: 'Bid expired automatically after 24 hours'
        }
      });

      // Send notifications to carriers about expired bids
      let notifiedCount = 0;
      for (const bid of expiredBids) {
        try {
          const carrierUserId = bid.carrierProfile.user?.airtableUserId;
          if (carrierUserId && this.notificationsService) {
            await this.notificationsService.broadcastToUser(carrierUserId, {
              type: 'bid_expired',
              title: 'Bid Expired',
              message: `Your bid of $${bid.bidAmount.toFixed(2)} on load ${bid.load.airtableRecordId} has expired.`,
              data: {
                bidId: bid.id,
                loadId: bid.loadId,
                bidAmount: bid.bidAmount,
                loadDetails: bid.load
              }
            });
            notifiedCount++;
          }
        } catch (notificationError) {
          this.logger.error(`Failed to notify carrier about expired bid ${bid.id}: ${notificationError.message}`);
        }
      }

      this.logger.log(`Successfully expired ${expiredBids.length} bids and notified ${notifiedCount} carriers`);
      
      return {
        expiredCount: expiredBids.length,
        notifiedCount
      };

    } catch (error) {
      this.logger.error(`Failed to process expired bids: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to process expired bids');
    }
  }

  /**
   * Get bids expiring soon (within specified hours)
   */
  async getBidsExpiringSoon(hoursFromNow: number = 2): Promise<any[]> {
    try {
      const expiryThreshold = new Date();
      expiryThreshold.setHours(expiryThreshold.getHours() + hoursFromNow);

      const expiringSoonBids = await this.prisma.bid.findMany({
        where: {
          expires_at: {
            lte: expiryThreshold,
            gt: new Date() // Not already expired
          },
          negotiation_status: NegotiationStatus.OPEN
        },
        include: {
          load: {
            select: {
              airtableRecordId: true,
              originCity: true,
              originState: true,
              destinationCity: true,
              destinationState: true
            }
          },
          carrierProfile: {
            select: {
              companyName: true,
              mcNumber: true,
              contact_email: true
            }
          }
        },
        orderBy: {
          expires_at: 'asc'
        }
      });

      // Add time remaining calculation
      const bidsWithTimeRemaining = expiringSoonBids.map(bid => ({
        ...bid,
        timeRemaining: bid.expires_at ? this.calculateTimeRemaining(bid.expires_at) : null
      }));

      this.logger.log(`Found ${bidsWithTimeRemaining.length} bids expiring within ${hoursFromNow} hours`);
      return bidsWithTimeRemaining;

    } catch (error) {
      this.logger.error(`Failed to get bids expiring soon: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve bids expiring soon');
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Calculate time remaining until bid expires
   */
  private calculateTimeRemaining(expiresAt: Date): string {
    const now = new Date();
    const diffMs = expiresAt.getTime() - now.getTime();
    
    if (diffMs <= 0) {
      return 'Expired';
    }

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Check if bid is expiring soon (within 2 hours)
   */
  private isExpiringSoon(expiresAt: Date): boolean {
    const now = new Date();
    const diffMs = expiresAt.getTime() - now.getTime();
    const twoHoursMs = 2 * 60 * 60 * 1000;
    
    return diffMs > 0 && diffMs <= twoHoursMs;
  }

  /**
   * Send real-time notifications for admin dashboard only (no emails - N8N handles those)
   */
  private async sendAdminDashboardNotifications(bid: any, response: string, adminUserId: string): Promise<void> {
    try {
      if (!this.notificationsService) {
        this.logger.warn('NotificationsService not available, skipping admin dashboard notifications');
        return;
      }

      // Only send admin-to-admin notifications for dashboard updates
      // No carrier notifications - N8N handles all carrier communication
      await this.notificationsService.broadcastToAdmins({
        type: 'bid_processed',
        title: `Bid ${response.charAt(0).toUpperCase() + response.slice(1)}`,
        message: `Bid ${bid.id} has been ${response} by admin`,
        data: {
          bidId: bid.id,
          loadId: bid.loadId,
          response,
          amount: bid.bidAmount,
          counterOfferAmount: bid.counter_offer_amount,
          processedBy: adminUserId,
          carrierMcNumber: bid.carrierProfile?.mcNumber
        }
      });

      this.logger.log(`Admin dashboard notified of bid ${response}: ${bid.id}`);

    } catch (error) {
      this.logger.error(`Failed to send admin dashboard notifications: ${error.message}`, error.stack);
      // Don't throw error as notification failure shouldn't block the bid response
    }
  }

  /**
   * Generate user-friendly bid response messages
   */
  private getBidResponseMessage(response: string, bidAmount: number, counterOfferAmount?: number): string {
    switch (response) {
      case 'accepted':
        return `Your bid of $${bidAmount.toFixed(2)} has been accepted! The load has been assigned to you.`;
      case 'countered':
        return `Your bid of $${bidAmount.toFixed(2)} has been countered with $${counterOfferAmount?.toFixed(2) || 0}. You can accept or decline this counter-offer.`;
      case 'declined':
        return `Your bid of $${bidAmount.toFixed(2)} was not accepted. Keep bidding on other loads!`;
      default:
        return `Your bid status has been updated.`;
    }
  }
} 