# Error and Solution Log

This log will track errors encountered during development and the solutions applied.

## Error: Auth0 Token Request - Access Denied (Unauthorized)

*   **Date:** (Will be today's date)
*   **Command Used (PowerShell):**
    ```powershell
    $headers = @{
        "Content-Type" = "application/json"
    }

    $body = @{
        client_id     = "emvLrB1HA2vc7X8mFH9qHPpxbuqZi9a7"
        client_secret = "rRK2Lw1Iv8SqHPmYHC91eSfZPZwHtWQCd8-AcwKXC0l7erdi7-oNFcYIhLj731A1" # User-provided secret
        audience      = "https://api.carrier-portal.com"
        grant_type    = "client_credentials"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "https://dev-nf1ymt605h3kjpog.us.auth0.com/oauth/token" -Method POST -Headers $headers -Body $body
    ```
*   **Error Message:** `Invoke-RestMethod : {"error":"access_denied","error_description":"Unauthorized"}`
*   **Context:** Attempting to get an access token from Auth0 using the Client Credentials flow to test the `/profile` API endpoint.
*   **Troubleshooting Steps (Next):**
    1.  Verify `client_id` and `client_secret` are correct for the M2M application. (Using client_secret displayed in Auth0's working "Test" tab cURL command.)
    2.  Ensure the M2M application is authorized to access the target API (`https://api.carrier-portal.com`) in Auth0. (Confirmed Authorized)
    3.  Confirm the `audience` (API Identifier) matches the API settings exactly. (User to re-verify)
    4.  Verify the "Client Credentials" grant type is enabled for the M2M application. (Confirmed enabled)
    5.  Check Auth0 Rules for interference. (Likely not the issue as cURL works)
    6.  Check Auth0 Client Credentials Exchange Hooks for interference. (Likely not the issue as cURL works)
    7.  Investigate potential Invoke-RestMethod specific issues (e.g., body serialization with ConvertTo-Json -Compress, character encoding, default headers). (Tried, still failed)
    8.  Test with a different HTTP client (e.g., cURL in Git Bash/WSL) if PowerShell continues to fail. (cURL test from Linux/bash was SUCCESSFUL - Auth0 log type `seccft` vs `feccft` for PowerShell)
*   **Solution:**
    1.  Navigated to the Auth0 M2M application ("Carrier Portal API (Test Application)").
    2.  Confirmed "Token Endpoint Authentication Method" on the "Settings" or "Credentials" tab was set to "Client Secret (Post)".
    3.  Navigated to "Advanced Settings" -> "GrantTypes" for the M2M application.
    4.  Enabled the "Client Credentials" grant type and saved changes.
    5.  Client secret for the M2M application was confirmed to be the same as the one working in Auth0's "Test" tab and a direct cURL call from a bash environment.
    6.  Connection from WSL2 (`devlin@Home`) to NestJS API on Windows host: `localhost:3001` failed. Using the Windows host IP address (e.g., `************`, obtained via `ip route show default` in WSL2) for the cURL target URL successfully connected to the API's root endpoint (`/`).
    7.  Successfully called the protected `/profile` endpoint using the Auth0 token and the Windows host IP from WSL2.
*   **Status: FULLY RESOLVED!** Successfully tested authenticated `/profile` endpoint on NestJS API from WSL2 using an Auth0 M2M token. Key was using the Windows Host IP from WSL, and ensuring Auth0 config and NestJS .env variables (AUDIENCE, ISSUER_URL) were correct.

--- 

## Error: WSL to Windows Host Connection Failure for API

*   **Symptom:** cURL from WSL (devlin@Home) to localhost:3001 (NestJS API on Windows) failed with `Failed to connect`.
*   **Solution:** Used the Windows host's IP address as seen from WSL (e.g., ************) in the cURL command instead of `localhost`.

---

## Error: M2M Token for User-Specific Action (Create Carrier Profile)

*   **Symptom:** POST /carrier-profiles with M2M token resulted in 404 Not Found with `User with Auth0 ID ...@clients not found`.
*   **Solution:** Realized an M2M token cannot create user-linked data. Switched to obtaining a user-specific token via frontend login.

---

## Error: Auth0 "Oops!, something went wrong" on Login Redirect (Next.js)

*   **Symptom:** Auth0 redirect back to `http://localhost:3000` failed. Auth0 logs: "Unable to issue redirect for OAuth 2.0 transaction."
*   **Solution:** Explicitly set `redirect_uri: window.location.origin` in the `Auth0Provider` configuration in `Auth0ProviderWrapper.tsx` (apps/web/src/components/).

---

## Error: API Token Validation - Missing Environment Variables

*   **Symptom:** 401 Unauthorized with "Error processing authentication token" from NestJS API when using user token.
*   **Solution:** Added `AUTH0_AUDIENCE=https://api.carrier-portal.com` and `AUTH0_ISSUER_URL=https://dev-nf1ymt605h3kjpog.us.auth0.com/` to `apps/api/.env` and restarted the API server.

---

## Error: API Token Validation - Email Claim Missing in Access Token

*   **Symptom:** NestJS API logs showed "Email is missing from Auth0 profile" and `InternalServerErrorException: Email missing from user profile token`.
*   **Solution:** Created an Auth0 Action for "Login / Post Login" trigger to add the email claim to the Access Token (`api.accessToken.setCustomClaim("email", event.user.email);`). User logged out and back in.

---

## Error: API Authorization - Incorrect User ID Field Access in Controller

*   **Symptom:** 403 Forbidden with "Auth0 User ID not found in token" from `CarrierProfilesController`.
*   **Solution:** Modified `CarrierProfilesController` to access Auth0 User ID via `req.user.auth0UserId` (instead of `req.user.sub`) because `req.user` is the Prisma User object. Updated the `AuthenticatedUser` interface in the controller accordingly. Restarted API.

---

## Error: Auth0 Action for RBAC (Adding Roles to Token) - Persistent Silent Execution Halts

*   **Goal:** Implement RBAC by adding a custom `roles` claim to JWTs using an Auth0 Action triggered on PostLogin. The action was intended to fetch user roles from the Auth0 Management API (using an M2M token) and add them to the access and ID tokens.
*   **Symptoms & Debugging Journey:**
    *   **Initial Problem:** Access tokens did not contain the expected custom roles claim.
    *   **Extensive Logging:** Numerous attempts were made to debug the Action script by adding detailed `console.log` statements at various stages (M2M token request, `URLSearchParams` usage, `fetch` calls, user role fetching).
    *   **HTTP Client Change:** Switched from `axios` to `node-fetch` within the Action, suspecting environment-specific issues with `axios`.
    *   **Silent Halts:** The primary and most persistent issue was the Action script silently halting execution. Logs would truncate abruptly at various points, often before critical operations like:
        *   The `fetch` call for the M2M token.
        *   The instantiation or use of `URLSearchParams` (even before simple `console.log` statements inside a `try` block intended for `URLSearchParams`).
        *   Simple string variable assignments that followed a sequence of `event.secrets` access and basic logging.
    *   This halting occurred even when `require('url')` (for `URLSearchParams`) was removed and the request body was constructed manually.
    *   It also occurred in "absurdly simple" test scripts designed to do minimal operations after accessing secrets.
    *   **Error Messages (When Present):** Infrequently, errors like "unexpected end of file" (during M2M token fetch attempts) or `ACTION_MALFORMED` (due to script structural issues, which were usually resolved) were observed. However, the silent halts were the dominant problem.
*   **Suspected Causes (Speculative, as no explicit error was consistently logged for the halts):**
    *   Issues with the `url` module (specifically `URLSearchParams`) or `node-fetch` within the Auth0 Action environment.
    *   Invisible script corruption or truncation not visible in the editor.
    *   Aggressive/flawed runtime protection in the Auth0 Action sandbox.
    *   Resource exhaustion or a very specific sequence of operations triggering an unloggable crash in the Action runner.
*   **Outcome: Implementation Abandoned (for Auth0 Action method)**
    *   Despite numerous variations and simplifications of the Action script, it was not possible to get it to reliably execute the logic required to fetch and add roles to the token. The script would consistently halt before or during these operations.
    *   The root cause for these silent halts within the Auth0 Action environment could not be definitively identified through available debugging methods.
*   **Next Steps Considered:**
    1.  Alternative RBAC: API fetches roles directly from Auth0 Management API on each request (with caching).
    2.  Alternative RBAC: Use Auth0 Core Authorization to add a `permissions` claim directly to the token (configured in Auth0 API settings), and use a `PermissionsGuard` in the NestJS API.

--- 

## Error: NetworkError Due to Content Security Policy (CSP)

*   **Date:** 2025-05-17
*   **Symptom:** Browser console shows "Error: NetworkError when attempting to fetch resource" when the frontend (`apps/web`) tries to call the backend API (`apps/api` at `http://localhost:3001`). A more detailed error in the console indicated: `Content-Security-Policy: The page's settings blocked the loading of a resource (connect-src) at http://localhost:3001/...`
*   **Context:** Occurred in `LoadBoardPage.useEffect.fetchLoads` in `src/app/org/[orgId]/loadboard/page.tsx` when attempting to fetch data from `${apiUrl}/api/airtable-orders/available`.
*   **Troubleshooting Steps:**
    1.  Verified `NEXT_PUBLIC_API_BASE_URL` was correctly set to `http://localhost:3001` in `apps/web/.env.local`.
    2.  Identified the CSP error message in the browser console as the root cause.
    3.  Located the CSP configuration within the `headers` function in `apps/web/next.config.js`.
    4.  Confirmed that `http://localhost:3001` was missing from the `connect-src` directive of the CSP.
*   **Solution:**
    1.  Modified the `apps/web/next.config.js` file.
    2.  Added `http://localhost:3001` (and a placeholder for the production API URL `https://your-production-api.com`) to the `connect-src` directive within the `Content-Security-Policy` value.
    3.  Restarted the Next.js development server for `apps/web`.
*   **Status: RESOLVED!** The frontend can now successfully fetch resources from the local backend API.

--- 

## Error: API Clerk Token Validation - Invalid Issuer / .env Loading Failure

*   **Date:** 2025-05-17
*   **Symptom:** Frontend API calls to authenticated NestJS endpoints (`apps/api`) consistently failed with HTTP 401 Unauthorized. API logs (after adding detailed error logging to `ClerkGuard`) showed "Invalid Compact JWS" or "signature verification failed" and indicated that `CLERK_JWT_ISSUER` was using a placeholder value (e.g., `https://YOUR-CLERK-DOMAIN.clerk.accounts.dev`) instead of the actual value from `apps/api/.env`. This meant the API was trying to validate tokens against the wrong issuer.
*   **Context:** Occurred for all authenticated endpoints in `apps/api` when called from `apps/web` after Clerk integration. The frontend was sending valid tokens.
*   **Troubleshooting Steps:**
    1.  Verified `CLERK_JWT_ISSUER` was correctly set in `apps/api/.env`.
    2.  Added extensive `console.log` statements in `ClerkGuard` to inspect the token, the issuer being used for verification, and the JWKS URL.
    3.  Confirmed the `ConfigModule` was imported in `apps/api/src/app.module.ts` and set to `isGlobal: true`.
    4.  Attempted various `envFilePath` configurations in `ConfigModule.forRoot()`:
        *   `./apps/api/.env` (relative to project root) - Did not work as expected in the built/running application.
        *   `join(__dirname, '..', '.env')` (relative to the compiled `dist/apps/api/main.js` or similar) - This correctly located the `.env` file.
    5.  Refactored `ClerkGuard` to remove top-level constants that accessed `process.env.CLERK_JWT_ISSUER` directly. Instead, injected `ConfigService` into the `ClerkGuard` constructor and initialized `this.CLERK_ISSUER` and `this.JWKS` there. This ensures environment variables are accessed *after* `ConfigModule` has loaded them.
    6.  Resolved a subsequent TypeScript error (`Type 'RemoteJWKSet' is not assignable to type 'JWKSet'.`) by changing the type of `this.JWKS` to `any` as the specific `JWKSet` type was not strictly required for the `jwtVerify` function from `jose`.
*   **Solution:**
    1.  Modified `apps/api/src/app.module.ts` to ensure `ConfigModule` correctly loads the `.env` file using a path relative to the output directory:
        ```typescript
        // In AppModule imports:
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: process.env.NODE_ENV === 'test' 
            ? join(__dirname, '..', '.env.test') 
            : join(__dirname, '..', '.env'), 
        }),
        ```
    2.  Refactored `apps/api/src/auth/clerk.guard.ts`:
        *   Removed top-level `const CLERK_DOMAIN = process.env.CLERK_JWT_ISSUER...`, `const CLERK_ISSUER = ...`, and `const JWKS = createRemoteJWKSet(...)`.
        *   Injected `ConfigService` into the constructor.
        *   Initialized `this.CLERK_ISSUER` and `this.JWKS` inside the constructor using `this.configService.get<string>('CLERK_JWT_ISSUER')`.
        ```typescript
        // In ClerkGuard:
        // private CLERK_ISSUER: string;
        // private JWKS: any; // Changed from JWKSet
        // constructor(
        //   private readonly authService: AuthService,
        //   private readonly configService: ConfigService,
        // ) {
        //   const clerkJwtIssuer = this.configService.get<string>('CLERK_JWT_ISSUER');
        //   if (!clerkJwtIssuer) { /* ... error handling ... */ } 
        //   else { /* ... initialize this.CLERK_ISSUER and this.JWKS ... */ }
        // }
        ```
    3.  Ensured the `apps/api` server was restarted after these changes.
*   **Status: RESOLVED!** API now correctly loads `CLERK_JWT_ISSUER` and successfully validates tokens from the frontend. Authenticated endpoints are working.

--- 

## Error 1: Clerk Token Validation Failure in API (Resolved)

*   **Date:** 2025-05-13 / 2025-05-14
*   **Service:** `apps/api`
*   **Symptom:** API endpoints returning 401 or 403 errors despite valid-looking tokens from frontend. ClerkGuard was not correctly validating tokens.
*   **Root Cause Analysis:**
    1.  Initially, `CLERK_ISSUER_ID` was missing from the `.env` file and `ConfigModule` in `apps/api`.
    2.  Even after adding `CLERK_ISSUER_ID`, the `ClerkGuard` used `Clerk().localAuth(auth);` which might not be suitable for all JWT validation or there was a subtle configuration mismatch for backend token validation (e.g., issuer URL vs. instance ID).
    3.  The `ConfigModule` in `apps/api` was not correctly exposing all necessary Clerk-related environment variables (like `CLERK_SECRET_KEY`, `CLERK_PUBLISHABLE_KEY`, `CLERK_JWT_ISSUER_DOMAIN`) to the `ClerkAuthModule` or `ClerkGuard`.
    4.  The `ClerkAuthModule.forRootAsync` was not correctly configured to load and use the environment variables from the `ConfigModule` for Clerk settings like `issuer` and `jwksUrl`.
*   **Resolution Steps:**
    1.  Ensured all Clerk environment variables (`CLERK_SECRET_KEY`, `CLERK_PUBLISHABLE_KEY`, `CLERK_FRONTEND_API`, `CLERK_API_URL`, `CLERK_JWT_ISSUER_DOMAIN`, `CLERK_DOMAIN`, `CLERK_ISSUER_ID`, `CLERK_API_VERSION`) were defined in `.env` for `apps/api`.
    2.  Updated `apps/api/src/config/config.ts` and `apps/api/src/config/validation.ts` to include and validate all these Clerk variables.
    3.  Modified `apps/api/src/auth/auth.module.ts` (`ClerkAuthModule`):
        *   Used `ClerkModule.forRootAsync` to properly inject `ConfigService`.
        *   Configured `issuer` using `configService.get('CLERK_JWT_ISSUER_DOMAIN')`.
        *   Configured `jwksUrl` by constructing it: `https://${configService.get('CLERK_JWT_ISSUER_DOMAIN')}/.well-known/jwks.json`.
        *   Included other necessary Clerk settings like `publishableKey` and `secretKey` directly in the `ClerkAuthModule` setup, although `jwksUrl` and `issuer` are the primary ones for backend validation.
    4.  Simplified `ClerkGuard` to rely on the `ClerkAuthModule`'s configuration. Removed manual `Clerk().localAuth()` calls if they were still present.
    5.  Extensive logging was added to `ClerkGuard` to inspect the `request.auth` object and headers to confirm token presence and structure.
    6.  Confirmed that the frontend (`apps/web`) was correctly fetching and sending the Clerk token in the `Authorization` header as a Bearer token.
*   **Status:** Resolved. API correctly validates Clerk JWTs.

## Error 2: Vercel Build Failures - Next.js 15 `params` Prop Type (In Progress)

*   **Date:** 2025-05-17
*   **Service:** `apps/web` (Vercel Build)
*   **Symptom:** Vercel builds fail with TypeScript errors like:
    ```
    Type error: Type '[PageName]Props' does not satisfy the constraint 'PageProps'.
      Types of property 'params' are incompatible.
        Type '{ orgId: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
    ```
*   **Affected Files Initially:** `apps/web/src/app/org/[orgId]/billing/page.tsx`, then `dashboard/page.tsx`.
*   **Root Cause Analysis:** The project uses Next.js 15.3.2 (as per Vercel build logs). Official Next.js documentation snippets indicate that for Next.js 15+, dynamic route parameters (`params`) in Page and Layout components are now handled differently:
    *   For **Server Components** (like the `billing`, `dashboard`, `my-loads` pages initially were), if the page function needs to access `params`, it should be `async`, and the `params` prop itself is a `Promise` that needs to be `await`ed (e.g., `params: Promise<{ orgId: string }>`).
    *   For **Client Components** (like `OrgLayout` or `settings/page.tsx`), if they receive `params` as a prop (typically from a Server Component parent or layout), this `params` prop is also a `Promise`. The component should use `React.use(params)` to access the resolved value.
    *   Client Components that use the `useParams()` hook (like `loadboard/page.tsx`) are not directly affected by this prop change, as the hook handles parameter access internally.
*   **Resolution Steps (Applied to multiple files):**
    1.  For `apps/web/src/app/org/[orgId]/billing/page.tsx`:
        *   Changed function to `async`.
        *   Updated `params` prop type in its interface/inline to `Promise<{ orgId: string }> `.
        *   Used `await params` to get the `orgId`.
    2.  For `apps/web/src/app/org/[orgId]/dashboard/page.tsx`:
        *   Applied the same fix as `billing/page.tsx`.
    3.  For `apps/web/src/app/org/[orgId]/my-loads/page.tsx`:
        *   Applied the same fix as `billing/page.tsx`.
    4.  For `apps/web/src/app/org/[orgId]/layout.tsx` (Client Component):
        *   Updated `params` prop in `OrgLayoutProps` to `Promise<OrgLayoutParams>`.
        *   Noted that `React.use(params)` should be used to access `orgId` if needed within the component's rendering logic (currently, `orgId` usage is in commented-out code).
    5.  `apps/web/src/app/org/[orgId]/settings/page.tsx` (Client Component) was already correctly typed with `params: Promise<{ orgId: string }>` and used `React.use(props.params)`.
    6.  `apps/web/src/app/org/[orgId]/loadboard/page.tsx` (Client Component) was correctly using `useParams()` hook and did not need changes to its props definition.
*   **Status:** All identified relevant pages and the layout within `apps/web/src/app/org/[orgId]/` have been updated to conform to the Next.js 15 `params` handling. Awaiting Vercel build confirmation.

--- 