'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';

export function ApiDebugger() {
  const { getToken } = useAuth();
  const [testResults, setTestResults] = useState<any[]>([]);

  const testApiCall = async (endpoint: string, description: string) => {
    try {
      const token = await getToken();
      console.log(`Testing ${description}...`);
      
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      const result = {
        endpoint,
        description,
        status: response.status,
        success: response.ok,
        timestamp: new Date().toLocaleTimeString(),
        error: null as string | null,
        data: null as any
      };
      
      if (response.ok) {
        result.data = await response.json();
        console.log(`✅ ${description} Success:`, result.data);
      } else {
        result.error = await response.text();
        console.log(`❌ ${description} Error (${response.status}):`, result.error);
      }
      
      setTestResults(prev => [result, ...prev].slice(0, 10));
      
    } catch (error) {
      const result = {
        endpoint,
        description,
        status: 0,
        success: false,
        timestamp: new Date().toLocaleTimeString(),
        error: (error as Error).message,
        data: null as any
      };
      
      console.error(`💥 ${description} Failed:`, error);
      setTestResults(prev => [result, ...prev].slice(0, 10));
    }
  };

  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-blue-100 p-4 rounded-lg shadow-lg max-w-md z-50 max-h-80 overflow-y-auto">
      <h4 className="font-bold text-blue-800 mb-2">API Debugger</h4>
      
      <div className="space-y-2 mb-3">
        <Button 
          size="sm" 
          onClick={() => testApiCall('/api/v1/auth/me', 'Auth Check')}
          className="w-full text-xs"
        >
          Test Auth
        </Button>
        <Button 
          size="sm" 
          onClick={() => testApiCall('/api/v1/carrier-profiles/me', 'Carrier Profile')}
          className="w-full text-xs"
        >
          Test Profile
        </Button>
        <Button 
          size="sm" 
          onClick={() => setTestResults([])}
          variant="outline"
          className="w-full text-xs"
        >
          Clear Results
        </Button>
      </div>
      
      <div className="space-y-2 max-h-40 overflow-y-auto">
        {testResults.map((result, index) => (
          <div key={index} className={`text-xs p-2 rounded ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
            <div className="font-semibold">
              {result.success ? '✅' : '❌'} {result.description} ({result.status})
            </div>
            <div className="text-gray-600">{result.timestamp}</div>
            {result.error && (
              <div className="text-red-700 mt-1 text-xs">
                {result.error.length > 100 ? result.error.substring(0, 100) + '...' : result.error}
              </div>
            )}
            {result.data && (
              <div className="text-green-700 mt-1 text-xs">
                User: {result.data.name || result.data.email}, Org: {result.data.orgName || 'N/A'}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 