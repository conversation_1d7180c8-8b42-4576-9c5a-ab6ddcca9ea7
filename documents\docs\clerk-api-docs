---
title: Clerk environment variables
description: Learn which Clerk environment variables are necessary to configure your projects.
---

You can use environment variables to configure how your Clerk app behaves, such as where users are redirected after signing out of their account, or whether or not <PERSON>'s telemetry should be enabled.

This page is a reference for all available Clerk environment variables.

## Compatibility

In the frontend, Clerk's environment variables work for most popular meta-frameworks, such as Next.js or Remix.

If you're building a pure React app, you should use the props on the components you're using. For example, to force users to redirect to a specific URL after signing in, you would use the `signInForceRedirectUrl` prop on [`<ClerkProvider>`](/docs/components/clerk-provider) rather than the `CLERK_SIGN_IN_FORCE_REDIRECT_URL` environment variable.

## Sign-in and sign-up redirects

Components, such as [`<ClerkProvider>`](/docs/components/clerk-provider), [`<SignIn>`](/docs/components/authentication/sign-in), and more, provide props for you to specify where users will be redirected. For example, `<ClerkProvider>` has the `signInFallbackRedirectUrl` and `signUpFallbackRedirectUrl` props.

However, **it's recommended to use environment variables instead of these props whenever possible.**

<Include src="_partials/environment-variables" />

## Clerk Publishable and Secret Keys

To access your Clerk app in your local project, you must specify your app's Publishable Keys for use in the frontend, and Secret Keys for use in the backend.

You can find these keys on the [**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page in the Clerk Dashboard.

<Tabs items={["General", "Next.js"]}>
  <Tab>
    | Variable | Description |
    | - | - |
    | `CLERK_PUBLISHABLE_KEY` | Your Clerk app's Publishable Key. It will be prefixed with `pk_test_` in development instances and `pk_live_` in production instances. |
    | `CLERK_SECRET_KEY` | Your Clerk app's Secret Key, which you can find in the Clerk Dashboard. It will be prefixed with `sk_test_` in development instances and `sk_live_` in production instances. **Do not expose this on the frontend with a public environment variable**. |
  </Tab>

  <Tab>
    | Variable | Description |
    | - | - |
    | `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` | Your Clerk app's Publishable Key, which you can find in the Clerk Dashboard. It will be prefixed with `pk_test_` in development instances and `pk_live_` in production instances. |
    | `CLERK_SECRET_KEY` | Your Clerk app's Secret Key, which you can find in the Clerk Dashboard. It will be prefixed with `sk_test_` in development instances and `sk_live_` in production instances. **Do not expose this on the frontend with a public environment variable**. |
  </Tab>
</Tabs>

## API and SDK configuration

The following environment variables enable you to configure API and SDK behavior, such as what version of the SDK your project uses, what proxy URL you use to connect to Clerk's Frontend API, and more.

<Tabs items={["General", "Next.js"]}>
  <Tab>
    | Variable | Description |
    | - | - |
    | `CLERK_JS_URL` | Sets the URL that `@clerk/clerk-react` should hot-load `@clerk/clerk-js` from. `CLERK_JS` does the same but is deprecated. |
    | `CLERK_JS_VERSION` | Sets the npm version for `@clerk/clerk-js`. |
    | `CLERK_API_URL` | Sets the Clerk API URL for debugging. Defaults to `"https://api.clerk.com"` |
    | `CLERK_API_VERSION` | Sets the version of the Clerk API to use. Defaults to `"v1"` |
    | `CLERK_JWT_KEY` | Sets the JWT verification key that Clerk will use to provide networkless JWT session token verification. Refer to [Manual JWT verification](/docs/backend-requests/manual-jwt). |
    | `CLERK_FAPI` | Sets the URL to your Clerk apps' Frontend API. |
    | `CLERK_PROXY_URL` | Sets the URL for your proxy. |
  </Tab>

  <Tab>
    | Variable | Description |
    | - | - |
    | `NEXT_PUBLIC_CLERK_JS_URL` | Sets the URL that `@clerk/clerk-react` should hot-load `@clerk/clerk-js` from. `NEXT_PUBLIC_CLERK_JS` does the same but is deprecated. |
    | `NEXT_PUBLIC_CLERK_JS_VERSION` | Sets the npm version for `@clerk/clerk-js`. |
    | `NEXT_PUBLIC_CLERK_API_URL` | Sets the Clerk API URL for debugging. Defaults to `"https://api.clerk.com"` |
    | `NEXT_PUBLIC_CLERK_API_VERSION` | Sets the version of the Clerk API to use. Defaults to `"v1"` |
    | `NEXT_PUBLIC_CLERK_FAPI` | Sets the URL to your Clerk app's Frontend API. |
    | `NEXT_PUBLIC_CLERK_PROXY_URL` | Sets the URL for your proxy. |
    | `CLERK_ENCRYPTION_KEY` | Sets the encryption key to securely propagate `clerkMiddleware` dynamic keys during request time. A 128-bit, pseudorandom value should be used. See [Dynamic keys](/docs/references/nextjs/clerk-middleware#dynamic-keys) to learn more. |
    | `CLERK_JWT_KEY` | Sets the JWT verification key that Clerk will use to provide networkless JWT session token verification. Refer to [Manual JWT verification](/docs/backend-requests/manual-jwt). |
  </Tab>
</Tabs>

## Satellite domains

Clerk supports sharing sessions across different domains by adding one or many satellite domains to an application. See [the satellite domains guide](/docs/advanced-usage/satellite-domains) for more information.

<Tabs items={["General", "Next.js"]}>
  <Tab>
    | Variable | Description |
    | - | - |
    | `CLERK_DOMAIN` | Sets your satellite application's domain. Required to share sessions across multiple domains. |
    | `CLERK_IS_SATELLITE` | Indicates whether or not the application is a satellite application. |
  </Tab>

  <Tab>
    | Variable | Description |
    | - | - |
    | `NEXT_PUBLIC_CLERK_DOMAIN` | Sets your satellite application's domain. Required to share sessions across multiple domains. |
    | `NEXT_PUBLIC_CLERK_IS_SATELLITE` | Indicates whether or not the application is a satellite application. |
  </Tab>
</Tabs>

## Webhooks

The following environment variable allows you to protect your webhook signing secret. It is read by Clerk's [`verifyWebhook()`](/docs/references/backend/verify-webhook) function.

| Variable | Description |
| - | - |
| `CLERK_WEBHOOK_SIGNING_SECRET` | The signing secret for your webhook. |

## Telemetry

Clerk provides environment variables for opting out of telemetry data collection. See [the telemetry documentation](/docs/telemetry) for more information.

<Tabs items={["General", "Next.js"]}>
  <Tab>
    | Variable | Description |
    | - | - |
    | `CLERK_TELEMETRY_DISABLED` | Set this to `1` to disable Clerk's telemetry data collection. |
    | `CLERK_TELEMETRY_DEBUG` | Set this to `1` to prevent telemetry data from being sent to Clerk. It will be logged to the console instead. |
  </Tab>

  <Tab>
    | Variable | Description |
    | - | - |
    | `NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED` | Set this to `1` to disable Clerk's telemetry data collection. |
    | `NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG` | Set this to `1` to prevent telemetry data from being sent to Clerk. It will be logged to the console instead. |
  </Tab>
</Tabs>

## Deprecated

The following environment variables are deprecated but still supported to avoid breaking changes. Don't use them in new projects. It is recommended to switch to using the recommended alternatives in old projects.

<Tabs items={["General", "Next.js"]}>
  <Tab>
    | Variable | Description |
    | - | - |
    | `CLERK_AFTER_SIGN_UP_URL` | Full URL or path to navigate to after successful sign up. Defaults to `/`. `CLERK_SIGN_UP_FALLBACK_REDIRECT_URL` and `CLERK_SIGN_UP_FORCE_REDIRECT_URL` have priority and should be used instead. |
    | `CLERK_AFTER_SIGN_IN_URL` | Full URL or path to navigate to after successful sign in. Defaults to `/`. `CLERK_SIGN_IN_FALLBACK_REDIRECT_URL` and `CLERK_SIGN_IN_FORCE_REDIRECT_URL` have priority and should be used instead. |
  </Tab>

  <Tab>
    | Variable | Description |
    | - | - |
    | `NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL` | Full URL or path to navigate to after successful sign up. Defaults to `/`. `NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL` and `NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL` have priority and should be used instead. |
    | `NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL` | Full URL or path to navigate to after successful sign in. Defaults to `/`. `NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL` and `NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL` have priority and should be used instead. |
  </Tab>
</Tabs>
