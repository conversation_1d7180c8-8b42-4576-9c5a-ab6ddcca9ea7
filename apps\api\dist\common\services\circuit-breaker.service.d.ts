export interface CircuitBreakerConfig {
    failureThreshold: number;
    recoveryTimeout: number;
    monitoringPeriod: number;
    halfOpenMaxCalls: number;
}
export declare enum CircuitState {
    CLOSED = "CLOSED",
    OPEN = "OPEN",
    HALF_OPEN = "HALF_OPEN"
}
export declare class CircuitBreakerService {
    private readonly logger;
    private circuits;
    private readonly defaultConfig;
    execute<T>(circuitName: string, operation: () => Promise<T>, fallback?: () => Promise<T>, config?: Partial<CircuitBreakerConfig>): Promise<T>;
    private executeWithTimeout;
    private getOrCreateCircuit;
    private updateCircuitState;
    private logCircuitState;
    getCircuitStatus(): Record<string, any>;
    resetCircuit(circuitName: string): void;
    getHealthCheck(): {
        status: string;
        circuits: Record<string, any>;
    };
}
