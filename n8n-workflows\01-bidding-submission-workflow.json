{"name": "Bidding Submission Workflow", "nodes": [{"parameters": {"authentication": "jwtAuth", "httpMethod": "POST", "path": "bidding/submit", "options": {}}, "id": "webhook-node", "name": "Webhook - Bid Submission", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 300], "webhookId": "bidding-submit-webhook", "credentials": {"jwtAuth": {"id": "jwt-bidding-credential", "name": "JW<PERSON>"}}}, {"parameters": {"functionCode": "// N8N automatically validates JWT and provides payload in $input.item.json.jwtPayload\nconst jwtPayload = $input.item.json.jwtPayload;\nconst requestBody = $input.item.json.body;\n\n// Validate JWT payload structure\nif (!jwtPayload || !jwtPayload.id || !jwtPayload.email || !jwtPayload.mcNumber) {\n  throw new Error('Invalid JWT token: Missing required user information');\n}\n\n// Validate request body\nif (!requestBody.loadId || !requestBody.bidAmount) {\n  throw new Error('Missing required fields: loadId and bidAmount');\n}\n\n// Generate unique bid ID with MC number\nconst timestamp = Date.now();\nconst randomSuffix = Math.random().toString(36).substring(2, 8);\nconst bidId = `bid_MC${jwtPayload.mcNumber}_${timestamp}_${randomSuffix}`;\n\n// Prepare bid data for Airtable\nconst bidData = {\n  'Bid ID': bidId,\n  'Load ID': requestBody.loadId,\n  'Carrier MC Number': jwtPayload.mcNumber,\n  'Carrier Email': jwtPayload.email,\n  'Carrier User ID': jwtPayload.id,\n  'Bid Amount': parseFloat(requestBody.bidAmount),\n  'Bid Status': 'Pending',\n  'Submitted At': new Date().toISOString(),\n  'Expires At': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours\n  'Notes': requestBody.notes || '',\n  'Truck Empty': requestBody.isTruckEmpty || false,\n  'Empty Location': requestBody.truckEmptyLocation || '',\n  'ETA to Shipper': requestBody.etaToShipper || '',\n  'JWT Token': JSON.stringify(jwtPayload) // Store for audit trail\n};\n\n// Calculate rate per mile if miles available\nif (requestBody.miles && requestBody.miles > 0) {\n  bidData['Rate Per Mile'] = (parseFloat(requestBody.bidAmount) / parseFloat(requestBody.miles)).toFixed(2);\n}\n\nreturn {\n  json: {\n    bidData: bidData,\n    userInfo: {\n      id: jwtPayload.id,\n      email: jwtPayload.email,\n      mcNumber: jwtPayload.mcNumber\n    },\n    bidId: bidId,\n    loadId: requestBody.loadId,\n    success: true\n  }\n};"}, "id": "process-bid-function", "name": "Process Authenticated Bid", "type": "n8n-nodes-base.function", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"authentication": "airtableTokenApi", "operation": "append", "base": {"__rl": true, "value": "YOUR_AIRTABLE_BASE_ID", "mode": "id"}, "table": {"__rl": true, "value": "Bids", "mode": "id"}, "columns": {"mappingMode": "defineBelow", "value": {"Bid ID": "={{ $json.bidData['Bid ID'] }}", "Load ID": "={{ $json.bidData['Load ID'] }}", "Carrier MC Number": "={{ $json.bidData['Carrier MC Number'] }}", "Carrier Email": "={{ $json.bidData['Carrier Email'] }}", "Carrier User ID": "={{ $json.bidData['Carrier User ID'] }}", "Bid Amount": "={{ $json.bidData['Bid Amount'] }}", "Bid Status": "={{ $json.bidData['Bid Status'] }}", "Submitted At": "={{ $json.bidData['Submitted At'] }}", "Expires At": "={{ $json.bidData['Expires At'] }}", "Notes": "={{ $json.bidData['Notes'] }}", "Truck Empty": "={{ $json.bidData['Truck Empty'] }}", "Empty Location": "={{ $json.bidData['Empty Location'] }}", "ETA to Shipper": "={{ $json.bidData['ETA to Shipper'] }}", "Rate Per Mile": "={{ $json.bidData['Rate Per Mile'] }}", "JWT Token": "={{ $json.bidData['JWT Token'] }}"}}, "options": {}}, "id": "airtable-store-bid", "name": "Store Bid in Airtable", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [600, 300], "credentials": {"airtableTokenApi": {"id": "airtable-credential", "name": "Airtable API"}}}, {"parameters": {"authentication": "airtableTokenApi", "operation": "list", "base": {"__rl": true, "value": "YOUR_AIRTABLE_BASE_ID", "mode": "id"}, "table": {"__rl": true, "value": "Loads", "mode": "id"}, "filterByFormula": "={Record ID} = '{{ $json.loadId }}'", "options": {}}, "id": "get-load-details", "name": "Get Load Details", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [800, 300], "credentials": {"airtableTokenApi": {"id": "airtable-credential", "name": "Airtable API"}}}, {"parameters": {"url": "https://firstcutproduce.app.n8n.cloud/webhook/admin/bid-alert", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-N8N-Internal", "value": "true"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"bidId\": \"{{ $('Store Bid in Airtable').item.json.fields['Bid ID'] }}\",\n  \"loadId\": \"{{ $('Store Bid in Airtable').item.json.fields['Load ID'] }}\",\n  \"carrierMcNumber\": \"{{ $('Store Bid in Airtable').item.json.fields['Carrier MC Number'] }}\",\n  \"carrierCompanyName\": \"MC{{ $('Store Bid in Airtable').item.json.fields['Carrier MC Number'] }}\",\n  \"carrierEmail\": \"{{ $('Store Bid in Airtable').item.json.fields['Carrier Email'] }}\",\n  \"carrierUserId\": \"{{ $('Store Bid in Airtable').item.json.fields['Carrier User ID'] }}\",\n  \"bidAmount\": {{ $('Store Bid in Airtable').item.json.fields['Bid Amount'] }},\n  \"submittedAt\": \"{{ $('Store Bid in Airtable').item.json.fields['Submitted At'] }}\",\n  \"expiresAt\": \"{{ $('Store Bid in Airtable').item.json.fields['Expires At'] }}\",\n  \"carrierNotes\": \"{{ $('Store Bid in Airtable').item.json.fields['Notes'] || '' }}\",\n  \"authenticatedUser\": true,\n  \"loadDetails\": {\n    \"origin\": \"{{ $json['Shipper City'] || 'Unknown' }}, {{ $json['Shipper State'] || 'Unknown' }}\",\n    \"destination\": \"{{ $json['Receiver City'] || 'Unknown' }}, {{ $json['Receiver State'] || 'Unknown' }}\",\n    \"miles\": \"{{ $json['Miles'] || 'N/A' }}\",\n    \"equipment\": \"{{ $json['Equipment'] || 'N/A' }}\",\n    \"weight\": \"{{ $json['Weight'] || 'N/A' }}\",\n    \"commodity\": \"{{ $json['Commodity'] || 'N/A' }}\",\n    \"pickupDate\": \"{{ $json['Pickup Date'] || 'N/A' }}\",\n    \"deliveryDate\": \"{{ $json['Delivery Date'] || 'N/A' }}\",\n    \"rate\": \"{{ $json['Rate'] || 'N/A' }}\"\n  }\n}", "options": {}}, "id": "trigger-admin-notification", "name": "Trigger Admin Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1000, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ \n  {\n    \"success\": true,\n    \"message\": \"Bid submitted successfully\",\n    \"bidId\": $('Store Bid in Airtable').item.json.fields['Bid ID'],\n    \"bidAmount\": $('Store Bid in Airtable').item.json.fields['Bid Amount'],\n    \"status\": \"pending\",\n    \"expiresAt\": $('Store Bid in Airtable').item.json.fields['Expires At']\n  }\n}}", "options": {"responseCode": 201}}, "id": "respond-success", "name": "Respond with Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 2, "position": [1200, 300]}], "connections": {"Webhook - Bid Submission": {"main": [[{"node": "Process Authenticated Bid", "type": "main", "index": 0}]]}, "Process Authenticated Bid": {"main": [[{"node": "Store Bid in Airtable", "type": "main", "index": 0}]]}, "Store Bid in Airtable": {"main": [[{"node": "Get Load Details", "type": "main", "index": 0}]]}, "Get Load Details": {"main": [[{"node": "Trigger Admin Notification", "type": "main", "index": 0}]]}, "Trigger Admin Notification": {"main": [[{"node": "Respond with Success", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1.0.0", "meta": {"instanceId": "n8n-bidding-system"}, "id": "bidding-submission-workflow", "tags": []}