TITLE: Configuring Prisma Datasource with Environment Variable
DESCRIPTION: Configures the Prisma schema datasource block to use the `DATABASE_URL` environment variable for the database connection. The provider should match your specific database type.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/500-deployment/301-edge/485-deploy-to-vercel.mdx#_snippet_0

LANGUAGE: prisma
CODE:
```
datasource db {
  provider = "postgresql" // this might also be `mysql` or another value depending on your database
  url      = env("DATABASE_URL")
}
```

----------------------------------------

TITLE: Include Relations for Multiple Records (Prisma Client, TypeScript)
DESCRIPTION: Illustrates using the `include` option with `findMany` to fetch all fields of related `posts` and `profile` records for multiple `User` records.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/500-reference/050-prisma-client-reference.mdx#_snippet_61

LANGUAGE: TypeScript
CODE:
```
const users = await prisma.user.findMany({
  include: {
    posts: true, // Returns all fields for all posts
    profile: true, // Returns all Profile fields
  },
});
```

----------------------------------------

TITLE: Setting PostgreSQL Connection URL in Environment Variables
DESCRIPTION: This snippet demonstrates how to define the DATABASE_URL environment variable in a .env file with the proper PostgreSQL connection string format.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/100-connect-your-database-node-postgresql.mdx#2025-04-21_snippet_1

LANGUAGE: bash
CODE:
```
DATABASE_URL="postgresql://johndoe:randompassword@localhost:5432/mydb?schema=public"
```

----------------------------------------

TITLE: Installing Prisma CLI in TypeScript Project
DESCRIPTION: Adds the Prisma CLI as a development dependency to the project using npm. This is the first step in setting up Prisma ORM in an existing project.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/110-relational-databases-typescript-sqlserver.mdx#2025-04-21_snippet_0

LANGUAGE: bash
CODE:
```
npm install prisma --save-dev
```

----------------------------------------

TITLE: Initializing Prisma Client Connection with TypeScript
DESCRIPTION: Sets up the basic Prisma Client connection and error handling structure. Creates a main async function for database operations and handles proper disconnection.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/110-relational-databases/250-querying-the-database-typescript-mysql.mdx#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // ... you will write your Prisma Client queries here
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
```

----------------------------------------

TITLE: Defining Prisma Schema for User and Password Models
DESCRIPTION: This Prisma schema defines the data model for User and Password, including their relationships and field types. It specifies PostgreSQL as the database provider and sets up the Prisma Client generator.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/100-queries/064-custom-models.mdx#2025-04-21_snippet_1

LANGUAGE: prisma
CODE:
```
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id       String    @id @default(cuid())
  email    String
  password Password?
}

model Password {
  hash   String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique
}
```

----------------------------------------

TITLE: Installing Prisma Client
DESCRIPTION: Install the `@prisma/client` package using npm. This package contains the generated client code that allows your application to interact with the database.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/060-migrate-from-drizzle.mdx#_snippet_10

LANGUAGE: terminal
CODE:
```
npm install @prisma/client
```

----------------------------------------

TITLE: Define User and Post Models in Prisma Schema
DESCRIPTION: Add User and Post models to the schema.prisma file. Defines fields, types, constraints (like @id, @unique, @default), and relationships (@relation) for a simple blog structure.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/090-nextjs.mdx#_snippet_4

LANGUAGE: prisma
CODE:
```
generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String?
  posts Post[]
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  authorId  Int
  author    User    @relation(fields: [authorId], references: [id])
}
```

----------------------------------------

TITLE: Creating Nested Records with Prisma Client
DESCRIPTION: Creates a new user record with nested creation of related posts and profile records, then queries the created data with included relations.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/110-relational-databases/250-querying-the-database-node-mysql.mdx#2025-04-21_snippet_2

LANGUAGE: javascript
CODE:
```
async function main() {
  await prisma.user.create({
    data: {
      name: 'Alice',
      email: '<EMAIL>',
      posts: {
        create: { title: 'Hello World' },
      },
      profile: {
        create: { bio: 'I like turtles' },
      },
    },
  })

  const allUsers = await prisma.user.findMany({
    include: {
      posts: true,
      profile: true,
    },
  })
  console.dir(allUsers, { depth: null })
}
```

----------------------------------------

TITLE: Creating Nested Records with Prisma Client
DESCRIPTION: TypeScript example showing how to create a User record with nested Post and Category records using Prisma Client's nested write capabilities.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/100-prisma-schema/20-data-model/10-models.mdx#2025-04-21_snippet_2

LANGUAGE: typescript
CODE:
```
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient({})

async function main() {
  const user = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Ariadne',
      posts: {
        create: [
          {
            title: 'My first day at Prisma',
            categories: {
              create: {
                name: 'Office',
              },
            },
          },
          {
            title: 'How to connect to a SQLite database',
            categories: {
              create: [{ name: 'Databases' }, { name: 'Tutorials' }],
            },
          },
        ],
      },
    },
  })

  const returnUser = await prisma.user.findUnique({
    where: {
      id: user.id,
    },
    include: {
      posts: {
        include: {
          categories: true,
        },
      },
    },
  })

  console.log(returnUser)
}

main()
```

----------------------------------------

TITLE: Querying All Users with Prisma Client
DESCRIPTION: Simple query to fetch all users from the database using Prisma Client's findMany method. Demonstrates basic read operation.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/110-relational-databases/250-querying-the-database-typescript-postgresql.mdx#2025-04-21_snippet_1

LANGUAGE: typescript
CODE:
```
async function main() {
  const allUsers = await prisma.user.findMany()
  console.log(allUsers)
}
```

----------------------------------------

TITLE: Initializing Prisma in SolidStart Project
DESCRIPTION: Run the Prisma initialization command to set up the Prisma environment. This command creates the default `prisma` directory, the `schema.prisma` file, and configures the database connection URL in a `.env` file. The `--output` flag specifies the location for the generated Prisma Client.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/180-solid-start.mdx#_snippet_4

LANGUAGE: Terminal
CODE:
```
npx prisma init --db --output ../src/generated/prisma
```

----------------------------------------

TITLE: Creating Records with Nested Relations in Prisma Client
DESCRIPTION: Shows how to create a User record with nested Post and Profile records in a single transaction using Prisma's nested writes capability. The query also retrieves the created data with related records included.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-node-cockroachdb.mdx#2025-04-21_snippet_2

LANGUAGE: javascript
CODE:
```
async function main() {
  await prisma.user.create({
    data: {
      name: 'Alice',
      email: '<EMAIL>',
      posts: {
        create: { title: 'Hello World' },
      },
      profile: {
        create: { bio: 'I like turtles' },
      },
    },
  })

  const allUsers = await prisma.user.findMany({
    include: {
      posts: true,
      profile: true,
    },
  })
  console.dir(allUsers, { depth: null })
}
```

----------------------------------------

TITLE: Installing Prisma Client
DESCRIPTION: This command installs the Prisma Client package as a project dependency.  Prisma Client is a type-safe database client that provides an interface for interacting with the database.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/030-migrate-from-typeorm.mdx#2025-04-21_snippet_4

LANGUAGE: terminal
CODE:
```
npm install @prisma/client
```

----------------------------------------

TITLE: Initialize Prisma ORM
DESCRIPTION: Run the prisma init command to set up Prisma in the project, creating a prisma directory, schema.prisma file, and a .env file. Specifies the database type and output directory for the generated client.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/090-nextjs.mdx#_snippet_3

LANGUAGE: terminal
CODE:
```
npx prisma init --db --output ../app/generated/prisma
```

----------------------------------------

TITLE: Creating Objects with Prisma ORM
DESCRIPTION: Demonstrates how to create a new record with Prisma ORM. Prisma provides a straightforward create method that takes a data object with the fields to be created.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/800-more/400-comparisons/02-prisma-and-sequelize.mdx#2025-04-21_snippet_9

LANGUAGE: typescript
CODE:
```
const user = await prisma.user.create({
  data: {
    email: '<EMAIL>',
  },
})
```

----------------------------------------

TITLE: Updating Prisma Dependencies
DESCRIPTION: Command to update the Prisma CLI and client libraries to their latest versions using npm.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/210-shopify.mdx#_snippet_5

LANGUAGE: terminal
CODE:
```
npm install prisma --save-dev && npm install @prisma/client
```

----------------------------------------

TITLE: Complete Prisma Schema for New Project with Data Models
DESCRIPTION: Comprehensive Prisma schema for a new project that includes datasource, generator, and data models for a blog application with Post and User entities. This schema defines the relationships between models and field attributes.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/050-overview/300-prisma-in-your-stack/04-is-prisma-an-orm.mdx#2025-04-21_snippet_5

LANGUAGE: prisma
CODE:
```
// schema.prisma
datasource db {
  provider = "postgresql"
  url      = "postgresql://janedoe:janedoe@localhost:5432/hello-prisma"
}

generator client {
  provider = "prisma-client-js"
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String? @map("post_content")
  published Boolean @default(false)
  author    User?   @relation(fields: [authorId], references: [id])
  authorId  Int?
}

model User {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String?
  posts Post[]
}
```

----------------------------------------

TITLE: Example Prisma Schema for MongoDB
DESCRIPTION: Defines a complete Prisma schema for a MongoDB database including a data source, generator, and data models (User, Post) with relations, enums, and MongoDB-specific attributes like `@db.ObjectId` and `@map("_id")`.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/100-prisma-schema/10-overview/index.mdx#_snippet_1

LANGUAGE: prisma
CODE:
```
datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  createdAt DateTime @default(now())
  email     String   @unique
  name      String?
  role      Role     @default(USER)
  posts     Post[]
}

model Post {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  published Boolean  @default(false)
  title     String
  author    User?    @relation(fields: [authorId], references: [id])
  authorId  String   @db.ObjectId
}

enum Role {
  USER
  ADMIN
}
```

----------------------------------------

TITLE: Initializing Prisma Client with TypeScript
DESCRIPTION: Basic setup for Prisma Client including initialization, connection handling, and error management. Creates a reusable database client instance and defines the main execution structure.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/110-relational-databases/250-querying-the-database-typescript-cockroachdb.mdx#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // ... you will write your Prisma Client queries here
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
```

----------------------------------------

TITLE: Importing and Instantiating PrismaClient in TypeScript
DESCRIPTION: This snippet demonstrates how to import the PrismaClient from the @prisma/client package and create a new instance in TypeScript. It's the recommended way to set up Prisma Client in a TypeScript project.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/000-setup-and-configuration/015-instantiate-prisma-client.mdx#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()
```

----------------------------------------

TITLE: Generating Prisma Client from Prisma Schema
DESCRIPTION: This command reads your Prisma schema and generates the Prisma Client, creating TypeScript types, methods, and queries tailored to your schema.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/110-relational-databases/_install-prisma-client-partial.mdx#2025-04-21_snippet_1

LANGUAGE: terminal
CODE:
```
npx prisma generate
```

----------------------------------------

TITLE: Invoking Prisma CLI with npx
DESCRIPTION: This command demonstrates how to invoke the Prisma CLI using npx. It's the initial step in setting up a Prisma ORM project.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/200-add-to-existing-project/_prisma-init-partial.mdx#2025-04-21_snippet_0

LANGUAGE: terminal
CODE:
```
npx prisma
```

----------------------------------------

TITLE: Retrieving All User Records with Prisma Client (TypeScript)
DESCRIPTION: Shows how to fetch all `User` records currently in the database using the `prisma.user.findMany()` query. It retrieves all existing users and logs the resulting array of user objects.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/01-quickstart-sqlite.mdx#_snippet_13

LANGUAGE: ts
CODE:
```
import { PrismaClient } from './generated/prisma'

const prisma = new PrismaClient()

async function main() {
  const users = await prisma.user.findMany()
  console.log(users)
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
```

----------------------------------------

TITLE: Initializing Prisma Client in Node.js
DESCRIPTION: Sets up the Prisma Client instance for database access, defines an async main function for queries, and includes proper connection handling with disconnect on completion or error.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-node-cockroachdb.mdx#2025-04-21_snippet_0

LANGUAGE: javascript
CODE:
```
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  // ... you will write your Prisma Client queries here
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
```

----------------------------------------

TITLE: Installing Prisma CLI
DESCRIPTION: Command to install Prisma CLI as a dev dependency using npm.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/000-setup-and-configuration/010-generating-prisma-client.mdx#2025-04-21_snippet_0

LANGUAGE: terminal
CODE:
```
npm install prisma --save-dev
```

----------------------------------------

TITLE: Install Prisma Client
DESCRIPTION: Install the Prisma Client library in your project. This package contains the generated client code and the necessary runtime.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/000-setup-and-configuration/005-introduction.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
npm install @prisma/client
```

----------------------------------------

TITLE: Querying All Users with Prisma Client (TypeScript)
DESCRIPTION: Adds a query inside the `main` function to fetch all records from the `User` model using `prisma.user.findMany()` and logs the result to the console.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/120-mongodb/250-querying-the-database-typescript-mongodb.mdx#_snippet_1

LANGUAGE: typescript
CODE:
```
async function main() {
  // ... you will write your Prisma Client queries here
//add-start
  const allUsers = await prisma.user.findMany()
  console.log(allUsers)
//add-end
}
```

----------------------------------------

TITLE: Run Prisma Database Migration (Terminal)
DESCRIPTION: Executes the `prisma migrate dev` command to apply pending database schema changes defined in the Prisma schema file. The `--name init` flag names the migration. This step creates database tables if they don't exist, based on the schema.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/500-deployment/301-edge/485-deploy-to-vercel.mdx#_snippet_29

LANGUAGE: Terminal
CODE:
```
npx prisma migrate dev --name init
```

----------------------------------------

TITLE: Set Database Connection URL (Bash)
DESCRIPTION: Shows the format for the `DATABASE_URL` environment variable, which contains the connection string for the Prisma Postgres database, including the Accelerate endpoint and API key. This variable is typically placed in the `.env` file.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/01-quickstart-prismaPostgres.mdx#_snippet_3

LANGUAGE: bash
CODE:
```
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=ey...."
```

----------------------------------------

TITLE: Resulting Prisma Query
DESCRIPTION: This TypeScript code snippet is the Prisma query generated by Cursor based on the provided prompt. It uses the `prisma.user.findMany` method with nested `where` clauses to filter users based on their `deletedAt` status, and the status and `deletedAt` status of their related organization and subscription. It also includes related data using `include`, orders the results, and applies pagination.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/800-more/350-ai-tools/100-cursor.mdx#_snippet_4

LANGUAGE: typescript
CODE:
```
const activeUsers = await prisma.user.findMany({
  where: {
    deletedAt: null,
    organization: {
      deletedAt: null,
      subscription: {
        deletedAt: null,
        status: 'ACTIVE'
      }
    }
  },
  include: {
    organization: {
      include: {
        subscription: true
      }
    }
  },
  orderBy: {
    createdAt: 'desc'
  },
  skip: 0,
  take: 10
});
```

----------------------------------------

TITLE: Creating Reusable PrismaClient Instance (TypeScript)
DESCRIPTION: This snippet demonstrates how to create a single instance of `PrismaClient` within a module and export it. Node.js module caching ensures that subsequent imports return the same instance, preventing the creation of multiple clients in long-running applications.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/000-setup-and-configuration/050-databases-connections/index.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { PrismaClient } from '@prisma/client'

let prisma = new PrismaClient()

export default prisma
```

----------------------------------------

TITLE: Installing Prisma CLI - npm
DESCRIPTION: Installs the Prisma command-line interface as a development dependency in your project using npm. This is required to run Prisma commands like `init`, `migrate`, and `studio`.
SOURCE: https://github.com/prisma/docs/blob/main/content/250-postgres/1100-integrations/100-netlify.mdx#_snippet_2

LANGUAGE: terminal
CODE:
```
npm install prisma --save-dev
```

----------------------------------------

TITLE: Example .env File for Database URL - Environment
DESCRIPTION: Provides an example of a `.env` file content used to set the `DATABASE_URL` environment variable. This file is typically placed alongside the `schema.prisma` file and picked up by the Prisma CLI.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/500-reference/100-prisma-schema-reference.mdx#_snippet_2

LANGUAGE: env
CODE:
```
DATABASE_URL=postgresql://johndoe:mypassword@localhost:5432/mydb?schema=public
```

----------------------------------------

TITLE: Defining Data Models with Prisma Schema
DESCRIPTION: This snippet defines three models (Post, Profile, and User) in the Prisma schema with their fields and relationships. It includes one-to-many relation between User and Post, and a one-to-one relation between User and Profile.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/150-using-prisma-migrate-typescript-prismaPostgres.mdx#2025-04-21_snippet_0

LANGUAGE: prisma
CODE:
```
model Post {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  title     String
  content   String?
  published Boolean  @default(false)
  author    User     @relation(fields: [authorId], references: [id])
  authorId  Int
}

model Profile {
  id     Int     @id @default(autoincrement())
  bio    String?
  user   User    @relation(fields: [userId], references: [id])
  userId Int     @unique
}

model User {
  id      Int      @id @default(autoincrement())
  email   String   @unique
  name    String?
  posts   Post[]
  profile Profile?
}
```

----------------------------------------

TITLE: Run prisma init (Basic)
DESCRIPTION: Initializes Prisma in the current directory, creating default configuration files. The output provides guidance on the next steps.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/500-reference/200-prisma-cli-reference.mdx#_snippet_3

LANGUAGE: terminal
CODE:
```
prisma init
```

LANGUAGE: terminal
CODE:
```
✔ Your Prisma schema was created at prisma/schema.prisma.
  You can now open it in your favorite editor.

Next steps:
1. Set the DATABASE_URL in the .env file to point to your existing database. If your database has no tables yet, read https://pris.ly/d/getting-started
2. Set the provider of the datasource block in schema.prisma to match your database: postgresql, mysql, sqlite, sqlserver, mongodb or cockroachdb.
3. Run prisma db pull to turn your database schema into a Prisma schema.
4. Run prisma generate to generate Prisma Client. You can then start querying your database.

More information in our documentation:
https://pris.ly/d/getting-started
```

----------------------------------------

TITLE: Create User with Nested Relations and Find All Users (TypeScript)
DESCRIPTION: Demonstrates creating a new `User` record along with related `Post` and `Profile` records using a nested write. It then retrieves all users, including their related posts and profiles, using the `findMany` query with the `include` option.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-typescript-sqlserver.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
async function main() {
  await prisma.user.create({
    data: {
      name: 'Alice',
      email: '<EMAIL>',
      posts: {
        create: { title: 'Hello World' },
      },
      profile: {
        create: { bio: 'I like turtles' },
      },
    },
  })

  const allUsers = await prisma.user.findMany({
    include: {
      posts: true,
      profile: true,
    },
  })
  console.dir(allUsers, { depth: null })
}
```

----------------------------------------

TITLE: Import and Instantiate Prisma Client (TypeScript)
DESCRIPTION: Imports the `PrismaClient` class from the generated client library and creates a new instance, which is the entry point for all database queries. This snippet shows the TypeScript import syntax.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/050-overview/100-introduction/100-what-is-prisma.mdx#_snippet_4

LANGUAGE: ts
CODE:
```
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()
```

----------------------------------------

TITLE: Creating User with Nested Relations and Finding All Users (TypeScript)
DESCRIPTION: Demonstrates how to create a new User record along with related Post and Profile records using a nested write query with Prisma Client. It then fetches all users, including their related posts and profiles, using the `include` option.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-typescript-prismaPostgres.mdx#_snippet_4

LANGUAGE: typescript
CODE:
```
async function main() {
  //add-start
  await prisma.user.create({
    data: {
      name: 'Alice',
      email: '<EMAIL>',
      posts: {
        create: { title: 'Hello World' },
      },
      profile: {
        create: { bio: 'I like turtles' },
      },
    },
  })

  const allUsers = await prisma.user.findMany({
    include: {
      posts: true,
      profile: true,
    },
  })
  console.dir(allUsers, { depth: null })
  //add-end
}
```

----------------------------------------

TITLE: Defining a One-to-Many Relation in Prisma Schema
DESCRIPTION: This snippet demonstrates how to define a one-to-many relation between User and Post models using the @relation attribute in Prisma schema.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/100-prisma-schema/20-data-model/20-relations/420-relation-mode.mdx#2025-04-21_snippet_0

LANGUAGE: prisma
CODE:
```
model Post {
  id       Int    @id @default(autoincrement())
  title    String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  authorId Int
}

model User {
  id    Int    @id @default(autoincrement())
  posts Post[]
}
```

----------------------------------------

TITLE: Solving n+1 with Prisma's include for Nested Reads
DESCRIPTION: This snippet demonstrates how to use Prisma's include feature to fetch related data in a single query. This approach replaces multiple queries with just two SQL queries - one for users and one for all related posts.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/100-queries/100-query-optimization-performance.mdx#2025-04-21_snippet_6

LANGUAGE: typescript
CODE:
```
const usersWithPosts = await prisma.user.findMany({
  include: {
    posts: true,
  },
})
```

----------------------------------------

TITLE: Running First Database Migration - npx
DESCRIPTION: Executes a development migration using Prisma Migrate. This command applies the changes defined in the `schema.prisma` file (like creating the `User` table) to the database and generates the Prisma Client. The `--name init` flag names this migration.
SOURCE: https://github.com/prisma/docs/blob/main/content/250-postgres/1100-integrations/100-netlify.mdx#_snippet_5

LANGUAGE: terminal
CODE:
```
npx prisma migrate dev --name init
```

----------------------------------------

TITLE: Fetching relations with Include: Prisma ORM
DESCRIPTION: Fetches a user with associated posts using the `include` option in Prisma ORM.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/800-more/400-comparisons/01-prisma-and-typeorm.mdx#2025-04-21_snippet_33

LANGUAGE: typescript
CODE:
```
const posts = await prisma.user.findUnique({
  where: {
    id: 2,
  },
  include: {
    post: true,
  },
})
```

----------------------------------------

TITLE: Initializing Single Prisma Client Instance in Next.js
DESCRIPTION: Creates a singleton pattern for Prisma Client to prevent multiple instances during Next.js hot-reloading. Uses global variable to maintain single instance across development reloads.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/800-more/600-help-and-troubleshooting/400-nextjs-help.mdx#2025-04-21_snippet_0

LANGUAGE: typescript
CODE:
```
import { PrismaClient } from "@prisma/client";

const globalForPrisma = global as unknown as { prisma: PrismaClient };

export const prisma =
  globalForPrisma.prisma || new PrismaClient();

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;
```

----------------------------------------

TITLE: Configuring Basic Prisma Client Generator (Prisma)
DESCRIPTION: Shows the fundamental structure of a `generator` block for the default `prisma-client-js`, specifying the provider and output path. This block is required to generate Prisma Client.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/100-prisma-schema/10-overview/03-generators.mdx#_snippet_0

LANGUAGE: Prisma
CODE:
```
generator client {
  provider = "prisma-client-js"
  output   = "./generated/prisma-client-js"
}
```

----------------------------------------

TITLE: Import and Initialize Prisma Client in TypeScript
DESCRIPTION: Imports the `PrismaClient` class from the specified generated path and creates a new instance. This makes the Prisma Client available for use in the application code to interact with the database.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/100-prisma-schema/10-overview/03-generators.mdx#_snippet_7

LANGUAGE: ts
CODE:
```
import { PrismaClient } from "./generated/prisma/client"

const prisma = new PrismaClient()
```

----------------------------------------

TITLE: Defining Database Schema Models in Prisma
DESCRIPTION: Prisma schema definition containing three models (Post, Profile, User) with relationships and field configurations. Includes primary keys, timestamps, optional fields, and foreign key relationships.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/150-using-prisma-migrate-typescript-postgresql.mdx#2025-04-21_snippet_0

LANGUAGE: prisma
CODE:
```
model Post {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  title     String   @db.VarChar(255)
  content   String?
  published Boolean  @default(false)
  author    User     @relation(fields: [authorId], references: [id])
  authorId  Int
}

model Profile {
  id     Int     @id @default(autoincrement())
  bio    String?
  user   User    @relation(fields: [userId], references: [id])
  userId Int     @unique
}

model User {
  id      Int      @id @default(autoincrement())
  email   String   @unique
  name    String?
  posts   Post[]
  profile Profile?
}
```

----------------------------------------

TITLE: Initialize Prisma Client in Node.js Script
DESCRIPTION: Sets up a basic Node.js script using Prisma Client. It imports the client, instantiates it, defines an async main function for queries, and includes error handling and disconnection logic.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-typescript-sqlserver.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { PrismaClient } from './generated/prisma'

const prisma = new PrismaClient()

async function main() {
  // ... you will write your Prisma Client queries here
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
```

----------------------------------------

TITLE: Implementing Order Creation Function in TypeScript
DESCRIPTION: This function creates a customer order in the database using Prisma Client. It checks if a product exists, connects to an existing customer or creates a new one based on email, and creates order details with the product and quantity information.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/200-prisma-client/450-testing/150-integration-testing.mdx#2025-04-21_snippet_3

LANGUAGE: typescript
CODE:
```
import prisma from '../client'

export interface Customer {
  id?: number
  name?: string
  email: string
  address?: string
}

export interface OrderInput {
  customer: Customer
  productId: number
  quantity: number
}

/**
 * Creates an order with customer.
 * @param input The order parameters
 */
export async function createOrder(input: OrderInput) {
  const { productId, quantity, customer } = input
  const { name, email, address } = customer

  // Get the product
  const product = await prisma.product.findUnique({
    where: {
      id: productId,
    },
  })

  // If the product is null its out of stock, return error.
  if (!product) return new Error('Out of stock')

  // If the customer is new then create the record, otherwise connect via their unique email
  await prisma.customerOrder.create({
    data: {
      customer: {
        connectOrCreate: {
          create: {
            name,
            email,
            address,
          },
          where: {
            email,
          },
        },
      },
      orderDetails: {
        create: {
          total: product.price,
          quantity,
          products: {
            connect: {
              id: product.id,
            },
          },
        },
      },
    },
  })
}
```

----------------------------------------

TITLE: Defining Prisma Schema Models
DESCRIPTION: Define the data models (User and Post) and their relationships in the `schema.prisma` file, configuring the client generator output path.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/190-sveltekit.mdx#_snippet_4

LANGUAGE: prisma
CODE:
```
generator client {
	//edit-next-line
  provider = "prisma-client"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

//add-start
model User {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String?
  posts Post[]
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  authorId  Int
  author    User    @relation(fields: [authorId], references: [id])
}
//add-end
```

----------------------------------------

TITLE: Updated Prisma Schema with User and Post Models
DESCRIPTION: This Prisma schema defines two models: User and Post. The Post model has a relation to the User model via the authorId field.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/050-overview/500-databases/950-cloudflare-d1.mdx#2025-04-21_snippet_9

LANGUAGE: prisma
CODE:
```
model User {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String?
  posts Post[]
}

model Post {
  id       Int    @id @default(autoincrement())
  title    String
  author   User   @relation(fields: [authorId], references: [id])
  authorId Int
}
```

----------------------------------------

TITLE: Defining Database Schema with Prisma
DESCRIPTION: This Prisma schema defines a SQLite database with Post and User models. It includes relationships between the models and specifies field types and constraints.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/050-overview/300-prisma-in-your-stack/01-rest.mdx#2025-04-21_snippet_0

LANGUAGE: prisma
CODE:
```
datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

generator client {
  provider = "prisma-client-js"
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  author    User?   @relation(fields: [authorId], references: [id])
  authorId  Int?
}

model User {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String?
  posts Post[]
}
```

----------------------------------------

TITLE: Updating a Post with Both New and Existing Tags
DESCRIPTION: TypeScript code showing how to update a post by both connecting to existing tags and creating new ones in a single operation.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/800-more/600-help-and-troubleshooting/200-working-with-many-to-many-relations.mdx#2025-04-21_snippet_4

LANGUAGE: typescript
CODE:
```
await prisma.post.update({
  where: { id: 1 },
  data: {
    title: 'Prisma is awesome!',
    tags: { set: [{ id: 1 }, { id: 2 }], create: { name: 'typescript' } },
  },
})
```

----------------------------------------

TITLE: Prisma Schema Datasource Configuration (Prisma)
DESCRIPTION: Shows the standard `datasource` block in the `schema.prisma` file, configured to use the `postgresql` provider and read the database connection URL from the `DATABASE_URL` environment variable.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/03-prisma-postgres/110-import-from-existing-database-postgresql.mdx#_snippet_13

LANGUAGE: prisma
CODE:
```
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

----------------------------------------

TITLE: Installing Prisma Dependencies (Other DBs) - Terminal
DESCRIPTION: Installs the necessary npm packages for using Prisma with databases other than Postgres, including the Prisma CLI, tsx for running TypeScript, and the core Prisma client.
SOURCE: https://github.com/prisma/docs/blob/main/content/800-guides/999-making-guides.mdx#_snippet_6

LANGUAGE: terminal
CODE:
```
npm install prisma tsx --save-dev
npm install @prisma/client
```

----------------------------------------

TITLE: Fetching Users with Included Posts using Prisma Client (TypeScript)
DESCRIPTION: Shows how to retrieve all User records and include their associated Post records in the result using the `include` option in the `prisma.user.findMany` query.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/01-quickstart-sqlite.mdx#_snippet_17

LANGUAGE: TypeScript
CODE:
```
import { PrismaClient } from './generated/prisma'

const prisma = new PrismaClient()

async function main() {
// add-start
  const usersWithPosts = await prisma.user.findMany({
    include: {
      posts: true,
    },
  })
  console.dir(usersWithPosts, { depth: null })
  // add-end
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
```

----------------------------------------

TITLE: Defining Enum and Using in Model (Relational, Prisma)
DESCRIPTION: Defines a simple `Role` enum with two values, `USER` and `ADMIN`. It then shows how to use this enum as the type for a field (`role`) in a `User` model, demonstrating basic enum usage in a relational database context within the Prisma schema.
SOURCE: https://github.com/prisma/docs/blob/main/content/200-orm/500-reference/100-prisma-schema-reference.mdx#_snippet_169

LANGUAGE: prisma
CODE:
```
enum Role {
  USER
  ADMIN
}

model User {
  id   Int  @id @default(autoincrement())
  role Role
}
```

----------------------------------------

TITLE: Query All Users with Prisma Client findMany
DESCRIPTION: Adds a query inside the `main` function to fetch all records from the `User` model using `prisma.user.findMany()`. The result is then printed to the console.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-typescript-sqlserver.mdx#_snippet_1

LANGUAGE: typescript
CODE:
```
async function main() {
  // ... you will write your Prisma Client queries here
  //add-start
  const allUsers = await prisma.user.findMany()
  console.log(allUsers)
  //add-end
}
```

----------------------------------------

TITLE: Create User with Nested Relations and Find All Users (TypeScript)
DESCRIPTION: Demonstrates creating a new `User` record along with related `Post` and `Profile` records using a nested write. It then fetches all users, including their related posts and profiles, using `findMany` with the `include` option.
SOURCE: https://github.com/prisma/docs/blob/main/content/100-getting-started/02-setup-prisma/100-start-from-scratch/110-relational-databases/250-querying-the-database-typescript-postgresql.mdx#_snippet_4

LANGUAGE: TypeScript
CODE:
```
async function main() {
  //add-start
  await prisma.user.create({
    data: {
      name: 'Alice',
      email: '<EMAIL>',
      posts: {
        create: { title: 'Hello World' },
      },
      profile: {
        create: { bio: 'I like turtles' },
      },
    },
  })

  const allUsers = await prisma.user.findMany({
    include: {
      posts: true,
      profile: true,
    },
  })
  console.dir(allUsers, { depth: null })
  //add-end
}
```