# 🎯 Organization-Based Load Targeting

## Overview

The Carrier Portal now supports sophisticated organization-based load targeting using <PERSON>'s organization management system. This feature allows you to show specific loads only to certain carrier companies, creating a competitive advantage and more personalized experience.

## 🏢 How It Works

### Organization Structure
- Each carrier user belongs to a **Clerk Organization** named after their company
- Organizations are created and managed through <PERSON>'s admin dashboard
- Users are added to organizations as members or admins

### Load Visibility Logic
1. **Public Loads**: Visible to all carriers (default behavior)
2. **Targeted Loads**: Only visible to specific organizations
3. **Admin Override**: Admin users can see all loads regardless of targeting

### Visual Indicators
- **Blue Highlighting**: Loads specifically targeted to the user's organization appear with a subtle blue glow and left border
- **Legend**: When targeted loads are present, a blue legend appears explaining the highlighting
- **Counter**: Shows how many loads are specifically targeted to the user

## 🛠️ Setup Instructions

### 1. Clerk Organization Management

#### Creating Organizations
1. Log into Clerk Dashboard
2. Navigate to Organizations
3. Create a new organization with the **exact company name** as the organization name
4. Add carrier users as members to their respective organizations

#### Organization Roles
- `org:admin`: Organization administrators (mapped to ADMIN role in system)
- `org:member`: Regular organization members (mapped to CARRIER role in system)

### 2. Airtable Configuration

Add these new fields to your Airtable base:

#### Required Fields
| Field Name | Type | Description |
|------------|------|-------------|
| `Target Organizations` | Multiple select or Single line text | Organizations that can see this load |
| `Is Public` | Checkbox | Whether the load is visible to all carriers (checked = public) |

#### Field Setup in Airtable
1. **Target Organizations**:
   - Type: Multiple select (recommended) or Single line text
   - Options: Add all your carrier company names as options
   - Example values: "ABC Trucking", "XYZ Transport", "Fast Freight LLC"

2. **Is Public**:
   - Type: Checkbox
   - Default: Checked (loads are public by default)
   - Uncheck to make loads organization-specific

### 3. Load Targeting Examples

#### Example 1: Public Load (Default)
- `Is Public`: ✅ Checked
- `Target Organizations`: (empty)
- **Result**: All carriers can see this load

#### Example 2: Single Organization Target
- `Is Public`: ❌ Unchecked  
- `Target Organizations`: "ABC Trucking"
- **Result**: Only "ABC Trucking" organization members can see this load

#### Example 3: Multiple Organization Target
- `Is Public`: ❌ Unchecked
- `Target Organizations`: "ABC Trucking", "XYZ Transport"
- **Result**: Only "ABC Trucking" and "XYZ Transport" can see this load

#### Example 4: Exclusive Opportunity
- `Is Public`: ❌ Unchecked
- `Target Organizations`: "Premium Partner LLC"
- **Result**: Exclusive load visible only to your premium partner

## 💼 Business Use Cases

### 1. Premium Partners
Target high-value loads to your most trusted carrier partners:
```
Rate: $3,500
Target Organizations: "Elite Transport", "Reliable Freight Co"
Is Public: No
```

### 2. Regional Specialists
Show local loads to carriers with regional expertise:
```
Route: Los Angeles to Phoenix
Target Organizations: "Southwest Logistics", "Desert Express"
Is Public: No
```

### 3. Equipment-Specific Targeting
Target specialized loads to carriers with specific equipment:
```
Equipment: Temperature Controlled
Target Organizations: "Cold Chain Experts", "Reefer Solutions Inc"
Is Public: No
```

### 4. Preferred Rate Loads
Offer better rates to preferred carriers:
```
Rate: $2,800 (vs $2,500 public rate)
Target Organizations: "Top Tier Transport"
Is Public: No (also create a public version with lower rate)
```

## 🎯 Best Practices

### Organization Naming
- Use **exact company names** as they appear in legal documents
- Be consistent with naming conventions
- Avoid abbreviations unless that's how the company refers to itself
- Example: "ABC Transportation LLC" not "ABC Trans"

### Load Targeting Strategy
1. **Start with Public**: Make most loads public initially
2. **Strategic Targeting**: Use targeting for special opportunities
3. **Test and Iterate**: Monitor which targeted loads perform better
4. **Maintain Balance**: Don't over-target (carriers need volume)

### Airtable Workflow
1. Create your load record normally
2. If targeting specific organizations:
   - Uncheck "Is Public"
   - Select target organizations
3. If keeping public, leave "Is Public" checked

## 📊 Monitoring and Analytics

### Load Performance Tracking
- Monitor booking rates for targeted vs public loads
- Track which organizations respond fastest to targeted loads
- Analyze revenue impact of targeted load strategies

### Organization Engagement
- See which organizations get the most targeted loads
- Track response rates by organization
- Adjust targeting based on performance

## 🔧 Technical Details

### Database Changes
The system automatically:
- Syncs organization information from Clerk
- Filters loads based on user's organization
- Updates user organization data on each login

### API Behavior
- `GET /api/v1/airtable-orders/available` now filters by organization
- Admin users see all loads regardless of targeting
- Organization information is included in load response

### Security
- Organization filtering happens server-side
- Users cannot bypass organization restrictions
- Admin override requires proper role assignment

## 🚨 Troubleshooting

### Load Not Visible to Expected Organization
1. Check organization name spelling in Airtable matches Clerk exactly
2. Verify user is properly assigned to organization in Clerk
3. Ensure "Is Public" is unchecked for targeted loads
4. Check if user needs to log out/in to refresh organization data

### User Not in Correct Organization
1. Check Clerk organization membership
2. Verify organization name matches company name
3. User may need to log out and back in

### Admin Can't See Loads
1. Verify admin role is properly set in database
2. Check if admin is assigned to an organization (optional for admins)

## 📝 Migration Notes

### Existing Loads
- All existing loads default to public visibility
- No immediate action required
- Can be selectively targeted later

### User Organizations
- Existing users will be updated with organization info on next login
- No data loss occurs during migration
- Organization assignment can be updated anytime in Clerk

## 🔮 Future Enhancements

### Planned Features
- **Time-based Targeting**: Target loads during specific time windows
- **Geographic Targeting**: Auto-target based on carrier service areas
- **Performance-based Targeting**: Auto-target top-performing carriers
- **Auction-style Targeting**: Competitive bidding for exclusive loads

---

**Need Help?** Contact the development team for assistance with organization setup or targeting strategies.

**Last Updated**: January 2025 