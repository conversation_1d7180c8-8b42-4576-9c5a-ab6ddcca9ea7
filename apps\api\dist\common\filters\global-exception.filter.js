"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var GlobalExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const crypto_1 = require("crypto");
let GlobalExceptionFilter = GlobalExceptionFilter_1 = class GlobalExceptionFilter {
    logger = new common_1.Logger(GlobalExceptionFilter_1.name);
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const correlationId = (0, crypto_1.randomUUID)();
        request.correlationId = correlationId;
        const errorResponse = this.getErrorResponse(exception, correlationId, request);
        this.logError(exception, errorResponse, request, correlationId);
        response.status(errorResponse.statusCode).json(errorResponse.body);
    }
    getErrorResponse(exception, correlationId, request) {
        let statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let error = 'Internal Server Error';
        let details = null;
        if (exception instanceof common_1.HttpException) {
            statusCode = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
            }
            else if (typeof exceptionResponse === 'object') {
                message = exceptionResponse.message || exception.message;
                error = exceptionResponse.error || error;
                details = exceptionResponse.details || null;
            }
        }
        else if (exception instanceof Error) {
            if (exception.message.includes('timeout')) {
                statusCode = common_1.HttpStatus.REQUEST_TIMEOUT;
                message = 'Request timeout - please try again';
                error = 'Request Timeout';
            }
            else if (exception.message.includes('connection') || exception.message.includes('ECONNREFUSED')) {
                statusCode = common_1.HttpStatus.SERVICE_UNAVAILABLE;
                message = 'Service temporarily unavailable';
                error = 'Service Unavailable';
            }
            else if (exception.message.includes('AIRTABLE') || exception.message.includes('External API')) {
                statusCode = common_1.HttpStatus.BAD_GATEWAY;
                message = 'External service error - please try again';
                error = 'External Service Error';
            }
            else if (exception.message.includes('P2002')) {
                statusCode = common_1.HttpStatus.CONFLICT;
                message = 'This record already exists';
                error = 'Duplicate Entry';
            }
            else if (exception.message.includes('P2025')) {
                statusCode = common_1.HttpStatus.NOT_FOUND;
                message = 'Record not found';
                error = 'Not Found';
            }
            else if (exception.message.includes('P1001') || exception.message.includes('P1002')) {
                statusCode = common_1.HttpStatus.SERVICE_UNAVAILABLE;
                message = 'Database temporarily unavailable';
                error = 'Database Connection Error';
            }
            else {
                message = process.env.NODE_ENV === 'production'
                    ? 'An unexpected error occurred'
                    : exception.message;
            }
        }
        else if (typeof exception === 'object' && exception !== null && 'message' in exception) {
            message = process.env.NODE_ENV === 'production'
                ? 'An unexpected error occurred'
                : String(exception.message);
        }
        const errorClassification = this.classifyError(exception, statusCode);
        return {
            statusCode,
            body: {
                statusCode,
                error,
                message,
                correlationId,
                timestamp: new Date().toISOString(),
                path: request.url,
                method: request.method,
                ...(details && { details }),
                ...(errorClassification && { classification: errorClassification }),
                ...(process.env.NODE_ENV === 'development' && {
                    stack: exception instanceof Error ? exception.stack : undefined
                })
            }
        };
    }
    classifyError(exception, statusCode) {
        if (statusCode >= 500) {
            if (exception instanceof Error && exception.message.includes('P')) {
                return 'database_error';
            }
            if (exception instanceof Error) {
                if (exception.message.includes('timeout'))
                    return 'timeout_error';
                if (exception.message.includes('connection'))
                    return 'connection_error';
                if (exception.message.includes('AIRTABLE'))
                    return 'external_service_error';
            }
            return 'system_error';
        }
        else if (statusCode >= 400 && statusCode < 500) {
            return 'user_error';
        }
        return 'unknown_error';
    }
    logError(exception, errorResponse, request, correlationId) {
        const { statusCode, body } = errorResponse;
        const isServerError = statusCode >= 500;
        const logData = {
            correlationId,
            statusCode,
            error: body.error,
            message: body.message,
            classification: body.classification,
            method: request.method,
            url: request.url,
            userAgent: request.headers['user-agent'],
            ip: request.ip,
            userId: request.auth?.sub,
            timestamp: body.timestamp,
            stack: exception instanceof Error ? exception.stack : undefined
        };
        if (isServerError) {
            this.logger.error(`Server Error [${correlationId}]`, logData);
        }
        else {
            this.logger.warn(`Client Error [${correlationId}]`, logData);
        }
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = GlobalExceptionFilter_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, common_1.Catch)()
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map