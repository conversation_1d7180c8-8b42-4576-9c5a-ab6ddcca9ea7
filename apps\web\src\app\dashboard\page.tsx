"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Activity, 
  DollarSign, 
  Truck, 
  TrendingUp, 
  MapPin, 
  Calendar,
  Package,
  RefreshCw,
  Loader2,
  AlertCircle,
  Search,
  Briefcase,
  ExternalLink,
  ArrowUpRight,
  ArrowDownRight,
  BarChart3,
  Eye,
  Clock
} from "lucide-react";
import Link from "next/link";
import { ProfileCompletion, useProfileCompletion } from "@/components/ui/profile-completion";
import { HelpTooltip } from "@/components/ui/help-tooltip";
import { useOnboardingTour } from "@/components/ui/onboarding-tour";
import {
  NoAssignedLoadsEmptyState,
  NoBidsEmptyState,
  FirstTimeUserEmptyState
} from "@/components/ui/empty-state";
import { ApiDebug } from "@/components/debug/api-debug";

interface Load {
  id: string;
  airtableRecordId: string;
  proNumber?: string;
  originCity?: string;
  originState?: string;
  destinationCity?: string;
  destinationState?: string;
  pickupDateUtc?: string;
  deliveryDateUtc?: string;
  equipmentRequired?: string;
  weightLbs?: number;
  rate?: number;
  status?: string;
  awardedToCarrierProfileId?: string;
  createdAt: string;
  updatedAt: string;
}

interface Bid {
  id: string;
  loadId: string;
  bidAmount: number;
  carrierNotes?: string;
  status?: string;
  createdAt: string;
  load: Load;
}

interface DashboardMetrics {
  totalAssignedLoads: number;
  totalActiveBids: number;
  totalEarnings: number;
  recentLoads: Load[];
  recentBids: Bid[];
  loadsByStatus: { [key: string]: number };
  thisWeekLoads: number;
  lastWeekLoads: number;
}

interface QuickActionDataItem {
  label: string;
  linkPath: string;
  icon: React.ComponentType<any>;
  variant: "default" | "outline" | "secondary" | "ghost" | "link" | null | undefined;
}

// Progress Bar Component
const ProgressBar: React.FC<{ value: number; max: number; className?: string; color?: string }> = ({ 
  value, 
  max, 
  className = "", 
  color = "bg-blue-500" 
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  return (
    <div className={`w-full bg-muted rounded-full h-2 ${className}`}>
      <div 
        className={`h-2 rounded-full transition-all duration-700 ease-out ${color}`}
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
};

const quickActionsData: QuickActionDataItem[] = [
  {
    label: "Find New Loads",
    linkPath: "/loadboard",
    icon: Search,
    variant: "default" as const,
  },
  {
    label: "View My Loads",
    linkPath: "/my-loads",
    icon: Eye,
    variant: "outline" as const,
  },
];

export default function DashboardPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const { getToken } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<any>(null);
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalAssignedLoads: 0,
    totalActiveBids: 0,
    totalEarnings: 0,
    recentLoads: [],
    recentBids: [],
    loadsByStatus: {},
    thisWeekLoads: 0,
    lastWeekLoads: 0,
  });

  // Onboarding tour
  const { hasCompletedTour, startTour } = useOnboardingTour();
  
  // Profile completion
  const profileCompletion = useProfileCompletion(profileData);

  const fetchDashboardData = useCallback(async () => {
    if (!isUserLoaded || !user) return;
    
    setIsLoading(true);
    setError(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Fetch dashboard metrics using MC Number targeting
      const [metricsResponse, profileResponse] = await Promise.all([
        fetch('/api/v1/airtable-orders/dashboard-metrics', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
        fetch('/api/v1/carrier-profiles/me', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      ]);

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData);
      }

      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfileData(profileData);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, [isUserLoaded, user, getToken]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Show loading state
  if (!isUserLoaded || isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading dashboard...</span>
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Unable to Load Dashboard</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchDashboardData} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Welcome back, {user?.firstName || user?.companyName || 'Carrier'}!
            </h1>
            <p className="text-muted-foreground">
              {user?.mcNumber ? `MC ${user.mcNumber}` : 'Your carrier dashboard'}
            </p>
          </div>
          
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Button onClick={fetchDashboardData} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Profile Completion */}
                {profileCompletion && profileCompletion.completionPercentage < 100 && (
          <ProfileCompletion
            profileData={profileData}
            showCompact={true}
          />
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActionsData.map((action, index) => (
            <Link key={index} href={action.linkPath}>
              <Card className="cursor-pointer hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-lg bg-primary/10">
                      <action.icon className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{action.label}</h3>
                      <p className="text-sm text-muted-foreground">
                        {index === 0 ? 'Browse available loads' : 'Manage your current loads'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Loads</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalAssignedLoads}</div>
              <p className="text-xs text-muted-foreground">Currently assigned</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Bids</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalActiveBids}</div>
              <p className="text-xs text-muted-foreground">Pending responses</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${metrics.totalEarnings.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metrics.thisWeekLoads > metrics.lastWeekLoads ? '+' : ''}
                {metrics.thisWeekLoads - metrics.lastWeekLoads}
              </div>
              <p className="text-xs text-muted-foreground">vs last week</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Loads */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Recent Loads
              </CardTitle>
            </CardHeader>
            <CardContent>
              {metrics.recentLoads.length > 0 ? (
                <div className="space-y-3">
                  {metrics.recentLoads.slice(0, 5).map((load) => (
                    <div key={load.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">
                          {load.originCity}, {load.originState} → {load.destinationCity}, {load.destinationState}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {load.proNumber ? `PRO: ${load.proNumber}` : 'No PRO number'}
                        </p>
                      </div>
                      <Badge variant={load.status === 'completed' ? 'default' : 'secondary'}>
                        {load.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <NoAssignedLoadsEmptyState />
              )}
            </CardContent>
          </Card>

          {/* Recent Bids */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Briefcase className="mr-2 h-5 w-5" />
                Recent Bids
              </CardTitle>
            </CardHeader>
            <CardContent>
              {metrics.recentBids.length > 0 ? (
                <div className="space-y-3">
                  {metrics.recentBids.slice(0, 5).map((bid) => (
                    <div key={bid.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">
                          {bid.load.originCity}, {bid.load.originState} → {bid.load.destinationCity}, {bid.load.destinationState}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Bid: ${bid.bidAmount.toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={bid.status === 'accepted' ? 'default' : 'secondary'}>
                        {bid.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <NoBidsEmptyState />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Debug Section (Development only) */}
        {process.env.NODE_ENV === 'development' && (
          <ApiDebug />
        )}
      </div>
    </PageLayout>
  );
} 