"use client";

import React, { useState, useEffect } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { AdminOnlyWrapper } from "@/components/AdminOnlyWrapper";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  Truck, 
  BarChart3, 
  Settings, 
  Database,
  Shield,
  Activity,
  AlertTriangle,
  Loader2,
  Gavel,
  Clock,
  CheckCircle,
  XCircle,
  DollarSign,
  Phone,
  Mail,
  X,
  Calendar,
} from "lucide-react";
import { toast } from "sonner";
// Real-time notifications now handled by N8N via email/SMS
import { cn } from "@/lib/utils";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function AdminPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const { getToken } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [adminData, setAdminData] = useState<any>(null);
  const [biddingStats, setBiddingStats] = useState<any>(null);
  const [pendingBids, setPendingBids] = useState<any[]>([]);
  const [loadingBids, setLoadingBids] = useState(false);

  // Real-time notifications now handled by N8N via email/SMS
  // Mock state for UI consistency
  const adminBiddingState = { 
    lastBidUpdate: null,
    urgentBids: [],
    pendingBidsCount: 0,
    recentBids: [],
    connectionQuality: 'offline' as const
  };
  const getBiddingNotifications = () => [];
  const clearBidNotification = (id: string) => {};
  const wsConnected = false;
  const wsError = 'N8N handles notifications via email/SMS';

  // Fetch admin data and bidding stats
  useEffect(() => {
    if (isUserLoaded && user?.role === 'ADMIN') {
      fetchAdminData();
      fetchBiddingStats();
      fetchPendingBids();
    }
  }, [isUserLoaded, user]);

  // Auto-refresh when new bids arrive via WebSocket
  useEffect(() => {
    if (adminBiddingState.lastBidUpdate) {
      // Refresh data when new bids arrive
      fetchPendingBids();
      fetchBiddingStats();
    }
  }, [adminBiddingState.lastBidUpdate]);

  const fetchAdminData = async () => {
    try {
      setIsLoading(true);
      // Fetch general admin data
    } catch (error) {
      console.error('Failed to fetch admin data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBiddingStats = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/api/v1/admin/bidding/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const stats = await response.json();
        setBiddingStats(stats);
      }
    } catch (error) {
      console.error('Failed to fetch bidding stats:', error);
    }
  };

  const fetchPendingBids = async () => {
    try {
      setLoadingBids(true);
      const token = await getToken();
      const response = await fetch('/api/v1/admin/bids/pending', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const bids = await response.json();
        setPendingBids(bids);
      }
    } catch (error) {
      console.error('Failed to fetch pending bids:', error);
    } finally {
      setLoadingBids(false);
    }
  };

  const handleBidResponse = async (bidId: string, response: 'accepted' | 'countered' | 'declined', counterOfferAmount?: number, notes?: string) => {
    try {
      const token = await getToken();
      const requestBody: any = { response };
      if (counterOfferAmount) requestBody.counterOfferAmount = counterOfferAmount;
      if (notes) requestBody.notes = notes;

      const apiResponse = await fetch(`/api/v1/admin/bids/${bidId}/respond`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      
      if (apiResponse.ok) {
        toast.success(`Bid ${response} successfully!`);
        
        // Clear the bid notification from real-time state
        clearBidNotification(bidId);
        
        // Refresh the lists
        fetchPendingBids();
        fetchBiddingStats();
      } else {
        toast.error(`Failed to ${response} bid`);
      }
    } catch (error) {
      console.error(`Failed to ${response} bid:`, error);
      toast.error(`Failed to ${response} bid`);
    }
  };

  if (!isUserLoaded) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading...</span>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <AdminOnlyWrapper>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
              <p className="text-muted-foreground">
                Manage carriers, loads, and system settings
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                Admin Access
              </Badge>
            </div>
          </div>

          {/* Admin Tabs */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="bidding">
                Bidding
                {biddingStats?.overview?.pendingBids > 0 && (
                  <Badge variant="destructive" className="ml-2 text-xs">
                    {biddingStats.overview.pendingBids}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="carriers">Carriers</TabsTrigger>
              <TabsTrigger value="loads">Loads</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Carriers</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">--</div>
                    <p className="text-xs text-muted-foreground">Loading...</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Loads</CardTitle>
                    <Truck className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">--</div>
                    <p className="text-xs text-muted-foreground">Loading...</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">System Status</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">Healthy</div>
                    <p className="text-xs text-muted-foreground">All systems operational</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Alerts</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-xs text-muted-foreground">No active alerts</p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Admin Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <Users className="h-6 w-6" />
                      <span>Manage Carriers</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <Truck className="h-6 w-6" />
                      <span>Manage Loads</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <BarChart3 className="h-6 w-6" />
                      <span>Analytics</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <Database className="h-6 w-6" />
                      <span>Database</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <Settings className="h-6 w-6" />
                      <span>System Settings</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <Shield className="h-6 w-6" />
                      <span>Security</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="bidding" className="space-y-4">
              {/* Bidding Statistics */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending Bids</CardTitle>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-500" />
                      {wsConnected && (
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" title="Live updates active" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className={cn(
                      "text-2xl font-bold",
                      adminBiddingState.urgentBids.length > 0 ? "text-red-600" : "text-orange-600"
                    )}>
                      {adminBiddingState.pendingBidsCount || biddingStats?.overview?.pendingBids || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {adminBiddingState.urgentBids.length > 0 ? (
                        <span className="text-red-600 font-semibold">
                          {adminBiddingState.urgentBids.length} urgent • Live updates
                        </span>
                      ) : wsConnected ? (
                        <span className="text-green-600">Live updates active</span>
                      ) : (
                        "Need immediate attention"
                      )}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Accepted Today</CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {biddingStats?.activity?.bidsLast24Hours || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Last 24 hours
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Acceptance Rate</CardTitle>
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {biddingStats?.activity?.acceptanceRate || 0}%
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Overall success rate
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
                    <Activity className="h-4 w-4" />
                  </CardHeader>
                  <CardContent>
                    <div className={cn(
                      "text-lg font-bold",
                      wsConnected ? "text-green-600" : "text-red-600"
                    )}>
                      {wsConnected ? "Connected" : "Offline"}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {wsConnected ? (
                        <span className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                          Real-time updates
                        </span>
                      ) : wsError ? (
                        <span className="text-red-600">Connection error</span>
                      ) : (
                        "Manual refresh required"
                      )}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Real-Time Alert Section */}
              {adminBiddingState.urgentBids.length > 0 && (
                <Alert className="border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertTitle className="text-red-800 dark:text-red-200">
                    🔔 Urgent Bids Require Immediate Attention
                  </AlertTitle>
                  <AlertDescription className="text-red-700 dark:text-red-300">
                    {adminBiddingState.urgentBids.length} bid(s) are marked as urgent and need your immediate response.
                    <div className="mt-2 flex gap-2">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="border-red-300 text-red-700 hover:bg-red-100"
                        onClick={() => {
                          // Scroll to pending bids section
                          document.getElementById('pending-bids-section')?.scrollIntoView({ behavior: 'smooth' });
                        }}
                      >
                        View Urgent Bids
                      </Button>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="text-red-600 hover:bg-red-100"
                        onClick={() => {
                          // N8N handles notifications - no urgent bids to clear
                        }}
                      >
                        Dismiss Alerts
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Recent Real-Time Activity - Hidden since N8N handles notifications */}
              {false && adminBiddingState.recentBids.length > 0 && (
                <div className="hidden">
                  {/* N8N handles real-time bidding activity notifications */}
                </div>
              )}

              {/* Enhanced Pending Bids Section */}
              <Card id="pending-bids-section">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Gavel className="h-5 w-5" />
                        Pending Bids Management
                        {wsConnected ? (
                          <Badge variant="secondary" className="text-xs">
                            <Activity className="h-3 w-3 mr-1" />
                            Live Updates
                          </Badge>
                        ) : wsError ? (
                          <Badge variant="destructive" className="text-xs">
                            Connection Error
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-xs">
                            Offline Mode
                          </Badge>
                        )}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        Review and respond to carrier bids in real-time
                        {adminBiddingState.urgentBids.length > 0 && (
                          <span className="ml-2 text-red-600 font-semibold">
                            ({adminBiddingState.urgentBids.length} urgent responses needed)
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Connection Quality Indicator */}
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-red-500" title="Offline - N8N handles notifications" />
                        <span className="text-xs text-muted-foreground">
                          N8N Notifications
                        </span>
                      </div>
                      
                      <Button 
                        onClick={fetchPendingBids}
                        disabled={loadingBids}
                        variant="outline"
                        size="sm"
                      >
                        {loadingBids ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          'Refresh'
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  {loadingBids ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin" />
                      <span className="ml-2">Loading bids...</span>
                    </div>
                  ) : pendingBids.length === 0 ? (
                    <div className="text-center py-8">
                      <Gavel className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Pending Bids</h3>
                      <p className="text-muted-foreground">
                        {wsConnected 
                          ? "All caught up! You'll be notified when new bids arrive." 
                          : "Check back later or refresh to see if new bids have arrived."
                        }
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {pendingBids.map((bid) => {
                        const isUrgent = false; // N8N handles urgent notifications
                        
                        return (
                          <div
                            key={bid.id}
                            className={cn(
                              "border rounded-lg p-4 transition-all duration-300",
                              isUrgent 
                                ? "border-red-300 bg-red-50 shadow-lg dark:bg-red-950 dark:border-red-800" 
                                : "border-border bg-card hover:shadow-md"
                            )}
                          >
                            {/* Bid Header */}
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center gap-3">
                                {isUrgent && (
                                  <AlertTriangle className="h-5 w-5 text-red-500 animate-pulse" />
                                )}
                                <div>
                                  <h4 className="font-semibold text-lg flex items-center gap-2">
                                    ${bid.bidAmount.toLocaleString()}
                                    {isUrgent && (
                                      <Badge variant="destructive" className="text-xs animate-pulse">
                                        URGENT
                                      </Badge>
                                    )}
                                  </h4>
                                  <p className="text-sm text-muted-foreground">
                                    {bid.carrierProfile.companyName} • MC {bid.carrierProfile.mcNumber}
                                  </p>
                                </div>
                              </div>
                              
                              {/* Time remaining indicator */}
                              {bid.timeRemaining && (
                                <div className={cn(
                                  "text-right",
                                  bid.isExpiringSoon ? "text-red-600" : "text-muted-foreground"
                                )}>
                                  <p className="text-xs font-medium">
                                    {bid.isExpiringSoon ? "⚠️ Expires Soon" : "Time Remaining"}
                                  </p>
                                  <p className="text-sm">{bid.timeRemaining}</p>
                                </div>
                              )}
                            </div>

                            {/* Load Information */}
                            <div className="bg-muted/50 rounded-md p-3 mb-3">
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <p className="text-muted-foreground">Load</p>
                                  <p className="font-medium">{bid.load.airtableRecordId}</p>
                                </div>
                                <div>
                                  <p className="text-muted-foreground">Route</p>
                                  <p className="font-medium">
                                    {bid.load.originCity}, {bid.load.originState} → {bid.load.destinationCity}, {bid.load.destinationState}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-muted-foreground">Pickup</p>
                                  <p className="font-medium">
                                    {new Date(bid.load.pickupDateUtc).toLocaleDateString()}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-muted-foreground">Equipment</p>
                                  <p className="font-medium">{bid.load.equipmentRequired || 'Any'}</p>
                                </div>
                              </div>
                            </div>

                            {/* Carrier Notes */}
                            {bid.carrierNotes && (
                              <div className="mb-3">
                                <p className="text-xs text-muted-foreground mb-1">Carrier Notes:</p>
                                <p className="text-sm bg-background border rounded p-2">
                                  "{bid.carrierNotes}"
                                </p>
                              </div>
                            )}

                            {/* Action Buttons */}
                            <div className="flex flex-wrap gap-2">
                              <Button
                                onClick={() => handleBidResponse(bid.id, 'accepted')}
                                className="bg-green-600 hover:bg-green-700 text-white"
                                size="sm"
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Accept ${bid.bidAmount.toLocaleString()}
                              </Button>
                              
                              <Button
                                onClick={() => handleBidResponse(bid.id, 'declined')}
                                variant="destructive"
                                size="sm"
                              >
                                <X className="h-4 w-4 mr-1" />
                                Decline
                              </Button>
                              
                              <Button
                                onClick={() => {
                                  const counterAmount = prompt(
                                    `Enter counter-offer amount for $${bid.bidAmount.toLocaleString()} bid:`,
                                    (bid.bidAmount * 0.95).toFixed(2)
                                  );
                                  if (counterAmount && !isNaN(Number(counterAmount))) {
                                    handleBidResponse(bid.id, 'countered', Number(counterAmount));
                                  }
                                }}
                                variant="outline"
                                size="sm"
                              >
                                <DollarSign className="h-4 w-4 mr-1" />
                                Counter Offer
                              </Button>

                              {/* Contact Information */}
                              <div className="ml-auto flex items-center gap-2 text-xs text-muted-foreground">
                                <Phone className="h-3 w-3" />
                                {bid.carrierProfile.contact_phone}
                                <Mail className="h-3 w-3 ml-2" />
                                {bid.carrierProfile.contact_email}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="carriers">
              <Card>
                <CardHeader>
                  <CardTitle>Carrier Management</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Manage carrier profiles and authentication
                  </p>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-muted-foreground py-8">
                    Carrier management features will be implemented here
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="loads">
              <Card>
                <CardHeader>
                  <CardTitle>Load Management</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Manage loads, assignments, and tracking
                  </p>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-muted-foreground py-8">
                    Load management features will be implemented here
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="system">
              <Card>
                <CardHeader>
                  <CardTitle>System Configuration</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Configure system settings and integrations
                  </p>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-muted-foreground py-8">
                    System configuration will be implemented here
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminOnlyWrapper>
    </PageLayout>
  );
} 