import { Injectable, Logger, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import * as http from 'http';

@Injectable()
export class BidsService {
  private readonly logger = new Logger(BidsService.name);
  private readonly n8nBaseUrl: string;
  private readonly n8nJwtSecret?: string;

  constructor(private configService: ConfigService) {
    this.n8nBaseUrl = this.configService.get<string>('NEXT_PUBLIC_N8N_BASE_URL') || 'https://firstcutproduce.app.n8n.cloud';
    this.n8nJwtSecret = this.configService.get<string>('N8N_JWT_SECRET');
    
    if (!this.n8nJwtSecret) {
      this.logger.warn('N8N_JWT_SECRET not configured - N8N authentication may fail');
    }
  }

  async submitToN8N(bidData: any, jwtToken?: string): Promise<any> {
    try {
      // Validate JWT token is provided
      if (!jwtToken) {
        throw new UnauthorizedException('JWT token is required for bid submission');
      }

      // Validate required fields
      const requiredFields = ['loadId', 'bidAmount'];
      for (const field of requiredFields) {
        if (!bidData[field]) {
          throw new BadRequestException(`Missing required field: ${field}`);
        }
      }

      // Build N8N webhook URL
      const webhookUrl = `${this.n8nBaseUrl}/webhook/bidding/submit`;
      
      this.logger.log(`Submitting bid to N8N: ${webhookUrl}`);
      this.logger.log(`Bid data: ${JSON.stringify(bidData, null, 2)}`);
      this.logger.log(`Using JWT token: ${jwtToken ? jwtToken.substring(0, 20) + '...' : 'None'}`);

      // Submit to N8N webhook using Node.js built-in modules
      const result = await this.makeHttpRequest(webhookUrl, bidData, jwtToken);

      this.logger.log(`Successful bid submission result:`, result);
      return result;

    } catch (error) {
      this.logger.error('Failed to submit bid to N8N:', error);
      
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      
      throw new Error(`Bid submission failed: ${error.message}`);
    }
  }

  private async makeHttpRequest(url: string, data: any, jwtToken: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const httpModule = isHttps ? https : http;

      const postData = JSON.stringify(data);

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'Authorization': `Bearer ${jwtToken}`,
        },
      };

      const req = httpModule.request(options, (res) => {
        let body = '';

        res.on('data', (chunk) => {
          body += chunk;
        });

        res.on('end', () => {
          this.logger.log(`N8N Response Status: ${res.statusCode}`);
          this.logger.log(`N8N Response: ${body}`);

          if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
            try {
              const result = JSON.parse(body);
              resolve(result);
            } catch (e) {
              resolve({ success: true, message: body });
            }
          } else {
            this.logger.error(`N8N webhook failed:`, {
              status: res.statusCode,
              statusText: res.statusMessage,
              response: body,
              url: url,
            });
            reject(new Error(`N8N webhook failed with status ${res.statusCode}: ${body}`));
          }
        });
      });

      req.on('error', (error) => {
        this.logger.error('HTTP request error:', error);
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }
} 