// Quick Database Check for Lanes Generation
// Checks current database state and imports sample loads if needed
const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

// Load environment variables from multiple possible locations
function loadEnvFile() {
  let envVars = {};
  
  // Try multiple environment file locations
  const envFiles = [
    path.join(__dirname, '.env'),
    path.join(__dirname, 'apps/api/.env.local'),
    path.join(__dirname, 'apps/api/.env'),
    path.join(__dirname, '.env.local')
  ];
  
  for (const envPath of envFiles) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      console.log(`📄 Loading environment from: ${envPath}`);
      
      envContent.split('\n').forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=');
            // Remove surrounding quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) || 
                (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }
            envVars[key] = value;
          }
        }
      });
      
      // If we found DATABASE_URL, we can stop looking
      if (envVars.DATABASE_URL) {
        break;
      }
      
    } catch (error) {
      // File doesn't exist, continue to next
    }
  }
  
  // Use system environment variables as fallback
  if (!envVars.DATABASE_URL) {
    envVars.DATABASE_URL = process.env.DATABASE_URL;
  }
  
  return envVars;
}

async function checkDatabaseAndGenerateLanes() {
  console.log('🗄️  DATABASE LANES CHECK');
  console.log('========================\n');
  
  try {
    // Load environment variables
    const envVars = loadEnvFile();
    process.env.DATABASE_URL = envVars.DATABASE_URL;
    
    console.log('🔑 Configuration:');
    console.log(`- Database URL: ${process.env.DATABASE_URL ? 'Found' : 'Missing'}\n`);
    
    if (!process.env.DATABASE_URL) {
      console.log('❌ Missing DATABASE_URL in .env.local file');
      return;
    }
    
    // Initialize Prisma Client with timeout
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });
    
    // Test database connection first
    console.log('🔌 Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connected successfully\n');
    
    console.log('📊 CURRENT DATABASE STATE:');
    console.log('==========================');
    
    // Check load count
    const loadCount = await prisma.load.count();
    console.log(`📦 Total loads in database: ${loadCount}`);
    
    // Check user count
    const userCount = await prisma.user.count();
    console.log(`👥 Total users in database: ${userCount}`);
    
    // Check carrier profile count
    const carrierCount = await prisma.carrierProfile.count();
    console.log(`🚛 Total carrier profiles: ${carrierCount}`);
    
    if (loadCount === 0) {
      console.log('\n❗ No loads found - this is why lanes are not showing up!');
      console.log('Lanes are generated from existing load data (origin/destination combinations)');
      
      console.log('\n🔧 SOLUTION: Import sample loads to generate lanes');
      
      // Create some sample loads to generate lanes
      const sampleLoads = [
        {
          airtableRecordId: 'sample_001',
          originCity: 'Atlanta',
          originState: 'GA',
          destinationCity: 'Miami',
          destinationState: 'FL',
          equipmentRequired: 'Dry Van',
          weightLbs: 45000,
          rate: 1200.00,
          status: 'AVAILABLE',
          pickupDateUtc: new Date('2024-01-15T10:00:00Z'),
          deliveryDateUtc: new Date('2024-01-16T14:00:00Z'),
          rawAirtableData: { sample: true, created_by: 'database_check_script' }
        },
        {
          airtableRecordId: 'sample_002',
          originCity: 'Chicago',
          originState: 'IL',
          destinationCity: 'Dallas',
          destinationState: 'TX',
          equipmentRequired: 'Reefer',
          weightLbs: 42000,
          rate: 1800.00,
          status: 'AVAILABLE',
          pickupDateUtc: new Date('2024-01-15T08:00:00Z'),
          deliveryDateUtc: new Date('2024-01-17T16:00:00Z'),
          rawAirtableData: { sample: true, created_by: 'database_check_script' }
        },
        {
          airtableRecordId: 'sample_003',
          originCity: 'Los Angeles',
          originState: 'CA',
          destinationCity: 'Phoenix',
          destinationState: 'AZ',
          equipmentRequired: 'Dry Van',
          weightLbs: 35000,
          rate: 800.00,
          status: 'AVAILABLE',
          pickupDateUtc: new Date('2024-01-16T09:00:00Z'),
          deliveryDateUtc: new Date('2024-01-17T12:00:00Z'),
          rawAirtableData: { sample: true, created_by: 'database_check_script' }
        },
        {
          airtableRecordId: 'sample_004',
          originCity: 'Atlanta',
          originState: 'GA',
          destinationCity: 'Nashville',
          destinationState: 'TN',
          equipmentRequired: 'Flatbed',
          weightLbs: 48000,
          rate: 950.00,
          status: 'AVAILABLE',
          pickupDateUtc: new Date('2024-01-16T11:00:00Z'),
          deliveryDateUtc: new Date('2024-01-17T15:00:00Z'),
          rawAirtableData: { sample: true, created_by: 'database_check_script' }
        },
        {
          airtableRecordId: 'sample_005',
          originCity: 'Chicago',
          originState: 'IL',
          destinationCity: 'Indianapolis',
          destinationState: 'IN',
          equipmentRequired: 'Dry Van',
          weightLbs: 38000,
          rate: 650.00,
          status: 'AVAILABLE',
          pickupDateUtc: new Date('2024-01-17T07:00:00Z'),
          deliveryDateUtc: new Date('2024-01-17T18:00:00Z'),
          rawAirtableData: { sample: true, created_by: 'database_check_script' }
        }
      ];
      
      console.log('\n📥 Creating sample loads...');
      for (const load of sampleLoads) {
        try {
          await prisma.load.create({ data: load });
          console.log(`   ✅ Created: ${load.originCity}, ${load.originState} → ${load.destinationCity}, ${load.destinationState}`);
        } catch (error) {
          console.log(`   ❌ Failed to create ${load.airtableRecordId}: ${error.message}`);
        }
      }
      
      console.log('\n🎯 Sample loads created! This should generate lanes');
      
    } else {
      console.log('\n✅ Loads exist in database - checking lane combinations...');
      
      // Get unique lane combinations
      const uniqueLanes = await prisma.load.groupBy({
        by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
        where: {
          AND: [
            { originCity: { not: null } },
            { originState: { not: null } },
            { destinationCity: { not: null } },
            { destinationState: { not: null } }
          ]
        },
        _count: {
          id: true
        }
      });
      
      console.log(`🛤️  Found ${uniqueLanes.length} unique lane combinations:`);
      uniqueLanes.forEach((lane, index) => {
        console.log(`   ${index + 1}. ${lane.originCity}, ${lane.originState} → ${lane.destinationCity}, ${lane.destinationState} (${lane._count.id} loads)`);
      });
    }
    
    await prisma.$disconnect();
    
    console.log('\n🔄 Next Steps:');
    console.log('1. Refresh the Lane Library page in your browser');
    console.log('2. The lanes should now be visible');
    console.log('3. If still not showing, check the API logs or browser network tab');
    
  } catch (error) {
    console.error('💥 Error checking database:', error);
  }
}

// Execute the check
checkDatabaseAndGenerateLanes(); 