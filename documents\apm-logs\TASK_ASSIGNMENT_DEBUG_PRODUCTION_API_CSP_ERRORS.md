# TASK ASSIGNMENT: Debug Production API and CSP Errors

## AGENT ASSIGNMENT
**Primary Agent:** Debug Agent
**Task Priority:** HIGH - Production Critical Issues
**Estimated Complexity:** Medium-High

## CONTEXT
Multiple critical errors have been identified in the production environment of the Carrier Portal, including:
- API endpoints returning 500 errors
- Content Security Policy violations
- Missing font resources (404 errors)
- Client-side error handling failures

## ERROR ANALYSIS

### Critical API Failures (500 Errors)
```
XHRGET https://www.fcp-portal.com/api/v1/auth/me [HTTP/2 500  886ms]
XHRGET https://www.fcp-portal.com/api/v1/airtable-orders/assigned [HTTP/2 500  1587ms] 
XHRGET https://www.fcp-portal.com/api/v1/airtable-orders/bids [HTTP/2 500  1592ms]
```

### Content Security Policy Violations
```
Content-Security-Policy: The page's settings blocked an inline script (script-src-elem) from being executed because it violates the following directive: "script-src-elem 'self' 'nonce-MWMxMDk3YTctOTI3NC00OTQyLTg0ZGEtNjM3MmNlOTRlZmY4'...
```

### Missing Resources (404 Errors)
```
GET https://www.fcp-portal.com/fonts/GeistVF.woff [HTTP/2 404  278ms]
GET https://www.fcp-portal.com/fonts/GeistMonoVF.woff [HTTP/2 404  257ms]
```

### Client-Side Error
```
Error fetching dashboard data: Error: Failed to fetch assigned loads
```

## TASK OBJECTIVES

### 1. API Endpoint Investigation and Fixes
- **Investigate `/api/v1/auth/me` endpoint**: Check authentication logic, database connections, and error handling
- **Debug `/api/v1/airtable-orders/assigned` endpoint**: Verify Airtable integration, data fetching logic, and error responses
- **Debug `/api/v1/airtable-orders/bids` endpoint**: Similar investigation for bids functionality
- **Review server logs**: Check application logs for detailed error information
- **Validate database connections**: Ensure Prisma connections are stable and queries are working

### 2. Content Security Policy Resolution
- **Identify CSP violations**: Locate the inline scripts causing violations
- **Update CSP configuration**: Modify the CSP headers to allow necessary scripts while maintaining security
- **Implement proper nonce handling**: Ensure nonces are correctly applied to required scripts
- **Test CSP compliance**: Verify all legitimate scripts execute without violations

### 3. Font Resource Resolution
- **Locate missing font files**: Check if GeistVF.woff and GeistMonoVF.woff exist in the expected directories
- **Update font configurations**: Ensure proper font loading in CSS and Next.js configuration
- **Implement fallback fonts**: Add appropriate font fallbacks to prevent layout issues

### 4. Database Connection Fix (CRITICAL)
- **Fix Prisma connection pooling**: Configure proper connection limits for serverless
- **Add connection retry logic**: Implement exponential backoff for database connections  
- **Environment variables**: Verify DATABASE_URL and connection string are correct
- **Connection lifecycle**: Fix connection management in serverless cold starts

## TECHNICAL SPECIFICATIONS

### API Debugging Approach
1. **Server-side logging**: Add comprehensive logging to all failing endpoints
2. **Error response analysis**: Check what specific errors are being returned
3. **Database query validation**: Verify all Prisma queries are correctly formed
4. **Authentication flow**: Ensure Clerk integration is working properly

### CSP Configuration
1. **Review current CSP**: Analyze existing Content-Security-Policy headers
2. **Identify violation sources**: Find the specific inline scripts causing issues
3. **Nonce implementation**: Ensure proper nonce generation and application
4. **Whitelist necessary domains**: Update allowed script sources as needed

### Font Loading
1. **File system check**: Verify font files exist in `/public/fonts/`
2. **Next.js font configuration**: Check font loading configuration
3. **CSS font-face declarations**: Verify proper font declarations

## ACCEPTANCE CRITERIA

### Primary Success Metrics
- [ ] All API endpoints (`/api/v1/auth/me`, `/api/v1/airtable-orders/assigned`, `/api/v1/airtable-orders/bids`) return successful responses (200 status)
- [ ] Zero Content Security Policy violations in browser console
- [ ] All font resources load successfully (200 status)
- [ ] Dashboard loads without client-side errors
- [ ] Proper error handling and user feedback implemented

### Testing Requirements
- [ ] API endpoints tested with various authentication states
- [ ] CSP compliance verified across different browsers
- [ ] Font loading tested on various devices and network conditions
- [ ] Error scenarios properly handled and displayed to users

## IMPLEMENTATION PRIORITY
1. **CRITICAL**: Investigate database infrastructure (server down/unreachable) - ALL API endpoints failing
2. **PARALLEL**: Fix font loading (move to `/public/fonts/`) - 2 minute fix
3. **PARALLEL**: Remove duplicate CSP configuration - 2 minute fix  
4. **FINAL**: Verify database fix resolves API errors

## FILES TO INVESTIGATE

### Database Connection (PRIORITY)
- `apps/api/src/prisma/prisma.service.ts` - Database connection service
- `packages/db/prisma/schema.prisma` - Database schema and connection config
- Environment variables: `DATABASE_URL`, connection pooling settings

### Font Files
- Move `apps/web/src/app/fonts/*.woff` to `apps/web/public/fonts/`

### CSP Configuration  
- `apps/web/next.config.js` - Remove duplicate CSP configuration
- `apps/web/src/middleware.ts` - Keep this CSP configuration only

### Configuration Files
- `vercel.json` - Deployment and CSP configuration
- `next.config.js` - Next.js configuration
- Font files in `/public/fonts/`

### Client-Side
- Dashboard components with API calls
- Error boundary implementations
- Loading state management

## URGENT FIXES IDENTIFIED

### 1. Font Loading Issue (Quick Fix)
**Problem**: Font files are in `/src/app/fonts/` but the application is looking for them in `/fonts/`
**Files Found**: 
- `apps/web/src/app/fonts/GeistVF.woff`
- `apps/web/src/app/fonts/GeistMonoVF.woff`

**Fix**: Move font files to `apps/web/public/fonts/` directory

### 2. CSP Configuration Conflict 
**Problem**: Duplicate CSP headers being set in both `middleware.ts` and `next.config.js`
**Issue**: This creates conflicting CSP policies causing violations

**Fix**: Remove CSP configuration from `next.config.js` since `middleware.ts` is handling it

### 3. API Endpoint Database Issues - ROOT CAUSE IDENTIFIED
**Exact Error**: `PrismaClientInitializationError: Server has closed the connection. Error code: P1017`
**Location**: Database connection fails during NestJS app initialization in `PrismaService.onModuleInit()`
**Impact**: All API endpoints return 500 errors because the app can't connect to the database

## IMMEDIATE ACTION PLAN

### Step 1: Database Connection Fix (CRITICAL) - RETRY LOGIC ALREADY IMPLEMENTED
**Status**: Retry logic is working but ALL 5 attempts are failing with "Server has closed the connection"
**Issue**: This indicates a database infrastructure problem, not just connection pooling

**Immediate Checks Needed**:
```bash
# 1. Verify DATABASE_URL environment variable
echo $DATABASE_URL

# 2. Test database connectivity directly
npx prisma db pull --preview-feature

# 3. Check if database server is running
# 4. Verify connection string format and credentials
# 5. Check if connection pooling limits exceeded
```

**Possible Root Causes**:
- Database server is down or unreachable
- Invalid DATABASE_URL or credentials  
- Network connectivity issues between Vercel and database
- Database connection pool exhausted
- SSL/firewall blocking connections

### Step 2: Font Files (2-minute fix)
```bash
# Move font files to correct location
mv apps/web/src/app/fonts/*.woff apps/web/public/fonts/
```

### Step 3: Remove CSP Conflict (2-minute fix)
Remove the entire CSP configuration from `apps/web/next.config.js` (lines 17-58)

## GUIDING NOTES
- **CRITICAL**: Database infrastructure issue - server closing connections immediately  
- **Retry Logic**: Already implemented and working, but database is unreachable
- **Priority**: Check DATABASE_URL, database server status, and network connectivity
- **Quick Wins**: Font and CSP fixes can be done in parallel while debugging database
- **Escalation**: May need infrastructure team if database server is down

## EXPECTED DELIVERABLES
1. **Fix Report**: Detailed analysis of each error and the implemented solution
2. **Code Changes**: All necessary code modifications to resolve the issues
3. **Testing Documentation**: Evidence that all fixes work correctly
4. **Monitoring Setup**: Recommendations for preventing similar issues in the future

---
**Task Created:** [Current Date]
**Deadline:** ASAP (Production Critical)
**Dependencies:** None - Can start immediately 