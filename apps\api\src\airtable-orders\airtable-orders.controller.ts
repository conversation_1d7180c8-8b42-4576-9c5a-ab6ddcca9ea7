import { Controller, Get, Post, Body, UseGuards, Logger, Param, Request, Delete, BadRequestException, InternalServerErrorException, UnauthorizedException, Query, Put, Patch } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiBody, ApiQuery } from '@nestjs/swagger';
import { AirtableOrdersService } from './airtable-orders.service';
import { AuthGuard, AdminGuard } from '../auth/auth.guard';
import { AirtableLoadWebhookDto } from './dto/airtable-load.dto';
import { RequestBookingDetailsDto } from './dto/request-booking-details.dto';
import { CreateBidDto } from './dto/create-bid.dto';
import { UploadDocumentDto } from './dto/upload-document.dto';
import { AdvancedFiltersDto, SavedSearchDto } from './dto/advanced-filters.dto';
import { Load } from '@repo/db';

@ApiTags('airtable-orders')
@Controller('airtable-orders')
export class AirtableOrdersController {
  private readonly logger = new Logger(AirtableOrdersController.name);

  constructor(private readonly airtableOrdersService: AirtableOrdersService) {}

  @Get('available')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get available loads with advanced filtering',
    description: 'Fetch available loads from Airtable with advanced filtering options including geographic radius, date ranges, equipment types, and more.'
  })
  async getAvailableLoads(
    @Request() req: any,
    @Query() filters?: AdvancedFiltersDto
  ): Promise<{ loads: any[]; totalCount: number; page: number; pageSize: number }> {
    const userAirtableId = req.user?.airtableUserId;
    
    // 🔒 SECURITY VALIDATION: Ensure user is authenticated
    if (!userAirtableId) {
      this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/available');
      throw new UnauthorizedException('Authentication required');
    }
    
    this.logger.log(`GET /airtable-orders/available called by user ${userAirtableId} (MC: ${req.user?.mcNumber}) with filters:`, filters);
    
    try {
      // Add timeout and retry logic for service calls
      let retries = 2;
      let lastError;
      
      while (retries >= 0) {
        try {
          const result = await Promise.race([
            this.airtableOrdersService.getAvailableLoadsWithFilters(userAirtableId, filters),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Service timeout')), 30000)
            )
          ]) as any;
          
          // 🔒 SECURITY AUDIT: Log load access patterns for monitoring
          this.logger.log(`SECURITY: User ${userAirtableId} (MC: ${req.user?.mcNumber}) accessed ${result.loads.length} loads (total: ${result.totalCount})`);
          
          return result;
        } catch (error) {
          lastError = error;
          retries--;
          
          if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
            this.logger.warn(`Retrying request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          } else {
            throw error;
          }
        }
      }
      
      throw lastError;
      
    } catch (error) {
      this.logger.error(`Error fetching available loads for user ${userAirtableId}:`, error.message, error.stack);
      
      // Provide user-friendly error messages
      if (error.message === 'Service timeout') {
        throw new InternalServerErrorException('Service is currently slow. Please try again.');
      }
      
      if (error.message.includes('connection') || error.message.includes('closed')) {
        throw new InternalServerErrorException('Backend service temporarily unavailable. Please try again shortly.');
      }
      
      if (error.message.includes('AIRTABLE')) {
        throw new InternalServerErrorException('Load data service temporarily unavailable. Please try again.');
      }
      
      // Generic error for production
      throw new InternalServerErrorException('Unable to fetch loads. Please try again.');
    }
  }

  @Get('my-loads')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get loads assigned to carrier company using Airtable Carrier field',
    description: 'Fetch loads assigned to the authenticated carrier company from Airtable directly'
  })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by load status' })
  @ApiQuery({ name: 'search', required: false, description: 'Search loads by various fields' })
  @ApiQuery({ name: 'dateRange', required: false, description: 'Filter by date range (today, week, month)' })
  async getMyLoads(
    @Request() req: any,
    @Query('status') status?: string,
    @Query('search') search?: string,
    @Query('dateRange') dateRange?: string
  ): Promise<{ loads: any[]; totalCount: number; companyName: string }> {
    const userAirtableId = req.user?.airtableUserId;
    
    // 🔒 SECURITY VALIDATION: Ensure user is authenticated
    if (!userAirtableId) {
      this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/my-loads');
      throw new UnauthorizedException('Authentication required');
    }
    
    this.logger.log(`GET /airtable-orders/my-loads called by user ${userAirtableId} with filters:`, { status, search, dateRange });
    
    try {
      const filters = { status, search, dateRange };
      
      // Add timeout and retry logic for service calls
      let retries = 2;
      let lastError;
      
      while (retries >= 0) {
        try {
          const loads = await Promise.race([
            this.airtableOrdersService.getMyLoadsFromAirtable(userAirtableId, filters),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Service timeout')), 20000)
            )
          ]) as any[];
          
          this.logger.log(`Successfully fetched ${loads.length} loads for user ${userAirtableId}`);
          
          // Extract company name from first load for display
          const companyName = loads.length > 0 ? loads[0].carrier : 'Unknown Company';
          
          return {
            loads,
            totalCount: loads.length,
            companyName
          };
        } catch (error) {
          lastError = error;
          retries--;
          
          if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
            this.logger.warn(`Retrying my-loads request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          } else {
            throw error;
          }
        }
      }
      
      throw lastError;
      
    } catch (error) {
      this.logger.error(`Error fetching my loads for user ${userAirtableId}:`, error.message, error.stack);
      
      // Provide user-friendly error messages
      if (error.message === 'Service timeout') {
        throw new InternalServerErrorException('Service is currently slow. Please try again.');
      }
      
      if (error.message.includes('Company name not found')) {
        throw new BadRequestException('Please complete your company profile to view assigned loads.');
      }
      
      if (error.message.includes('connection') || error.message.includes('closed')) {
        throw new InternalServerErrorException('Load data service temporarily unavailable. Please try again shortly.');
      }
      
      if (error.message.includes('AIRTABLE')) {
        throw new InternalServerErrorException('Load data service temporarily unavailable. Please try again.');
      }
      
      // Generic error for production
      throw new InternalServerErrorException('Unable to fetch your loads. Please try again.');
    }
  }

  @Get('assigned')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: '[DEPRECATED] Get assigned loads for the authenticated carrier',
    description: '[DEPRECATED] Use /my-loads instead. Fetch all loads assigned to the authenticated carrier user'
  })
  async getAssignedLoadsForCarrier(@Request() req: any): Promise<any[]> {
    const userAirtableId = req.user?.airtableUserId;
    
    // 🔒 SECURITY VALIDATION: Ensure user is authenticated
    if (!userAirtableId) {
      this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/assigned');
      throw new UnauthorizedException('Authentication required');
    }
    
    this.logger.log(`GET /airtable-orders/assigned called by user ${userAirtableId} (MC: ${req.user?.mcNumber})`);
    
    try {
      // Add timeout and retry logic for service calls
      let retries = 2;
      let lastError;
      
      while (retries >= 0) {
        try {
          const result = await Promise.race([
            this.airtableOrdersService.getAssignedLoadsForCarrier(userAirtableId),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Service timeout')), 20000)
            )
          ]) as any;
          
          this.logger.log(`Successfully fetched ${result.length} assigned loads for user ${userAirtableId}`);
          return result;
        } catch (error) {
          lastError = error;
          retries--;
          
          if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
            this.logger.warn(`Retrying assigned loads request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          } else {
            throw error;
          }
        }
      }
      
      throw lastError;
      
    } catch (error) {
      this.logger.error(`Error fetching assigned loads for user ${userAirtableId}:`, error.message, error.stack);
      
      // Provide user-friendly error messages
      if (error.message === 'Service timeout') {
        throw new InternalServerErrorException('Service is currently slow. Please try again.');
      }
      
      if (error.message.includes('connection') || error.message.includes('closed')) {
        throw new InternalServerErrorException('Backend service temporarily unavailable. Please try again shortly.');
      }
      
      if (error.message.includes('not found') || error.message.includes('No assigned loads')) {
        // Return empty array instead of error for better UX
        this.logger.log(`No assigned loads found for user ${userAirtableId}, returning empty array`);
        return [];
      }
      
      // Generic error for production
      throw new InternalServerErrorException('Unable to fetch assigned loads. Please try again.');
    }
  }

  @Get('dashboard-metrics')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get dashboard metrics for the authenticated carrier',
    description: 'Fetch dashboard statistics including assigned loads, active bids, earnings, and recent activity'
  })
  async getDashboardMetrics(@Request() req: any): Promise<any> {
    const userAirtableId = req.user?.airtableUserId;
    
    // 🔒 SECURITY VALIDATION: Ensure user is authenticated
    if (!userAirtableId) {
      this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/dashboard-metrics');
      throw new UnauthorizedException('Authentication required');
    }
    
    this.logger.log(`GET /airtable-orders/dashboard-metrics called by user ${userAirtableId} (MC: ${req.user?.mcNumber})`);
    
    try {
      // Add timeout and retry logic for service calls
      let retries = 2;
      let lastError;
      
      while (retries >= 0) {
        try {
          const result = await Promise.race([
            this.airtableOrdersService.getDashboardMetrics(userAirtableId),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Service timeout')), 20000)
            )
          ]) as any;
          
          this.logger.log(`Successfully fetched dashboard metrics for user ${userAirtableId}`);
          return result;
        } catch (error) {
          lastError = error;
          retries--;
          
          if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
            this.logger.warn(`Retrying dashboard metrics request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          } else {
            throw error;
          }
        }
      }
      
      throw lastError;
      
    } catch (error) {
      this.logger.error(`Error fetching dashboard metrics for user ${userAirtableId}:`, error.message, error.stack);
      
      // Provide user-friendly error messages
      if (error.message === 'Service timeout') {
        throw new InternalServerErrorException('Service is currently slow. Please try again.');
      }
      
      if (error.message.includes('connection') || error.message.includes('closed')) {
        throw new InternalServerErrorException('Backend service temporarily unavailable. Please try again shortly.');
      }
      
      // Return default metrics on error for better UX
      this.logger.log(`Returning default dashboard metrics for user ${userAirtableId} due to error`);
      return {
        totalAssignedLoads: 0,
        totalActiveBids: 0,
        totalEarnings: 0,
        recentLoads: [],
        recentBids: [],
        loadsByStatus: {},
        thisWeekLoads: 0,
        lastWeekLoads: 0
      };
    }
  }

  @Post(':loadId/book')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Request to book a load',
    description: 'Submit a booking request for a specific load'
  })
  @ApiBody({ type: RequestBookingDetailsDto })
  async requestLoadBooking(
    @Param('loadId') loadId: string,
    @Body() bookingDetailsDto: RequestBookingDetailsDto,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/${loadId}/book called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.requestLoadBooking(
        loadId,
        req.user?.airtableUserId, // Airtable user ID from N8N JWT
        bookingDetailsDto
      );
    } catch (error) {
      this.logger.error('Error processing booking request:', error);
      throw error;
    }
  }

  @Post('webhook')
  @ApiOperation({ 
    summary: 'Process Airtable webhook',
    description: 'Endpoint for Airtable to send load updates via webhook'
  })
  @ApiBody({ type: AirtableLoadWebhookDto })
  async processWebhook(@Body() payload: AirtableLoadWebhookDto): Promise<Load> {
    this.logger.log('POST /airtable-orders/webhook called');
    try {
      return await this.airtableOrdersService.processLoadWebhook(payload);
    } catch (error) {
      this.logger.error('Error processing webhook:', error);
      throw error;
    }
  }

  @Post('sync')
  @UseGuards(AdminGuard) // Require admin authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Sync loads from Airtable',
    description: 'Manually trigger a sync of loads from Airtable (Admin use). Use syncAll=true to sync all loads.'
  })
  async syncAllLoads(@Query('syncAll') syncAll?: string): Promise<{ syncedCount: number; errors: any[] }> {
    this.logger.log(`POST /airtable-orders/sync called with syncAll=${syncAll}`);
    try {
      const shouldSyncAll = syncAll === 'true';
      return await this.airtableOrdersService.syncAllLoadsFromAirtable(shouldSyncAll);
    } catch (error) {
      this.logger.error('Error syncing loads:', error);
      throw error;
    }
  }

  @Post(':loadId/upload-document')
  @UseGuards(AuthGuard) // Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Upload a document for a load',
    description: 'Upload BOL, POD, or Invoice document for a specific load. Only HTTPS URLs are accepted.'
  })
  @ApiBody({ type: UploadDocumentDto })
  async uploadLoadDocument(
    @Param('loadId') loadId: string,
    @Body() uploadData: UploadDocumentDto,
    @Request() req: any
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`POST /airtable-orders/${loadId}/upload-document called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    
    // Additional security: Validate loadId format (CUID)
    if (!/^[a-z0-9]{25}$/.test(loadId)) {
      throw new BadRequestException('Invalid load ID format');
    }
    
    try {
      return await this.airtableOrdersService.uploadLoadDocument(
        loadId,
        uploadData.documentType,
        uploadData.fileUrl,
        req.user?.airtableUserId
      );
    } catch (error) {
      this.logger.error('Error uploading document:', error);
      
      // Don't expose internal error details in production
      if (process.env.NODE_ENV === 'production') {
        throw new InternalServerErrorException('Document upload failed');
      }
      throw error;
    }
  }

  @Post(':airtableRecordId/assign-to-me')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Manually assign a load to the authenticated carrier',
    description: 'Assign a load with "Assigned" status to the authenticated carrier (useful for fixing assignment issues)'
  })
  async assignLoadToCarrier(
    @Param('airtableRecordId') airtableRecordId: string,
    @Request() req: any
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`POST /airtable-orders/${airtableRecordId}/assign-to-me called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.assignLoadToCarrier(
        airtableRecordId,
        req.user?.airtableUserId
      );
    } catch (error) {
      this.logger.error('Error assigning load to carrier:', error);
      throw error;
    }
  }

  @Patch(':loadId/status')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update load status',
    description: 'Update the status of a load. For "assigned" status, also assigns the load to the authenticated carrier.'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', description: 'New status for the load' }
      },
      required: ['status']
    }
  })
  async updateLoadStatus(
    @Param('loadId') loadId: string,
    @Body() body: { status: string },
    @Request() req: any
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`PATCH /airtable-orders/${loadId}/status called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber}) - Status: ${body.status}`);
    try {
      return await this.airtableOrdersService.updateLoadStatus(
        loadId,
        body.status,
        req.user?.airtableUserId
      );
    } catch (error) {
      this.logger.error('Error updating load status:', error);
      throw error;
    }
  }

  @Post(':airtableRecordId/unassign')
  @UseGuards(AdminGuard)
  async unassignLoadFromCarrier(
    @Param('airtableRecordId') airtableRecordId: string,
    @Request() req: any,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`POST /api/v1/airtable-orders/${airtableRecordId}/unassign - Admin unassigning load by ${req.user?.airtableUserId}`);
    return this.airtableOrdersService.unassignLoadFromCarrier(airtableRecordId, req.user?.airtableUserId);
  }

  @Post(':loadId/cancel')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Cancel assigned load',
    description: 'Cancel a load that was assigned to the carrier'
  })
  async cancelAssignedLoad(
    @Param('loadId') loadId: string,
    @Body() body: { reason?: string },
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/${loadId}/cancel called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.carrierCancelLoad(
        loadId,
        req.user?.airtableUserId,
        body.reason
      );
    } catch (error) {
      this.logger.error('Error cancelling load:', error);
      throw error;
    }
  }

  // ===== BID ENDPOINTS =====

  @Post(':loadId/bid')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Submit a bid on a load',
    description: 'Submit or update a bid on a specific load'
  })
  @ApiBody({ type: CreateBidDto })
  async createBid(
    @Param('loadId') loadId: string,
    @Body() createBidDto: CreateBidDto,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/${loadId}/bid called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    this.logger.log(`DEBUG: Received DTO: ${JSON.stringify(createBidDto)}`);
    this.logger.log(`DEBUG: bidAmount type: ${typeof createBidDto.bidAmount}, value: ${createBidDto.bidAmount}`);
    this.logger.log(`DEBUG: carrierNotes type: ${typeof createBidDto.carrierNotes}, value: ${createBidDto.carrierNotes}`);
    try {
      return await this.airtableOrdersService.createBid(
        loadId,
        req.user?.airtableUserId,
        createBidDto.bidAmount,
        createBidDto.carrierNotes
      );
    } catch (error) {
      this.logger.error('Error creating bid:', error);
      throw error;
    }
  }

  @Post('bids/:bidId/respond')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Respond to admin counter-offer',
    description: 'Accept or decline an admin counter-offer on your bid'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        response: {
          type: 'string',
          enum: ['accepted', 'declined'],
          example: 'accepted'
        },
        notes: {
          type: 'string',
          example: 'Thank you for the counter-offer, I accept.'
        }
      },
      required: ['response']
    }
  })
  async respondToCounterOffer(
    @Param('bidId') bidId: string,
    @Body() body: { response: 'accepted' | 'declined'; notes?: string },
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/bids/${bidId}/respond called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    this.logger.log(`Carrier responding to counter-offer: ${body.response}`);
    
    try {
      return await this.airtableOrdersService.respondToCounterOffer(
        bidId,
        req.user?.airtableUserId,
        body.response,
        body.notes
      );
    } catch (error) {
      this.logger.error('Error responding to counter-offer:', error);
      throw error;
    }
  }

  @Get('bids')
  @UseGuards(AuthGuard) // SECURITY: Require N8N JWT authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get carrier bids',
    description: 'Fetch all bids submitted by the authenticated carrier'
  })
  async getCarrierBids(
    @Request() req: any
  ): Promise<any[]> {
    const userAirtableId = req.user?.airtableUserId;
    
    // 🔒 SECURITY VALIDATION: Ensure user is authenticated
    if (!userAirtableId) {
      this.logger.error('SECURITY: Unauthenticated request to /airtable-orders/bids');
      throw new UnauthorizedException('Authentication required');
    }
    
    this.logger.log(`GET /airtable-orders/bids called by user ${userAirtableId} (MC: ${req.user?.mcNumber})`);
    
    try {
      // Add timeout and retry logic for service calls
      let retries = 2;
      let lastError;
      
      while (retries >= 0) {
        try {
          const result = await Promise.race([
            this.airtableOrdersService.getCarrierBids(userAirtableId),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Service timeout')), 15000)
            )
          ]) as any;
          
          this.logger.log(`Successfully fetched ${result.length} bids for user ${userAirtableId}`);
          return result;
        } catch (error) {
          lastError = error;
          retries--;
          
          if (retries >= 0 && (error.message.includes('connection') || error.message.includes('timeout'))) {
            this.logger.warn(`Retrying bids request for user ${userAirtableId}, ${retries} attempts left. Error: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          } else {
            throw error;
          }
        }
      }
      
      throw lastError;
      
    } catch (error) {
      this.logger.error(`Error fetching carrier bids for user ${userAirtableId}:`, error.message, error.stack);
      
      // Provide user-friendly error messages
      if (error.message === 'Service timeout') {
        throw new InternalServerErrorException('Bid service is currently slow. Please try again.');
      }
      
      if (error.message.includes('connection') || error.message.includes('closed')) {
        throw new InternalServerErrorException('Bid service temporarily unavailable. Please try again shortly.');
      }
      
      if (error.message.includes('not found') || error.message.includes('No bids')) {
        // Return empty array instead of error for better UX
        this.logger.log(`No bids found for user ${userAirtableId}, returning empty array`);
        return [];
      }
      
      // Generic error for production
      throw new InternalServerErrorException('Unable to fetch bids. Please try again.');
    }
  }

  @Delete('bids/:bidId')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Withdraw a bid',
    description: 'Withdraw a bid that was previously submitted'
  })
  async withdrawBid(
    @Param('bidId') bidId: string,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`DELETE /airtable-orders/bids/${bidId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.withdrawBid(bidId, req.user?.airtableUserId);
    } catch (error) {
      this.logger.error('Error withdrawing bid:', error);
      throw error;
    }
  }

  // Alternative bid withdrawal route for frontend compatibility
  @Post('bid-withdraw/:bidId')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Withdraw a bid (alternative route)',
    description: 'Alternative POST route to withdraw a bid that was previously submitted'
  })
  async withdrawBidPost(
    @Param('bidId') bidId: string,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/bid-withdraw/${bidId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.withdrawBid(bidId, req.user?.airtableUserId);
    } catch (error) {
      this.logger.error('Error withdrawing bid (POST route):', error);
      throw error;
    }
  }

  // Frontend expected route - EXACT match for what frontend is calling
  @Post('bids/:bidId/withdraw')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Withdraw a bid (frontend expected route)',
    description: 'Exact route that frontend is calling: POST /bids/{bidId}/withdraw'
  })
  async withdrawBidFrontendRoute(
    @Param('bidId') bidId: string,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/bids/${bidId}/withdraw called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber}) - FRONTEND ROUTE`);
    try {
      return await this.airtableOrdersService.withdrawBid(bidId, req.user?.airtableUserId);
    } catch (error) {
      this.logger.error('Error withdrawing bid (frontend route):', error);
      throw error;
    }
  }

  // Debug endpoint to test Airtable bid updates
  @Post(':loadId/debug-bids')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Debug Airtable bid updates',
    description: 'Test endpoint to debug Airtable bid field updates'
  })
  async debugAirtableBids(
    @Param('loadId') loadId: string,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/${loadId}/debug-bids called by admin ${req.user?.airtableUserId}`);
    try {
      return await this.airtableOrdersService.debugAirtableBidUpdate(loadId);
    } catch (error) {
      this.logger.error('Error in debug bid update:', error);
      throw error;
    }
  }

  // Saved Search Endpoints
  @Get('saved-searches')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get saved searches for user',
    description: 'Retrieve all saved search criteria for the authenticated user'
  })
  async getSavedSearches(@Request() req: any): Promise<any[]> {
    this.logger.log(`GET /airtable-orders/saved-searches called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.getSavedSearches(req.user?.airtableUserId);
    } catch (error) {
      this.logger.error('Error fetching saved searches:', error);
      throw error;
    }
  }

  @Post('saved-searches')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Save search criteria',
    description: 'Save search criteria for the authenticated user'
  })
  @ApiBody({ type: SavedSearchDto })
  async saveSearch(
    @Request() req: any,
    @Body() savedSearchDto: SavedSearchDto
  ): Promise<any> {
    this.logger.log(`POST /airtable-orders/saved-searches called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.saveSearch(req.user?.airtableUserId, savedSearchDto);
    } catch (error) {
      this.logger.error('Error saving search:', error);
      throw error;
    }
  }

  @Put('saved-searches/:searchId')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Update saved search',
    description: 'Update an existing saved search for the authenticated user'
  })
  @ApiBody({ type: SavedSearchDto })
  async updateSavedSearch(
    @Request() req: any,
    @Param('searchId') searchId: string,
    @Body() savedSearchDto: SavedSearchDto
  ): Promise<any> {
    this.logger.log(`PUT /airtable-orders/saved-searches/${searchId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      return await this.airtableOrdersService.updateSavedSearch(req.user?.airtableUserId, searchId, savedSearchDto);
    } catch (error) {
      this.logger.error('Error updating saved search:', error);
      throw error;
    }
  }

  @Delete('saved-searches/:searchId')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Delete saved search',
    description: 'Delete a saved search for the authenticated user'
  })
  async deleteSavedSearch(
    @Request() req: any,
    @Param('searchId') searchId: string
  ): Promise<{ success: boolean }> {
    this.logger.log(`DELETE /airtable-orders/saved-searches/${searchId} called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    try {
      await this.airtableOrdersService.deleteSavedSearch(req.user?.airtableUserId, searchId);
      return { success: true };
    } catch (error) {
      this.logger.error('Error deleting saved search:', error);
      throw error;
    }
  }

  @Get(':loadId/debug')
  @UseGuards(AuthGuard) // Only require authentication, not carrier profile
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Debug load bidding eligibility',
    description: 'Diagnostic endpoint to check why a specific load cannot be found for bidding'
  })
  async debugLoadForBidding(
    @Param('loadId') loadId: string,
    @Request() req: any
  ): Promise<any> {
    this.logger.log(`GET /airtable-orders/${loadId}/debug called by user ${req.user?.airtableUserId} (MC: ${req.user?.mcNumber})`);
    
    try {
      const diagnostics = await this.airtableOrdersService.diagnoseLoadForBidding(loadId);
      
      return {
        success: true,
        loadId,
        diagnostics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Error debugging load ${loadId}:`, error);
      return {
        success: false,
        loadId,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}