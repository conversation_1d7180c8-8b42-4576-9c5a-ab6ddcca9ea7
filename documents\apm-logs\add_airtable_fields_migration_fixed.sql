-- Manual migration to add additional Airtable fields
-- Run this on the production database when ready

-- Create the InvStatus enum first (check if it exists to avoid errors)
DO $$ BEGIN
    CREATE TYPE "InvStatus" AS ENUM ('Not Sent', 'Sent', 'Paid');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add new columns to the loads table (one by one with error handling)
DO $$ 
BEGIN
    -- Add po_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='po_number') THEN
        ALTER TABLE loads ADD COLUMN po_number TEXT;
    END IF;
    
    -- Add so_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='so_number') THEN
        ALTER TABLE loads ADD COLUMN so_number TEXT;
    END IF;
    
    -- Add pickup_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='pickup_number') THEN
        ALTER TABLE loads ADD COLUMN pickup_number TEXT;
    END IF;
    
    -- Add delivery_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='delivery_number') THEN
        ALTER TABLE loads ADD COLUMN delivery_number TEXT;
    END IF;
    
    -- Add shipper_address column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='shipper_address') THEN
        ALTER TABLE loads ADD COLUMN shipper_address TEXT;
    END IF;
    
    -- Add receiver_name column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='receiver_name') THEN
        ALTER TABLE loads ADD COLUMN receiver_name TEXT;
    END IF;
    
    -- Add receiver_address column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='receiver_address') THEN
        ALTER TABLE loads ADD COLUMN receiver_address TEXT;
    END IF;
    
    -- Add inv_status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='inv_status') THEN
        ALTER TABLE loads ADD COLUMN inv_status "InvStatus";
    END IF;
    
    -- Add carrier column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='carrier') THEN
        ALTER TABLE loads ADD COLUMN carrier TEXT;
    END IF;
    
    -- Add cases column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='cases') THEN
        ALTER TABLE loads ADD COLUMN cases INTEGER;
    END IF;
    
    -- Add pallets column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='loads' AND column_name='pallets') THEN
        ALTER TABLE loads ADD COLUMN pallets INTEGER;
    END IF;
    
END $$;

-- Verify the columns were added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'loads' 
AND column_name IN ('po_number', 'so_number', 'pickup_number', 'delivery_number', 'shipper_address', 'receiver_name', 'receiver_address', 'inv_status', 'carrier', 'cases', 'pallets')
ORDER BY column_name; 