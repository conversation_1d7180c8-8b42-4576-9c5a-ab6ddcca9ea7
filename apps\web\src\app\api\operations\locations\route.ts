import { NextRequest, NextResponse } from 'next/server';
import { requireOperations } from '@/lib/auth';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

// In-memory cache for locations
interface LocationsCache {
  data: any[];
  lastUpdated: Date;
  isValid: boolean;
}

let locationsCache: LocationsCache = {
  data: [],
  lastUpdated: new Date(0), // Start with epoch time
  isValid: false
};

// Cache TTL in milliseconds (1 hour)
const CACHE_TTL = 60 * 60 * 1000;

async function fetchLocationsFromAirtable(): Promise<any[]> {
  const locations: any[] = [];

  await base('Locations').select({
    view: 'Grid view' // Use default view or specify your view name
  }).eachPage((records, fetchNextPage) => {
    records.forEach((record) => {
      // Get all fields from the record to be flexible with field names
      const fields = record.fields;

      locations.push({
        id: record.id,
        name: fields['Name'] || fields['Location Name'] || '',
        city: fields['City'] || '',
        state: fields['State'] || '',
        zipCode: fields['Zip Code'] || fields['ZIP'] || fields['Postal Code'] || '',
        address: fields['Address'] || fields['Full Address'] || ''
      });
    });
    fetchNextPage();
  });

  return locations;
}

function isCacheValid(): boolean {
  const now = new Date();
  const cacheAge = now.getTime() - locationsCache.lastUpdated.getTime();
  return locationsCache.isValid && cacheAge < CACHE_TTL;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication and operations access
    await requireOperations(request);

    // Check if this is a forced refresh
    const url = new URL(request.url);
    const forceRefresh = url.searchParams.get('refresh') === 'true';

    let locations: any[];
    let fromCache = false;

    // Use cache if valid and not forcing refresh
    if (!forceRefresh && isCacheValid()) {
      locations = locationsCache.data;
      fromCache = true;
      console.log(`Serving ${locations.length} locations from cache (age: ${Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60)} minutes)`);
    } else {
      // Fetch fresh data from Airtable
      console.log(forceRefresh ? 'Force refresh requested - fetching from Airtable' : 'Cache expired - fetching from Airtable');
      locations = await fetchLocationsFromAirtable();

      // Update cache
      locationsCache = {
        data: locations,
        lastUpdated: new Date(),
        isValid: true
      };

      console.log(`Fetched ${locations.length} locations from Airtable and updated cache`);
    }

    return NextResponse.json({
      success: true,
      locations: locations,
      cache: {
        fromCache,
        lastUpdated: locationsCache.lastUpdated.toISOString(),
        cacheAge: Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60), // minutes
        nextRefresh: new Date(locationsCache.lastUpdated.getTime() + CACHE_TTL).toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    
    if (error instanceof Error && error.message.includes('Operations access required')) {
      return NextResponse.json(
        { success: false, error: 'Access denied - Operations access required' },
        { status: 403 }
      );
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
