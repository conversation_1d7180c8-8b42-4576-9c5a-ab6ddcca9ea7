import { NextRequest, NextResponse } from 'next/server';
import { requireOperations } from '@/lib/auth';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

export async function GET(request: NextRequest) {
  try {
    // Check authentication and operations access
    await requireOperations(request);

    // Fetch locations from Airtable
    const locations: any[] = [];
    
    await base('Locations').select({
      view: 'Grid view' // Use default view or specify your view name
    }).eachPage((records, fetchNextPage) => {
      records.forEach((record) => {
        // Get all fields from the record to be flexible with field names
        const fields = record.fields;

        locations.push({
          id: record.id,
          name: fields['Name'] || fields['Location Name'] || '',
          city: fields['City'] || '',
          state: fields['State'] || '',
          zipCode: fields['Zip Code'] || fields['ZIP'] || fields['Postal Code'] || '',
          address: fields['Address'] || fields['Full Address'] || ''
        });
      });
      fetchNextPage();
    });

    console.log(`Fetched ${locations.length} locations from Airtable for operations`);

    return NextResponse.json({
      success: true,
      locations: locations
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    
    if (error instanceof Error && error.message.includes('Operations access required')) {
      return NextResponse.json(
        { success: false, error: 'Access denied - Operations access required' },
        { status: 403 }
      );
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
