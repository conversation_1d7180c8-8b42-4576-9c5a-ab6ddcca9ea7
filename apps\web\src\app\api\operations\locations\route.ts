import { NextRequest, NextResponse } from 'next/server';
import { requireOperations } from '@/lib/auth';
import Airtable from 'airtable';
import { promises as fs } from 'fs';
import path from 'path';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

// Long-term cache for locations (24 hours)
interface LocationsCache {
  data: any[];
  lastUpdated: Date;
  isValid: boolean;
}

let locationsCache: LocationsCache = {
  data: [],
  lastUpdated: new Date(0), // Start with epoch time
  isValid: false
};

// Long-term cache TTL in milliseconds (24 hours - locations rarely change)
const CACHE_TTL = 24 * 60 * 60 * 1000;

// File-based cache for persistence across server restarts
const CACHE_FILE_PATH = path.join(process.cwd(), 'tmp', 'locations-cache.json');

// Ensure tmp directory exists
async function ensureTmpDir() {
  try {
    const tmpDir = path.dirname(CACHE_FILE_PATH);
    await fs.mkdir(tmpDir, { recursive: true });
  } catch (error) {
    console.warn('Could not create tmp directory:', error);
  }
}

// Load cache from file
async function loadCacheFromFile(): Promise<LocationsCache | null> {
  try {
    const data = await fs.readFile(CACHE_FILE_PATH, 'utf8');
    const parsed = JSON.parse(data);
    return {
      data: parsed.data,
      lastUpdated: new Date(parsed.lastUpdated),
      isValid: parsed.isValid
    };
  } catch (error) {
    // File doesn't exist or is corrupted - this is fine
    return null;
  }
}

// Save cache to file
async function saveCacheToFile(cache: LocationsCache): Promise<void> {
  try {
    await ensureTmpDir();
    await fs.writeFile(CACHE_FILE_PATH, JSON.stringify({
      data: cache.data,
      lastUpdated: cache.lastUpdated.toISOString(),
      isValid: cache.isValid
    }), 'utf8');
  } catch (error) {
    console.warn('Could not save cache to file:', error);
    // Don't throw - file caching is optional
  }
}

async function fetchLocationsFromAirtable(): Promise<any[]> {
  const locations: any[] = [];

  await base('Locations').select({
    view: 'Grid view' // Use default view or specify your view name
  }).eachPage((records, fetchNextPage) => {
    records.forEach((record) => {
      // Get all fields from the record to be flexible with field names
      const fields = record.fields;

      locations.push({
        id: record.id,
        name: fields['Name'] || fields['Location Name'] || '',
        city: fields['City'] || '',
        state: fields['State'] || '',
        zipCode: fields['Zip Code'] || fields['ZIP'] || fields['Postal Code'] || '',
        address: fields['Address'] || fields['Full Address'] || ''
      });
    });
    fetchNextPage();
  });

  return locations;
}

function isCacheValid(cache: LocationsCache): boolean {
  const now = new Date();
  const cacheAge = now.getTime() - cache.lastUpdated.getTime();
  return cache.isValid && cacheAge < CACHE_TTL;
}

// Initialize cache from file on startup
async function initializeCache(): Promise<void> {
  if (locationsCache.data.length === 0) {
    const fileCache = await loadCacheFromFile();
    if (fileCache && isCacheValid(fileCache)) {
      locationsCache = fileCache;
      console.log(`Loaded ${locationsCache.data.length} locations from file cache (age: ${Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60 / 60)} hours)`);
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication and operations access
    await requireOperations(request);

    // Initialize cache from file if needed
    await initializeCache();

    // Check if this is a forced refresh
    const url = new URL(request.url);
    const forceRefresh = url.searchParams.get('refresh') === 'true';

    let locations: any[];
    let fromCache = false;

    // Use cache if valid and not forcing refresh
    if (!forceRefresh && isCacheValid(locationsCache)) {
      locations = locationsCache.data;
      fromCache = true;
      const cacheAgeHours = Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60 / 60);
      console.log(`Serving ${locations.length} locations from long-term cache (age: ${cacheAgeHours} hours)`);
    } else {
      // Fetch fresh data from Airtable
      console.log(forceRefresh ? 'Force refresh requested - fetching from Airtable' : 'Long-term cache expired - fetching from Airtable');
      locations = await fetchLocationsFromAirtable();

      // Update cache
      locationsCache = {
        data: locations,
        lastUpdated: new Date(),
        isValid: true
      };

      // Save to file for persistence
      await saveCacheToFile(locationsCache);

      console.log(`Fetched ${locations.length} locations from Airtable and updated long-term cache`);
    }

    const cacheAgeHours = Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60 / 60);

    return NextResponse.json({
      success: true,
      locations: locations,
      cache: {
        fromCache,
        lastUpdated: locationsCache.lastUpdated.toISOString(),
        cacheAge: cacheAgeHours, // hours instead of minutes
        cacheAgeDisplay: cacheAgeHours < 1 ? 'Less than 1 hour' : `${cacheAgeHours} hour${cacheAgeHours > 1 ? 's' : ''}`,
        nextRefresh: new Date(locationsCache.lastUpdated.getTime() + CACHE_TTL).toISOString(),
        cacheTTL: '24 hours'
      }
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    
    if (error instanceof Error && error.message.includes('Operations access required')) {
      return NextResponse.json(
        { success: false, error: 'Access denied - Operations access required' },
        { status: 403 }
      );
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
