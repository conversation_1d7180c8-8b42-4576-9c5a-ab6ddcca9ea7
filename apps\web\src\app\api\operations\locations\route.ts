import { NextRequest, NextResponse } from 'next/server';
import { requireOperations } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import Airtable from 'airtable';

console.log('=== OPERATIONS LOCATIONS ROUTE LOADING (v2.0) ===');
console.log('Environment check:', {
  NODE_ENV: process.env.NODE_ENV,
  AIRTABLE_API_KEY: !!process.env.AIRTABLE_API_KEY,
  AIRTABLE_BASE_ID: !!process.env.AIRTABLE_BASE_ID,
  N8N_JWT_SECRET: !!process.env.N8N_JWT_SECRET,
  DATABASE_URL: !!process.env.DATABASE_URL
});

// Debug Prisma client
console.log('Prisma client debug:', {
  prismaExists: !!prisma,
  prismaType: typeof prisma,
  prismaConstructor: prisma?.constructor?.name,
  cachedLocationExists: !!(prisma as any)?.cachedLocation,
  cachedLocationMethods: (prisma as any)?.cachedLocation ? Object.getOwnPropertyNames((prisma as any).cachedLocation) : 'N/A'
});

// Defer Airtable configuration until request time to avoid module-level errors
function getAirtableBase() {
  console.log('Configuring Airtable base at request time...');
  console.log('AIRTABLE_API_KEY present:', !!process.env.AIRTABLE_API_KEY);
  console.log('AIRTABLE_BASE_ID present:', !!process.env.AIRTABLE_BASE_ID);
  
  if (!process.env.AIRTABLE_API_KEY) {
    throw new Error('AIRTABLE_API_KEY environment variable is not set');
  }
  
  if (!process.env.AIRTABLE_BASE_ID) {
    throw new Error('AIRTABLE_BASE_ID environment variable is not set');
  }
  
  return new Airtable({
    apiKey: process.env.AIRTABLE_API_KEY
  }).base(process.env.AIRTABLE_BASE_ID);
}

// Permanent cache - only refreshes on manual sync (locations are static data)
// No TTL - cache persists until manually refreshed

// Fetch locations from Airtable and sync to database
async function syncLocationsFromAirtable(): Promise<any[]> {
  console.log('Fetching locations from Airtable...');
  console.log('Airtable config check:', {
    hasApiKey: !!process.env.AIRTABLE_API_KEY,
    hasBaseId: !!process.env.AIRTABLE_BASE_ID,
    apiKeyPrefix: process.env.AIRTABLE_API_KEY ? process.env.AIRTABLE_API_KEY.substring(0, 8) + '...' : 'none',
    baseId: process.env.AIRTABLE_BASE_ID || 'none'
  });

  const locations: any[] = [];

  try {
    const base = getAirtableBase();
    console.log('Airtable base configured successfully');
    
    await base('Locations').select({
      view: 'Grid view' // Use default view or specify your view name
    }).eachPage((records: any, fetchNextPage: () => void) => {
      console.log(`Processing ${records.length} records from Airtable`);
      records.forEach((record: any) => {
        // Get all fields from the record to be flexible with field names
        const fields = record.fields;

        locations.push({
          airtableRecordId: record.id,
          name: fields['Name'] || fields['Location Name'] || '',
          city: fields['City'] || '',
          state: fields['State'] || '',
          zipCode: fields['Zip Code'] || fields['ZIP'] || fields['Postal Code'] || '',
          address: fields['Address'] || fields['Full Address'] || ''
        });
      });
      fetchNextPage();
    });

    console.log(`Fetched ${locations.length} locations from Airtable`);
  } catch (airtableError) {
    console.error('Airtable fetch error:', airtableError);
    throw new Error(`Failed to fetch from Airtable: ${airtableError instanceof Error ? airtableError.message : 'Unknown error'}`);
  }

  try {
    // Clear existing cached locations and insert new ones
    console.log('Clearing existing cached locations...');
    await (prisma as any).cachedLocation.deleteMany();

    if (locations.length > 0) {
      console.log(`Inserting ${locations.length} locations into database...`);
      await (prisma as any).cachedLocation.createMany({
        data: locations.map(loc => ({
          airtableRecordId: loc.airtableRecordId,
          name: loc.name,
          city: loc.city,
          state: loc.state,
          zipCode: loc.zipCode,
          address: loc.address,
          lastSyncedAt: new Date()
        }))
      });
    }

    console.log(`Synced ${locations.length} locations to database`);
    return locations;
  } catch (dbError) {
    console.error('Database operation error:', dbError);
    throw new Error(`Failed to sync to database: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`);
  }
}

// Get locations from database cache
async function getCachedLocations(): Promise<{ locations: any[], lastSynced: Date | null }> {
  try {
    console.log('Fetching cached locations from database...');
    console.log('Prisma debug in getCachedLocations:', {
      prismaExists: !!prisma,
      prismaType: typeof prisma,
      cachedLocationExists: !!(prisma as any)?.cachedLocation
    });
    
    if (!prisma) {
      throw new Error('Prisma client is undefined - database connection failed');
    }
    
    // Test database connection first
    console.log('Testing database connection...');
    try {
      await prisma.$connect();
      console.log('Database connection successful');
    } catch (connectError) {
      console.error('Database connection failed:', connectError);
      throw new Error(`Database connection failed: ${connectError instanceof Error ? connectError.message : 'Unknown error'}`);
    }
    
    console.log('Attempting to call prisma.cachedLocation.findMany...');
    const cachedLocations = await (prisma as any).cachedLocation.findMany({
      orderBy: [
        { state: 'asc' },
        { city: 'asc' },
        { name: 'asc' }
      ]
    });

    console.log(`Found ${cachedLocations.length} cached locations in database`);

    const locations = cachedLocations.map((loc: any) => ({
      id: loc.airtableRecordId,
      name: loc.name,
      city: loc.city,
      state: loc.state,
      zipCode: loc.zipCode,
      address: loc.address
    }));

    const lastSynced = cachedLocations.length > 0 ? cachedLocations[0].lastSyncedAt : null;
    console.log('Last synced:', lastSynced);

    return { locations, lastSynced };
  } catch (dbError) {
    console.error('Database cache fetch error:', dbError);
    console.error('Error details:', {
      name: dbError instanceof Error ? dbError.name : 'Unknown',
      message: dbError instanceof Error ? dbError.message : 'Unknown error',
      stack: dbError instanceof Error ? dbError.stack : 'No stack trace',
      cause: dbError instanceof Error ? (dbError as any).cause : 'No cause'
    });
    throw new Error(`Failed to fetch cached locations: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`);
  }
}

// Check if cache needs refresh (only if no data exists)
function needsRefresh(lastSynced: Date | null): boolean {
  // Only refresh if no data exists in cache
  return !lastSynced;
}

export async function GET(request: NextRequest) {
  try {
    console.log('=== OPERATIONS LOCATIONS API START ===');
    console.log('Request received at:', new Date().toISOString());
    
    // Early environment check
    if (!process.env.AIRTABLE_API_KEY || !process.env.AIRTABLE_BASE_ID) {
      console.error('Missing Airtable environment variables!');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Airtable configuration missing',
          debug: {
            hasApiKey: !!process.env.AIRTABLE_API_KEY,
            hasBaseId: !!process.env.AIRTABLE_BASE_ID
          }
        },
        { status: 500 }
      );
    }

    // Check authentication and operations access
    console.log('Starting authentication check...');
    await requireOperations(request);
    console.log('Authentication check passed!');

    // Check if this is a forced refresh
    const url = new URL(request.url);
    const forceRefresh = url.searchParams.get('refresh') === 'true';

    let locations: any[];
    let lastSynced: Date | null;
    let fromCache = false;

    // Get cached locations from database
    const cachedData = await getCachedLocations();
    locations = cachedData.locations;
    lastSynced = cachedData.lastSynced;

    // Check if we need to refresh from Airtable
    if (forceRefresh || needsRefresh(lastSynced)) {
      console.log(forceRefresh ? 'Manual refresh requested - syncing from Airtable' : 'No cached data found - initial sync from Airtable');

      // Sync fresh data from Airtable to database
      const freshLocations = await syncLocationsFromAirtable();
      locations = freshLocations.map(loc => ({
        id: loc.airtableRecordId,
        name: loc.name,
        city: loc.city,
        state: loc.state,
        zipCode: loc.zipCode,
        address: loc.address
      }));
      lastSynced = new Date();
      fromCache = false;
    } else {
      fromCache = true;
      const cacheAgeDays = lastSynced ? Math.round((new Date().getTime() - lastSynced.getTime()) / 1000 / 60 / 60 / 24) : 0;
      console.log(`Serving ${locations.length} locations from permanent database cache (synced ${cacheAgeDays} days ago)`);
    }

    // Calculate cache age for display
    const cacheAgeDays = lastSynced ? Math.round((new Date().getTime() - lastSynced.getTime()) / 1000 / 60 / 60 / 24) : 0;

    return NextResponse.json({
      success: true,
      locations: locations,
      cache: {
        fromCache,
        lastUpdated: lastSynced?.toISOString() || new Date().toISOString(),
        cacheAge: cacheAgeDays,
        cacheAgeDisplay: cacheAgeDays === 0 ? 'Today' : cacheAgeDays === 1 ? '1 day ago' : `${cacheAgeDays} days ago`,
        nextRefresh: 'Manual refresh only',
        cacheTTL: 'Permanent (manual refresh only)',
        cacheType: 'permanent database'
      }
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    
    if (error instanceof Error && error.message.includes('Operations access required')) {
      return NextResponse.json(
        { success: false, error: 'Access denied - Operations access required' },
        { status: 403 }
      );
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Return the actual error message for debugging
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch locations';
    return NextResponse.json(
      { success: false, error: errorMessage, debug: true },
      { status: 500 }
    );
  }
}
