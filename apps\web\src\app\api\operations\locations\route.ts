import { NextRequest, NextResponse } from 'next/server';
import { requireOperations } from '@/lib/auth';
import Airtable from 'airtable';
import { PrismaClient } from '@repo/db';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

// Initialize Prisma client
const prisma = new PrismaClient();

// Long-term cache TTL in milliseconds (7 days - locations rarely change)
const CACHE_TTL = 7 * 24 * 60 * 60 * 1000;

// Fetch locations from Airtable and sync to database
async function syncLocationsFromAirtable(): Promise<any[]> {
  console.log('Fetching locations from Airtable...');
  const locations: any[] = [];

  await base('Locations').select({
    view: 'Grid view' // Use default view or specify your view name
  }).eachPage((records, fetchNextPage) => {
    records.forEach((record) => {
      // Get all fields from the record to be flexible with field names
      const fields = record.fields;

      locations.push({
        airtableRecordId: record.id,
        name: fields['Name'] || fields['Location Name'] || '',
        city: fields['City'] || '',
        state: fields['State'] || '',
        zipCode: fields['Zip Code'] || fields['ZIP'] || fields['Postal Code'] || '',
        address: fields['Address'] || fields['Full Address'] || ''
      });
    });
    fetchNextPage();
  });

  // Clear existing cached locations and insert new ones
  await prisma.cachedLocation.deleteMany();

  if (locations.length > 0) {
    await prisma.cachedLocation.createMany({
      data: locations.map(loc => ({
        airtableRecordId: loc.airtableRecordId,
        name: loc.name,
        city: loc.city,
        state: loc.state,
        zipCode: loc.zipCode,
        address: loc.address,
        lastSyncedAt: new Date()
      }))
    });
  }

  console.log(`Synced ${locations.length} locations to database`);
  return locations;
}

// Get locations from database cache
async function getCachedLocations(): Promise<{ locations: any[], lastSynced: Date | null }> {
  const cachedLocations = await prisma.cachedLocation.findMany({
    orderBy: [
      { state: 'asc' },
      { city: 'asc' },
      { name: 'asc' }
    ]
  });

  const locations = cachedLocations.map(loc => ({
    id: loc.airtableRecordId,
    name: loc.name,
    city: loc.city,
    state: loc.state,
    zipCode: loc.zipCode,
    address: loc.address
  }));

  const lastSynced = cachedLocations.length > 0 ? cachedLocations[0].lastSyncedAt : null;

  return { locations, lastSynced };
}

// Check if cache needs refresh
function needsRefresh(lastSynced: Date | null): boolean {
  if (!lastSynced) return true;
  const now = new Date();
  const cacheAge = now.getTime() - lastSynced.getTime();
  return cacheAge > CACHE_TTL;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication and operations access
    await requireOperations(request);

    // Initialize cache from file if needed
    await initializeCache();

    // Check if this is a forced refresh
    const url = new URL(request.url);
    const forceRefresh = url.searchParams.get('refresh') === 'true';

    let locations: any[];
    let fromCache = false;

    // Use cache if valid and not forcing refresh
    if (!forceRefresh && isCacheValid(locationsCache)) {
      locations = locationsCache.data;
      fromCache = true;
      const cacheAgeHours = Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60 / 60);
      console.log(`Serving ${locations.length} locations from long-term cache (age: ${cacheAgeHours} hours)`);
    } else {
      // Fetch fresh data from Airtable
      console.log(forceRefresh ? 'Force refresh requested - fetching from Airtable' : 'Long-term cache expired - fetching from Airtable');
      locations = await fetchLocationsFromAirtable();

      // Update cache
      locationsCache = {
        data: locations,
        lastUpdated: new Date(),
        isValid: true
      };

      // Save to file for persistence
      await saveCacheToFile(locationsCache);

      console.log(`Fetched ${locations.length} locations from Airtable and updated long-term cache`);
    }

    const cacheAgeHours = Math.round((new Date().getTime() - locationsCache.lastUpdated.getTime()) / 1000 / 60 / 60);

    return NextResponse.json({
      success: true,
      locations: locations,
      cache: {
        fromCache,
        lastUpdated: locationsCache.lastUpdated.toISOString(),
        cacheAge: cacheAgeHours, // hours instead of minutes
        cacheAgeDisplay: cacheAgeHours < 1 ? 'Less than 1 hour' : `${cacheAgeHours} hour${cacheAgeHours > 1 ? 's' : ''}`,
        nextRefresh: new Date(locationsCache.lastUpdated.getTime() + CACHE_TTL).toISOString(),
        cacheTTL: '24 hours'
      }
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    
    if (error instanceof Error && error.message.includes('Operations access required')) {
      return NextResponse.json(
        { success: false, error: 'Access denied - Operations access required' },
        { status: 403 }
      );
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
