import { NextRequest, NextResponse } from 'next/server';
import { requireOperations } from '@/lib/auth';
import Airtable from 'airtable';
import { PrismaClient } from '@repo/db';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

// Initialize Prisma client
const prisma = new PrismaClient();

// Long-term cache TTL in milliseconds (7 days - locations rarely change)
const CACHE_TTL = 7 * 24 * 60 * 60 * 1000;

// Fetch locations from Airtable and sync to database
async function syncLocationsFromAirtable(): Promise<any[]> {
  console.log('Fetching locations from Airtable...');
  const locations: any[] = [];

  await base('Locations').select({
    view: 'Grid view' // Use default view or specify your view name
  }).eachPage((records, fetchNextPage) => {
    records.forEach((record) => {
      // Get all fields from the record to be flexible with field names
      const fields = record.fields;

      locations.push({
        airtableRecordId: record.id,
        name: fields['Name'] || fields['Location Name'] || '',
        city: fields['City'] || '',
        state: fields['State'] || '',
        zipCode: fields['Zip Code'] || fields['ZIP'] || fields['Postal Code'] || '',
        address: fields['Address'] || fields['Full Address'] || ''
      });
    });
    fetchNextPage();
  });

  // Clear existing cached locations and insert new ones
  await prisma.cachedLocation.deleteMany();

  if (locations.length > 0) {
    await prisma.cachedLocation.createMany({
      data: locations.map(loc => ({
        airtableRecordId: loc.airtableRecordId,
        name: loc.name,
        city: loc.city,
        state: loc.state,
        zipCode: loc.zipCode,
        address: loc.address,
        lastSyncedAt: new Date()
      }))
    });
  }

  console.log(`Synced ${locations.length} locations to database`);
  return locations;
}

// Get locations from database cache
async function getCachedLocations(): Promise<{ locations: any[], lastSynced: Date | null }> {
  const cachedLocations = await prisma.cachedLocation.findMany({
    orderBy: [
      { state: 'asc' },
      { city: 'asc' },
      { name: 'asc' }
    ]
  });

  const locations = cachedLocations.map(loc => ({
    id: loc.airtableRecordId,
    name: loc.name,
    city: loc.city,
    state: loc.state,
    zipCode: loc.zipCode,
    address: loc.address
  }));

  const lastSynced = cachedLocations.length > 0 ? cachedLocations[0].lastSyncedAt : null;

  return { locations, lastSynced };
}

// Check if cache needs refresh
function needsRefresh(lastSynced: Date | null): boolean {
  if (!lastSynced) return true;
  const now = new Date();
  const cacheAge = now.getTime() - lastSynced.getTime();
  return cacheAge > CACHE_TTL;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication and operations access
    await requireOperations(request);

    // Check if this is a forced refresh
    const url = new URL(request.url);
    const forceRefresh = url.searchParams.get('refresh') === 'true';

    let locations: any[];
    let lastSynced: Date | null;
    let fromCache = false;

    // Get cached locations from database
    const cachedData = await getCachedLocations();
    locations = cachedData.locations;
    lastSynced = cachedData.lastSynced;

    // Check if we need to refresh from Airtable
    if (forceRefresh || needsRefresh(lastSynced)) {
      console.log(forceRefresh ? 'Force refresh requested - syncing from Airtable' : 'Database cache expired - syncing from Airtable');

      // Sync fresh data from Airtable to database
      const freshLocations = await syncLocationsFromAirtable();
      locations = freshLocations.map(loc => ({
        id: loc.airtableRecordId,
        name: loc.name,
        city: loc.city,
        state: loc.state,
        zipCode: loc.zipCode,
        address: loc.address
      }));
      lastSynced = new Date();
      fromCache = false;
    } else {
      fromCache = true;
      const cacheAgeDays = lastSynced ? Math.round((new Date().getTime() - lastSynced.getTime()) / 1000 / 60 / 60 / 24) : 0;
      console.log(`Serving ${locations.length} locations from database cache (age: ${cacheAgeDays} days)`);
    }

    // Calculate cache age for display
    const cacheAgeDays = lastSynced ? Math.round((new Date().getTime() - lastSynced.getTime()) / 1000 / 60 / 60 / 24) : 0;
    const nextRefresh = lastSynced ? new Date(lastSynced.getTime() + CACHE_TTL) : new Date();

    return NextResponse.json({
      success: true,
      locations: locations,
      cache: {
        fromCache,
        lastUpdated: lastSynced?.toISOString() || new Date().toISOString(),
        cacheAge: cacheAgeDays,
        cacheAgeDisplay: cacheAgeDays === 0 ? 'Less than 1 day' : `${cacheAgeDays} day${cacheAgeDays > 1 ? 's' : ''}`,
        nextRefresh: nextRefresh.toISOString(),
        cacheTTL: '7 days',
        cacheType: 'database'
      }
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    
    if (error instanceof Error && error.message.includes('Operations access required')) {
      return NextResponse.json(
        { success: false, error: 'Access denied - Operations access required' },
        { status: 403 }
      );
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
