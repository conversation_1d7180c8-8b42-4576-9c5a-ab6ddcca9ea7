"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PrismaService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const db_1 = require("@repo/db");
let PrismaService = PrismaService_1 = class PrismaService extends db_1.PrismaClient {
    logger = new common_1.Logger(PrismaService_1.name);
    constructor() {
        super({
            log: [
                {
                    emit: 'event',
                    level: 'query',
                },
                {
                    emit: 'event',
                    level: 'error',
                },
                {
                    emit: 'event',
                    level: 'info',
                },
                {
                    emit: 'event',
                    level: 'warn',
                },
            ],
        });
    }
    async onModuleInit() {
        try {
            this.logger.log('🔄 Connecting to database...');
            await Promise.race([
                this.$connect(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Database connection timeout')), 10000))
            ]);
            this.logger.log('✅ Database connected successfully');
        }
        catch (error) {
            this.logger.error(`❌ Database connection failed: ${error.message}`);
            if (!process.env.VERCEL) {
                throw error;
            }
        }
    }
    async onModuleDestroy() {
        try {
            this.logger.log('🔄 Disconnecting from database...');
            await this.$disconnect();
            this.logger.log('✅ Database disconnected successfully');
        }
        catch (error) {
            this.logger.error(`❌ Database disconnection failed: ${error.message}`);
        }
    }
    async healthCheck() {
        try {
            await Promise.race([
                this.$queryRaw `SELECT 1`,
                new Promise((_, reject) => setTimeout(() => reject(new Error('Health check timeout')), 5000))
            ]);
            return true;
        }
        catch (error) {
            this.logger.error('Database health check failed:', error.message);
            return false;
        }
    }
    async verifyConnection() {
        try {
            const result = await Promise.race([
                this.$queryRaw `SELECT 1 as test`,
                new Promise((_, reject) => setTimeout(() => reject(new Error('Connection verification timeout')), 3000))
            ]);
            if (result && result[0]?.test === 1) {
                return { connected: true };
            }
            else {
                return { connected: false, error: 'Unexpected query result' };
            }
        }
        catch (error) {
            return { connected: false, error: error.message };
        }
    }
    async executeWithRetry(operation, operationName, maxRetries = 3) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                if (this.isRetryableError(error)) {
                    this.logger.warn(`${operationName} failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
                    if (attempt < maxRetries) {
                        const delay = Math.pow(2, attempt) * 1000;
                        this.logger.log(`Retrying ${operationName} in ${delay}ms...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        continue;
                    }
                }
                this.logger.error(`${operationName} failed after ${attempt} attempts:`, error);
                throw this.enhanceError(error, operationName);
            }
        }
        throw this.enhanceError(lastError, operationName);
    }
    isRetryableError(error) {
        if (error && error.message) {
            const message = error.message.toLowerCase();
            return message.includes('p1001') ||
                message.includes('p1002') ||
                message.includes('p2024') ||
                message.includes('connection') ||
                message.includes('timeout') ||
                message.includes('econnrefused') ||
                message.includes('network');
        }
        return false;
    }
    enhanceError(error, operationName) {
        if (error && error.message) {
            const message = error.message.toLowerCase();
            if (message.includes('p1001') || message.includes('p1002')) {
                throw new common_1.HttpException('Database connection error - please try again', common_1.HttpStatus.SERVICE_UNAVAILABLE);
            }
            if (message.includes('p2024')) {
                throw new common_1.HttpException('Database timeout - please try again', common_1.HttpStatus.REQUEST_TIMEOUT);
            }
            if (message.includes('p2002')) {
                throw new common_1.HttpException('Data conflict - record already exists', common_1.HttpStatus.CONFLICT);
            }
            if (message.includes('p2025')) {
                throw new common_1.HttpException('Record not found', common_1.HttpStatus.NOT_FOUND);
            }
        }
        this.logger.error(`Error in ${operationName}:`, error);
        throw new common_1.HttpException('Operation failed - please try again', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
    }
    async transaction(fn, operationName = 'transaction') {
        return this.executeWithRetry(() => this.$transaction(fn), operationName);
    }
    async getDetailedHealthCheck() {
        const startTime = Date.now();
        try {
            await Promise.race([
                this.$queryRaw `SELECT 1 as health_check`,
                new Promise((_, reject) => setTimeout(() => reject(new Error('Health check timeout')), 5000))
            ]);
            const responseTime = Date.now() - startTime;
            return {
                status: responseTime > 3000 ? 'degraded' : 'healthy',
                database: {
                    connected: true,
                    responseTime
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error('Database health check failed:', error);
            return {
                status: 'unhealthy',
                database: {
                    connected: false,
                    error: error.message
                },
                timestamp: new Date().toISOString()
            };
        }
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = PrismaService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PrismaService);
//# sourceMappingURL=prisma.service.js.map