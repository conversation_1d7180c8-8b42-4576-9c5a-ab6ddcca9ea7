'use client';

import React, { useState, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  HelpCircle, 
  Search, 
  ChevronDown, 
  ChevronRight, 
  Building2, 
  Truck, 
  FileText, 
  CreditCard, 
  Phone, 
  Shield,
  Settings,
  MapPin,
  Clock,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  isExpanded?: boolean;
}

interface FAQCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
}

const faqCategories: FAQCategory[] = [
  {
    id: 'getting-started',
    name: 'Getting Started',
    description: 'Account setup and first steps',
    icon: Building2,
    color: 'bg-blue-100 text-blue-800'
  },
  {
    id: 'profile-setup',
    name: 'Profile & Verification',
    description: 'DOT, MC numbers and company info',
    icon: Shield,
    color: 'bg-green-100 text-green-800'
  },
  {
    id: 'finding-loads',
    name: 'Finding & Bidding on Loads',
    description: 'How to use the loadboard',
    icon: Truck,
    color: 'bg-purple-100 text-purple-800'
  },
  {
    id: 'load-management',
    name: 'Managing Your Loads',
    description: 'Status updates and documentation',
    icon: FileText,
    color: 'bg-orange-100 text-orange-800'
  },
  {
    id: 'billing-payments',
    name: 'Billing & Payments',
    description: 'Payment processing and tracking',
    icon: CreditCard,
    color: 'bg-indigo-100 text-indigo-800'
  },
  {
    id: 'technical-support',
    name: 'Technical Support',
    description: 'Troubleshooting and contact info',
    icon: Phone,
    color: 'bg-red-100 text-red-800'
  }
];

const faqData: FAQItem[] = [
  // Getting Started
  {
    id: 'account-creation',
    question: 'How do I create an account on the FCP Carrier Portal?',
    answer: 'To create an account: 1) Click "Sign Up" on the login page, 2) Enter your email and create a password, 3) Verify your email address, 4) Complete your carrier profile with company information, DOT number, and MC number. Your account will be reviewed by our team before activation.',
    category: 'getting-started',
    tags: ['signup', 'account', 'registration']
  },
  {
    id: 'account-verification',
    question: 'How long does account verification take?',
    answer: 'Account verification typically takes 1-2 business days. Our team reviews your carrier profile, DOT/MC numbers, and insurance information. You\'ll receive an email notification once your account is approved and you can start bidding on loads.',
    category: 'getting-started',
    tags: ['verification', 'approval', 'waiting']
  },
  {
    id: 'first-login',
    question: 'What should I do after my first login?',
    answer: 'After your first login: 1) Complete your profile in Settings with all required information, 2) Upload required documents (insurance, W9), 3) Review available loads on the Loadboard, 4) Familiarize yourself with the bidding process. Take the guided tour for a walkthrough of all features.',
    category: 'getting-started',
    tags: ['first-time', 'setup', 'tour']
  },

  // Profile Setup
  {
    id: 'required-profile-info',
    question: 'What information is required for my carrier profile?',
    answer: 'Required information includes: Company name, DOT number, MC number, primary contact information, equipment types you operate, service areas, current insurance coverage, and W9 tax form. All fields marked with * are mandatory for account approval.',
    category: 'profile-setup',
    tags: ['profile', 'required', 'information', 'dot', 'mc']
  },
  {
    id: 'equipment-types',
    question: 'How do I specify my equipment types?',
    answer: 'In your profile settings, select all equipment types you can provide: Dry Van, Refrigerated, Flatbed, Step Deck, Lowboy, etc. This helps match you with appropriate loads. You can update your equipment list anytime to reflect fleet changes.',
    category: 'profile-setup',
    tags: ['equipment', 'trucks', 'fleet']
  },
  {
    id: 'service-areas',
    question: 'How do I set my service areas?',
    answer: 'Define your service areas by: 1) Selecting states you operate in, 2) Setting preferred lane types (regional, OTR, local), 3) Specifying any restrictions (no NYC, no California, etc.). This helps filter loads relevant to your operation.',
    category: 'profile-setup',
    tags: ['service-areas', 'states', 'lanes']
  },

  // Finding Loads
  {
    id: 'loadboard-navigation',
    question: 'How do I find loads on the loadboard?',
    answer: 'Use the loadboard filters to find relevant loads: 1) Set pickup/delivery locations, 2) Select your equipment type, 3) Choose date ranges, 4) Set minimum rate requirements. Loads are updated in real-time. Use the advanced filters for more precise searches.',
    category: 'finding-loads',
    tags: ['loadboard', 'search', 'filters']
  },
  {
    id: 'bidding-process',
    question: 'How does the bidding process work?',
    answer: 'To bid on a load: 1) Click "Bid" on any available load, 2) Enter your rate and any notes, 3) Submit your bid. The shipper will review all bids and select the best option. You\'ll be notified if your bid is accepted or if you need to revise it.',
    category: 'finding-loads',
    tags: ['bidding', 'rates', 'proposals']
  },
  {
    id: 'load-statuses',
    question: 'What do the different load statuses mean?',
    answer: 'Load statuses: Available (open for bidding), Pending (bids being reviewed), Awarded (assigned to a carrier), In Transit (being delivered), Delivered (completed), Cancelled (no longer available). You can track your loads in "My Loads" section.',
    category: 'finding-loads',
    tags: ['status', 'tracking', 'lifecycle']
  },

  // Load Management
  {
    id: 'awarded-loads',
    question: 'What happens after I\'m awarded a load?',
    answer: 'After being awarded: 1) You\'ll receive pickup instructions and BOL, 2) Update load status at pickup, 3) Provide updates during transit, 4) Upload delivery receipt/POD, 5) Load status changes to "Delivered" for payment processing.',
    category: 'load-management',
    tags: ['awarded', 'pickup', 'delivery', 'bol']
  },
  {
    id: 'document-uploads',
    question: 'What documents do I need to upload?',
    answer: 'Required documents include: Proof of Delivery (POD), Bill of Lading (BOL), any inspection reports, photos of freight condition, delivery receipts. Upload these in the "My Loads" section for each shipment to ensure prompt payment.',
    category: 'load-management',
    tags: ['documents', 'pod', 'bol', 'receipts']
  },
  {
    id: 'status-updates',
    question: 'How do I update load status?',
    answer: 'Update load status in "My Loads": 1) Select the load, 2) Click "Update Status", 3) Choose appropriate status (Picked Up, In Transit, Delivered), 4) Add notes if needed, 5) Upload required documents. Real-time updates help with tracking.',
    category: 'load-management',
    tags: ['status', 'updates', 'tracking']
  },

  // Billing & Payments
  {
    id: 'payment-process',
    question: 'How and when do I get paid?',
    answer: 'Payment process: 1) Complete delivery and upload POD, 2) Invoice is automatically generated, 3) Payment terms are typically NET 30 days, 4) Payments are processed via ACH to your bank account. Check "Billing" section for payment status and history.',
    category: 'billing-payments',
    tags: ['payment', 'invoice', 'net30', 'ach']
  },
  {
    id: 'invoice-tracking',
    question: 'How can I track my invoices and payments?',
    answer: 'Track invoices in the "Billing" section: View pending invoices, payment dates, amounts, status updates. You can download invoice copies and payment confirmations. Set up email notifications for payment updates.',
    category: 'billing-payments',
    tags: ['invoices', 'tracking', 'billing']
  },

  // Technical Support
  {
    id: 'forgot-password',
    question: 'I forgot my password. How do I reset it?',
    answer: 'To reset your password: 1) Click "Forgot Password" on the login page, 2) Enter your email address, 3) Check your email for reset instructions, 4) Follow the link to create a new password. Contact support if you don\'t receive the email within 10 minutes.',
    category: 'technical-support',
    tags: ['password', 'reset', 'login']
  },
  {
    id: 'contact-support',
    question: 'How do I contact customer support?',
    answer: 'Contact support: Email: <EMAIL>, Phone: 1-800-FCP-LOAD (**************), Hours: Monday-Friday 8AM-6PM EST. For urgent load issues, use the 24/7 dispatch line: 1-800-FCP-DISP (**************).',
    category: 'technical-support',
    tags: ['contact', 'support', 'phone', 'email']
  },
  {
    id: 'browser-issues',
    question: 'The portal isn\'t working properly in my browser',
    answer: 'For browser issues: 1) Clear your browser cache and cookies, 2) Try an incognito/private window, 3) Update to the latest browser version, 4) Disable ad blockers temporarily. Supported browsers: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+.',
    category: 'technical-support',
    tags: ['browser', 'technical', 'cache', 'compatibility']
  }
];

interface HelpModalProps {
  trigger?: React.ReactNode;
  defaultOpen?: boolean;
}

export function HelpModal({ trigger, defaultOpen = false }: HelpModalProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const filteredFAQs = useMemo(() => {
    let filtered = faqData;

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(faq =>
        faq.question.toLowerCase().includes(term) ||
        faq.answer.toLowerCase().includes(term) ||
        faq.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    return filtered;
  }, [searchTerm, selectedCategory]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const CategoryCard = ({ category }: { category: FAQCategory }) => {
    const count = faqData.filter(faq => faq.category === category.id).length;
    const Icon = category.icon;
    
    return (
      <Card 
        className={`cursor-pointer transition-all hover:shadow-md ${
          selectedCategory === category.id ? 'ring-2 ring-primary' : ''
        }`}
        onClick={() => setSelectedCategory(selectedCategory === category.id ? null : category.id)}
      >
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-sm font-medium">
            <Icon className="h-4 w-4 mr-2" />
            {category.name}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <p className="text-xs text-muted-foreground mb-2">{category.description}</p>
          <Badge variant="secondary" className={category.color}>
            {count} {count === 1 ? 'article' : 'articles'}
          </Badge>
        </CardContent>
      </Card>
    );
  };

  const FAQItem = ({ faq }: { faq: FAQItem }) => {
    const isExpanded = expandedItems.includes(faq.id);
    
    return (
      <Collapsible 
        open={isExpanded} 
        onOpenChange={() => toggleExpanded(faq.id)}
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left hover:bg-muted/50 rounded-lg transition-colors">
          <span className="font-medium pr-4">{faq.question}</span>
          {isExpanded ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          )}
        </CollapsibleTrigger>
        <CollapsibleContent className="px-4 pb-4">
          <div className="text-sm text-muted-foreground leading-relaxed">
            {faq.answer}
          </div>
          <div className="flex flex-wrap gap-1 mt-3">
            {faq.tags.map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <HelpCircle className="h-4 w-4 mr-2" />
            Help & FAQ
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <HelpCircle className="h-5 w-5 mr-2" />
            Help Center & FAQ
          </DialogTitle>
          <DialogDescription>
            Find answers to common questions about using the FCP Carrier Portal
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search help articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Categories */}
          {!searchTerm && !selectedCategory && (
            <div>
              <h3 className="font-semibold mb-3">Browse by Category</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {faqCategories.map((category) => (
                  <CategoryCard key={category.id} category={category} />
                ))}
              </div>
            </div>
          )}

          {/* Results */}
          <div className="max-h-96 overflow-y-auto">
            {selectedCategory && (
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">
                  {faqCategories.find(c => c.id === selectedCategory)?.name}
                </h3>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setSelectedCategory(null)}
                >
                  View All Categories
                </Button>
              </div>
            )}

            {filteredFAQs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <HelpCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No help articles found matching your search.</p>
                <p className="text-sm">Try different keywords or browse categories above.</p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredFAQs.map((faq) => (
                  <FAQItem key={faq.id} faq={faq} />
                ))}
              </div>
            )}
          </div>

          {/* Contact Support */}
          <Card className="bg-muted/50">
            <CardContent className="pt-4">
              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h4 className="font-medium">Still need help?</h4>
                  <p className="text-sm text-muted-foreground">
                    Contact our support team: <strong><EMAIL></strong> or <strong>1-800-FCP-LOAD</strong>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
} 