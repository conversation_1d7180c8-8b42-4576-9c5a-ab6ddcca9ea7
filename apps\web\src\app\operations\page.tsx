'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Plus, Package, Truck, MapPin, Calendar, DollarSign, Clock, Loader2, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api-client';

// Types
interface Location {
  id: string;
  name: string;
  city: string;
  state: string;
  zipCode?: string;
  address?: string;
}

interface LaneInfo {
  id: string;
  originCity: string;
  originState: string;
  destinationCity: string;
  destinationState: string;
  distance: number;
  rate: number;
}

interface Template {
  id: string;
  name: string;
  pickupLocationId: string;
  deliveryLocationId: string;
  rate: number;
  daysToDelivery: number;
}

interface LocationsResponse {
  success: boolean;
  locations: Location[];
  cache?: {
    fromCache: boolean;
    lastUpdated: string;
    cacheAge: number; // minutes
    nextRefresh: string;
  };
}

interface LaneInfoResponse {
  success: boolean;
  lanes: LaneInfo[];
}

interface TemplatesResponse {
  success: boolean;
  templates: Template[];
}

interface OrderCreationResponse {
  success: boolean;
  createdCount: number;
  createdIds: string[];
  parseErrors: string[];
  creationErrors: string[];
  totalAttempted: number;
}

export default function OperationsPage() {
  const { user, getToken } = useAuth();
  
  // Check if user has operations access
  const hasOperationsAccess = user && (
    user.role === 'ADMIN' ||
    user.companyName === 'First Cut Produce' ||
    user.companyName === 'FIRST CUT PRODUCE' ||
    user.email?.includes('@firstcutproduce.com') ||
    user.email === '<EMAIL>'
  );

  if (!hasOperationsAccess) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">This page is only accessible to First Cut Produce team members and administrators.</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // State management
  const [locations, setLocations] = useState<Location[]>([]);
  const [laneInfo, setLaneInfo] = useState<LaneInfo[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [cacheInfo, setCacheInfo] = useState<LocationsResponse['cache'] | null>(null);
  
  // Form state
  const [orderMode, setOrderMode] = useState<'single' | 'batch'>('single');
  const [pickupLocationId, setPickupLocationId] = useState('');
  const [deliveryLocationId, setDeliveryLocationId] = useState('');
  const [pickupDate, setPickupDate] = useState('');
  const [daysToDelivery, setDaysToDelivery] = useState(1);
  const [rate, setRate] = useState('');
  const [soNumber, setSoNumber] = useState('');
  const [coyoteLoadNo, setCoyoteLoadNo] = useState('');
  const [poNumber, setPoNumber] = useState('');
  const [batchData, setBatchData] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [newTemplateName, setNewTemplateName] = useState('');

  // Batch import configuration
  const [batchIdentifierType, setBatchIdentifierType] = useState<'so-coyote' | 'po-so' | 'coyote-only' | 'custom'>('so-coyote');
  const [customIdentifierLabels, setCustomIdentifierLabels] = useState({ field1: 'Field 1', field2: 'Field 2' });

  // Load locations data
  const loadLocations = async (forceRefresh = false) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const url = forceRefresh ? '/operations/locations?refresh=true' : '/operations/locations';
      const locationsResponse = await apiClient.get<LocationsResponse>(url);

      if (locationsResponse.success) {
        setLocations(locationsResponse.locations);
        setCacheInfo(locationsResponse.cache || null);

        const cacheStatus = locationsResponse.cache?.fromCache ? 'from cache' : 'fresh from Airtable';
        console.log(`Loaded ${locationsResponse.locations.length} locations ${cacheStatus}`);

        if (forceRefresh) {
          toast.success(`Synced ${locationsResponse.locations.length} locations from Airtable to database cache`);
        }
      } else {
        console.error('Failed to load locations:', locationsResponse);
        toast.error('Failed to load locations from Airtable');
      }
    } catch (error) {
      console.error('Error loading locations:', error);
      toast.error('Failed to load locations');
    }
  };

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load locations
        await loadLocations();

        // Mock lane info until lane API is available
        const mockLaneInfo = [
          { id: '1', originCity: 'Los Angeles', originState: 'CA', destinationCity: 'Phoenix', destinationState: 'AZ', distance: 370, rate: 1200 },
          { id: '2', originCity: 'Phoenix', originState: 'AZ', destinationCity: 'Dallas', destinationState: 'TX', distance: 887, rate: 2100 },
          { id: '3', originCity: 'Dallas', originState: 'TX', destinationCity: 'Atlanta', destinationState: 'GA', distance: 781, rate: 1900 },
          { id: '4', originCity: 'Atlanta', originState: 'GA', destinationCity: 'Miami', destinationState: 'FL', distance: 662, rate: 1600 },
        ];

        setLaneInfo(mockLaneInfo);
        setTemplates([]);

      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load operations data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [getToken]);

  // Handle manual refresh
  const handleRefreshLocations = async () => {
    setIsRefreshing(true);
    try {
      await loadLocations(true);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Get suggested rate based on selected route
  const getSuggestedRate = () => {
    if (pickupLocationId && deliveryLocationId) {
      const matchingLane = laneInfo.find(lane => {
        const pickupLocation = locations.find(loc => loc.id === pickupLocationId);
        const deliveryLocation = locations.find(loc => loc.id === deliveryLocationId);

        return pickupLocation && deliveryLocation &&
               lane.originCity === pickupLocation.city &&
               lane.originState === pickupLocation.state &&
               lane.destinationCity === deliveryLocation.city &&
               lane.destinationState === deliveryLocation.state;
      });

      return matchingLane?.rate;
    }
    return null;
  };

  const getBatchPlaceholder = () => {
    switch (batchIdentifierType) {
      case 'so-coyote':
        return 'SO12345, CL67890\nSO12346, CL67891\nSO12347, CL67892';
      case 'po-so':
        return 'PO98765, SO12345\nPO98766, SO12346\nPO98767, SO12347';
      case 'coyote-only':
        return 'CL67890\nCL67891\nCL67892';
      case 'custom':
        return `${customIdentifierLabels.field1}1, ${customIdentifierLabels.field2}1\n${customIdentifierLabels.field1}2, ${customIdentifierLabels.field2}2`;
      default:
        return 'Enter data here...';
    }
  };

  const getBatchFormatDescription = () => {
    switch (batchIdentifierType) {
      case 'so-coyote':
        return 'Format: SO Number, Coyote Load Number (one per line, comma-separated)';
      case 'po-so':
        return 'Format: PO Number, SO Number (one per line, comma-separated)';
      case 'coyote-only':
        return 'Format: Coyote Load Number (one per line)';
      case 'custom':
        return `Format: ${customIdentifierLabels.field1}, ${customIdentifierLabels.field2} (one per line, comma-separated)`;
      default:
        return 'Select an identifier type above';
    }
  };

  const handleSaveTemplate = async () => {
    if (!newTemplateName || !pickupLocationId || !deliveryLocationId || !rate) {
      toast.error('Please fill in all fields before saving template');
      return;
    }

    try {
      const templateData = {
        name: newTemplateName,
        pickupLocationId,
        deliveryLocationId,
        rate,
        daysToDelivery: daysToDelivery.toString()
      };

      const response = await apiClient.post<TemplatesResponse>('/admin/order-templates', templateData);

      if (response.success) {
        toast.success('Template saved successfully');
        setNewTemplateName('');
        // Reload templates
        const templatesResponse = await apiClient.get<TemplatesResponse>('/admin/order-templates');
        if (templatesResponse.success) {
          setTemplates(templatesResponse.templates);
        }
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    }
  };

  const handleSubmit = async () => {
    if (!pickupLocationId || !deliveryLocationId || !pickupDate || !rate) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (orderMode === 'single' && !soNumber && !poNumber && !coyoteLoadNo) {
      toast.error('Please fill in at least one identifier (SO Number, PO Number, or Coyote Load Number)');
      return;
    }

    if (orderMode === 'batch' && !batchData.trim()) {
      toast.error('Please enter batch data for batch orders');
      return;
    }

    try {
      setIsSubmitting(true);

      const orderData = {
        orderMode,
        pickupLocationId,
        deliveryLocationId,
        pickupDate,
        daysToDelivery,
        rate,
        soNumber: orderMode === 'single' ? soNumber : undefined,
        poNumber: orderMode === 'single' ? poNumber : undefined,
        coyoteLoadNo: orderMode === 'single' ? coyoteLoadNo : undefined,
        batchData: orderMode === 'batch' ? batchData : undefined,
        batchIdentifierType: orderMode === 'batch' ? batchIdentifierType : undefined,
        customIdentifierLabels: orderMode === 'batch' && batchIdentifierType === 'custom' ? customIdentifierLabels : undefined
      };

      const response = await apiClient.post<OrderCreationResponse>('/admin/orders/create', orderData);

      if (response.success) {
        toast.success(`Successfully created ${response.createdCount} order(s)`);
        
        // Reset form
        setPickupLocationId('');
        setDeliveryLocationId('');
        setPickupDate('');
        setRate('');
        setSoNumber('');
        setPoNumber('');
        setCoyoteLoadNo('');
        setBatchData('');
        setDaysToDelivery(1);
      } else {
        toast.error('Failed to create orders');
      }
    } catch (error) {
      console.error('Error creating orders:', error);
      toast.error('Failed to create orders');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Operations</h1>
            <p className="text-muted-foreground">
              Manage freight operations and create orders
            </p>
            {cacheInfo && (
              <p className="text-xs text-muted-foreground mt-1">
                Locations: {cacheInfo.fromCache ? `Database cache (${cacheInfo.cacheAgeDisplay} ago)` : 'Fresh from Airtable'} •
                Last synced: {new Date(cacheInfo.lastUpdated).toLocaleString()} •
                Cache TTL: {cacheInfo.cacheTTL}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshLocations}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh Locations'}
            </Button>
            <Badge variant="secondary" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Operations Access
            </Badge>
          </div>
        </div>

        <Tabs defaultValue="order-creation" className="space-y-4">
          <TabsList>
            <TabsTrigger value="order-creation">Order Creation</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="order-creation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Create New Order
                </CardTitle>
                <CardDescription>
                  Create single orders or batch import multiple orders
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Order Mode Selection */}
                <div className="space-y-2">
                  <Label>Order Mode</Label>
                  <Select value={orderMode} onValueChange={(value: 'single' | 'batch') => setOrderMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single">Single Order</SelectItem>
                      <SelectItem value="batch">Batch Import</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Location Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pickup-location">Pickup Location *</Label>
                    <Select value={pickupLocationId} onValueChange={setPickupLocationId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select pickup location" />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name ? `${location.name} - ${location.city}, ${location.state}` : `${location.city}, ${location.state}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="delivery-location">Delivery Location *</Label>
                    <Select value={deliveryLocationId} onValueChange={setDeliveryLocationId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select delivery location" />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name ? `${location.name} - ${location.city}, ${location.state}` : `${location.city}, ${location.state}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Order Details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pickup-date">Pickup Date *</Label>
                    <Input
                      id="pickup-date"
                      type="date"
                      value={pickupDate}
                      onChange={(e) => setPickupDate(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="days-to-delivery">Days to Delivery</Label>
                    <Input
                      id="days-to-delivery"
                      type="number"
                      min="1"
                      value={daysToDelivery}
                      onChange={(e) => setDaysToDelivery(parseInt(e.target.value) || 1)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rate">Rate ($) *</Label>
                    <Input
                      id="rate"
                      type="number"
                      step="0.01"
                      value={rate}
                      onChange={(e) => setRate(e.target.value)}
                      placeholder={getSuggestedRate() ? `Suggested: $${getSuggestedRate()}` : 'Enter rate'}
                    />
                  </div>
                </div>

                {/* Single Order Fields */}
                {orderMode === 'single' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="so-number">SO Number</Label>
                      <Input
                        id="so-number"
                        value={soNumber}
                        onChange={(e) => setSoNumber(e.target.value)}
                        placeholder="Enter SO number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="po-number">PO Number</Label>
                      <Input
                        id="po-number"
                        value={poNumber}
                        onChange={(e) => setPoNumber(e.target.value)}
                        placeholder="Enter PO number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="coyote-load-no">Coyote Load Number</Label>
                      <Input
                        id="coyote-load-no"
                        value={coyoteLoadNo}
                        onChange={(e) => setCoyoteLoadNo(e.target.value)}
                        placeholder="Enter Coyote load number"
                      />
                    </div>
                  </div>
                )}

                {/* Batch Import Fields */}
                {orderMode === 'batch' && (
                  <div className="space-y-4">
                    {/* Batch Identifier Type Selection */}
                    <div className="space-y-2">
                      <Label>Identifier Type</Label>
                      <Select value={batchIdentifierType} onValueChange={(value: any) => setBatchIdentifierType(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="so-coyote">SO Number + Coyote Load Number</SelectItem>
                          <SelectItem value="po-so">PO Number + SO Number</SelectItem>
                          <SelectItem value="coyote-only">Coyote Load Number Only</SelectItem>
                          <SelectItem value="custom">Custom Fields</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Field Labels */}
                    {batchIdentifierType === 'custom' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="custom-field1">First Field Label</Label>
                          <Input
                            id="custom-field1"
                            value={customIdentifierLabels.field1}
                            onChange={(e) => setCustomIdentifierLabels(prev => ({ ...prev, field1: e.target.value }))}
                            placeholder="e.g., Reference Number"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="custom-field2">Second Field Label</Label>
                          <Input
                            id="custom-field2"
                            value={customIdentifierLabels.field2}
                            onChange={(e) => setCustomIdentifierLabels(prev => ({ ...prev, field2: e.target.value }))}
                            placeholder="e.g., Load ID"
                          />
                        </div>
                      </div>
                    )}

                    {/* Batch Data Input */}
                    <div className="space-y-2">
                      <Label htmlFor="batch-data">Batch Data *</Label>
                      <Textarea
                        id="batch-data"
                        value={batchData}
                        onChange={(e) => setBatchData(e.target.value)}
                        placeholder={getBatchPlaceholder()}
                        rows={8}
                      />
                      <p className="text-sm text-muted-foreground">
                        {getBatchFormatDescription()}
                      </p>
                    </div>
                  </div>
                )}

                {/* Lane Information Display */}
                {pickupLocationId && deliveryLocationId && (
                  <Card className="bg-muted/50">
                    <CardHeader>
                      <CardTitle className="text-sm">Route Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {(() => {
                        const pickupLocation = locations.find(loc => loc.id === pickupLocationId);
                        const deliveryLocation = locations.find(loc => loc.id === deliveryLocationId);
                        const matchingLane = laneInfo.find(lane =>
                          pickupLocation && deliveryLocation &&
                          lane.originCity === pickupLocation.city &&
                          lane.originState === pickupLocation.state &&
                          lane.destinationCity === deliveryLocation.city &&
                          lane.destinationState === deliveryLocation.state
                        );

                        if (matchingLane) {
                          return (
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Distance:</span>
                                <span className="text-sm font-medium">{matchingLane.distance} miles</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Suggested Rate:</span>
                                <span className="text-sm font-medium">${matchingLane.rate.toLocaleString()}</span>
                              </div>
                            </div>
                          );
                        } else {
                          return (
                            <p className="text-sm text-muted-foreground">
                              No lane information found for this route.
                            </p>
                          );
                        }
                      })()}
                    </CardContent>
                  </Card>
                )}

                {/* Template Saving */}
                <Card className="bg-blue-50 dark:bg-blue-950/20">
                  <CardHeader>
                    <CardTitle className="text-sm">Save as Template</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="template-name">Template Name</Label>
                      <Input
                        id="template-name"
                        value={newTemplateName}
                        onChange={(e) => setNewTemplateName(e.target.value)}
                        placeholder="Enter template name"
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={handleSaveTemplate}
                      disabled={!newTemplateName || !pickupLocationId || !deliveryLocationId || !rate}
                    >
                      Save Template
                    </Button>
                  </CardContent>
                </Card>

                {/* Submit Button */}
                <div className="flex justify-end">
                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className="min-w-[120px]"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Order{orderMode === 'batch' ? 's' : ''}
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader>
                <CardTitle>Order Templates</CardTitle>
                <CardDescription>Manage saved order templates for quick creation</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Template management coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Operations Analytics</CardTitle>
                <CardDescription>View operations performance and metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Analytics dashboard coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}
