"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AirtableOrdersModule = void 0;
const common_1 = require("@nestjs/common");
const airtable_orders_controller_1 = require("./airtable-orders.controller");
const airtable_orders_service_1 = require("./airtable-orders.service");
const prisma_module_1 = require("../prisma/prisma.module");
const auth_module_1 = require("../auth/auth.module");
const notifications_module_1 = require("../notifications/notifications.module");
const circuit_breaker_service_1 = require("../common/services/circuit-breaker.service");
let AirtableOrdersModule = class AirtableOrdersModule {
};
exports.AirtableOrdersModule = AirtableOrdersModule;
exports.AirtableOrdersModule = AirtableOrdersModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, auth_module_1.AuthModule, notifications_module_1.NotificationsModule],
        controllers: [airtable_orders_controller_1.AirtableOrdersController],
        providers: [airtable_orders_service_1.AirtableOrdersService, circuit_breaker_service_1.CircuitBreakerService],
        exports: [airtable_orders_service_1.AirtableOrdersService],
    })
], AirtableOrdersModule);
//# sourceMappingURL=airtable-orders.module.js.map