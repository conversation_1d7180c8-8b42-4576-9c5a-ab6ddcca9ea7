import { PartialType } from '@nestjs/swagger';
import { CreateCarrierProfileDto } from './create-carrier-profile.dto';
import { IsBoolean, IsOptional, IsString, ValidateIf } from 'class-validator';

// AdminUpdateCarrierProfileDto can include all fields from CreateCarrierProfileDto
// as optional, plus admin-specific fields.
// Alternatively, if you want admins to only update certain fields and not others,
// you can be more restrictive.
export class AdminUpdateCarrierProfileDto extends PartialType(CreateCarrierProfileDto) {
  @IsBoolean()
  @IsOptional()
  isVerifiedByAdmin?: boolean;

  @IsString()
  @IsOptional()
  // @ValidateIf((object, value) => value !== null) // Allows explicit null to clear the field
  adminNotes?: string | null;
} 