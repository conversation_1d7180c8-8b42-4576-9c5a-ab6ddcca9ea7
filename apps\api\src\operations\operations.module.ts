import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { OperationsController } from './operations.controller';
import { OperationsService } from './operations.service';
import { PatternAnalysisService } from './services/pattern-analysis.service';
import { SmartSuggestionsService } from './services/smart-suggestions.service';
import { RadarDistanceService } from './services/radar-distance.service';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    ConfigModule,
  ],
  controllers: [OperationsController],
  providers: [
    OperationsService,
    PatternAnalysisService,
    SmartSuggestionsService,
    RadarDistanceService
  ],
  exports: [OperationsService],
})
export class OperationsModule {} 