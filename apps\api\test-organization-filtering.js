#!/usr/bin/env node
/**
 * Test Script: Organization Filtering Validation
 * Purpose: Verify organization-based access controls for load visibility
 * 
 * Expected Results:
 * ✅ Public loads visible to all carriers
 * ✅ Targeted loads only visible to specified organizations
 * ✅ Private loads (no targeting, not public) invisible to everyone
 * ✅ Different organizations see different load sets
 */

const { PrismaClient } = require('@prisma/client');
const { ConfigService } = require('@nestjs/config');
const Airtable = require('airtable');

console.log('🧪 ORGANIZATION FILTERING TEST: Validating access controls');
console.log('======================================================');

async function testOrganizationFiltering() {
  try {
    // Mock config service
    const configService = {
      get: (key) => {
        const config = {
          'AIRTABLE_API_KEY': process.env.AIRTABLE_API_KEY,
          'AIRTABLE_BASE_ID': process.env.AIRTABLE_BASE_ID,
          'AIRTABLE_TABLE_NAME': process.env.AIRTABLE_TABLE_NAME || 'Loads'
        };
        return config[key];
      }
    };

    const apiKey = configService.get('AIRTABLE_API_KEY');
    const baseId = configService.get('AIRTABLE_BASE_ID');
    const tableName = configService.get('AIRTABLE_TABLE_NAME');

    if (!apiKey || !baseId) {
      throw new Error('Missing Airtable configuration. Please check AIRTABLE_API_KEY and AIRTABLE_BASE_ID');
    }

    console.log(`🔧 Configuration:`);
    console.log(`   Base ID: ${baseId}`);
    console.log(`   Table: ${tableName}`);
    console.log(`   API Key: ${apiKey.substring(0, 8)}...`);
    console.log('');

    // Initialize Airtable
    const base = new Airtable({ apiKey }).base(baseId);

    console.log('📋 STEP 1: Analyzing Load Access Control Configuration');
    console.log('====================================================');

    // Fetch all available loads with organization targeting info
    const records = await base(tableName)
      .select({
        view: 'Grid view',
        fields: ['Order ID.', 'Status', 'Synced to API', 'Is Public', 'Target Organizations', 'Rate to Carrier'],
        filterByFormula: `AND({Synced to API}, {Status} = "Available")`
      })
      .all();

    console.log(`✅ Found ${records.length} "Available" loads with "Synced to API" = true`);

    // Analyze access control patterns
    let publicLoads = 0;
    let targetedLoads = 0;
    let privateLoads = 0;
    const organizationTargets = new Set();
    const loadsByAccessType = {
      public: [],
      targeted: [],
      private: []
    };

    records.forEach(record => {
      const orderId = record.get('Order ID.');
      const isPublic = record.get('Is Public') !== false; // Default to true unless explicitly false
      const targetOrganizations = record.get('Target Organizations');
      const rate = record.get('Rate to Carrier');

      const loadInfo = {
        orderId,
        isPublic,
        targetOrganizations,
        rate: rate || 'N/A'
      };

      if (isPublic) {
        publicLoads++;
        loadsByAccessType.public.push(loadInfo);
      } else if (targetOrganizations) {
        targetedLoads++;
        loadsByAccessType.targeted.push(loadInfo);
        
        // Track unique organizations
        const orgs = Array.isArray(targetOrganizations) ? targetOrganizations : [targetOrganizations];
        orgs.forEach(org => organizationTargets.add(org));
      } else {
        privateLoads++;
        loadsByAccessType.private.push(loadInfo);
      }
    });

    console.log('\n📊 Access Control Distribution:');
    console.log(`   🌍 Public loads (visible to all): ${publicLoads}`);
    console.log(`   🎯 Targeted loads (visible to specific orgs): ${targetedLoads}`);
    console.log(`   🔒 Private loads (no access): ${privateLoads}`);
    console.log(`   🏢 Unique organizations being targeted: ${organizationTargets.size}`);

    console.log('\n🏢 Organizations with Targeted Loads:');
    Array.from(organizationTargets).forEach(org => {
      const loadsForOrg = loadsByAccessType.targeted.filter(load => {
        const orgs = Array.isArray(load.targetOrganizations) ? load.targetOrganizations : [load.targetOrganizations];
        return orgs.includes(org);
      });
      console.log(`   📈 ${org}: ${loadsForOrg.length} targeted loads`);
    });

    console.log('\n📋 STEP 2: Simulating Carrier Access Scenarios');
    console.log('===============================================');

    // Test different organization scenarios
    const testOrganizations = Array.from(organizationTargets).slice(0, 3);
    if (testOrganizations.length === 0) {
      testOrganizations.push('Test Organization'); // Fallback for testing
    }

    for (const testOrg of testOrganizations) {
      console.log(`\n🏢 Testing access for: "${testOrg}"`);
      
      // Simulate the filtering logic from the service
      const visibleLoads = [];
      
      records.forEach(record => {
        const orderId = record.get('Order ID.');
        const isPublic = record.get('Is Public') !== false;
        const targetOrganizations = record.get('Target Organizations');
        
        let canUserSeeLoad = false;
        
        if (isPublic) {
          canUserSeeLoad = true;
        } else if (targetOrganizations) {
          const orgArray = Array.isArray(targetOrganizations) ? targetOrganizations : [targetOrganizations];
          canUserSeeLoad = orgArray.some(org => org.toLowerCase().trim() === testOrg.toLowerCase().trim());
        }
        
        if (canUserSeeLoad) {
          visibleLoads.push({
            orderId,
            accessReason: isPublic ? 'Public' : 'Targeted'
          });
        }
      });

      console.log(`   👁️  Total loads visible: ${visibleLoads.length}`);
      console.log(`   🌍 Public loads: ${visibleLoads.filter(l => l.accessReason === 'Public').length}`);
      console.log(`   🎯 Targeted loads: ${visibleLoads.filter(l => l.accessReason === 'Targeted').length}`);
    }

    console.log('\n📋 STEP 3: Testing User Without Organization');
    console.log('===========================================');

    const noOrgVisibleLoads = records.filter(record => {
      const isPublic = record.get('Is Public') !== false;
      return isPublic; // Only public loads should be visible
    });

    console.log(`   👤 User with no organization can see: ${noOrgVisibleLoads.length} loads (all public)`);

    console.log('\n📋 STEP 4: Security Validation');
    console.log('==============================');

    // Check for potential security issues
    const securityIssues = [];

    // Issue 1: Private loads with no targeting (should be invisible to everyone)
    if (privateLoads > 0) {
      securityIssues.push(`⚠️  ${privateLoads} loads are private with no targeting (invisible to all carriers)`);
    }

    // Issue 2: Loads without proper access control flags
    const loadsWithoutProperFlags = records.filter(record => {
      const isPublic = record.get('Is Public');
      const targetOrganizations = record.get('Target Organizations');
      return isPublic === undefined && !targetOrganizations;
    });

    if (loadsWithoutProperFlags.length > 0) {
      securityIssues.push(`⚠️  ${loadsWithoutProperFlags.length} loads without proper access control flags`);
    }

    console.log('🔒 Security Analysis:');
    if (securityIssues.length === 0) {
      console.log('   ✅ No security issues detected');
    } else {
      securityIssues.forEach(issue => console.log(`   ${issue}`));
    }

    console.log('\n📋 STEP 5: Sample Load Access Examples');
    console.log('======================================');

    // Show examples of each access type
    if (loadsByAccessType.public.length > 0) {
      const publicExample = loadsByAccessType.public[0];
      console.log(`📝 PUBLIC LOAD EXAMPLE:`);
      console.log(`   Order: ${publicExample.orderId}`);
      console.log(`   Rate: ${publicExample.rate}`);
      console.log(`   Access: Public (visible to ALL carriers)`);
      console.log('');
    }

    if (loadsByAccessType.targeted.length > 0) {
      const targetedExample = loadsByAccessType.targeted[0];
      console.log(`📝 TARGETED LOAD EXAMPLE:`);
      console.log(`   Order: ${targetedExample.orderId}`);
      console.log(`   Rate: ${targetedExample.rate}`);
      console.log(`   Targets: ${Array.isArray(targetedExample.targetOrganizations) ? targetedExample.targetOrganizations.join(', ') : targetedExample.targetOrganizations}`);
      console.log(`   Access: Restricted (visible only to targeted organizations)`);
      console.log('');
    }

    if (loadsByAccessType.private.length > 0) {
      const privateExample = loadsByAccessType.private[0];
      console.log(`📝 PRIVATE LOAD EXAMPLE:`);
      console.log(`   Order: ${privateExample.orderId}`);
      console.log(`   Rate: ${privateExample.rate}`);
      console.log(`   Access: Private (visible to NO carriers)`);
      console.log('');
    }

    console.log('🎉 ORGANIZATION FILTERING TEST COMPLETED');
    console.log('=======================================');
    console.log(`✅ SUCCESS: Access controls are properly configured`);
    console.log(`🌍 Public loads: ${publicLoads} (visible to all)`);
    console.log(`🎯 Targeted loads: ${targetedLoads} (org-specific)`);
    console.log(`🔒 Private loads: ${privateLoads} (hidden from all)`);
    console.log(`🏢 Organizations targeted: ${organizationTargets.size}`);

    return {
      success: true,
      publicLoads,
      targetedLoads,
      privateLoads,
      organizationCount: organizationTargets.size,
      securityIssues: securityIssues.length
    };

  } catch (error) {
    console.error('❌ ORGANIZATION FILTERING TEST FAILED:', error.message);
    console.error('Stack trace:', error.stack);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
testOrganizationFiltering()
  .then(result => {
    if (result.success) {
      console.log('\n✅ All organization filtering tests passed!');
      process.exit(0);
    } else {
      console.log('\n❌ Organization filtering tests failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }); 