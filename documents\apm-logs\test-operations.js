// Test script for Operations API endpoints
// Run with: node test-operations.js

const API_BASE_URL = 'http://localhost:3001/api/v1';

// You'll need to replace this with a valid JWT token from a First Cut Produce user
const TEST_TOKEN = 'your-jwt-token-here';

async function testOperationsEndpoints() {
  console.log('🧪 Testing Operations API Endpoints\n');

  const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  };

  try {
    // Test 1: Get Lanes
    console.log('1️⃣ Testing GET /operations/lanes');
    const lanesResponse = await fetch(`${API_BASE_URL}/operations/lanes`, {
      headers
    });
    
    if (lanesResponse.ok) {
      const lanesData = await lanesResponse.json();
      console.log('✅ Lanes endpoint successful');
      console.log(`   Found ${lanesData.total} lanes`);
      console.log(`   Sample lane:`, lanesData.lanes[0] || 'No lanes found');
    } else {
      console.log('❌ Lanes endpoint failed:', lanesResponse.status, lanesResponse.statusText);
      const errorText = await lanesResponse.text();
      console.log('   Error:', errorText);
    }

    console.log('\n');

    // Test 2: Create Order (only if we have lanes)
    console.log('2️⃣ Testing POST /operations/orders');
    
    const testOrder = {
      laneId: 'lane_1_CA_TX',
      originCity: 'Los Angeles',
      originState: 'CA',
      destinationCity: 'Houston',
      destinationState: 'TX',
      estimatedMiles: 1400,
      poNumber: `TEST-PO-${Date.now()}`,
      soNumber: 'TEST-SO-001',
      pickupDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      deliveryDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
      equipmentRequired: 'Dry Van',
      weightLbs: 45000,
      rate: 2500,
      notes: 'Test order created via operations interface',
      priority: 'normal'
    };

    const orderResponse = await fetch(`${API_BASE_URL}/operations/orders`, {
      method: 'POST',
      headers,
      body: JSON.stringify(testOrder)
    });

    if (orderResponse.ok) {
      const orderData = await orderResponse.json();
      console.log('✅ Order creation successful');
      console.log(`   Order ID: ${orderData.id}`);
      console.log(`   Airtable Record ID: ${orderData.airtableRecordId}`);
      console.log(`   Message: ${orderData.message}`);
    } else {
      console.log('❌ Order creation failed:', orderResponse.status, orderResponse.statusText);
      const errorText = await orderResponse.text();
      console.log('   Error:', errorText);
    }

  } catch (error) {
    console.error('🚨 Test failed with error:', error.message);
  }
}

// Instructions for running the test
if (TEST_TOKEN === 'your-jwt-token-here') {
  console.log('📋 To run this test:');
  console.log('1. Start the API server: cd apps/api && npm run start:dev');
  console.log('2. Get a JWT token from a First Cut Produce user by:');
  console.log('   - Logging into the web app');
  console.log('   - Opening browser dev tools');
  console.log('   - Going to Application > Local Storage');
  console.log('   - Finding the Clerk session token');
  console.log('3. Replace TEST_TOKEN in this file with the actual token');
  console.log('4. Run: node test-operations.js');
  console.log('\n⚠️  Make sure the user belongs to "First Cut Produce" organization or is an admin');
} else {
  testOperationsEndpoints();
} 