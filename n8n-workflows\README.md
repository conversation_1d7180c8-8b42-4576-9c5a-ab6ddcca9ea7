# N8N Bidding Workflows - Import Instructions (JWT Secured)

This directory contains 3 ready-to-import N8N workflow JSON files for the complete carrier bidding system with **JWT authentication and HMAC security**.

## Workflow Files

1. **01-bidding-submission-workflow.json** - Handles JWT-authenticated bid submissions from carriers
2. **02-admin-notification-workflow.json** - Sends secure email alerts to admins with HMAC-signed action tokens
3. **03-admin-response-workflow.json** - Processes admin responses with token validation and JWT generation

## Import Instructions

### Step 1: Import Workflows into N8N

1. Open your N8N instance (https://firstcutproduce.app.n8n.cloud)
2. Go to **Workflows** in the left sidebar
3. Click **Import from File** button
4. Upload each JSON file one by one:
   - Start with `01-bidding-submission-workflow.json`
   - Then `02-admin-notification-workflow.json`
   - Finally `03-admin-response-workflow.json`

### Step 2: Configure Credentials

You need to set up these credentials in N8N:

#### Airtable API Credential
1. Go to N8N **Credentials** → **Add Credential**
2. Select **Airtable API**
3. Enter your Airtable API Key
4. Set the credential ID as: `AIRTABLE_CREDENTIAL_ID`

#### SMTP Email Credential
1. Go to N8N **Credentials** → **Add Credential**
2. Select **SMTP**
3. Enter your email provider settings:
   - **Host**: smtp.gmail.com (for Gmail) or your provider
   - **Port**: 587 (for TLS) or 465 (for SSL)
   - **User**: <EMAIL>
   - **Password**: your app password
4. Set the credential ID as: `EMAIL_CREDENTIAL_ID`

### Step 3: Configure JWT Secret

**CRITICAL**: Set up the JWT secret environment variable in N8N:

1. Go to N8N **Settings** → **Environment Variables**
2. Add: `N8N_JWT_SECRET` = `your-super-secure-secret-key-here`
3. Use the SAME secret that your frontend authentication system uses
4. This secret is used for JWT validation and HMAC signing

### Step 4: Update Configuration Values

In each workflow, update these values:

#### All Workflows:
- Replace `YOUR_AIRTABLE_BASE_ID` with your actual Airtable base ID
- Replace `AIRTABLE_CREDENTIAL_ID` with your actual credential ID
- Replace `EMAIL_CREDENTIAL_ID` with your actual credential ID

#### Admin Notification Workflow:
- Replace `<EMAIL>` with your actual admin email
- Replace `<EMAIL>` with your actual from email
- Update the webhook URL base: `https://firstcutproduce.app.n8n.cloud` (if different)

#### Admin Response Workflow:
- Replace `<EMAIL>` with your actual from email
- Update webhook URLs if your N8N instance has a different domain

### Step 5: Activate Workflows

1. Open each workflow
2. Click the **Active/Inactive** toggle to activate
3. Test the webhook endpoints with proper JWT authentication

## Webhook URLs

After importing and activating, your webhook URLs will be:

1. **Bid Submission**: `https://firstcutproduce.app.n8n.cloud/webhook/bidding/submit`
2. **Admin Alert**: `https://firstcutproduce.app.n8n.cloud/webhook/admin/bid-alert`
3. **Admin Response**: `https://firstcutproduce.app.n8n.cloud/webhook/admin/respond`

## Airtable Table Setup

Create a "Bids" table in Airtable with these fields:

- **BidId** (Single line text)
- **LoadId** (Single line text) 
- **CarrierUserId** (Single line text)
- **BidAmount** (Number)
- **CarrierNotes** (Long text)
- **CarrierCompanyName** (Single line text)
- **CarrierMcNumber** (Single line text)
- **Status** (Single select: pending, accepted, declined)
- **SubmittedAt** (Date)
- **ExpiresAt** (Date)
- **AdminResponse** (Single select: accepted, declined, counter)
- **RespondedAt** (Date)
- **CarrierEmail** (Email) *optional - for notifications*
- **AdminJWT** (Long text) *stores admin JWT tokens for audit trail*

## Frontend Integration

Update your frontend environment variables:

```bash
# Add to your .env.local file
NEXT_PUBLIC_N8N_WEBHOOK_BID_SUBMIT=https://firstcutproduce.app.n8n.cloud/webhook/bidding/submit
```

## Testing

1. Test bid submission by sending a POST request to the bid submission webhook
2. Check that admin email is received
3. Test admin response by clicking email buttons
4. Verify Airtable records are created and updated

## Troubleshooting

- **Webhook not triggering**: Check if workflows are active
- **Email not sending**: Verify SMTP credentials and from/to addresses
- **Airtable errors**: Confirm base ID and field names match exactly
- **Token errors**: Ensure webhook URLs in emails match your N8N domain

## Security Features

✅ **JWT Authentication**: All bid submissions require valid JWT tokens
✅ **HMAC Signatures**: Admin action tokens use HMAC-SHA256 for tamper protection
✅ **Token Expiration**: Response tokens expire after 24 hours
✅ **User Identity Verification**: Carrier info extracted from authenticated JWT
✅ **Action Validation**: Tokens validated for specific actions (accept/decline/counter)
✅ **Audit Trail**: All admin actions logged with JWT tokens in Airtable

## Security Notes

- **JWT Secret**: Must match your frontend authentication system
- **Token Tampering**: HMAC signatures prevent email link manipulation
- **Carrier Authentication**: Only authenticated users can submit bids
- **Admin Security**: Response tokens include carrier MC numbers for verification
- **Audit Logging**: All bid actions tracked with admin JWT tokens

---

**Need Help?** Check the full setup guide in `N8N_Bidding_Setup_Instructions.md` 