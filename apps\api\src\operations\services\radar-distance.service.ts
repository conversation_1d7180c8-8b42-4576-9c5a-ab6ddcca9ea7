import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';

export interface DistanceResult {
  distanceMiles: number;
  durationHours: number;
  success: boolean;
  source: 'radar' | 'fallback' | 'cache';
}

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface AddressDistanceRequest {
  originAddress: string;
  destinationAddress: string;
}

/**
 * Rate limiter for controlling API call frequency
 */
class RateLimiter {
  private lastRequestTime = 0;
  private requestCount = 0;
  private resetTime = 0;

  constructor(
    private readonly maxRequestsPerSecond: number,
    private readonly windowSizeMs: number = 1000
  ) {}

  async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    
    // Reset counter if window has passed
    if (now >= this.resetTime) {
      this.requestCount = 0;
      this.resetTime = now + this.windowSizeMs;
    }

    // Check if we're at the rate limit
    if (this.requestCount >= this.maxRequestsPerSecond) {
      const waitTime = this.resetTime - now;
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
        // Reset after waiting
        this.requestCount = 0;
        this.resetTime = Date.now() + this.windowSizeMs;
      }
    }

    this.requestCount++;
    this.lastRequestTime = Date.now();
  }
}

@Injectable()
export class RadarDistanceService {
  private readonly logger = new Logger(RadarDistanceService.name);
  private readonly radarApiKey: string;
  private readonly BASE_URL = 'https://api.radar.io/v1';

  // Rate limiters for different endpoints
  private readonly geocodingRateLimiter = new RateLimiter(50); // Conservative: 50 req/sec vs 1000 limit
  private readonly routingRateLimiter = new RateLimiter(10);   // Conservative: 10 req/sec vs 100 limit

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService
  ) {
    this.radarApiKey = this.configService.get<string>('RADAR_SECRET_KEY') || '';
    
    if (!this.radarApiKey) {
      this.logger.warn('RADAR_SECRET_KEY not found in environment variables - using fallback distance calculations only');
      this.logger.warn('To enable accurate distance calculations, please set RADAR_SECRET_KEY environment variable');
    } else {
      this.logger.log('RadarDistanceService initialized with Radar.com API, caching, and rate limiting');
      this.logger.log('Rate limits: Geocoding 50/sec, Routing 10/sec (conservative)');
      this.logger.log('Cache: Distance calculations stored in database to avoid repeated API calls');
    }
  }

  /**
   * Calculate accurate road distance between two full addresses using Radar.com API with caching and rate limiting
   */
  async calculateDistanceByAddress(
    originAddress: string,
    destinationAddress: string
  ): Promise<DistanceResult> {
    this.logger.log(`Calculating distance: ${originAddress} → ${destinationAddress}`);

    // Normalize addresses for consistent caching
    const normalizedOrigin = this.normalizeAddress(originAddress);
    const normalizedDestination = this.normalizeAddress(destinationAddress);

    // Check cache first
    const cachedResult = await this.getCachedDistance(normalizedOrigin, normalizedDestination);
    if (cachedResult) {
      this.logger.debug(`Cache hit: ${normalizedOrigin} → ${normalizedDestination} = ${cachedResult.distanceMiles} miles`);
      return {
        distanceMiles: cachedResult.distanceMiles,
        durationHours: cachedResult.durationHours,
        success: cachedResult.calculatedBy === 'radar',
        source: 'cache'
      };
    }

    // If no API key, use fallback immediately
    if (!this.radarApiKey) {
      this.logger.debug('No Radar API key available, using fallback distance calculation');
      const fallbackResult = this.calculateFallbackDistanceFromAddresses(normalizedOrigin, normalizedDestination);
      
      // Cache the fallback result
      await this.cacheDistance(normalizedOrigin, normalizedDestination, fallbackResult.distanceMiles, fallbackResult.durationHours, 'fallback');
      
      return {
        distanceMiles: fallbackResult.distanceMiles,
        durationHours: fallbackResult.durationHours,
        success: false,
        source: 'fallback'
      };
    }

    try {
      // Apply rate limiting for geocoding calls
      await this.geocodingRateLimiter.waitForRateLimit();
      await this.geocodingRateLimiter.waitForRateLimit(); // Two geocoding calls needed

      // Step 1: Geocode both addresses with rate limiting
      const [originCoords, destCoords] = await Promise.all([
        this.geocodeAddress(normalizedOrigin),
        this.geocodeAddress(normalizedDestination)
      ]);

      // Apply rate limiting for routing call
      await this.routingRateLimiter.waitForRateLimit();

      // Step 2: Calculate road distance using Radar routing
      const routeResult = await this.calculateRoute(originCoords, destCoords);

      this.logger.log(`Radar distance calculation successful: ${routeResult.distanceMiles} miles, ${routeResult.durationHours} hours`);
      
      // Cache the successful result
      await this.cacheDistance(normalizedOrigin, normalizedDestination, routeResult.distanceMiles, routeResult.durationHours, 'radar');
      
      return {
        distanceMiles: routeResult.distanceMiles,
        durationHours: routeResult.durationHours,
        success: true,
        source: 'radar'
      };

    } catch (error) {
      this.logger.error(`Radar distance calculation failed: ${error.message}`, error.stack);
      
      // Fallback to enhanced distance calculation
      const fallbackResult = this.calculateFallbackDistanceFromAddresses(normalizedOrigin, normalizedDestination);
      
      this.logger.warn(`Using fallback distance calculation: ${fallbackResult.distanceMiles} miles`);
      
      // Cache the fallback result
      await this.cacheDistance(normalizedOrigin, normalizedDestination, fallbackResult.distanceMiles, fallbackResult.durationHours, 'fallback');
      
      return {
        distanceMiles: fallbackResult.distanceMiles,
        durationHours: fallbackResult.durationHours,
        success: false,
        source: 'fallback'
      };
    }
  }

  /**
   * Batch calculate distances with controlled rate limiting for lane generation using full addresses
   */
  async calculateDistancesBatchByAddress(
    requests: AddressDistanceRequest[]
  ): Promise<Array<DistanceResult & { route: string }>> {
    this.logger.log(`Starting batch distance calculation for ${requests.length} routes using full addresses`);
    
    const results: Array<DistanceResult & { route: string }> = [];
    let radarSuccess = 0;
    let fallbackUsed = 0;
    let cacheHits = 0;

    for (let i = 0; i < requests.length; i++) {
      const request = requests[i];
      const route = `${request.originAddress} → ${request.destinationAddress}`;
      
      try {
        this.logger.debug(`Processing ${i + 1}/${requests.length}: ${route}`);
        
        const result = await this.calculateDistanceByAddress(
          request.originAddress,
          request.destinationAddress
        );

        results.push({
          ...result,
          route
        });

        if (result.source === 'cache') {
          cacheHits++;
          if (result.success) radarSuccess++; else fallbackUsed++;
        } else if (result.success && result.source === 'radar') {
          radarSuccess++;
        } else {
          fallbackUsed++;
        }

        // Log progress every 10 routes
        if ((i + 1) % 10 === 0) {
          this.logger.log(`Progress: ${i + 1}/${requests.length} routes processed. Cache: ${cacheHits}, Radar: ${radarSuccess}, Fallback: ${fallbackUsed}`);
        }

      } catch (error) {
        this.logger.error(`Failed to calculate distance for ${route}: ${error.message}`);
        
        // Add fallback result for failed request
        const fallbackResult = this.calculateFallbackDistanceFromAddresses(request.originAddress, request.destinationAddress);
        results.push({
          ...fallbackResult,
          route,
          success: false,
          source: 'fallback'
        });
        fallbackUsed++;
      }
    }

    this.logger.log(`Batch calculation complete! Total: ${results.length}, Cache hits: ${cacheHits}, Radar API: ${radarSuccess}, Fallback: ${fallbackUsed}`);
    this.logger.log(`Cache efficiency: ${((cacheHits / results.length) * 100).toFixed(1)}%`);
    this.logger.log(`Radar success rate: ${(((radarSuccess + cacheHits) / results.length) * 100).toFixed(1)}%`);

    return results;
  }

  /**
   * Get cached distance from database
   */
  private async getCachedDistance(originAddress: string, destinationAddress: string) {
    try {
      const cached = await this.prisma.distanceCache.findUnique({
        where: {
          originAddress_destinationAddress: {
            originAddress,
            destinationAddress
          }
        }
      });

      return cached;
    } catch (error) {
      this.logger.error(`Error retrieving cached distance: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache distance calculation result in the database using upsert to handle unique constraints
   */
  private async cacheDistance(
    originAddress: string, 
    destinationAddress: string, 
    distanceMiles: number, 
    durationHours: number, 
    calculatedBy: string
  ) {
    try {
      await this.prisma.distanceCache.upsert({
        where: {
          originAddress_destinationAddress: {
            originAddress: this.normalizeAddress(originAddress),
            destinationAddress: this.normalizeAddress(destinationAddress)
          }
        },
        update: {
          distanceMiles,
          durationHours,
          calculatedBy,
          updatedAt: new Date()
        },
        create: {
          originAddress: this.normalizeAddress(originAddress),
          destinationAddress: this.normalizeAddress(destinationAddress),
          distanceMiles,
          durationHours,
          calculatedBy
        }
      });
      
      this.logger.debug(`Cached distance: ${originAddress} → ${destinationAddress} = ${distanceMiles} miles`);
    } catch (error) {
      this.logger.warn(`Failed to cache distance: ${error.message}`);
      // Don't throw - caching failure shouldn't break the distance calculation
    }
  }

  /**
   * Normalize address for consistent caching
   */
  private normalizeAddress(address: string): string {
    return address
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[,.]/g, '')
      .replace(/\b(street|st|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|court|ct|place|pl)\b/gi, '')
      .trim();
  }

  /**
   * Enhanced fallback distance calculation using address parsing
   */
  private calculateFallbackDistanceFromAddresses(originAddress: string, destinationAddress: string): { distanceMiles: number; durationHours: number } {
    // Extract state information from addresses for basic fallback
    const originState = this.extractStateFromAddress(originAddress);
    const destinationState = this.extractStateFromAddress(destinationAddress);
    
    if (originState && destinationState) {
      return this.calculateFallbackDistance(originState, destinationState);
    }
    
    // If we can't extract states, use a conservative estimate based on address similarity
    const similarity = this.calculateAddressSimilarity(originAddress, destinationAddress);
    
    if (similarity > 0.8) {
      // Very similar addresses - probably same city/area
      return { distanceMiles: 25, durationHours: 0.5 };
    } else if (similarity > 0.6) {
      // Somewhat similar - probably same region
      return { distanceMiles: 150, durationHours: 2.5 };
    } else {
      // Very different - assume cross-country
      return { distanceMiles: 1200, durationHours: 20 };
    }
  }

  /**
   * Extract state from address string
   */
  private extractStateFromAddress(address: string): string | null {
    const stateRegex = /\b([A-Z]{2})\b/g;
    const matches = address.match(stateRegex);
    return matches ? matches[matches.length - 1] : null; // Get the last state match
  }

  /**
   * Calculate similarity between two addresses
   */
  private calculateAddressSimilarity(addr1: string, addr2: string): number {
    const normalize = (str: string) => str.toLowerCase().replace(/[^a-z0-9]/g, '');
    const norm1 = normalize(addr1);
    const norm2 = normalize(addr2);
    
    if (norm1 === norm2) return 1;
    
    const longer = norm1.length > norm2.length ? norm1 : norm2;
    const shorter = norm1.length > norm2.length ? norm2 : norm1;
    
    if (longer.length === 0) return 1;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Geocode a full address using Radar.com API
   */
  private async geocodeAddress(address: string): Promise<Coordinates> {
    const response = await fetch(`${this.BASE_URL}/geocode/forward?query=${encodeURIComponent(address)}`, {
      headers: {
        'Authorization': this.radarApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Radar geocoding failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.addresses || data.addresses.length === 0) {
      throw new Error(`No geocoding results found for address: ${address}`);
    }

    const coords = data.addresses[0];
    this.logger.debug(`Geocoded ${address} → ${coords.latitude}, ${coords.longitude}`);
    
    return {
      lat: coords.latitude,
      lng: coords.longitude
    };
  }

  /**
   * Calculate route between two coordinates using Radar.com API
   */
  private async calculateRoute(origin: Coordinates, destination: Coordinates): Promise<{ distanceMiles: number; durationHours: number }> {
    // Radar API expects GET request with query parameters, not POST with JSON body
    const params = new URLSearchParams({
      origin: `${origin.lat},${origin.lng}`,
      destination: `${destination.lat},${destination.lng}`,
      modes: 'car',
      units: 'imperial'
    });

    const response = await fetch(`${this.BASE_URL}/route/distance?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': this.radarApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Radar routing failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.routes || !data.routes.car) {
      throw new Error('No car route found between coordinates');
    }

    const route = data.routes.car;
    const distanceMiles = route.distance ? route.distance.value / 5280 : 0; // Convert feet to miles (imperial units)
    const durationHours = route.duration ? route.duration.value / 60 : 0; // Convert minutes to hours

    return {
      distanceMiles: Math.round(distanceMiles * 10) / 10, // Round to 1 decimal place
      durationHours: Math.round(durationHours * 10) / 10  // Round to 1 decimal place
    };
  }

  // Keep the existing fallback distance calculation for state-to-state as backup
  private calculateFallbackDistance(originState: string, destinationState: string): { distanceMiles: number; durationHours: number } {
    // Enhanced state-to-state distance estimation
    const stateDistances: { [key: string]: number } = {
      'CA-AZ': 500, 'AZ-CA': 500,
      'CA-NV': 250, 'NV-CA': 250,
      'CA-TX': 1200, 'TX-CA': 1200,
      'FL-GA': 300, 'GA-FL': 300,
      'FL-AL': 350, 'AL-FL': 350,
      'NY-MA': 200, 'MA-NY': 200,
      'NY-CT': 100, 'CT-NY': 100,
      'IL-TX': 900, 'TX-IL': 900,
      'IL-IN': 150, 'IN-IL': 150,
      'CO-UT': 350, 'UT-CO': 350,
      'WA-OR': 300, 'OR-WA': 300,
    };

    const key = `${originState}-${destinationState}`;
    const distance = stateDistances[key] || 800; // Default fallback distance
    const duration = distance / 60; // Assume 60 mph average

    return {
      distanceMiles: distance,
      durationHours: Math.round(duration * 10) / 10
    };
  }

  // Legacy methods for backward compatibility with city/state calls
  async calculateDistance(
    originCity: string,
    originState: string,
    destinationCity: string,
    destinationState: string
  ): Promise<DistanceResult> {
    const originAddress = `${originCity}, ${originState}`;
    const destinationAddress = `${destinationCity}, ${destinationState}`;
    
    return this.calculateDistanceByAddress(originAddress, destinationAddress);
  }

  async calculateDistancesBatch(
    requests: Array<{
      originCity: string;
      originState: string;
      destinationCity: string;
      destinationState: string;
    }>
  ): Promise<Array<DistanceResult & { route: string }>> {
    const addressRequests = requests.map(req => ({
      originAddress: `${req.originCity}, ${req.originState}`,
      destinationAddress: `${req.destinationCity}, ${req.destinationState}`
    }));

    return this.calculateDistancesBatchByAddress(addressRequests);
  }
} 