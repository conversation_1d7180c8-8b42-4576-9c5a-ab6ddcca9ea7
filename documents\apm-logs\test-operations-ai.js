// Test script to debug AI operations endpoints
const https = require('https');

// Test configuration
const BASE_URL = 'https://www.fcp-portal.com';
const TEST_ENDPOINTS = [
  {
    name: 'Smart Suggestions',
    method: 'GET',
    path: '/api/v1/operations/suggestions?originCity=Los%20Angeles&originState=CA&destinationCity=Phoenix&destinationState=AZ&currentValues=%7B%7D'
  },
  {
    name: 'Order Validation',
    method: 'POST',
    path: '/api/v1/operations/validate',
    body: {
      orderData: {
        rate: 2500,
        equipmentRequired: 'Dry Van',
        weightLbs: 35000
      },
      context: {
        originCity: 'Los Angeles',
        originState: 'CA',
        destinationCity: 'Phoenix',
        destinationState: 'AZ'
      }
    }
  },
  {
    name: 'Auto-complete Rate',
    method: 'GET',
    path: '/api/v1/operations/autocomplete/rate?value=25&context=%7B%22equipmentRequired%22%3A%22Dry%20Van%22%7D'
  },
  {
    name: 'Feedback Recording',
    method: 'POST', 
    path: '/api/v1/operations/feedback/test-order-123',
    body: {
      suggestions: [],
      actualValues: { rate: 2500 },
      orderContext: { laneId: 'test' }
    }
  }
];

console.log('🔍 Testing AI Operations Endpoints...\n');

async function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.path, BASE_URL);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Endpoint-Test/1.0'
      }
    };

    let requestBody = '';
    if (endpoint.body) {
      requestBody = JSON.stringify(endpoint.body);
      options.headers['Content-Length'] = Buffer.byteLength(requestBody);
    }

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        let responseData;
        try {
          responseData = JSON.parse(data);
        } catch (e) {
          responseData = data;
        }
        
        resolve({
          name: endpoint.name,
          status: res.statusCode,
          headers: res.headers,
          body: responseData,
          success: res.statusCode >= 200 && res.statusCode < 300
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: endpoint.name,
        status: 'ERROR',
        error: error.message,
        success: false
      });
    });

    if (requestBody) {
      req.write(requestBody);
    }
    
    req.end();
  });
}

async function runTests() {
  for (const endpoint of TEST_ENDPOINTS) {
    console.log(`Testing: ${endpoint.name}`);
    console.log(`${endpoint.method} ${endpoint.path}`);
    
    const result = await testEndpoint(endpoint);
    
    if (result.success) {
      console.log(`✅ SUCCESS - Status: ${result.status}`);
      console.log(`Response:`, JSON.stringify(result.body, null, 2));
    } else {
      console.log(`❌ FAILED - Status: ${result.status}`);
      if (result.error) {
        console.log(`Error: ${result.error}`);
      } else {
        console.log(`Response:`, JSON.stringify(result.body, null, 2));
      }
    }
    
    console.log('-'.repeat(60));
  }
}

runTests().catch(console.error); 