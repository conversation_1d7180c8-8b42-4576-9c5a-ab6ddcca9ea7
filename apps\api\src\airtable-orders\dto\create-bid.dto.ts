import { IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateBidDto {
  @ApiProperty({
    description: 'The bid amount in dollars',
    example: 1500.00,
    minimum: 0.01,
  })
  @IsNumber({}, { message: 'Bid amount must be a valid number' })
  @Min(0.01, { message: 'Bid amount must be greater than 0' })
  bidAmount: number;

  @ApiPropertyOptional({
    description: 'Optional notes about the bid',
    example: 'Driver empty 5 miles away',
  })
  @IsOptional()
  @IsString()
  carrierNotes?: string;
} 