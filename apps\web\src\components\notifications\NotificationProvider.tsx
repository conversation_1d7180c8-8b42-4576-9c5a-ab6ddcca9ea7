'use client';

import React, { createContext, useContext, useState } from 'react';
import { NotificationToast } from './NotificationToast';
// Real-time notifications now handled by N8N via email/SMS
// Define NotificationData interface locally
interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  data?: any;
}

interface NotificationContextType {
  showToast: (notification: NotificationData) => void;
  hideToast: (id: string) => void;
  notifications: NotificationData[];
  unreadCount: number;
  connected: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};

interface ToastNotification extends NotificationData {
  showAsToast?: boolean;
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [toastNotifications, setToastNotifications] = useState<ToastNotification[]>([]);
  const [readNotifications, setReadNotifications] = useState<Set<string>>(new Set());

  // Real-time notifications now handled by N8N via email/SMS
  // Mock state for UI consistency
  const notifications: NotificationData[] = [];
  const unreadCount = 0;
  const connected = false;
  const markAsRead = (id: string) => {};
  const removeNotification = (id: string) => {};

  const showToast = React.useCallback((notification: NotificationData) => {
    const toastNotification: ToastNotification = {
      ...notification,
      showAsToast: true,
    };

    setToastNotifications(prev => [toastNotification, ...prev.slice(0, 4)]); // Keep max 5 toasts

    // Auto-hide toast after 8 seconds
    setTimeout(() => {
      setToastNotifications(prevToasts => prevToasts.filter(n => n.id !== notification.id));
    }, 8000);
  }, []);

  // Handle new notifications - show as toast for important ones
  React.useEffect(() => {
    const latestNotification = notifications[0];
    if (!latestNotification) return;

    // Only show toast for certain notification types
    const shouldShowToast = [
      'load_new',
      'load_assigned',
      'bid_update',
      'booking_confirmation',
      'system_announcement',
    ].includes(latestNotification.type);

    if (shouldShowToast && !toastNotifications.find(n => n.id === latestNotification.id)) {
      showToast(latestNotification);
    }
  }, [notifications, toastNotifications, showToast]);

  const hideToast = (id: string) => {
    setToastNotifications(prev => prev.filter(n => n.id !== id));
  };

  const handleMarkAsRead = (id: string) => {
    setReadNotifications(prev => new Set([...prev, id]));
    markAsRead(id);
  };

  const handleRemoveNotification = (id: string) => {
    hideToast(id);
    removeNotification(id);
  };

  const contextValue: NotificationContextType = {
    showToast,
    hideToast,
    notifications,
    unreadCount,
    connected,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Toast Notifications Container */}
      <div className="fixed top-4 right-4 z-[9999] space-y-2 pointer-events-none">
        {toastNotifications.map((notification) => (
          <div key={notification.id} className="pointer-events-auto">
            <NotificationToast
              notification={notification}
              onClose={() => handleRemoveNotification(notification.id)}
              onMarkAsRead={() => handleMarkAsRead(notification.id)}
              isRead={readNotifications.has(notification.id)}
            />
          </div>
        ))}
      </div>
    </NotificationContext.Provider>
  );
}; 