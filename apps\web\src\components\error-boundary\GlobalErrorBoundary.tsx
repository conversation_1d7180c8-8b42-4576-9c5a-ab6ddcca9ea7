'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ert<PERSON>ircle, RefreshCw, Home, Bug, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface GlobalErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
  errorType: 'network' | 'auth' | 'api' | 'render' | 'unknown';
}

interface GlobalErrorBoundaryProps {
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  maxRetries?: number;
  enableReporting?: boolean;
}

export class GlobalErrorBoundary extends Component<GlobalErrorBoundaryProps, GlobalErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: GlobalErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      errorType: 'unknown'
    };
  }

  static getDerivedStateFromError(error: Error): Partial<GlobalErrorBoundaryState> {
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const errorType = GlobalErrorBoundary.classifyError(error);
    
    return {
      hasError: true,
      error,
      errorId,
      errorType
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Generate error report
    const errorReport = this.generateErrorReport(error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError && this.state.errorId) {
      this.props.onError(error, errorInfo, this.state.errorId);
    }

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Global Error Boundary');
      console.error('Error ID:', this.state.errorId);
      console.error('Error Type:', this.state.errorType);
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Error Report:', errorReport);
      console.groupEnd();
    }

    // Send error report to monitoring service
    if (this.props.enableReporting !== false) {
      this.reportError(errorReport);
    }
  }

  componentDidUpdate(prevProps: GlobalErrorBoundaryProps) {
    // Reset error state if children change
    if (this.state.hasError && prevProps.children !== this.props.children) {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: 0,
        errorType: 'unknown'
      });
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private static classifyError(error: Error): 'network' | 'auth' | 'api' | 'render' | 'unknown' {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('network') || message.includes('fetch') || 
        message.includes('connection') || message.includes('offline')) {
      return 'network';
    }

    if (message.includes('auth') || message.includes('unauthorized') || 
        message.includes('403') || message.includes('401')) {
      return 'auth';
    }

    if (message.includes('api') || message.includes('500') || 
        message.includes('502') || message.includes('503') || message.includes('504')) {
      return 'api';
    }

    if (stack.includes('react') || message.includes('render') || 
        message.includes('component') || message.includes('hydration')) {
      return 'render';
    }

    return 'unknown';
  }

  private generateErrorReport(error: Error, errorInfo: ErrorInfo) {
    return {
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getUserId(),
      errorType: this.state.errorType,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      errorInfo: {
        componentStack: errorInfo.componentStack
      },
      retryCount: this.state.retryCount,
      environment: process.env.NODE_ENV,
      buildInfo: {
        timestamp: process.env.NEXT_PUBLIC_BUILD_TIME,
        version: process.env.NEXT_PUBLIC_APP_VERSION
      }
    };
  }

  private getUserId(): string | null {
    // Try to get user ID from various sources
    try {
      // From localStorage
      const user = localStorage.getItem('clerk-user');
      if (user) {
        const parsed = JSON.parse(user);
        return parsed.id || null;
      }
    } catch {
      // Ignore localStorage errors
    }
    
    return null;
  }

  private reportError(errorReport: any) {
    try {
      // Send error to monitoring service (implement based on your monitoring solution)
      if (typeof window !== 'undefined') {
        // Example: Send to an error reporting endpoint
        fetch('/api/errors', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(errorReport)
        }).catch(() => {
          // Silently fail if error reporting fails
        });
      }
    } catch {
      // Silently fail if error reporting fails
    }
  }

  private handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;
    
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: prevState.retryCount + 1,
        errorType: 'unknown'
      }));
    } else {
      // Force full page reload after max retries
      window.location.reload();
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private getErrorIcon() {
    switch (this.state.errorType) {
      case 'network':
        return <WifiOff className="h-12 w-12 text-orange-600 dark:text-orange-400" />;
      case 'auth':
        return <AlertCircle className="h-12 w-12 text-red-600 dark:text-red-400" />;
      case 'api':
        return <AlertCircle className="h-12 w-12 text-red-600 dark:text-red-400" />;
      case 'render':
        return <Bug className="h-12 w-12 text-purple-600 dark:text-purple-400" />;
      default:
        return <AlertCircle className="h-12 w-12 text-gray-600 dark:text-gray-400" />;
    }
  }

  private getErrorContent() {
    const maxRetries = this.props.maxRetries || 3;
    const canRetry = this.state.retryCount < maxRetries;

    switch (this.state.errorType) {
      case 'network':
        return {
          title: 'Connection Problem',
          description: 'Unable to connect to our servers. Please check your internet connection and try again.',
          primaryAction: canRetry ? this.handleRetry : this.handleReload,
          primaryLabel: canRetry ? 'Try Again' : 'Reload Page',
          showHomeButton: true
        };

      case 'auth':
        return {
          title: 'Authentication Error',
          description: 'Your session may have expired. Please sign in again to continue.',
          primaryAction: () => window.location.href = '/sign-in',
          primaryLabel: 'Sign In',
          showHomeButton: true
        };

      case 'api':
        return {
          title: 'Service Error',
          description: 'Our servers are experiencing difficulties. Please try again in a few moments.',
          primaryAction: canRetry ? this.handleRetry : this.handleReload,
          primaryLabel: canRetry ? 'Try Again' : 'Reload Page',
          showHomeButton: true
        };

      case 'render':
        return {
          title: 'Display Error',
          description: 'There was a problem displaying this page. This might be a temporary issue.',
          primaryAction: canRetry ? this.handleRetry : this.handleReload,
          primaryLabel: canRetry ? 'Try Again' : 'Reload Page',
          showHomeButton: true
        };

      default:
        return {
          title: 'Something went wrong',
          description: 'An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.',
          primaryAction: canRetry ? this.handleRetry : this.handleReload,
          primaryLabel: canRetry ? 'Try Again' : 'Reload Page',
          showHomeButton: true
        };
    }
  }

  render() {
    if (this.state.hasError) {
      const errorContent = this.getErrorContent();
      const maxRetries = this.props.maxRetries || 3;

      return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {this.getErrorIcon()}
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {errorContent.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {errorContent.description}
                </p>
                
                {this.state.errorId && (
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <Badge variant="outline" className="font-mono text-xs">
                      Error ID: {this.state.errorId}
                    </Badge>
                    <Badge variant="outline">
                      Type: {this.state.errorType}
                    </Badge>
                  </div>
                )}

                {this.state.retryCount > 0 && (
                  <Alert className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Retry Attempt</AlertTitle>
                    <AlertDescription>
                      Attempt {this.state.retryCount} of {maxRetries}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button onClick={errorContent.primaryAction} className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  {errorContent.primaryLabel}
                </Button>

                {errorContent.showHomeButton && (
                  <Button variant="outline" onClick={this.handleGoHome} className="flex items-center gap-2">
                    <Home className="h-4 w-4" />
                    Go Home
                  </Button>
                )}
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6">
                  <summary className="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                    Error Details (Development)
                  </summary>
                  <pre className="mt-3 text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-48 text-gray-800 dark:text-gray-200">
                    {this.state.error.stack}
                    {'\n\n=== Component Stack ===\n'}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
} 