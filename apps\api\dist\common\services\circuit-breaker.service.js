"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CircuitBreakerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreakerService = exports.CircuitState = void 0;
const common_1 = require("@nestjs/common");
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
})(CircuitState || (exports.CircuitState = CircuitState = {}));
let CircuitBreakerService = CircuitBreakerService_1 = class CircuitBreakerService {
    logger = new common_1.Logger(CircuitBreakerService_1.name);
    circuits = new Map();
    defaultConfig = {
        failureThreshold: 5,
        recoveryTimeout: 60000,
        monitoringPeriod: 300000,
        halfOpenMaxCalls: 3
    };
    async execute(circuitName, operation, fallback, config = {}) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const circuit = this.getOrCreateCircuit(circuitName);
        this.updateCircuitState(circuit, finalConfig);
        this.logCircuitState(circuitName, circuit);
        switch (circuit.state) {
            case CircuitState.OPEN:
                if (fallback) {
                    this.logger.warn(`Circuit breaker OPEN for ${circuitName}, executing fallback`);
                    return await fallback();
                }
                else {
                    throw new Error(`Service ${circuitName} is temporarily unavailable`);
                }
            case CircuitState.HALF_OPEN:
                if (circuit.halfOpenCalls >= finalConfig.halfOpenMaxCalls) {
                    circuit.state = CircuitState.OPEN;
                    circuit.lastFailureTime = Date.now();
                    if (fallback) {
                        this.logger.warn(`Circuit breaker reopened for ${circuitName}, executing fallback`);
                        return await fallback();
                    }
                    else {
                        throw new Error(`Service ${circuitName} is temporarily unavailable`);
                    }
                }
                circuit.halfOpenCalls++;
                break;
            case CircuitState.CLOSED:
                break;
        }
        try {
            const result = await this.executeWithTimeout(operation, 30000);
            circuit.successes++;
            circuit.lastSuccessTime = Date.now();
            if (circuit.state === CircuitState.HALF_OPEN) {
                circuit.state = CircuitState.CLOSED;
                circuit.failures = 0;
                circuit.halfOpenCalls = 0;
                this.logger.log(`Circuit breaker CLOSED for ${circuitName} after successful recovery`);
            }
            return result;
        }
        catch (error) {
            circuit.failures++;
            circuit.lastFailureTime = Date.now();
            if (circuit.state === CircuitState.HALF_OPEN) {
                circuit.state = CircuitState.OPEN;
                circuit.halfOpenCalls = 0;
                this.logger.warn(`Circuit breaker reopened for ${circuitName} after failure in half-open state`);
            }
            else if (circuit.failures >= finalConfig.failureThreshold) {
                circuit.state = CircuitState.OPEN;
                this.logger.error(`Circuit breaker OPENED for ${circuitName} after ${circuit.failures} failures`);
            }
            if (circuit.state === CircuitState.OPEN && fallback) {
                this.logger.warn(`Circuit breaker OPEN for ${circuitName}, executing fallback after failure`);
                return await fallback();
            }
            throw error;
        }
    }
    executeWithTimeout(operation, timeoutMs) {
        return Promise.race([
            operation(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Operation timeout')), timeoutMs))
        ]);
    }
    getOrCreateCircuit(circuitName) {
        if (!this.circuits.has(circuitName)) {
            this.circuits.set(circuitName, {
                failures: 0,
                successes: 0,
                lastFailureTime: 0,
                lastSuccessTime: 0,
                state: CircuitState.CLOSED,
                halfOpenCalls: 0
            });
        }
        return this.circuits.get(circuitName);
    }
    updateCircuitState(circuit, config) {
        const now = Date.now();
        if (now - Math.max(circuit.lastFailureTime, circuit.lastSuccessTime) > config.monitoringPeriod) {
            circuit.failures = 0;
            circuit.successes = 0;
        }
        if (circuit.state === CircuitState.OPEN &&
            now - circuit.lastFailureTime > config.recoveryTimeout) {
            circuit.state = CircuitState.HALF_OPEN;
            circuit.halfOpenCalls = 0;
        }
    }
    logCircuitState(circuitName, circuit) {
        const now = Date.now();
        const timeSinceLastLog = now - circuit.lastLogTime || 0;
        if (timeSinceLastLog > 300000 || !circuit.lastLoggedState ||
            circuit.lastLoggedState !== circuit.state) {
            this.logger.log(`Circuit Breaker Status [${circuitName}]`, {
                circuitName,
                state: circuit.state,
                failures: circuit.failures,
                successes: circuit.successes,
                halfOpenCalls: circuit.halfOpenCalls,
                lastFailureTime: circuit.lastFailureTime ? new Date(circuit.lastFailureTime).toISOString() : 'never',
                lastSuccessTime: circuit.lastSuccessTime ? new Date(circuit.lastSuccessTime).toISOString() : 'never'
            });
            circuit.lastLogTime = now;
            circuit.lastLoggedState = circuit.state;
        }
    }
    getCircuitStatus() {
        const status = {};
        for (const [name, circuit] of this.circuits.entries()) {
            status[name] = {
                state: circuit.state,
                failures: circuit.failures,
                successes: circuit.successes,
                healthyStatus: circuit.state === CircuitState.CLOSED ? 'healthy' : 'degraded',
                lastFailureTime: circuit.lastFailureTime ? new Date(circuit.lastFailureTime).toISOString() : null,
                lastSuccessTime: circuit.lastSuccessTime ? new Date(circuit.lastSuccessTime).toISOString() : null
            };
        }
        return status;
    }
    resetCircuit(circuitName) {
        const circuit = this.circuits.get(circuitName);
        if (circuit) {
            circuit.failures = 0;
            circuit.successes = 0;
            circuit.state = CircuitState.CLOSED;
            circuit.halfOpenCalls = 0;
            this.logger.log(`Circuit breaker ${circuitName} has been manually reset`);
        }
    }
    getHealthCheck() {
        const circuits = this.getCircuitStatus();
        const hasOpenCircuits = Object.values(circuits).some((c) => c.state === CircuitState.OPEN);
        return {
            status: hasOpenCircuits ? 'degraded' : 'healthy',
            circuits
        };
    }
};
exports.CircuitBreakerService = CircuitBreakerService;
exports.CircuitBreakerService = CircuitBreakerService = CircuitBreakerService_1 = __decorate([
    (0, common_1.Injectable)()
], CircuitBreakerService);
//# sourceMappingURL=circuit-breaker.service.js.map