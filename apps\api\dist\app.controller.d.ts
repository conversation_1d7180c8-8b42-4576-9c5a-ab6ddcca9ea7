import { AppService } from './app.service';
import { AuthenticatedRequest } from './auth/authenticated-request.interface';
import { PrismaService } from './prisma/prisma.service';
import { CircuitBreakerService } from './common/services/circuit-breaker.service';
export declare class AppController {
    private readonly appService;
    private readonly prismaService;
    private readonly circuitBreakerService;
    private readonly logger;
    constructor(appService: AppService, prismaService: PrismaService, circuitBreakerService: CircuitBreakerService);
    getSimpleTest(): {
        status: string;
        timestamp: string;
        message: string;
    };
    getHello(): string;
    getHealth(): Promise<{
        status: string;
        timestamp: string;
        uptime: number;
        responseTime: string;
        version: string;
        environment: string;
        message: string;
        error?: undefined;
    } | {
        status: string;
        timestamp: string;
        uptime: number;
        responseTime: string;
        error: any;
        version?: undefined;
        environment?: undefined;
        message?: undefined;
    }>;
    getDetailedHealth(): Promise<{
        status: "healthy" | "degraded" | "unhealthy";
        timestamp: string;
        uptime: number;
        responseTime: string;
        version: string;
        environment: string;
        services: {
            database: {
                status: "healthy" | "degraded" | "unhealthy";
                database: {
                    connected: boolean;
                    responseTime?: number;
                    error?: string;
                };
                timestamp: string;
            };
            circuitBreakers: {
                status: string;
                circuits: Record<string, any>;
            };
        };
        error?: undefined;
    } | {
        status: string;
        timestamp: string;
        uptime: number;
        responseTime: string;
        error: any;
        version?: undefined;
        environment?: undefined;
        services?: undefined;
    }>;
    getReadiness(): Promise<{
        status: string;
        timestamp: string;
        reason?: undefined;
    } | {
        status: string;
        reason: any;
        timestamp?: undefined;
    }>;
    getLiveness(): {
        status: string;
        timestamp: string;
        uptime: number;
    };
    getDatabaseHealth(): Promise<{
        connected: boolean;
        healthy: boolean;
        error: string | undefined;
        timestamp: string;
    }>;
    getDebug(): {
        status: string;
        timestamp: string;
        environment: string | undefined;
        environmentVariables: Record<string, string>;
        allEnvKeys: string[];
    };
    getProfile(req: AuthenticatedRequest): {
        airtableUserId: string;
        email: string;
        mcNumber?: string;
        companyName?: string;
        role: "CARRIER" | "ADMIN";
        isAdmin: boolean;
    } | undefined;
    getDebugUser(req: AuthenticatedRequest): Promise<{
        error: string;
        n8nPayload?: undefined;
        userContext?: undefined;
        databaseUser?: undefined;
        authSync?: undefined;
        stack?: undefined;
    } | {
        n8nPayload: import("./auth/authenticated-request.interface").N8NJwtPayload | undefined;
        userContext: {
            airtableUserId: string;
            email: string;
            mcNumber?: string;
            companyName?: string;
            role: "CARRIER" | "ADMIN";
            isAdmin: boolean;
        } | undefined;
        databaseUser: {
            id: string;
            airtableUserId: string | null;
            email: string | null;
            firstName: string | null;
            lastName: string | null;
            role: import("@repo/db").$Enums.Role;
            mcNumber: string | null;
            createdAt: Date;
            updatedAt: Date;
        } | {
            airtableUserId: any;
            email: any;
            firstName: any;
            lastName: any;
            companyName: any;
            mcNumber: any;
            role: any;
        } | null;
        authSync: {
            hasUserId: boolean;
            hasEmail: boolean;
            hasMcNumber: boolean;
            hasRole: boolean;
        };
        error?: undefined;
        stack?: undefined;
    } | {
        error: any;
        n8nPayload: import("./auth/authenticated-request.interface").N8NJwtPayload | undefined;
        userContext: {
            airtableUserId: string;
            email: string;
            mcNumber?: string;
            companyName?: string;
            role: "CARRIER" | "ADMIN";
            isAdmin: boolean;
        } | undefined;
        stack: any;
        databaseUser?: undefined;
        authSync?: undefined;
    }>;
    private determineOverallStatus;
}
