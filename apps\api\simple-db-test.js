// Simple database connection test
async function testConnection() {
  console.log('🔧 Testing Database Connection...\n');
  
  try {
    // Check if DATABASE_URL environment variable exists
    console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
    console.log('DATABASE_URL preview:', process.env.DATABASE_URL ? 
      process.env.DATABASE_URL.substring(0, 20) + '...' : 'NOT SET');
    
    // Try to import Prisma Client
    console.log('\n📦 Importing Prisma Client...');
    const { PrismaClient } = require('@repo/db');
    console.log('✅ Prisma Client imported successfully');
    
    // Create Prisma instance
    console.log('\n🔌 Creating Prisma instance...');
    const prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
    console.log('✅ Prisma instance created');
    
    // Test connection with a simple query
    console.log('\n🏥 Testing database connection...');
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful:', result);
    
    // Try to count users (simple table)
    console.log('\n👥 Testing user table access...');
    const userCount = await prisma.user.count();
    console.log(`✅ User table accessible, count: ${userCount}`);
    
    // Try to count loads
    console.log('\n📦 Testing loads table access...');
    const loadCount = await prisma.load.count();
    console.log(`✅ Loads table accessible, count: ${loadCount}`);
    
    if (loadCount > 0) {
      console.log('\n📋 Sample load data:');
      const sampleLoads = await prisma.load.findMany({
        select: {
          id: true,
          airtableRecordId: true,
          status: true,
          isPublic: true,
          createdAt: true,
        },
        take: 3,
        orderBy: { createdAt: 'desc' }
      });
      
      sampleLoads.forEach(load => {
        console.log(`- ${load.airtableRecordId}: ${load.status} (Public: ${load.isPublic}) - ${load.createdAt}`);
      });
    }
    
    await prisma.$disconnect();
    console.log('\n🎉 All tests passed!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testConnection(); 