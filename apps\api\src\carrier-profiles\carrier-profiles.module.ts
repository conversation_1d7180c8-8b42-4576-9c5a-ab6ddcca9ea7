import { Module, forwardRef } from '@nestjs/common';
import { CarrierProfilesService } from './carrier-profiles.service';
import { CarrierProfilesController } from './carrier-profiles.controller';
import { PrismaModule } from '../prisma/prisma.module'; // Adjust path if prisma.module is elsewhere
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [PrismaModule, forwardRef(() => AuthModule)], // Importing PrismaModule and AuthModule
  controllers: [CarrierProfilesController],
  providers: [CarrierProfilesService],
  exports: [CarrierProfilesService], // Export if other modules might need it
})
export class CarrierProfilesModule {} 