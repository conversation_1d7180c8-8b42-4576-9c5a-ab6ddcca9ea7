export interface Coordinates {
    lat: number;
    lng: number;
}
export declare class GeographicUtils {
    static parseCoordinates(coordString: string): Coordinates | null;
    static calculateDistance(coord1: Coordinates, coord2: Coordinates): number;
    private static toRadians;
    static geocodeLocation(location: string): Promise<Coordinates | null>;
    static isWithinRadius(center: Coordinates, point: Coordinates, radiusMiles: number): boolean;
    static parseLocation(location: string): {
        city: string;
        state: string;
    } | null;
    static getCityCoordinates(city: string, state: string): Coordinates | null;
}
