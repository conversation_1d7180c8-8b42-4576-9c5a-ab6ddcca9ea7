{"version": 3, "file": "circuit-breaker.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/circuit-breaker.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AASpD,IAAY,YAIX;AAJD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,uCAAuB,CAAA;AACzB,CAAC,EAJW,YAAY,4BAAZ,YAAY,QAIvB;AAYM,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IACf,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IACzD,QAAQ,GAAqC,IAAI,GAAG,EAAE,CAAC;IAE9C,aAAa,GAAyB;QACrD,gBAAgB,EAAE,CAAC;QACnB,eAAe,EAAE,KAAK;QACtB,gBAAgB,EAAE,MAAM;QACxB,gBAAgB,EAAE,CAAC;KACpB,CAAC;IAEF,KAAK,CAAC,OAAO,CACX,WAAmB,EACnB,SAA2B,EAC3B,QAA2B,EAC3B,SAAwC,EAAE;QAE1C,MAAM,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAGrD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAG9C,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAE3C,QAAQ,OAAO,CAAC,KAAK,EAAE,CAAC;YACtB,KAAK,YAAY,CAAC,IAAI;gBACpB,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,WAAW,sBAAsB,CAAC,CAAC;oBAChF,OAAO,MAAM,QAAQ,EAAE,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,WAAW,WAAW,6BAA6B,CAAC,CAAC;gBACvE,CAAC;YAEH,KAAK,YAAY,CAAC,SAAS;gBACzB,IAAI,OAAO,CAAC,aAAa,IAAI,WAAW,CAAC,gBAAgB,EAAE,CAAC;oBAE1D,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;oBAClC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAErC,IAAI,QAAQ,EAAE,CAAC;wBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,sBAAsB,CAAC,CAAC;wBACpF,OAAO,MAAM,QAAQ,EAAE,CAAC;oBAC1B,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,WAAW,WAAW,6BAA6B,CAAC,CAAC;oBACvE,CAAC;gBACH,CAAC;gBACD,OAAO,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM;YAER,KAAK,YAAY,CAAC,MAAM;gBAEtB,MAAM;QACV,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAG/D,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAErC,IAAI,OAAO,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;gBAE7C,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;gBACpC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACrB,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,WAAW,4BAA4B,CAAC,CAAC;YACzF,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAErC,IAAI,OAAO,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;gBAE7C,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;gBAClC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,mCAAmC,CAAC,CAAC;YACnG,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAC,gBAAgB,EAAE,CAAC;gBAE5D,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,WAAW,UAAU,OAAO,CAAC,QAAQ,WAAW,CAAC,CAAC;YACpG,CAAC;YAGD,IAAI,OAAO,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,WAAW,oCAAoC,CAAC,CAAC;gBAC9F,OAAO,MAAM,QAAQ,EAAE,CAAC;YAC1B,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAI,SAA2B,EAAE,SAAiB;QAC1E,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,SAAS,EAAE;YACX,IAAI,OAAO,CAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC3B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,SAAS,CAAC,CACpE;SACF,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,WAAmB;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;gBAC7B,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;gBACZ,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,CAAC;gBAClB,KAAK,EAAE,YAAY,CAAC,MAAM;gBAC1B,aAAa,EAAE,CAAC;aACjB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;IACzC,CAAC;IAEO,kBAAkB,CAAC,OAA4B,EAAE,MAA4B;QACnF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAGvB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC/F,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YACrB,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;QACxB,CAAC;QAGD,IAAI,OAAO,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI;YACnC,GAAG,GAAG,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3D,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;YACvC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,WAAmB,EAAE,OAA4B;QAEvE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,GAAG,GAAI,OAAe,CAAC,WAAW,IAAI,CAAC,CAAC;QAGjE,IAAI,gBAAgB,GAAG,MAAM,IAAI,CAAE,OAAe,CAAC,eAAe;YAC7D,OAAe,CAAC,eAAe,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,WAAW,GAAG,EAAE;gBACzD,WAAW;gBACX,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,OAAO;gBACpG,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,OAAO;aACrG,CAAC,CAAC;YAEF,OAAe,CAAC,WAAW,GAAG,GAAG,CAAC;YAClC,OAAe,CAAC,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC;QACnD,CAAC;IACH,CAAC;IAGD,gBAAgB;QACd,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,GAAG;gBACb,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,aAAa,EAAE,OAAO,CAAC,KAAK,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;gBAC7E,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI;gBACjG,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI;aAClG,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAGD,YAAY,CAAC,WAAmB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YACrB,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;YACtB,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;YACpC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,WAAW,0BAA0B,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAGD,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC;QAEhG,OAAO;YACL,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;YAChD,QAAQ;SACT,CAAC;IACJ,CAAC;CACF,CAAA;AA3MY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;GACA,qBAAqB,CA2MjC"}