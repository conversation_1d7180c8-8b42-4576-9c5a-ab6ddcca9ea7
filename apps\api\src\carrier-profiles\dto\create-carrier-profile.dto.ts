import {
  IsString,
  IsOptional,
  <PERSON><PERSON><PERSON>y,
  // IsBoolean, // Not needed for carrier-settable fields
  Max<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  // IsPhoneNumber, // Consider a more specific phone number validation library if needed
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'; // For Swagger docs
import { Transform } from 'class-transformer';

export class CreateCarrierProfileDto {
  @ApiPropertyOptional({ example: 'Big Rigz Inc.', description: 'Company name' })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  companyName?: string;

  @ApiPropertyOptional({ example: '1234567', description: 'DOT number' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  dotNumber?: string;

  @ApiPropertyOptional({ example: 'MC987654', description: 'MC number' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  mcNumber?: string;

  @ApiPropertyOptional({ example: '+14155552671', description: 'Contact phone number' })
  @IsOptional()
  // @IsPhoneNumber(null) // Basic validation, region can be specified e.g. @IsPhoneNumber('US')
  @IsString() // Keeping it simple for now, can enhance with specific phone validation
  @MaxLength(20)
  phoneNumber?: string;

  @ApiPropertyOptional({ example: 'John Smith', description: 'Contact person name' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  contact_name?: string;

  @ApiPropertyOptional({ example: '<EMAIL>', description: 'Contact email' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  contact_email?: string;

  @ApiPropertyOptional({ example: '+14155552671', description: 'Contact phone number' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  contact_phone?: string;

  @ApiPropertyOptional({
    example: ['Dry Van 53ft', 'Reefer'],
    description: 'List of equipment types',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    // Handle both JSON string and array inputs
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (e) {
        return [value]; // If not valid JSON, treat as single item array
      }
    }
    return value;
  })
  equipmentTypes?: string[];

  @ApiPropertyOptional({
    example: ['Northeast', 'CA', 'Midwest'],
    description: 'List of serviceable regions or states',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    // Handle both JSON string and array inputs
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (e) {
        return [value]; // If not valid JSON, treat as single item array
      }
    }
    return value;
  })
  serviceableRegions?: string[];

  // userId will be taken from the authenticated user (req.user.sub or similar)
  // and not from the DTO directly for security reasons.

  // isVerifiedByAdmin should not be settable by the carrier directly on creation
  // adminNotes should not be settable by the carrier
} 