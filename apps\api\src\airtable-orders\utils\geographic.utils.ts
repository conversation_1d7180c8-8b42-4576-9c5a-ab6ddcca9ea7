export interface Coordinates {
  lat: number;
  lng: number;
}

export class GeographicUtils {
  /**
   * Parse a coordinate string in format "lat,lng" into Coordinates object
   */
  static parseCoordinates(coordString: string): Coordinates | null {
    if (!coordString || typeof coordString !== 'string') {
      return null;
    }

    const parts = coordString.split(',').map(part => part.trim());
    if (parts.length !== 2) {
      return null;
    }

    const lat = parseFloat(parts[0]);
    const lng = parseFloat(parts[1]);

    if (isNaN(lat) || isNaN(lng)) {
      return null;
    }

    // Validate coordinate ranges
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return null;
    }

    return { lat, lng };
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   * Returns distance in miles
   */
  static calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
    const R = 3959; // Earth's radius in miles
    
    const lat1Rad = this.toRadians(coord1.lat);
    const lat2Rad = this.toRadians(coord2.lat);
    const deltaLatRad = this.toRadians(coord2.lat - coord1.lat);
    const deltaLngRad = this.toRadians(coord2.lng - coord1.lng);

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Geocode a city/state string to coordinates
   * This is a simple approximation - in production you'd use a real geocoding service
   */
  static async geocodeLocation(location: string): Promise<Coordinates | null> {
    // This is a simplified implementation
    // In production, you would integrate with Google Geocoding API, MapBox, or similar
    
    // For now, we'll return null and let the frontend handle geocoding
    // or integrate with a free service like OpenStreetMap Nominatim
    
    try {
      // Example using a free geocoding service (commented out for now)
      // const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(location)}&countrycodes=us&limit=1`);
      // const data = await response.json();
      // 
      // if (data && data.length > 0) {
      //   return {
      //     lat: parseFloat(data[0].lat),
      //     lng: parseFloat(data[0].lon)
      //   };
      // }
      
      return null;
    } catch (error) {
      console.error('Geocoding error:', error);
      return null;
    }
  }

  /**
   * Check if a coordinate is within radius of center point
   */
  static isWithinRadius(center: Coordinates, point: Coordinates, radiusMiles: number): boolean {
    const distance = this.calculateDistance(center, point);
    return distance <= radiusMiles;
  }

  /**
   * Parse a location string like "City, State" or "City, ST" to get standardized components
   */
  static parseLocation(location: string): { city: string; state: string } | null {
    if (!location || typeof location !== 'string') {
      return null;
    }

    const parts = location.split(',').map(part => part.trim());
    if (parts.length < 2) {
      return null;
    }

    return {
      city: parts[0],
      state: parts[1]
    };
  }

  /**
   * Get approximate coordinates for major US cities (fallback for common locations)
   * This is a simple lookup table for major cities
   */
  static getCityCoordinates(city: string, state: string): Coordinates | null {
    const cityKey = `${city.toLowerCase()}, ${state.toLowerCase()}`;
    
    // Simple lookup table for major US cities
    const cityCoords: Record<string, Coordinates> = {
      'new york, ny': { lat: 40.7128, lng: -74.0060 },
      'los angeles, ca': { lat: 34.0522, lng: -118.2437 },
      'chicago, il': { lat: 41.8781, lng: -87.6298 },
      'houston, tx': { lat: 29.7604, lng: -95.3698 },
      'phoenix, az': { lat: 33.4484, lng: -112.0740 },
      'philadelphia, pa': { lat: 39.9526, lng: -75.1652 },
      'san antonio, tx': { lat: 29.4241, lng: -98.4936 },
      'san diego, ca': { lat: 32.7157, lng: -117.1611 },
      'dallas, tx': { lat: 32.7767, lng: -96.7970 },
      'san jose, ca': { lat: 37.3382, lng: -121.8863 },
      'austin, tx': { lat: 30.2672, lng: -97.7431 },
      'jacksonville, fl': { lat: 30.3322, lng: -81.6557 },
      'fort worth, tx': { lat: 32.7555, lng: -97.3308 },
      'columbus, oh': { lat: 39.9612, lng: -82.9988 },
      'charlotte, nc': { lat: 35.2271, lng: -80.8431 },
      'san francisco, ca': { lat: 37.7749, lng: -122.4194 },
      'indianapolis, in': { lat: 39.7684, lng: -86.1581 },
      'seattle, wa': { lat: 47.6062, lng: -122.3321 },
      'denver, co': { lat: 39.7392, lng: -104.9903 },
      'washington, dc': { lat: 38.9072, lng: -77.0369 },
      'boston, ma': { lat: 42.3601, lng: -71.0589 },
      'el paso, tx': { lat: 31.7619, lng: -106.4850 },
      'detroit, mi': { lat: 42.3314, lng: -83.0458 },
      'nashville, tn': { lat: 36.1627, lng: -86.7816 },
      'portland, or': { lat: 45.5152, lng: -122.6784 },
      'memphis, tn': { lat: 35.1495, lng: -90.0490 },
      'oklahoma city, ok': { lat: 35.4676, lng: -97.5164 },
      'las vegas, nv': { lat: 36.1699, lng: -115.1398 },
      'louisville, ky': { lat: 38.2527, lng: -85.7585 },
      'baltimore, md': { lat: 39.2904, lng: -76.6122 },
      'milwaukee, wi': { lat: 43.0389, lng: -87.9065 },
      'albuquerque, nm': { lat: 35.0844, lng: -106.6504 },
      'tucson, az': { lat: 32.2226, lng: -110.9747 },
      'fresno, ca': { lat: 36.7378, lng: -119.7871 },
      'sacramento, ca': { lat: 38.5816, lng: -121.4944 },
      'mesa, az': { lat: 33.4152, lng: -111.8315 },
      'kansas city, mo': { lat: 39.0997, lng: -94.5786 },
      'atlanta, ga': { lat: 33.7490, lng: -84.3880 },
      'long beach, ca': { lat: 33.7701, lng: -118.1937 },
      'colorado springs, co': { lat: 38.8339, lng: -104.8214 },
      'raleigh, nc': { lat: 35.7796, lng: -78.6382 },
      'miami, fl': { lat: 25.7617, lng: -80.1918 },
      'virginia beach, va': { lat: 36.8529, lng: -75.9780 },
      'omaha, ne': { lat: 41.2565, lng: -95.9345 },
      'oakland, ca': { lat: 37.8044, lng: -122.2711 },
      'minneapolis, mn': { lat: 44.9778, lng: -93.2650 },
      'tulsa, ok': { lat: 36.1540, lng: -95.9928 },
      'arlington, tx': { lat: 32.7357, lng: -97.1081 },
      'new orleans, la': { lat: 29.9511, lng: -90.0715 },
      'wichita, ks': { lat: 37.6872, lng: -97.3301 },
      'cleveland, oh': { lat: 41.4993, lng: -81.6944 },
      'tampa, fl': { lat: 27.9506, lng: -82.4572 },
      'bakersfield, ca': { lat: 35.3733, lng: -119.0187 },
      'aurora, co': { lat: 39.7294, lng: -104.8319 },
      'anaheim, ca': { lat: 33.8366, lng: -117.9143 },
      'honolulu, hi': { lat: 21.3099, lng: -157.8581 },
      'santa ana, ca': { lat: 33.7455, lng: -117.8677 },
      'corpus christi, tx': { lat: 27.8006, lng: -97.3964 },
      'riverside, ca': { lat: 33.9533, lng: -117.3962 },
      'lexington, ky': { lat: 38.0406, lng: -84.5037 },
      'stockton, ca': { lat: 37.9577, lng: -121.2908 },
      'henderson, nv': { lat: 36.0397, lng: -114.9817 },
      'saint paul, mn': { lat: 44.9537, lng: -93.0900 },
      'st. louis, mo': { lat: 38.6270, lng: -90.1994 },
      'cincinnati, oh': { lat: 39.1031, lng: -84.5120 },
      'pittsburgh, pa': { lat: 40.4406, lng: -79.9959 },
    };

    return cityCoords[cityKey] || null;
  }
} 