'use client';

import { useEffect, useState } from 'react';

interface HydrationSafeProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * HydrationSafe component prevents React hydration mismatches (Error #418)
 * by ensuring content only renders on the client side after hydration
 */
export function HydrationSafe({ children, fallback = null }: HydrationSafeProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // This effect only runs on the client after hydration
    setIsHydrated(true);
  }, []);

  // During SSR and before hydration, show fallback or nothing
  if (!isHydrated) {
    return <>{fallback}</>;
  }

  // After hydration, show the actual content
  return <>{children}</>;
}

/**
 * Hook to check if component is hydrated
 * Useful for conditional rendering of client-only content
 */
export function useIsHydrated() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
} 