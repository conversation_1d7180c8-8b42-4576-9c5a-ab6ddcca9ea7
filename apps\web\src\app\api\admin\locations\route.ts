import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here when user roles are implemented
    // For now, we'll allow any authenticated user

    // Fetch locations from Airtable
    const locations: any[] = [];
    
    await base('Locations').select({
      view: 'Grid view' // Use default view or specify your view name
    }).eachPage((records, fetchNextPage) => {
      records.forEach((record) => {
        // Get all fields from the record to be flexible with field names
        const fields = record.fields;

        locations.push({
          id: record.id,
          name: fields['Name'] || fields['Location Name'] || '',
          city: fields['City'] || '',
          state: fields['State'] || '',
          address: fields['Address'] || fields['Full Address'] || ''
        });
      });
      fetchNextPage();
    });

    return NextResponse.json({ 
      success: true, 
      locations: locations.sort((a, b) => a.name.localeCompare(b.name))
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
