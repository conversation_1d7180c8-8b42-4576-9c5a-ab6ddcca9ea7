import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    await requireAdmin(request);

    // Fetch locations from Airtable
    const locations: any[] = [];
    
    await base('Locations').select({
      view: 'Grid view' // Use default view or specify your view name
    }).eachPage((records, fetchNextPage) => {
      records.forEach((record) => {
        // Get all fields from the record to be flexible with field names
        const fields = record.fields;

        locations.push({
          id: record.id,
          name: fields['Name'] || fields['Location Name'] || '',
          city: fields['City'] || '',
          state: fields['State'] || '',
          address: fields['Address'] || fields['Full Address'] || ''
        });
      });
      fetchNextPage();
    });

    return NextResponse.json({ 
      success: true, 
      locations: locations.sort((a, b) => a.name.localeCompare(b.name))
    });

  } catch (error) {
    console.error('Error fetching locations:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to fetch locations' },
      { status: 500 }
    );
  }
}
