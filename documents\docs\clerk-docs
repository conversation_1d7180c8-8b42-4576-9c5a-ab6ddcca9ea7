TITLE: Setting Clerk API Keys in .env File (Environment Variables)
DESCRIPTION: This snippet demonstrates how to configure required Clerk API keys in a .env file for both the publishable (frontend) and secret (backend) keys. These variables must be placed in the root of your project for development and production environments. Required: valid keys from the Clerk dashboard. Input: environment variables; Output: runtime access to Clerk keys.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/quickstarts/react-router.mdx#2025-04-23_snippet_1

LANGUAGE: env
CODE:
```
VITE_CLERK_PUBLISHABLE_KEY={{pub_key}}\nCLERK_SECRET_KEY={{secret}}
```

----------------------------------------

TITLE: Initializing Standalone Clerk Client
DESCRIPTION: Creating a standalone Clerk client instance using createClerk<PERSON>lient with a secret key.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/backend/overview.mdx#2025-04-23_snippet_1

LANGUAGE: typescript
CODE:
```
import { createClerkClient } from '@clerk/backend'

const clerkClient = createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY })
```

----------------------------------------

TITLE: Configuring environment variables for Clerk API keys
DESCRIPTION: Guides the user to add Clerk's publishable and secret API keys into a `.env` file, referencing the Clerk Dashboard for retrieval. These keys are essential for authenticating requests to Clerk services.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/quickstarts/fastify.mdx#_snippet_1

LANGUAGE: YAML
CODE:
```
CLERK_PUBLISHABLE_KEY={{pub_key}}
CLERK_SECRET_KEY={{secret}}
```

----------------------------------------

TITLE: Setting Clerk Publishable and Secret Keys (Next.js)
DESCRIPTION: Defines the Next.js specific environment variables `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` for frontend access and `CLERK_SECRET_KEY` for backend use. These are required to connect your Next.js application to Clerk, obtained from the Clerk Dashboard. The `CLERK_SECRET_KEY` must remain private.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/deployments/clerk-environment-variables.mdx#2025-04-23_snippet_1

LANGUAGE: markdown
CODE:
```
| Variable | Description |
| - | - |
| `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` | Your Clerk app's Publishable Key, which you can find in the Clerk Dashboard. It will be prefixed with `pk_test_` in development instances and `pk_live_` in production instances. |
| `CLERK_SECRET_KEY` | Your Clerk app's Secret Key, which you can find in the Clerk Dashboard. It will be prefixed with `sk_test_` in development instances and `sk_live_` in production instances. **Do not expose this on the frontend with a public environment variable**. |
```

----------------------------------------

TITLE: Mounting Clerk Authentication Components in JavaScript
DESCRIPTION: This code checks the user's authentication status and mounts either a Clerk user button for authenticated users or a sign-in component for unauthenticated users. It uses Clerk's JavaScript SDK to interact with authentication components.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/quickstarts/javascript.mdx#2025-04-23_snippet_11

LANGUAGE: javascript
CODE:
```
Clerk.mountUserButton(userButtonDiv)
```

LANGUAGE: javascript
CODE:
```
document.getElementById('app').innerHTML = `
  <div id="sign-in"></div>
`

const signInDiv = document.getElementById('sign-in')

Clerk.mountSignIn(signInDiv)
```

----------------------------------------

TITLE: Implementing Token Verification in Next.js API Route
DESCRIPTION: This example demonstrates how to use the verifyToken function in a Next.js API route. It retrieves the session token from either a cookie or the Authorization header, verifies it using the JWKS Public Key, and checks against authorized parties.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/backend/verify-token.mdx#2025-04-23_snippet_1

LANGUAGE: typescript
CODE:
```
import { verifyToken } from '@clerk/backend'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  const cookieStore = cookies()
  const sessToken = cookieStore.get('__session')?.value
  const bearerToken = request.headers.get('Authorization')?.replace('Bearer ', '')
  const token = sessToken || bearerToken

  if (!token) {
    return Response.json({ error: 'Token not found. User must sign in.' }, { status: 401 })
  }

  try {
    const verifiedToken = await verifyToken(token, {
      jwtKey: process.env.CLERK_JWT_KEY,
      authorizedParties: ['http://localhost:3001', 'api.example.com'], // Replace with your authorized parties
    })

    return Response.json({ verifiedToken })
  } catch (error) {
    return Response.json({ error: 'Token not verified.' }, { status: 401 })
  }
}
```

----------------------------------------

TITLE: Implementing Email/Password Sign-up with Verification using Clerk Elements in React
DESCRIPTION: This code snippet showcases a complete sign-up flow implementation using Clerk Elements in a React component. It includes email and password input fields, email verification, and styling using Tailwind CSS. The component handles both the initial sign-up step and the email verification step.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/customization/elements/examples/sign-up.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
'use client'

import * as Clerk from '@clerk/elements/common'
import * as SignUp from '@clerk/elements/sign-up'

export default function SignUpPage() {
  return (
    <div className="grid w-full flex-grow items-center bg-black px-4 sm:justify-center">
      <SignUp.Root>
        <SignUp.Step
          name="start"
          className="w-full space-y-6 rounded-2xl bg-neutral-900 bg-[radial-gradient(circle_at_50%_0%,theme(colors.white/10%),transparent)] px-4 py-10 ring-1 ring-inset ring-white/5 sm:w-96 sm:px-8"
        >
          <header className="text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 40 40"
              className="mx-auto size-10"
            >
              <mask id="a" width="40" height="40" x="0" y="0" maskUnits="userSpaceOnUse">
                <circle cx="20" cy="20" r="20" fill="#D9D9D9" />
              </mask>
              <g fill="#fff" mask="url(#a)">
                <path d="M43.5 3a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46V2ZM43.5 8a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46V7ZM43.5 13a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 18a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 23a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 28a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 33a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 38a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1Z" />
                <path d="M27 3.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM25 8.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM23 13.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM21.5 18.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM20.5 23.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM22.5 28.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM25 33.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM27 38.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2Z" />
              </g>
            </svg>
            <h1 className="mt-4 text-xl font-medium tracking-tight text-white">
              Create an account
            </h1>
          </header>
          <Clerk.GlobalError className="block text-sm text-red-400" />
          <div className="space-y-4">
            <Clerk.Field name="emailAddress" className="space-y-2">
              <Clerk.Label className="text-sm font-medium text-white">Email address</Clerk.Label>
              <Clerk.Input
                type="text"
                required
                className="w-full rounded-md bg-neutral-900 px-3.5 py-2 text-sm text-white outline-none ring-1 ring-inset ring-zinc-700 hover:ring-zinc-600 focus:bg-transparent focus:ring-[1.5px] focus:ring-blue-400 data-[invalid]:ring-red-400"
              />
              <Clerk.FieldError className="block text-sm text-red-400" />
            </Clerk.Field>
            <Clerk.Field name="password" className="space-y-2">
              <Clerk.Label className="text-sm font-medium text-white">Password</Clerk.Label>
              <Clerk.Input
                type="password"
                required
                className="w-full rounded-md bg-neutral-900 px-3.5 py-2 text-sm text-white outline-none ring-1 ring-inset ring-zinc-700 hover:ring-zinc-600 focus:bg-transparent focus:ring-[1.5px] focus:ring-blue-400 data-[invalid]:ring-red-400"
              />
              <Clerk.FieldError className="block text-sm text-red-400" />
            </Clerk.Field>
          </div>
          <SignUp.Captcha className="empty:hidden" />
          <SignUp.Action
            submit
            className="relative isolate w-full rounded-md bg-blue-500 px-3.5 py-1.5 text-center text-sm font-medium text-white shadow-[0_1px_0_0_theme(colors.white/10%)_inset,0_0_0_1px_theme(colors.white/5%)] outline-none before:absolute before:inset-0 before:-z-10 before:rounded-md before:bg-white/5 before:opacity-0 hover:before:opacity-100 focus-visible:outline-[1.5px] focus-visible:outline-offset-2 focus-visible:outline-blue-400 active:text-white/70 active:before:bg-black/10"
          >
            Sign Up
          </SignUp.Action>
          <p className="text-center text-sm text-zinc-400">
            Have an account?{' '}
            <Clerk.Link
              navigate="sign-in"
              className="font-medium text-white decoration-white/20 underline-offset-4 outline-none hover:underline focus-visible:underline"
            >
              Sign in
            </Clerk.Link>
          </p>
        </SignUp.Step>
        <SignUp.Step
          name="verifications"
          className="w-full space-y-6 rounded-2xl bg-neutral-900 bg-[radial-gradient(circle_at_50%_0%,theme(colors.white/10%),transparent)] px-4 py-10 ring-1 ring-inset ring-white/5 sm:w-96 sm:px-8"
        >
          <header className="text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 40 40"
              className="mx-auto size-10"
            >
              <mask id="a" width="40" height="40" x="0" y="0" maskUnits="userSpaceOnUse">
                <circle cx="20" cy="20" r="20" fill="#D9D9D9" />
              </mask>
              <g fill="#fff" mask="url(#a)">
                <path d="M43.5 3a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46V2ZM43.5 8a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46V7ZM43.5 13a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 18a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 23a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 28a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 33a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 38a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1Z" />
                <path d="M27 3.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM25 8.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM23 13.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM21.5 18.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM20.5 23.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM22.5 28.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM25 33.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM27 38.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2Z" />
              </g>
            </svg>
            <h1 className="mt-4 text-xl font-medium tracking-tight text-white">
              Verify email code
            </h1>
          </header>
          <Clerk.GlobalError className="block text-sm text-red-400" />
          <SignUp.Strategy name="email_code">
            <Clerk.Field name="code" className="space-y-2">
              <Clerk.Label className="text-sm font-medium text-white">Email code</Clerk.Label>
              <Clerk.Input
                required
                className="w-full rounded-md bg-neutral-900 px-3.5 py-2 text-sm text-white outline-none ring-1 ring-inset ring-zinc-700 hover:ring-zinc-600 focus:bg-transparent focus:ring-[1.5px] focus:ring-blue-400 data-[invalid]:ring-red-400"
              />
              <Clerk.FieldError className="block text-sm text-red-400" />
            </Clerk.Field>
            <SignUp.Action
              submit
              className="relative isolate w-full rounded-md bg-blue-500 px-3.5 py-1.5 text-center text-sm font-medium text-white shadow-[0_1px_0_0_theme(colors.white/10%)_inset,0_0_0_1px_theme(colors.white/5%)] outline-none before:absolute before:inset-0 before:-z-10 before:rounded-md before:bg-white/5 before:opacity-0 hover:before:opacity-100 focus-visible:outline-[1.5px] focus-visible:outline-offset-2 focus-visible:outline-blue-400 active:text-white/70 active:before:bg-black/10"
            >
              Finish registration
            </SignUp.Action>
          </SignUp.Strategy>
          <p className="text-center text-sm text-zinc-400">
            Have an account?{' '}
            <Clerk.Link
              navigate="sign-in"
              className="font-medium text-white decoration-white/20 underline-offset-4 outline-none hover:underline focus-visible:underline"
            >
              Sign in
            </Clerk.Link>
          </p>
        </SignUp.Step>
      </SignUp.Root>
    </div>
  )
}
```

----------------------------------------

TITLE: Implementing Authentication in Next.js App Router Route Handlers
DESCRIPTION: Demonstrates how to protect API routes using Clerk's auth() helper and access user information with currentUser() in a Next.js App Router route handler. This pattern ensures only authenticated users can access the API endpoint.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/nextjs/read-session-data.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
import { NextResponse } from 'next/server'
import { currentUser, auth } from '@clerk/nextjs/server'

export async function GET() {
  // Use `auth()` to get the user's ID
  const { userId } = await auth()

  // Protect the route by checking if the user is signed in
  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }

  // Use `currentUser()` to get the Backend API User object
  const user = await currentUser()

  // Add your Route Handler's logic with the returned `user` object

  return NextResponse.json({ user: user }, { status: 200 })
}
```

----------------------------------------

TITLE: Setting Environment Variables for Clerk Integration
DESCRIPTION: Configuration for the .env file to set up Clerk's public and secret keys for authentication.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/nextjs/authjs-migration.mdx#2025-04-23_snippet_1

LANGUAGE: env
CODE:
```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY={{pub_key}}
CLERK_SECRET_KEY={{secret}}
```

----------------------------------------

TITLE: Configuring ClerkProvider and TRPCProvider in Next.js Layout
DESCRIPTION: Sets up the root layout component with ClerkProvider wrapped around TRPCProvider to ensure tRPC has access to Clerk's authentication context. This configuration enables authentication throughout the application.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/nextjs/trpc.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
// ...Imports and other code...

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <TRPCProvider>
        <html lang="en">
          <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
            <header className="flex justify-end items-center p-4 gap-4 h-16">
              <SignedOut>
                <SignInButton />
                <SignUpButton />
              </SignedOut>
              <SignedIn>
                <Link href="/">Home</Link>
                <UserButton />
              </SignedIn>
            </header>
            {children}
          </body>
        </html>
      </TRPCProvider>
    </ClerkProvider>
  )
}
```

----------------------------------------

TITLE: User Authentication in Next.js Route Handler with Clerk
DESCRIPTION: Demonstrates how to use Clerk's auth() helper and Backend SDK in a Next.js App Router route handler to authenticate users and retrieve user data. Shows protection of routes and fetching user information using the Backend SDK.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/backend/overview.mdx#2025-04-23_snippet_6

LANGUAGE: tsx
CODE:
```
import { auth, clerkClient } from '@clerk/nextjs/server'

export async function GET() {
  // Use `auth()` to get the user's ID
  const { userId } = await auth()

  // Protect the route by checking if the user is signed in
  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }

  const client = await clerkClient()

  // Use the Backend SDK's `getUser()` method to get the Backend User object
  const user = await client.users.getUser(userId)

  // Return the Backend User object
  return NextResponse.json({ user: user }, { status: 200 })
}
```

----------------------------------------

TITLE: Importing and Using ClerkProvider for Authentication Context
DESCRIPTION: The ClerkProvider component serves as a context provider that makes authentication data accessible throughout the application. It should be implemented at the root of your application to provide global access to user session data and authentication state.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/_partials/clerk-provider/explanation.mdx#2025-04-23_snippet_0

LANGUAGE: jsx
CODE:
```
<ClerkProvider>
```

----------------------------------------

TITLE: Wrapping the Application with ClerkProvider in TypeScript
DESCRIPTION: This TypeScript code snippet shows how to wrap the root React component (`App`) with the `ClerkProvider` component from `@clerk/clerk-react` in `src/main.tsx`. The `publishableKey` prop is required and is set using the imported environment variable. The `afterSignOutUrl` prop redirects the user to the homepage after signing out.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/quickstarts/react.mdx#2025-04-23_snippet_4

LANGUAGE: tsx
CODE:
```
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { ClerkProvider } from '@clerk/clerk-react'

// Import your Publishable Key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error('Add your Clerk Publishable Key to the .env file')
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ClerkProvider publishableKey={PUBLISHABLE_KEY} afterSignOutUrl="/">
      <App />
    </ClerkProvider>
  </React.StrictMode>,
)
```

----------------------------------------

TITLE: Implementing RedirectToSignIn in Next.js App Router
DESCRIPTION: This snippet demonstrates how to use the RedirectToSignIn component in a Next.js application using the App Router. It wraps the component in ClerkProvider and uses SignedIn and SignedOut components to control access.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/components/control/redirect-to-signin.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { ClerkProvider, SignedIn, SignedOut, RedirectToSignIn } from '@clerk/nextjs'

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body>
        <ClerkProvider>
          <SignedIn>{children}</SignedIn>
          <SignedOut>
            <RedirectToSignIn />
          </SignedOut>
        </ClerkProvider>
      </body>
    </html>
  )
}
```

----------------------------------------

TITLE: Loading ClerkJS Client Environment - TypeScript
DESCRIPTION: Declares a TypeScript `load` function for initializing the Clerk object, fetching environment settings, and preparing client state from the Frontend API. Accepts an optional `ClerkOptions` object for customizing appearance, localization, navigation behavior, session handling, and other settings. Returns a Promise that resolves to void. Must be called before other Clerk methods are used. Relies on the imported Clerk client in a frontend (browser) context.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/javascript/clerk.mdx#2025-04-23_snippet_20

LANGUAGE: typescript
CODE:
```
function load(options?: ClerkOptions): Promise<void>
```

----------------------------------------

TITLE: GetToken Implementation in Express
DESCRIPTION: Example of using getToken in an Express API route.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/references/backend/types/auth-object.mdx#2025-04-23_snippet_6

LANGUAGE: javascript
CODE:
```
app.get('/api/get-token', async (req, res) => {
  const getToken = req.auth.getToken

  const template = 'test'

  const token = await getToken({ template })

  res.json({ token })
})
```

----------------------------------------

TITLE: Astro `<SignedIn>` component usage example
DESCRIPTION: Provides an example of conditional rendering for authenticated users within an Astro framework, importing `<SignedIn>` from `@clerk/astro/components` and wrapping content to show only when a user is signed in.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/components/control/signed-in.mdx#_snippet_5

LANGUAGE: TypeScript
CODE:
```
---
import { SignedIn } from '@clerk/astro/components'
---

<SignedIn>
  <div>You are signed in</div>
</SignedIn>
<p>This content is always visible.</p>

```

----------------------------------------

TITLE: Initializing a Paginated Organization List with Clerk in Next.js
DESCRIPTION: This React component (`JoinedOrganizations`) uses the `useOrganizationList` hook from `@clerk/nextjs` to fetch and display a user's organization memberships with pagination enabled (`pageSize: 5`). It renders the memberships in a table, provides buttons to set an organization as active using `setActive`, and includes Next/Previous buttons powered by `fetchNext` and `fetchPrevious`. If the user has no memberships, it renders a `CreateOrganization` component.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/custom-flows/organization-switcher.mdx#2025-04-23_snippet_0

LANGUAGE: jsx
CODE:
```
```jsx {{ filename: 'app/components/CustomOrganizationSwitcher.tsx', collapsible: true }}
'use client'

import { useAuth, useOrganizationList } from '@clerk/nextjs'
import CreateOrganization from '../components/create-organization' // See /docs/custom-flows/create-organizations for this component

// List user's organization memberships
export default function JoinedOrganizations() {
  const { isLoaded, setActive, userMemberships } = useOrganizationList({
    userMemberships: {
      // Set pagination parameters
      pageSize: 5,
      keepPreviousData: true,
    },
  })
  const { orgId } = useAuth()

  if (!isLoaded) {
    return <p>Loading...</p>
  }

  return (
    <>
      <h1>Joined organizations</h1>
      {userMemberships?.data?.length > 0 && (
        <>
          <table>
            <thead>
              <tr>
                <th>Identifier</th>
                <th>Organization</th>
                <th>Joined</th>
                <th>Role</th>
                <th>Set as active org</th>
              </tr>
            </thead>
            <tbody>
              {userMemberships?.data?.map((mem) => (
                <tr key={mem.id}>
                  <td>{mem.publicUserData.identifier}</td>
                  <td>{mem.organization.name}</td>
                  <td>{mem.createdAt.toLocaleDateString()}</td>
                  <td>{mem.role}</td>
                  <td>
                    {orgId === mem.organization.id ? (
                      <button onClick={() => setActive({ organization: mem.organization.id })}>
                        Set as active
                      </button>
                    ) : (
                      <p>Currently active</p>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div>
            <button
              disabled={!userMemberships?.hasPreviousPage || userMemberships?.isFetching}
              onClick={() => userMemberships?.fetchPrevious?.()}
            >
              Previous
            </button>

            <button
              disabled={!userMemberships?.hasNextPage || userMemberships?.isFetching}
              onClick={() => userMemberships?.fetchNext?.()}
            >
              Next
            </button>
          </div>
        </>
      )}
      {userMemberships?.data?.length === 0 && (
        <div>
          <p>No organizations found</p>
          <CreateOrganization />
        </div>
      )}
    </>
  )
}
```
```

----------------------------------------

TITLE: Protecting Express Routes with requireAuth (TypeScript)
DESCRIPTION: Illustrates how to secure an Express route using the `requireAuth()` middleware from `@clerk/express`. This middleware ensures that only authenticated users can access the specified route (`/protected` in this example); unauthenticated users are typically redirected (e.g., to a sign-in page or homepage). The example also shows how to retrieve the authenticated user's ID using `getAuth(req)` and fetch the full user object using `clerkClient.users.getUser(userId)`.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/quickstarts/express.mdx#2025-04-23_snippet_4

LANGUAGE: typescript
CODE:
```
import 'dotenv/config'
import express from 'express'
import { clerkClient, requireAuth, getAuth } from '@clerk/express'

const app = express()
const PORT = 3000

// Use requireAuth() to protect this route
// If user isn't authenticated, requireAuth() will redirect back to the homepage
app.get('/protected', requireAuth(), async (req, res) => {
  // Use `getAuth()` to get the user's `userId`
  const { userId } = getAuth(req)

  // Use Clerk's JavaScript Backend SDK to get the user's User object
  const user = await clerkClient.users.getUser(userId)

  return res.json({ user })
})

// Start the server and listen on the specified port
app.listen(PORT, () => {
  console.log(`Example app listening at http://localhost:${PORT}`)
})
```

----------------------------------------

TITLE: Building the Home Page UI with React Server Components
DESCRIPTION: Next.js home page component that retrieves user messages from the database and displays them. It allows users to add new messages or delete existing ones using the Server Actions. The component uses Clerk's auth() helper to get the user ID for database queries.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/integrations/databases/neon.mdx#2025-04-23_snippet_8

LANGUAGE: tsx
CODE:
```
import { createUserMessage, deleteUserMessage } from './actions'
import { db } from './db'
import { auth } from '@clerk/nextjs/server'

export default async function Home() {
  const { userId } = await auth()
  if (!userId) throw new Error('User not found')
  const existingMessage = await db.query.UserMessages.findFirst({
    where: (messages, { eq }) => eq(messages.user_id, userId),
  })

  return (
    <main>
      <h1>Neon + Clerk Example</h1>
      {existingMessage ? (
        <div>
          <p>{existingMessage.message}</p>
          <form action={deleteUserMessage}>
            <button>Delete Message</button>
          </form>
        </div>
      ) : (
        <form action={createUserMessage}>
          <input type="text" name="message" placeholder="Enter a message" />
          <button>Save Message</button>
        </form>
      )}
    </main>
  )
}
```

----------------------------------------

TITLE: Implementing Google OAuth Sign-in with Clerk Elements in React
DESCRIPTION: A React component implementing a Google OAuth sign-in flow using Clerk Elements. Features a Google sign-in button and navigation to sign-up. Includes custom styling with Tailwind CSS for a dark theme.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/customization/elements/examples/sign-in.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
'use client'

import * as Clerk from '@clerk/elements/common'
import * as SignIn from '@clerk/elements/sign-in'

export default function SignInPage() {
  return (
    <div className="grid w-full flex-grow items-center bg-black px-4 sm:justify-center">
      <SignIn.Root>
        <SignIn.Step
          name="start"
          className="w-full flex-grow space-y-6 rounded-2xl bg-neutral-900 bg-[radial-gradient(circle_at_50%_0%,theme(colors.white/10%),transparent)] px-4 py-10 ring-1 ring-inset ring-white/5 sm:w-96 sm:px-8"
        >
          <header className="text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 40 40"
              className="mx-auto size-10"
            >
              <mask id="a" width="40" height="40" x="0" y="0" maskUnits="userSpaceOnUse">
                <circle cx="20" cy="20" r="20" fill="#D9D9D9" />
              </mask>
              <g fill="#fff" mask="url(#a)">
                <path d="M43.5 3a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46V2ZM43.5 8a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46V7ZM43.5 13a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 18a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 23a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 28a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 33a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1ZM43.5 38a.5.5 0 0 0 0-1v1Zm0-1h-46v1h46v-1Z" />
                <path d="M27 3.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM25 8.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM23 13.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM21.5 18.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM20.5 23.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM22.5 28.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM25 33.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2ZM27 38.5a1 1 0 1 0 0-2v2Zm0-2h-46v2h46v-2Z" />
              </g>
            </svg>
            <h1 className="mt-4 text-xl font-medium tracking-tight text-white">
              Sign in to Clover
            </h1>
          </header>
          <Clerk.GlobalError className="block text-sm text-red-400" />
          <div className="space-y-2">
            <Clerk.Connection
              name="google"
              className="flex w-full items-center justify-center gap-x-3 rounded-md bg-neutral-700 px-3.5 py-1.5 text-sm font-medium text-white shadow-[0_1px_0_0_theme(colors.white/5%)_inset,0_0_0_1px_theme(colors.white/2%)_inset] outline-none hover:bg-gradient-to-b hover:from-white/5 hover:to-white/5 focus-visible:outline-[1.5px] focus-visible:outline-offset-2 focus-visible:outline-white active:bg-gradient-to-b active:from-black/20 active:to-black/20 active:text-white/70"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 17 16"
                className="w-4"
                aria-hidden
              >
                <path
                  fill="currentColor"
                  d="M8.82 7.28v2.187h5.227c-.16 1.226-.57 2.124-1.192 2.755-.764.765-1.955 1.6-4.035 1.6-3.218 0-5.733-2.595-5.733-5.813 0-3.218 2.515-5.814 5.733-5.814 1.733 0 3.005.685 3.938 1.565l1.538-1.538C12.998.96 11.256 0 8.82 0 4.41 0 .705 3.591.705 8s3.706 8 8.115 8c2.382 0 4.178-.782 5.582-2.24 1.44-1.44 1.893-3.475 1.893-5.111 0-.507-.035-.978-.115-1.369H8.82Z"
                />
              </svg>
              Login with Google
            </Clerk.Connection>
          </div>
          <p className="text-center text-sm text-neutral-400">
            No account?{' '}
            <Clerk.Link
              navigate="sign-up"
              className="font-medium text-white decoration-white/20 underline-offset-4 outline-none hover:underline focus-visible:underline"
            >
              Create an account
            </Clerk.Link>
          </p>
        </SignIn.Step>
      </SignIn.Root>
    </div>
  )
}
```

----------------------------------------

TITLE: Setting up ClerkProvider in a React Application
DESCRIPTION: This snippet illustrates how to implement ClerkProvider in a React application's entry point. It includes setting up the publishable key and configuring the sign-out URL.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/components/clerk-provider.mdx#2025-04-23_snippet_2

LANGUAGE: tsx
CODE:
```
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import { ClerkProvider } from '@clerk/clerk-react'

// Import your Publishable Key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error('Add your Clerk Publishable Key to the .env file')
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ClerkProvider publishableKey={PUBLISHABLE_KEY} afterSignOutUrl="/">
      <App />
    </ClerkProvider>
  </React.StrictMode>,
)
```

----------------------------------------

TITLE: Setting Clerk Publishable and Secret Keys (General)
DESCRIPTION: Defines the general environment variables `CLERK_PUBLISHABLE_KEY` for frontend use and `CLERK_SECRET_KEY` for backend use. These keys are essential for authenticating your application with Clerk and are found on the Clerk Dashboard API keys page. The secret key must not be exposed publicly.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/deployments/clerk-environment-variables.mdx#2025-04-23_snippet_0

LANGUAGE: markdown
CODE:
```
| Variable | Description |
| - | - |
| `CLERK_PUBLISHABLE_KEY` | Your Clerk app's Publishable Key. It will be prefixed with `pk_test_` in development instances and `pk_live_` in production instances. |
| `CLERK_SECRET_KEY` | Your Clerk app's Secret Key, which you can find in the Clerk Dashboard. It will be prefixed with `sk_test_` in development instances and `sk_live_` in production instances. **Do not expose this on the frontend with a public environment variable**. |
```

----------------------------------------

TITLE: Setting up ClerkProvider and UI Components in Next.js Layout (TypeScript)
DESCRIPTION: Illustrates how to integrate Clerk into the root layout (`app/layout.tsx`) of a Next.js App Router application. It wraps the main HTML structure with the `<ClerkProvider>` component to provide authentication context. It also includes Clerk's prebuilt UI components (`SignInButton`, `SignUpButton`, `SignedIn`, `SignedOut`, `UserButton`) within the header to handle user sign-in, sign-up, and displaying user information based on authentication status. Requires the `@clerk/nextjs` package to be installed.
SOURCE: https://github.com/clerk/clerk-docs/blob/main/docs/quickstarts/nextjs.mdx#2025-04-23_snippet_3

LANGUAGE: tsx
CODE:
```
import type { Metadata } from 'next'
import {
  ClerkProvider,
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { Geist, Geist_Mono } from 'next/font/google'
import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Clerk Next.js Quickstart',
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
          <header className="flex justify-end items-center p-4 gap-4 h-16">
            <SignedOut>
              <SignInButton />
              <SignUpButton />
            </SignedOut>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </header>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
```