import { <PERSON>, Get, Patch, Body, UseGuards, Request, Logger } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { IsOptional, IsString, IsEmail } from 'class-validator';
import { AuthGuard } from './auth.guard';
import { AuthService } from './auth.service';
import { AuthenticatedRequest } from './authenticated-request.interface';

// DTO for updating user profile
export class UpdateUserProfileDto {
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsEmail()
  email?: string;
}

@ApiTags('auth')
@ApiBearerAuth()
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Get('profile')
  @UseGuards(AuthGuard)
  @ApiOperation({ 
    summary: 'Get current user profile',
    description: 'Get the current authenticated user\'s profile information'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns current user profile',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        airtableUserId: { type: 'string' },
        email: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string', enum: ['ADMIN', 'CARRIER'] },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getUserProfile(@Request() req: AuthenticatedRequest) {
    const userId = req.user?.airtableUserId;
    
    if (!userId) {
      this.logger.error('No user ID found in request user data');
      throw new Error('User authentication required');
    }

    try {
      this.logger.log(`Retrieving user profile for: ${userId}`);
      
      const userProfile = await this.authService.findUserByAirtableUserId(userId);
      
      return {
        id: userProfile.id,
        airtableUserId: userProfile.airtableUserId,
        email: userProfile.email,
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        role: userProfile.role,
        createdAt: userProfile.createdAt,
        updatedAt: userProfile.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Error retrieving user profile for ${userId}:`, error.message, error.stack);
      throw new Error('Unable to retrieve user profile');
    }
  }

  @Patch('profile')
  @UseGuards(AuthGuard)
  @ApiOperation({ 
    summary: 'Update current user profile',
    description: 'Update the current authenticated user\'s profile information'
  })
  @ApiBody({ type: UpdateUserProfileDto })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns updated user profile'
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateUserProfile(
    @Request() req: AuthenticatedRequest,
    @Body() updateData: UpdateUserProfileDto
  ) {
    const userId = req.user?.airtableUserId;
    
    if (!userId) {
      this.logger.error('No user ID found in request user data');
      throw new Error('User authentication required');
    }

    try {
      this.logger.log(`Updating user profile for: ${userId}`);
      this.logger.debug(`Update data:`, updateData);
      
      const updatedProfile = await this.authService.updateUserProfile(userId, updateData);
      
      this.logger.debug(`Updated profile result:`, updatedProfile);
      
      return {
        id: updatedProfile.id,
        airtableUserId: updatedProfile.airtableUserId,
        email: updatedProfile.email,
        firstName: updatedProfile.firstName,
        lastName: updatedProfile.lastName,
        role: updatedProfile.role,
        createdAt: updatedProfile.createdAt,
        updatedAt: updatedProfile.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Error updating user profile for ${userId}:`, error.message, error.stack);
      this.logger.error(`Full error object:`, error);
      throw new Error('Unable to update user profile');
    }
  }

  @Get('me')
  @UseGuards(AuthGuard)
  @ApiOperation({ 
    summary: 'Get current user information',
    description: 'Get the current authenticated user\'s profile and role information'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns current user information including role',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        clerkUserId: { type: 'string' },
        email: { type: 'string' },
        name: { type: 'string' },
        role: { type: 'string', enum: ['ADMIN', 'CARRIER'] },
        orgName: { type: 'string' },
        orgRole: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async getCurrentUser(@Request() req: AuthenticatedRequest) {
    const userId = req.user?.airtableUserId;
    
    if (!userId) {
      this.logger.error('No user ID found in request user data');
      throw new Error('User authentication required');
    }

    try {
      this.logger.log(`Attempting to retrieve user info for: ${userId}`);
      
      // Get user profile using N8N authentication
      const userProfile = await Promise.race([
        this.authService.findUserByAirtableUserId(userId),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Database timeout - using degraded mode')), 5000)
        )
      ]) as any;
      
      this.logger.log(`Successfully retrieved user info for: ${userId}`);
      
      return {
        id: userProfile.airtableUserId,
        email: userProfile.email,
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        companyName: userProfile.companyName,
        mcNumber: userProfile.mcNumber,
        dotNumber: userProfile.dotNumber,
        role: userProfile.role,
        verificationStatus: userProfile.verificationStatus,
      };
    } catch (error) {
      this.logger.error(`Error retrieving user info for ${userId}:`, error.message, error.stack);
      
      // DEGRADED MODE: Return basic user info from N8N JWT token
      if (error.message.includes('timeout') || error.message.includes('connection')) {
        this.logger.warn(`Database unavailable, returning degraded user info for ${userId}`);
        
        return {
          id: userId,
          email: req.user?.email || '<EMAIL>',
          firstName: 'Unknown',
          lastName: 'User',
          companyName: 'Unknown Company',
          mcNumber: req.user?.mcNumber || null,
          role: req.user?.role || 'CARRIER',
          verificationStatus: 'Pending',
          _degradedMode: true,
          _message: 'Database temporarily unavailable - showing basic user info from JWT'
        };
      }
      
      // Provide different error responses based on error type
      if (error.message === 'Database query timeout') {
        throw new Error('Service temporarily unavailable. Please try again.');
      }
      
      if (error.message.includes('not found')) {
        throw new Error('User profile not found. Please contact support.');
      }
      
      if (error.message.includes('connection') || error.message.includes('closed')) {
        throw new Error('Database connection error. Please try again shortly.');
      }
      
      // Generic error for production
      throw new Error('Unable to retrieve user information. Please try again.');
    }
  }
} 