#!/bin/bash

# Test Admin Notification Webhook
# URL: https://firstcutproduce.app.n8n.cloud/webhook/admin/bid-alert

echo "🧪 Testing Admin Notification Webhook..."
echo "📤 Sending test bid notification..."

curl -X POST \
  "https://firstcutproduce.app.n8n.cloud/webhook/admin/bid-alert" \
  -H "Content-Type: application/json" \
  -H "User-Agent: Carrier-Portal-Test/1.0" \
  -d '{
    "bidId": "test_bid_'$(date +%s)'",
    "carrierCompanyName": "ABC Trucking LLC",
    "carrierMcNumber": "123456",
    "carrierUserId": "user_test_123",
    "bidAmount": 2750,
    "carrierNotes": "Available for immediate pickup. Clean carrier with excellent safety record. Can deliver early if needed.",
    "authenticatedUser": true,
    "expiresAt": "'$(date -d '+24 hours' -Iseconds)'",
    "loadDetails": {
      "origin": "Chicago, IL",
      "destination": "Dallas, TX",
      "miles": "925",
      "equipment": "Dry Van",
      "weight": "45000",
      "pickupDate": "'$(date -d '+1 day' +%Y-%m-%d)'",
      "deliveryDate": "'$(date -d '+3 days' +%Y-%m-%d)'",
      "commodity": "Electronics"
    },
    "submittedAt": "'$(date -Iseconds)'",
    "urgencyLevel": "high"
  }' \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo ""
echo "✅ Test completed!"
echo "📧 Check your email: <EMAIL>"
echo "🔗 Expected: Professional bid notification with one-click action buttons" 