'use server';

// DEPRECATED: Legacy Clerk-based profile actions
// These are disabled as part of N8N authentication migration

export interface CarrierProfileData {
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
  mcNumber?: string;
  equipmentList?: string;
  companyName?: string;
  dotNumber?: string;
}

interface UpdateProfileResult {
  success: boolean;
  error?: string;
  updatedProfile?: CarrierProfileData;
}

interface CarrierProfile {
  id: number;
  userId: number;
  companyName?: string | null;
  dotNumber?: string | null;
  mcNumber?: string | null;
  phoneNumber?: string | null;
  contact_name?: string | null;
  contact_email?: string | null;
  contact_phone?: string | null;
  equipmentTypes?: string | null;
  isVerifiedByAdmin?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface GetProfileResult {
  success: boolean;
  profile?: CarrierProfile;
  error?: string;
  isAutoCreated?: boolean;
}

export async function updateCarrierProfile(
  _orgIdString: string,
  _profileData: CarrierProfileData
): Promise<UpdateProfileResult> {
  // DEPRECATED: This function is disabled as part of N8N authentication migration
  return { 
    success: false, 
    error: "Profile updates are temporarily disabled during authentication migration" 
  };
}

export async function getCarrierProfile(): Promise<GetProfileResult> {
  // DEPRECATED: This function is disabled as part of N8N authentication migration
  return { 
    success: false, 
    error: "Profile fetching is temporarily disabled during authentication migration" 
  };
} 