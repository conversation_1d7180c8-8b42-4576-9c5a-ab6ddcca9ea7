**DEVELOPMENT PHASES (CHECKLIST):**

**Phase 0: Setup & Foundations**
- [ ] Initialize Monorepo (e.g., using Turborepo or Lerna) or separate Git repos.
- [ ] Frontend: `create-next-app`, setup Tailwind CSS.
- [x] Backend: NestJS project setup, configure TypeORM or Prisma for PostgreSQL.
- [ ] Database: Setup PostgreSQL instance on Render.com (or locally).
- [x] Clerk: Configure Application, Organization Roles (Admin, Carrier), UI components.
- [ ] Airtable: Finalize "Loads" table structure. Create an API key and Automation.

**Phase 1: User Authentication & Carrier Profile (MVP)**
- [ ] **Backend:**
    - [ ] Integrate Clerk for user creation/sync to local `Users` table (if maintaining a separate local table). // We saw logs for findOrCreateUserByClerkPayload, so this might be partially or fully done.
    - [x] CRUD API for `CarrierProfiles`.
    - [x] Secure endpoints using Clerk JWTs. Role guards implemented (`ClerkGuard`, `ClerkAdminGuard`).
- [ ] **Frontend:**
    - [x] Implement Clerk login/logout/signup flows & organization switching.
    - [ ] Create Carrier Profile page (view/edit - for equipment, regions, company info).
    - [ ] Admin: Page to view list of carriers and their profiles.
    - [ ] Admin: Functionality to toggle `is_verified_by_admin` on profiles.

**Phase 2: Airtable Sync & Loadboard Display (MVP)**
- [ ] **Backend:**
    - [ ] Create webhook endpoint for Airtable Automations.
    - [ ] Logic to parse Airtable payload and upsert into `Loads` table (match on `airtable_record_id`).
    - [x] API endpoint for frontend to fetch `Loads` (using direct Airtable fetch for now, basic filtering by view/checkbox).
- [ ] **Frontend:**
    - [x] Loadboard page to display available loads (DataTable with sorting, filtering placeholders, column visibility, specific fields formatted).
    - [ ] Load Detail view (modal or separate page).
- [ ] **Airtable:**
    - [ ] Configure Airtable Automation to call the backend webhook on load create/update.
    - [ ] Test sync thoroughly.

**Phase 3: Bidding & Awarding (MVP)**
- [ ] **Backend:**
    - [ ] API endpoint for carriers to submit bids (creates `Bids` record).
    - [ ] API endpoint for admins to view bids for a load.
    - [ ] API endpoint for admins to "award" a load (updates `Loads` and `Bids` statuses).
- [ ] **Frontend:**
    - [ ] "Place Bid" form on Load Detail page.
    - [ ] Carrier: "My Bids" page showing their active bids and statuses.
    - [ ] Admin: Interface to view bids on a load detail page.
    - [ ] Admin: "Award to this Carrier" button.

**Phase 4: Polish & Pre-Launch MVP**
- [ ] Basic UI/UX refinement for all MVP flows.
- [x] Implement basic email notifications (e.g., bid awarded - *load booking request email is working, foundation laid*).
- [ ] Error handling and user feedback messages.
- [ ] Thorough testing of all MVP features.
- [ ] Setup Vercel for frontend deployment.
- [ ] Setup Render.com for backend & DB deployment.
- [ ] CI/CD pipeline basics.

**Post-MVP Enhancements (Examples):**
- [ ] Advanced Loadboard Filtering & Sorting (by equipment, location radius, date range).
- [ ] Real-time notifications (Socket.IO) for new loads, bid updates.
- [ ] Carrier Document Uploads (insurance, W9) to `CarrierProfiles` (use S3/Cloudinary).
- [ ] Admin Dashboard with key metrics.
- [ ] Counter-offer functionality.
- [ ] "Book Now" option on loads.
- [ ] Saved Searches / Load Alerts for Carriers.
- [ ] Map view integration for loads. 