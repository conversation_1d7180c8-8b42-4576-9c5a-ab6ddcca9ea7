# APM Task Assignment: Debug API Errors & Column Spacing Issues

## 1. Agent Role & APM Context

**Introduction:** You are activated as a **Debug Agent** within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As a Debug Agent, you are responsible for fixing critical API errors causing dashboard and loadboard failures, optimizing column spacing for full-width utilization, and resolving Content Security Policy violations.

**Workflow:** You will work directly with the Manager Agent (via the User) to diagnose and resolve backend API connectivity issues, frontend data fetching errors, and layout optimization problems.

## 2. Context from Prior Work

**Critical Issues Context:**
- ❌ **API Failures:** Multiple 500 errors on `/api/v1/auth/me` and `/api/v1/airtable-orders/available`
- ❌ **Backend Connection:** "Server has closed the connection" errors
- ❌ **Column Spacing:** Equipment column cut off as "Equipm", poor space utilization
- ❌ **CSP Violations:** 24 Content Security Policy warnings for inline scripts
- ❌ **Data Loading:** Dashboard and loadboard failing to load data
- 🎯 **Goal:** Restore API functionality and optimize full-width layout

**Error Evidence from Console:**
- `Error fetching dashboard data: Error: Failed to fetch assigned loads`
- `Failed to fetch loads: Error: HTTP error! status: 500`
- `{"error":"Backend API Error","details":{"error":"Internal Server Error","message":"Server has closed the connection."},"status":500}`
- Column headers showing "Equipm" instead of "Equipment"

## 3. Task Assignment

**Reference Implementation Plan:** Critical API debugging and layout optimization for Phase 2.5

**Objective:** Fix API connectivity issues, implement proper error handling, optimize column spacing for full-width layout, and resolve CSP violations.

### Critical Debug Requirements:

#### Issue #1: API Endpoint Failures (HTTP 500)
**Problem:** Multiple API endpoints returning 500 Internal Server Error
- `/api/v1/auth/me` - User authentication data (1565ms timeout)
- `/api/v1/airtable-orders/available` - Load data fetching (1714ms timeout)
- Backend API connection instability
- "Server has closed the connection" errors

**Debug Strategy Required:**
- **Identify:** Root cause of API endpoint failures
- **Analyze:** Backend server connectivity and timeout issues
- **Fix:** API error handling and retry logic
- **Implement:** Graceful degradation for failed API calls

#### Issue #2: Column Spacing & Width Optimization
**Problem:** Table columns not utilizing full page width properly
- Equipment column truncated to "Equipm"
- Poor space distribution across columns
- Not taking advantage of top navigation full-width layout
- Columns appear cramped despite available screen space

**Solution Required:**
- **Optimize column widths** for full screen utilization
- **Implement responsive column sizing** based on content
- **Fix text truncation** issues
- **Proper space distribution** across all columns

#### Issue #3: Content Security Policy Violations
**Problem:** 24 CSP warnings affecting page functionality
- Inline script execution blocked
- Violating script-src-elem directive
- Potential security and functionality issues

**Fix Required:**
- **Resolve CSP violations** for inline scripts
- **Implement proper nonce usage** for allowed scripts
- **Ensure security compliance** without breaking functionality

#### Issue #4: Error Handling & User Experience
**Problem:** Poor error states and loading experience
- Users see raw error messages
- No fallback UI for failed API calls
- Loading states not properly handled

**Enhancement Required:**
- **Implement proper error boundaries**
- **Add loading states and fallbacks**
- **User-friendly error messages**
- **Retry mechanisms for failed requests**

### Detailed Debug Steps:

#### A. API Endpoint Debugging & Fixes
1. **Identify API Failure Root Causes:**
   ```typescript
   // Debug API endpoint issues
   
   // Check /api/v1/auth/me endpoint
   export async function GET(request: Request) {
     try {
       // Add comprehensive logging
       console.log('Auth endpoint called:', new Date().toISOString());
       
       const authHeader = request.headers.get('authorization');
       if (!authHeader) {
         console.error('No authorization header provided');
         return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
       }
       
       // Verify database connection
       const dbStatus = await verifyDatabaseConnection();
       if (!dbStatus.connected) {
         console.error('Database connection failed:', dbStatus.error);
         return NextResponse.json({ 
           error: 'Database connection failed', 
           details: dbStatus.error 
         }, { status: 500 });
       }
       
       // Add timeout handling
       const user = await Promise.race([
         fetchUserData(authHeader),
         new Promise((_, reject) => 
           setTimeout(() => reject(new Error('Request timeout')), 10000)
         )
       ]);
       
       return NextResponse.json(user);
       
     } catch (error) {
       console.error('Auth endpoint error:', error);
       return NextResponse.json({ 
         error: 'Internal Server Error',
         message: error.message,
         timestamp: new Date().toISOString()
       }, { status: 500 });
     }
   }
   ```

2. **Fix Airtable Orders API:**
   ```typescript
   // Debug /api/v1/airtable-orders/available endpoint
   export async function GET(request: Request) {
     try {
       console.log('Airtable orders endpoint called');
       
       // Check environment variables
       if (!process.env.AIRTABLE_API_KEY) {
         console.error('Missing AIRTABLE_API_KEY environment variable');
         return NextResponse.json({ 
           error: 'Configuration Error',
           message: 'Missing API configuration'
         }, { status: 500 });
       }
       
       // Add connection retry logic
       let retries = 3;
       let lastError;
       
       while (retries > 0) {
         try {
           const orders = await fetchAirtableOrders();
           return NextResponse.json(orders);
         } catch (error) {
           lastError = error;
           retries--;
           if (retries > 0) {
             console.log(`Retrying Airtable request, ${retries} attempts left`);
             await new Promise(resolve => setTimeout(resolve, 1000));
           }
         }
       }
       
       throw lastError;
       
     } catch (error) {
       console.error('Airtable orders error:', error);
       return NextResponse.json({ 
         error: 'Backend API Error',
         details: {
           error: 'Internal Server Error',
           message: error.message
         },
         status: 500
       }, { status: 500 });
     }
   }
   ```

3. **Database Connection Stability:**
   ```typescript
   // Add database connection pooling and error handling
   import { PrismaClient } from '@prisma/client';
   
   const prisma = new PrismaClient({
     log: ['error', 'warn'],
     datasources: {
       db: {
         url: process.env.DATABASE_URL
       }
     },
     // Add connection pooling
     __internal: {
       engine: {
         connectTimeout: 10000,
         requestTimeout: 10000,
         retryCount: 3
       }
     }
   });
   
   // Connection health check
   export async function verifyDatabaseConnection() {
     try {
       await prisma.$queryRaw`SELECT 1`;
       return { connected: true };
     } catch (error) {
       console.error('Database connection failed:', error);
       return { 
         connected: false, 
         error: error.message 
       };
     }
   }
   ```

#### B. Column Spacing & Width Optimization
1. **Fix Column Width Distribution:**
   ```css
   /* Optimize table for full-width utilization */
   .load-results-table {
     width: 100%;
     table-layout: fixed;
     border-collapse: collapse;
   }
   
   /* Optimized column widths for full screen */
   .load-results-table th,
   .load-results-table td {
     padding: 12px 16px; /* Increased padding for better spacing */
     text-align: left;
     border-bottom: 1px solid #e5e7eb;
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap;
   }
   
   /* Better column width distribution */
   .col-pro-number { width: 10%; } /* Increased from 8% */
   .col-origin { width: 13%; } /* Increased from 12% */
   .col-destination { width: 13%; } /* Increased from 12% */
   .col-miles { width: 7%; } /* Increased from 6% */
   .col-dh-o { width: 7%; } /* New column */
   .col-pickup { width: 12%; }
   .col-delivery { width: 12%; }
   .col-equipment { width: 10%; } /* Fixed truncation - increased from 8% */
   .col-weight { width: 8%; }
   .col-commodity { width: 8%; } /* Reduced from 10% */
   .col-status { width: 6%; } /* Reduced from 8% */
   .col-rate { width: 8%; } /* New column for rate */
   .col-actions { width: 6%; } /* Increased from 4% */
   ```

2. **Responsive Column Headers:**
   ```jsx
   // Fix column header component
   const ColumnHeader = ({ label, sortKey, className }) => (
     <th 
       className={`${className} cursor-pointer hover:bg-gray-50 transition-colors`}
       onClick={() => handleSort(sortKey)}
       title={label} // Add tooltip for full text
     >
       <div className="flex items-center gap-2">
         <span className="font-medium text-gray-900">{label}</span>
         <SortIcon direction={getSortDirection(sortKey)} />
       </div>
     </th>
   );
   
   // Column definitions with proper widths
   const columns = [
     { key: 'proNumber', label: 'Pro #', className: 'col-pro-number' },
     { key: 'origin', label: 'Origin', className: 'col-origin' },
     { key: 'destination', label: 'Destination', className: 'col-destination' },
     { key: 'miles', label: 'Miles', className: 'col-miles' },
     { key: 'dhO', label: 'DH-O', className: 'col-dh-o' },
     { key: 'pickup', label: 'Pickup', className: 'col-pickup' },
     { key: 'delivery', label: 'Delivery', className: 'col-delivery' },
     { key: 'equipment', label: 'Equipment', className: 'col-equipment' }, // Fixed
     { key: 'weight', label: 'Weight', className: 'col-weight' },
     { key: 'commodity', label: 'Commodity', className: 'col-commodity' },
     { key: 'status', label: 'Status', className: 'col-status' },
     { key: 'rate', label: 'Rate', className: 'col-rate' },
     { key: 'actions', label: 'Actions', className: 'col-actions' }
   ];
   ```

3. **Dynamic Column Sizing:**
   ```typescript
   // Add dynamic column width calculation
   useEffect(() => {
     const calculateColumnWidths = () => {
       const tableContainer = document.querySelector('.load-results-table-container');
       if (!tableContainer) return;
       
       const containerWidth = tableContainer.clientWidth;
       const minColumnWidth = 100; // Minimum column width
       const totalColumns = columns.length;
       
       // Calculate optimal column widths based on content
       const columnWidths = columns.map(column => {
         switch (column.key) {
           case 'proNumber': return Math.max(containerWidth * 0.08, minColumnWidth);
           case 'origin': return Math.max(containerWidth * 0.12, minColumnWidth);
           case 'destination': return Math.max(containerWidth * 0.12, minColumnWidth);
           case 'equipment': return Math.max(containerWidth * 0.10, 120); // Ensure equipment fits
           case 'actions': return 100; // Fixed width for actions
           default: return Math.max(containerWidth * 0.08, minColumnWidth);
         }
       });
       
       // Apply calculated widths
       columns.forEach((column, index) => {
         const elements = document.querySelectorAll(`.${column.className}`);
         elements.forEach(el => {
           el.style.width = `${columnWidths[index]}px`;
         });
       });
     };
     
     calculateColumnWidths();
     window.addEventListener('resize', calculateColumnWidths);
     
     return () => window.removeEventListener('resize', calculateColumnWidths);
   }, []);
   ```

#### C. Content Security Policy Fixes
1. **Fix CSP Violations:**
   ```typescript
   // Update next.config.js for proper CSP
   const nextConfig = {
     async headers() {
       return [
         {
           source: '/(.*)',
           headers: [
             {
               key: 'Content-Security-Policy',
               value: `
                 script-src 'self' 'nonce-${process.env.CSP_NONCE}' 
                 https://clerk.fcp-portal.com 
                 https://*.clerk.com 
                 https://*.segment.com 
                 https://js.sentry-cdn.com 
                 https://browser.sentry-cdn.com 
                 https://*.sentry.io 
                 https://js.stripe.com 
                 https://*.js.stripe.com 
                 https://maps.googleapis.com 
                 https://*.googletagmanager.com 
                 https://*.google-analytics.com 
                 https://*.vercel.live 
                 https://vercel.live 
                 https://*.hotjar.com 
                 https://static.hotjar.com 
                 https://*.intercom.io 
                 https://widget.intercom.io;
                 script-src-elem 'self' 'nonce-${process.env.CSP_NONCE}' 
                 https://clerk.fcp-portal.com 
                 https://*.clerk.com;
               `.replace(/\s+/g, ' ').trim()
             }
           ]
         }
       ];
     }
   };
   ```

2. **Implement Proper Nonce Usage:**
   ```typescript
   // Add nonce to inline scripts
   import { headers } from 'next/headers';
   
   export default function RootLayout({ children }) {
     const nonce = headers().get('x-nonce') || process.env.CSP_NONCE;
     
     return (
       <html>
         <head>
           <script nonce={nonce}>
             {/* Move inline scripts to external files or use nonce */}
           </script>
         </head>
         <body>{children}</body>
       </html>
     );
   }
   ```

#### D. Error Handling & User Experience
1. **Implement Error Boundaries:**
   ```jsx
   // Add error boundary for API failures
   'use client';
   
   import { Component } from 'react';
   
   class LoadboardErrorBoundary extends Component {
     constructor(props) {
       super(props);
       this.state = { hasError: false, error: null };
     }
     
     static getDerivedStateFromError(error) {
       return { hasError: true, error };
     }
     
     componentDidCatch(error, errorInfo) {
       console.error('Loadboard error:', error, errorInfo);
     }
     
     render() {
       if (this.state.hasError) {
         return (
           <div className="flex items-center justify-center min-h-96 bg-gray-50 rounded-lg">
             <div className="text-center">
               <div className="text-red-500 text-5xl mb-4">⚠️</div>
               <h3 className="text-lg font-semibold text-gray-900 mb-2">
                 Unable to Load Data
               </h3>
               <p className="text-gray-600 mb-4">
                 We're experiencing technical difficulties. Please try again.
               </p>
               <button 
                 onClick={() => window.location.reload()}
                 className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
               >
                 Retry
               </button>
             </div>
           </div>
         );
       }
       
       return this.props.children;
     }
   }
   ```

2. **Add Loading States and Retry Logic:**
   ```jsx
   // Improved data fetching with error handling
   const useLoadboardData = () => {
     const [data, setData] = useState(null);
     const [loading, setLoading] = useState(true);
     const [error, setError] = useState(null);
     const [retryCount, setRetryCount] = useState(0);
     
     const fetchData = async () => {
       try {
         setLoading(true);
         setError(null);
         
         const response = await fetch('/api/v1/airtable-orders/available', {
           headers: {
             'Cache-Control': 'no-cache'
           }
         });
         
         if (!response.ok) {
           throw new Error(`HTTP ${response.status}: ${response.statusText}`);
         }
         
         const result = await response.json();
         setData(result);
         setRetryCount(0); // Reset retry count on success
         
       } catch (err) {
         console.error('Failed to fetch loadboard data:', err);
         setError(err.message);
         
         // Auto-retry up to 3 times with exponential backoff
         if (retryCount < 3) {
           const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
           setTimeout(() => {
             setRetryCount(prev => prev + 1);
             fetchData();
           }, delay);
         }
       } finally {
         setLoading(false);
       }
     };
     
     useEffect(() => {
       fetchData();
     }, []);
     
     return { data, loading, error, retry: fetchData };
   };
   ```

## 4. Technical Implementation Guidelines

**Files to Debug/Fix:**
- `apps/api/src/auth/auth.controller.ts` - Fix /api/v1/auth/me endpoint
- `apps/api/src/airtable-orders/airtable-orders.controller.ts` - Fix airtable orders API
- `apps/web/src/app/org/[orgId]/loadboard/page.tsx` - Column width optimization
- `apps/web/src/app/org/[orgId]/loadboard/columns.tsx` - Column definitions
- `apps/web/next.config.js` - CSP policy fixes
- `apps/web/src/app/globals.css` - Table styling optimization

**Debug Process:**
1. **API Testing:** Use Postman/curl to test endpoints directly
2. **Database Check:** Verify database connectivity and query performance
3. **Column Measurement:** Use browser dev tools to measure optimal column widths
4. **CSP Validation:** Check browser console for CSP violations

## 5. Expected Output & Deliverables

**Define Success:** Successful completion means:
- ✅ **API endpoints returning 200 responses**
- ✅ **Dashboard and loadboard loading data successfully**
- ✅ **All column headers fully visible (Equipment not truncated)**
- ✅ **Optimal column width distribution across full screen**
- ✅ **No CSP violations in browser console**
- ✅ **Proper error handling with user-friendly messages**
- ✅ **Loading states and retry mechanisms working**

**Critical Success Criteria:**
- **API Stability:** All endpoints responding without 500 errors
- **Data Loading:** Dashboard and loadboard displaying data properly
- **Column Optimization:** Full text visible, optimal space utilization
- **User Experience:** Proper loading states and error handling
- **Security:** CSP compliance without functionality loss

## 6. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Ensure your log includes:
- Reference to **API Errors & Column Spacing Fixes**
- **API Issues:** Root causes and solutions for 500 errors
- **Column Optimization:** Width distribution improvements
- **CSP Fixes:** Security policy resolution
- **Error Handling:** User experience improvements

## 7. Immediate Action Required

**Priority Instructions:**
1. **Fix API endpoints** causing 500 errors
2. **Optimize column widths** for full-screen utilization
3. **Resolve CSP violations** for inline scripts
4. **Implement error handling** and loading states
5. **Test thoroughly** across all scenarios

---

**Priority:** 🔴 **CRITICAL** - API failures preventing core functionality

**Estimated Duration:** 3-4 hours

**Success Metric:** Dashboard and loadboard loading successfully with optimized full-width layout and no API errors

**Dependencies:** Backend API access, database connectivity, CSP configuration

**Impact:** Restores core application functionality and provides optimal user experience with full-width layout utilization 