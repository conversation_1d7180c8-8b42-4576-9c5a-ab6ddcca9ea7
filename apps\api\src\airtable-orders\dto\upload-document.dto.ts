import { IsEnum, IsUrl, IsNotEmpty, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum DocumentType {
  BOL = 'bol',
  POD = 'pod', 
  INVOICE = 'invoice'
}

export class UploadDocumentDto {
  @ApiProperty({
    description: 'Type of document being uploaded',
    enum: DocumentType,
    example: DocumentType.BOL
  })
  @IsEnum(DocumentType, {
    message: 'documentType must be one of: bol, pod, invoice'
  })
  @IsNotEmpty()
  documentType: DocumentType;

  @ApiProperty({
    description: 'Secure URL of the uploaded file',
    example: 'https://secure-bucket.s3.amazonaws.com/documents/abc123.pdf'
  })
  @IsUrl({
    protocols: ['https'], // Only allow HTTPS URLs
    require_protocol: true
  }, {
    message: 'fileUrl must be a valid HTTPS URL'
  })
  @Matches(/\.(pdf|jpg|jpeg|png|doc|docx)$/i, {
    message: 'File must be a valid document type (pdf, jpg, jpeg, png, doc, docx)'
  })
  @IsNotEmpty()
  fileUrl: string;
} 