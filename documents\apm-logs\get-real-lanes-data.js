// Get Real Lanes Data from Production Database with ACCURATE Radar Distance Calculations
// Uses FIXED RadarDistanceService for real city-to-city distances and automatic database caching

const fs = require('fs');
const { PrismaClient } = require('@prisma/client');

// Use the exact database URL provided by the user
const DATABASE_URL = 'postgresql://fcp_carrier_portal_user:<EMAIL>/fcp_carrier_portal';

// Mock ConfigService for RadarDistanceService
class MockConfigService {
  get(key) {
    const config = {
      'RADAR_SECRET_KEY': process.env.RADAR_SECRET_KEY || 'prj_live_sk_697d505dee429eb3682100708575a07e20ff5925'
    };
    return config[key];
  }
}

// Rate limiter for API calls
class RateLimiter {
  constructor(maxRequestsPerSecond, windowSizeMs = 1000) {
    this.maxRequestsPerSecond = maxRequestsPerSecond;
    this.windowSizeMs = windowSizeMs;
    this.lastRequestTime = 0;
    this.requestCount = 0;
    this.resetTime = 0;
  }

  async waitForRateLimit() {
    const now = Date.now();
    
    if (now - this.resetTime >= this.windowSizeMs) {
      this.requestCount = 0;
      this.resetTime = now;
    }
    
    if (this.requestCount >= this.maxRequestsPerSecond) {
      const waitTime = this.windowSizeMs - (now - this.resetTime);
      if (waitTime > 0) {
        console.log(`Rate limit reached, waiting ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        this.requestCount = 0;
        this.resetTime = Date.now();
      }
    }
    
    this.requestCount++;
    this.lastRequestTime = now;
  }
}

// RadarDistanceService with FIXED API implementation
class RadarDistanceService {
  constructor(configService, prisma) {
    this.configService = configService;
    this.prisma = prisma;
    this.radarApiKey = configService.get('RADAR_SECRET_KEY');
    this.BASE_URL = 'https://api.radar.io/v1';
    this.geocodingRateLimiter = new RateLimiter(50); // Conservative: 50 req/sec vs 1000 limit
    this.routingRateLimiter = new RateLimiter(10);   // Conservative: 10 req/sec vs 100 limit
  }

  async calculateDistanceByAddress(originAddress, destinationAddress) {
    console.log(`🧮 Calculating distance: ${originAddress} → ${destinationAddress}`);
    
    try {
      // Check cache first
      const cached = await this.getCachedDistance(originAddress, destinationAddress);
      if (cached) {
        console.log(`📋 Found cached distance: ${cached.distanceMiles} miles`);
        return {
          distanceMiles: cached.distanceMiles,
          durationHours: cached.durationHours,
          success: true,
          source: 'cache'
        };
      }

      // Rate limit for geocoding
      await this.geocodingRateLimiter.waitForRateLimit();
      
      // Geocode both addresses
      const originCoords = await this.geocodeAddress(originAddress);
      await this.geocodingRateLimiter.waitForRateLimit();
      const destinationCoords = await this.geocodeAddress(destinationAddress);

      // Rate limit for routing
      await this.routingRateLimiter.waitForRateLimit();
      
      // Calculate route using FIXED API call
      const route = await this.calculateRoute(originCoords, destinationCoords);
      
      // Cache the result using FIXED database logic
      await this.cacheDistance(originAddress, destinationAddress, route.distanceMiles, route.durationHours, 'radar');
      
      console.log(`✅ Radar API success: ${route.distanceMiles} miles, ${route.durationHours} hours`);
      
      return {
        distanceMiles: route.distanceMiles,
        durationHours: route.durationHours,
        success: true,
        source: 'radar'
      };
      
    } catch (error) {
      console.log(`❌ Radar API error: ${error.message}`);
      
      // Fallback to address-based estimation
      const fallback = this.calculateFallbackDistanceFromAddresses(originAddress, destinationAddress);
      
      // Cache fallback result
      await this.cacheDistance(originAddress, destinationAddress, fallback.distanceMiles, fallback.durationHours, 'fallback');
      
      return {
        distanceMiles: fallback.distanceMiles,
        durationHours: fallback.durationHours,
        success: false,
        source: 'fallback'
      };
    }
  }

  async getCachedDistance(originAddress, destinationAddress) {
    try {
      return await this.prisma.distanceCache.findUnique({
        where: {
          originAddress_destinationAddress: {
            originAddress: this.normalizeAddress(originAddress),
            destinationAddress: this.normalizeAddress(destinationAddress)
          }
        }
      });
    } catch (error) {
      console.log(`Cache lookup failed: ${error.message}`);
      return null;
    }
  }

  async cacheDistance(originAddress, destinationAddress, distanceMiles, durationHours, calculatedBy) {
    try {
      await this.prisma.distanceCache.upsert({
        where: {
          originAddress_destinationAddress: {
            originAddress: this.normalizeAddress(originAddress),
            destinationAddress: this.normalizeAddress(destinationAddress)
          }
        },
        update: {
          distanceMiles,
          durationHours,
          calculatedBy,
          updatedAt: new Date()
        },
        create: {
          originAddress: this.normalizeAddress(originAddress),
          destinationAddress: this.normalizeAddress(destinationAddress),
          distanceMiles,
          durationHours,
          calculatedBy
        }
      });
      
      console.log(`💾 Cached distance: ${originAddress} → ${destinationAddress} = ${distanceMiles} miles`);
    } catch (error) {
      console.log(`Failed to cache distance: ${error.message}`);
      // Don't throw - caching failure shouldn't break the distance calculation
    }
  }

  normalizeAddress(address) {
    return address.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  async geocodeAddress(address) {
    const response = await fetch(`${this.BASE_URL}/geocode/forward?query=${encodeURIComponent(address)}`, {
      headers: {
        'Authorization': this.radarApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Radar geocoding failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.addresses || data.addresses.length === 0) {
      throw new Error(`No geocoding results found for address: ${address}`);
    }

    const coords = data.addresses[0];
    console.log(`📍 Geocoded ${address} → ${coords.latitude}, ${coords.longitude}`);
    
    return {
      lat: coords.latitude,
      lng: coords.longitude
    };
  }

  async calculateRoute(origin, destination) {
    // FIXED: Use GET request with query parameters (not POST with JSON body)
    const params = new URLSearchParams({
      origin: `${origin.lat},${origin.lng}`,
      destination: `${destination.lat},${destination.lng}`,
      modes: 'car',
      units: 'imperial'
    });

    const response = await fetch(`${this.BASE_URL}/route/distance?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': this.radarApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Radar routing failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.routes || !data.routes.car) {
      throw new Error('No car route found between coordinates');
    }

    const route = data.routes.car;
    const distanceMiles = route.distance ? route.distance.value / 5280 : 0; // Convert feet to miles (imperial units)
    const durationHours = route.duration ? route.duration.value / 60 : 0; // Convert minutes to hours

    return {
      distanceMiles: Math.round(distanceMiles * 10) / 10, // Round to 1 decimal place
      durationHours: Math.round(durationHours * 10) / 10  // Round to 1 decimal place
    };
  }

  calculateFallbackDistanceFromAddresses(originAddress, destinationAddress) {
    // Simple address similarity calculation for fallback
    const similarity = this.calculateAddressSimilarity(originAddress, destinationAddress);
    
    if (similarity > 0.8) {
      // Very similar addresses - probably same city/area
      return { distanceMiles: 25, durationHours: 0.5 };
    } else if (similarity > 0.6) {
      // Somewhat similar - probably same region
      return { distanceMiles: 150, durationHours: 2.5 };
    } else {
      // Very different - assume cross-country
      return { distanceMiles: 800, durationHours: 13 };
    }
  }

  calculateAddressSimilarity(addr1, addr2) {
    const normalize = (str) => str.toLowerCase().replace(/[^a-z0-9]/g, '');
    const norm1 = normalize(addr1);
    const norm2 = normalize(addr2);
    
    if (norm1 === norm2) return 1;
    
    const longer = norm1.length > norm2.length ? norm1 : norm2;
    const shorter = norm1.length > norm2.length ? norm2 : norm1;
    
    if (longer.length === 0) return 1;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}

async function main() {
  console.log('🚀 Starting Real Lanes Data Extraction with FIXED RadarDistanceService...\n');

  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: DATABASE_URL
      }
    }
  });

  try {
    // Initialize RadarDistanceService with fixed implementation
    const configService = new MockConfigService();
    const radarService = new RadarDistanceService(configService, prisma);

    console.log('📊 Querying loads from production database...\n');

    // Get unique lane combinations from real load data
    const laneGroups = await prisma.load.groupBy({
      by: ['originCity', 'originState', 'destinationCity', 'destinationState'],
      _count: {
        id: true
      },
      where: {
        AND: [
          { originCity: { not: null } },
          { originState: { not: null } },
          { destinationCity: { not: null } },
          { destinationState: { not: null } },
          { status: { not: 'CANCELLED' } }
        ]
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    });

    console.log(`✅ Found ${laneGroups.length} unique lane combinations from your production data\n`);

    const lanes = [];

    // Process each unique lane with REAL distance calculations
    for (let i = 0; i < laneGroups.length; i++) {
      const group = laneGroups[i];
      const originAddress = `${group.originCity}, ${group.originState}`;
      const destinationAddress = `${group.destinationCity}, ${group.destinationState}`;
      
      console.log(`\n[${i + 1}/${laneGroups.length}] Processing: ${originAddress} → ${destinationAddress}`);
      
      // Use the FIXED RadarDistanceService to get real distances
      const distanceResult = await radarService.calculateDistanceByAddress(originAddress, destinationAddress);
      
      const lane = {
        laneId: `LANE_${i + 1}`,
        originCity: group.originCity,
        originState: group.originState,
        destinationCity: group.destinationCity,
        destinationState: group.destinationState,
        distanceMiles: distanceResult.distanceMiles,
        durationHours: distanceResult.durationHours,
        loadCount: group._count.id,
        source: distanceResult.source, // 'radar', 'cache', or 'fallback'
        lastUsed: new Date().toISOString().split('T')[0] // Today's date
      };
      
      lanes.push(lane);
      
      console.log(`✅ Lane complete: ${distanceResult.distanceMiles} miles (${distanceResult.source})`);
    }

    // Generate CSV file
    const csvHeaders = 'Lane ID,Origin City,Origin State,Destination City,Destination State,Distance (Miles),Duration (Hours),Load Count,Source,Last Used';
    const csvRows = lanes.map(lane => 
      `${lane.laneId},"${lane.originCity}","${lane.originState}","${lane.destinationCity}","${lane.destinationState}",${lane.distanceMiles},${lane.durationHours},${lane.loadCount},${lane.source},${lane.lastUsed}`
    ).join('\n');
    
    const csvContent = csvHeaders + '\n' + csvRows;
    
    fs.writeFileSync('lane-library-data.csv', csvContent);

    // Summary
    const radarSuccesses = lanes.filter(l => l.source === 'radar').length;
    const cacheHits = lanes.filter(l => l.source === 'cache').length;
    const fallbacks = lanes.filter(l => l.source === 'fallback').length;
    const totalDistance = lanes.reduce((sum, lane) => sum + lane.distanceMiles, 0);
    const avgDistance = Math.round(totalDistance / lanes.length);

    console.log('\n' + '='.repeat(80));
    console.log('🎉 REAL PRODUCTION LANE DATA EXTRACTION COMPLETE!');
    console.log('='.repeat(80));
    console.log(`📋 Total unique lanes: ${lanes.length}`);
    console.log(`📡 Radar API successful calls: ${radarSuccesses}`);
    console.log(`💾 Cache hits: ${cacheHits}`);
    console.log(`🔄 Fallback estimates: ${fallbacks}`);
    console.log(`📏 Average distance: ${avgDistance} miles`);
    console.log(`📄 File saved: lane-library-data.csv`);
    console.log('='.repeat(80));

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error); 