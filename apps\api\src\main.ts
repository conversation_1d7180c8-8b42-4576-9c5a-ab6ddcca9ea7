// Vercel Function configuration (as recommended in the docs)
export const runtime = 'nodejs';
export const maxDuration = 30; // 30 seconds for API operations

// Initialize OpenTelemetry as early as possible with dynamic import
async function initializeOTel() {
  if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV) {
    try {
      // 🔧 FIX: Use dynamic import to handle ESM module properly
      const otelModule = await import("@vercel/otel");
      if (otelModule && typeof otelModule.registerOTel === 'function') {
        otelModule.registerOTel({
          serviceName: "fcp-portal-api",
        instrumentationConfig: {
          fetch: {
            propagateContextUrls: [], // Start with none, add if needed
          },
        },
      });
      console.log('[MAIN.TS OTEL] OpenTelemetry for Vercel registered for fcp-portal-api.');
      } else {
        console.log('[MAIN.TS OTEL] OpenTelemetry module structure unexpected, skipping.');
      }
    } catch (error) {
      // 🔧 FIX: Make OpenTelemetry initialization non-blocking
      console.warn('[MAIN.TS OTEL] Failed to initialize OpenTelemetry (non-critical):', error.message);
      // Don't throw the error - continue without telemetry
    }
  } else {
    console.log('[MAIN.TS OTEL] OpenTelemetry for Vercel SKIPPED (not in Vercel production/preview/dev).');
  }
}

// Initialize OpenTelemetry immediately but don't block on it
initializeOTel().catch(error => {
  console.warn('[MAIN.TS OTEL] OpenTelemetry initialization failed silently:', error.message);
});

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { ExpressAdapter } from '@nestjs/platform-express';
import express from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

import { GlobalExceptionFilter } from './common/filters/global-exception.filter';

// [MAIN.TS DEBUG] Top-level execution START
console.log('[MAIN.TS DEBUG] Top-level execution START');

let cachedServer: any;

async function bootstrapServer() {
  // [MAIN.TS DEBUG] Entering bootstrapServer
  console.log('[MAIN.TS DEBUG] Entering bootstrapServer');
  if (!cachedServer) {
    // [MAIN.TS DEBUG] cachedServer is null, initializing NestJS...
    console.log('[MAIN.TS DEBUG] cachedServer is null, initializing NestJS...');
    const expressApp = express();
    
    // Configure Express Trust Proxy (required for rate limiting behind Vercel proxy)
    if (process.env.VERCEL || process.env.NODE_ENV === 'production') {
      expressApp.set('trust proxy', 1); // Trust first proxy (Vercel)
      console.log('[MAIN.TS DEBUG] Trust proxy enabled for Vercel/production environment');
    }
    
    // Security Middleware - Apply before NestJS
    console.log('[MAIN.TS DEBUG] Applying security middleware...');
    
    // Helmet for security headers
    expressApp.use(helmet({
      contentSecurityPolicy: false, // Let Next.js handle CSP
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      noSniff: true,
      frameguard: { action: 'deny' },
      xssFilter: true,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: process.env.NODE_ENV === 'production' ? 100 : 1000, // limit each IP to 100 requests per windowMs in production
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting for health checks
        return req.path === '/api/v1/health';
      }
    });
    expressApp.use('/api', limiter);

    // [MAIN.TS DEBUG] Express app created.
    console.log('[MAIN.TS DEBUG] Express app created.');

    const nestApp = await NestFactory.create(AppModule, new ExpressAdapter(expressApp), {
      // logger: process.env.NODE_ENV === 'development' ? new Logger() : false, // Keep or adjust logging as needed
    });
    // [MAIN.TS DEBUG] NestJS app created.
    console.log('[MAIN.TS DEBUG] NestJS app created.');

    // [MAIN.TS DEBUG] Setting global prefix 'api/v1'.
    console.log("[MAIN.TS DEBUG] Setting global prefix 'api/v1'.");
    nestApp.setGlobalPrefix('api/v1');
    // [MAIN.TS DEBUG] Global prefix 'api/v1' set.
    console.log("[MAIN.TS DEBUG] Global prefix 'api/v1' set.");

    // [MAIN.TS DEBUG] Setting up global validation pipe.
    console.log('[MAIN.TS DEBUG] Setting up global validation pipe.');
    nestApp.useGlobalPipes(new ValidationPipe({ 
      whitelist: true, 
      transform: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: false, // Always show validation errors for debugging
      exceptionFactory: (errors) => {
        // Log validation errors for debugging
        console.log('[MAIN.TS VALIDATION] Validation errors:', JSON.stringify(errors, null, 2));
        return new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
      }
    }));
    // [MAIN.TS DEBUG] Global validation pipe set.
    console.log('[MAIN.TS DEBUG] Global validation pipe set.');

    // [MAIN.TS DEBUG] Setting up global exception filter.
    console.log('[MAIN.TS DEBUG] Setting up global exception filter.');
    nestApp.useGlobalFilters(new GlobalExceptionFilter());
    console.log('[MAIN.TS DEBUG] Global exception filter set.');

    // [MAIN.TS DEBUG] Configuring SECURE CORS...
    console.log('[MAIN.TS DEBUG] Configuring SECURE CORS...');
    const allowedOrigins = [
      'https://fcp-portal.com',
      'https://www.fcp-portal.com',
      'https://api.fcp-portal.com',
      'https://clerk.fcp-portal.com',
      // Explicit Vercel deployment URLs
      'https://carrier-portal-q32rlxfk7-amers-projects-baa614f1.vercel.app',
      'https://carrier-portal-api-git-main-amers-projects-baa614f1.vercel.app',
      // Only allow localhost in development
      ...(process.env.NODE_ENV === 'development' ? [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:4200'
      ] : []),
      // Add Vercel preview URLs
      ...(process.env.VERCEL_URL ? [`https://${process.env.VERCEL_URL}`] : []),
      ...(process.env.VERCEL_BRANCH_URL ? [`https://${process.env.VERCEL_BRANCH_URL}`] : []),
    ].filter(Boolean) as string[];

    nestApp.enableCors({
      origin: (requestOrigin, callback) => {
        console.log(`[MAIN.TS CORS DEBUG] Request origin: ${requestOrigin}`);
        console.log(`[MAIN.TS CORS DEBUG] Allowed origins: ${JSON.stringify(allowedOrigins)}`);
        
        // In production, allow requests with no origin only if from Vercel infrastructure
        if (process.env.NODE_ENV === 'production' && !requestOrigin) {
          console.log('[MAIN.TS CORS DEBUG] Production mode: no origin header detected');
          // Allow requests with no origin if we're on Vercel (internal requests)
          if (process.env.VERCEL) {
            console.log('[MAIN.TS CORS DEBUG] Vercel environment: allowing request with no origin (internal request)');
            return callback(null, true);
          }
          console.log('[MAIN.TS CORS DEBUG] Non-Vercel production: origin required but missing');
          callback(new Error('Origin header required'), false);
          return;
        }

        // Allow requests with no origin only in development (mobile apps, curl, Postman, etc.)
        if (!requestOrigin && process.env.NODE_ENV === 'development') {
          console.log('[MAIN.TS CORS DEBUG] Development: Request has no origin. Allowing.');
          return callback(null, true);
        }

        // Check if origin is in allowed list
        if (requestOrigin && allowedOrigins.includes(requestOrigin)) {
          console.log(`[MAIN.TS CORS DEBUG] Origin ALLOWED: ${requestOrigin}`);
          return callback(null, true);
        }

        // Allow Vercel preview deployments
        if (requestOrigin) {
          const isVercelPreview = /.*\.vercel\.app$/.test(requestOrigin);
          if (isVercelPreview) {
            console.log(`[MAIN.TS CORS DEBUG] Vercel preview origin ALLOWED: ${requestOrigin}`);
            return callback(null, true);
          }

          // Allow localhost in development
          const isLocalhost = /^https?:\/\/localhost(:\d+)?$/.test(requestOrigin);
          if (process.env.NODE_ENV === 'development' && isLocalhost) {
            console.log(`[MAIN.TS CORS DEBUG] Development localhost ALLOWED: ${requestOrigin}`);
            return callback(null, true);
          }
        }

        console.log(`[MAIN.TS CORS DEBUG] Origin DISALLOWED: ${requestOrigin}`);
        callback(new Error('Not allowed by CORS'), false);
      },
      methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204,
      maxAge: 86400, // Cache preflight for 24 hours
      allowedHeaders: [
        'Content-Type', 
        'Accept', 
        'Authorization', 
        'X-Requested-With', 
        'X-CSRF-Token',
        'Origin',
        'Cache-Control',
        'Pragma'
      ],
    });
    // [MAIN.TS DEBUG] SECURE CORS configured.
    console.log('[MAIN.TS DEBUG] SECURE CORS configured.');

    // [MAIN.TS DEBUG] WebSocket adapter removed - using N8N for notifications
    console.log('[MAIN.TS DEBUG] WebSocket adapter removed - using N8N for notifications');

    // [MAIN.TS DEBUG] Initializing NestJS app modules...
    console.log('[MAIN.TS DEBUG] Initializing NestJS app modules...');
    await nestApp.init();
    // [MAIN.TS DEBUG] NestJS app modules initialized.
    console.log('[MAIN.TS DEBUG] NestJS app modules initialized.');

    cachedServer = expressApp;
    // [MAIN.TS DEBUG] cachedServer set.
    console.log('[MAIN.TS DEBUG] cachedServer set.');
  } else {
    // [MAIN.TS DEBUG] Using cached server.
    console.log('[MAIN.TS DEBUG] Using cached server.');
  }
  // [MAIN.TS DEBUG] Exiting bootstrapServer
  console.log('[MAIN.TS DEBUG] Exiting bootstrapServer');
  return cachedServer;
}

// [MAIN.TS DEBUG] Defining Vercel handler function.
console.log('[MAIN.TS DEBUG] Defining Vercel handler function.');

// Export the handler function for Vercel
export default async (req: any, res: any) => {
  console.log(`[MAIN.TS DEBUG] Vercel handler invoked. Method: ${req.method}, URL: ${req.url}`);
  
  try {
    const server = await bootstrapServer();
    console.log('[MAIN.TS DEBUG] Server bootstrapped, forwarding request to NestJS.');
    
    // Ensure proper error handling
    server(req, res);
  } catch (error) {
    console.error('[MAIN.TS DEBUG] Error in handler:', error);
    res.status(500).json({ 
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// [MAIN.TS DEBUG] Top-level execution END
console.log('[MAIN.TS DEBUG] Top-level execution END');

// Original bootstrap for local development
async function localBootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('api/v1');
  app.useGlobalPipes(new ValidationPipe({ 
    whitelist: true, 
    forbidNonWhitelisted: true, 
    transform: true, 
    transformOptions: { enableImplicitConversion: true } 
  }));
  app.enableCors({ 
    origin: ["http://localhost:3000", "http://localhost:3001", "http://localhost:4200"],
    credentials: true 
  });
  
  // N8N handles all notifications via email/SMS - no WebSocket needed
  
  await app.listen(process.env.PORT || 3001);
  Logger.log(`🚀 API application is running on: ${await app.getUrl()}`, 'LocalBootstrap');
}

if (process.env.NODE_ENV !== 'production' && !process.env.VERCEL) {
  localBootstrap();
}

// The simple export default async (req,res) should be enough for Vercel.
// The `bootstrapServer` function initializes NestJS only once per cold start.
