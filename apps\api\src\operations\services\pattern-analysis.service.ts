import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

interface LanePattern {
  laneId: string;
  originCity: string;
  originState: string;
  destinationCity: string;
  destinationState: string;
  
  // Rate Intelligence
  averageRate: number;
  medianRate: number;
  rateRange: { min: number; max: number };
  rateConfidence: number; // 0-1 scale
  
  // Equipment Intelligence
  mostCommonEquipment: string;
  equipmentDistribution: Record<string, number>;
  
  // Weight Intelligence
  averageWeight: number;
  typicalWeightRange: { min: number; max: number };
  
  // Timing Intelligence
  averageTransitDays: number;
  seasonalFactors: Record<string, number>;
  
  // Success Metrics
  orderCount: number;
  successRate: number;
  lastUpdated: Date;
}

interface UserPreference {
  userId: string;
  
  // Preferred defaults
  preferredEquipment: Record<string, number>; // equipment -> frequency
  preferredRateAdjustment: number; // +/- percentage from suggested
  preferredWeightRanges: Record<string, { min: number; max: number }>; // equipment -> weight range
  
  // Modification patterns
  rateModificationFrequency: number;
  equipmentOverrideFrequency: number;
  dateAdjustmentPattern: number; // days typically added/subtracted
  
  // Learning metadata
  orderCount: number;
  lastActivity: Date;
  confidenceScore: number; // 0-1 scale
}

interface SmartSuggestion {
  type: 'rate' | 'equipment' | 'weight' | 'timing' | 'warning';
  confidence: number; // 0-1 scale
  suggestion: any;
  reasoning: string;
  source: 'historical' | 'user_preference' | 'market_data' | 'business_rule';
}

@Injectable()
export class PatternAnalysisService {
  private readonly logger = new Logger(PatternAnalysisService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Analyze historical patterns for a specific lane
   */
  async analyzeLanePatterns(originCity: string, originState: string, destinationCity: string, destinationState: string): Promise<LanePattern | null> {
    this.logger.log(`Analyzing patterns for lane: ${originCity}, ${originState} → ${destinationCity}, ${destinationState}`);

    try {
      // Get historical orders for this lane
      const historicalOrders = await this.prisma.load.findMany({
        where: {
          originCity,
          originState,
          destinationCity,
          destinationState,
          rate: { not: null },
          createdAt: {
            gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) // Last 12 months
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 200 // Limit for performance
      });

      if (historicalOrders.length < 3) {
        this.logger.log(`Insufficient data for lane analysis (${historicalOrders.length} orders)`);
        return null;
      }

      const laneId = `${originState}_${destinationState}_${originCity}_${destinationCity}`.replace(/\s+/g, '_');

      // Rate Analysis
      const rates = historicalOrders.filter(o => o.rate).map(o => o.rate!);
      const rateAnalysis = this.analyzeRates(rates);

      // Equipment Analysis
      const equipmentAnalysis = this.analyzeEquipment(historicalOrders);

      // Weight Analysis
      const weights = historicalOrders.filter(o => o.weightLbs).map(o => o.weightLbs!);
      const weightAnalysis = this.analyzeWeights(weights);

      // Timing Analysis
      const timingAnalysis = this.analyzeTimings(historicalOrders);

      // Success Rate Analysis
      const successfulOrders = historicalOrders.filter(o => 
        o.status === 'DELIVERED_EMPTY' || o.status === 'ASSIGNED' || o.status === 'INVOICED'
      ).length;
      const successRate = historicalOrders.length > 0 ? successfulOrders / historicalOrders.length : 0;

      const pattern: LanePattern = {
        laneId,
        originCity,
        originState,
        destinationCity,
        destinationState,
        
        // Rate Intelligence
        averageRate: rateAnalysis.average,
        medianRate: rateAnalysis.median,
        rateRange: rateAnalysis.range,
        rateConfidence: rateAnalysis.confidence,
        
        // Equipment Intelligence
        mostCommonEquipment: equipmentAnalysis.mostCommon,
        equipmentDistribution: equipmentAnalysis.distribution,
        
        // Weight Intelligence
        averageWeight: weightAnalysis.average,
        typicalWeightRange: weightAnalysis.range,
        
        // Timing Intelligence
        averageTransitDays: timingAnalysis.averageDays,
        seasonalFactors: timingAnalysis.seasonalFactors,
        
        // Success Metrics
        orderCount: historicalOrders.length,
        successRate,
        lastUpdated: new Date()
      };

      this.logger.log(`Lane pattern analysis complete: ${pattern.orderCount} orders, ${(pattern.successRate * 100).toFixed(1)}% success rate`);
      return pattern;

    } catch (error) {
      this.logger.error('Error analyzing lane patterns:', error);
      return null;
    }
  }

  /**
   * Analyze user preferences and modification patterns
   */
  async analyzeUserPreferences(userId: string): Promise<UserPreference | null> {
    this.logger.log(`Analyzing user preferences for: ${userId}`);

    try {
      // Get user's historical orders from rawAirtableData
      const userOrders = await this.prisma.load.findMany({
        where: {
          rawAirtableData: {
            path: ['createdBy'],
            equals: userId
          },
          createdAt: {
            gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // Last 3 months
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 100
      });

      if (userOrders.length < 5) {
        this.logger.log(`Insufficient data for user preference analysis (${userOrders.length} orders)`);
        return null;
      }

      // Analyze equipment preferences
      const equipmentPrefs = this.analyzeUserEquipmentPreferences(userOrders);

      // Analyze rate adjustment patterns
      const rateAdjustment = await this.analyzeUserRateAdjustments(userOrders);

      // Analyze weight preferences by equipment
      const weightPrefs = this.analyzeUserWeightPreferences(userOrders);

      // Calculate confidence score based on data volume and consistency
      const confidenceScore = Math.min(userOrders.length / 20, 1) * 
                             this.calculateConsistencyScore(userOrders);

      const preference: UserPreference = {
        userId,
        preferredEquipment: equipmentPrefs,
        preferredRateAdjustment: rateAdjustment,
        preferredWeightRanges: weightPrefs,
        rateModificationFrequency: this.calculateRateModificationFrequency(userOrders),
        equipmentOverrideFrequency: this.calculateEquipmentOverrideFrequency(userOrders),
        dateAdjustmentPattern: this.calculateDateAdjustmentPattern(userOrders),
        orderCount: userOrders.length,
        lastActivity: new Date(),
        confidenceScore
      };

      this.logger.log(`User preference analysis complete: ${preference.orderCount} orders, ${(preference.confidenceScore * 100).toFixed(1)}% confidence`);
      return preference;

    } catch (error) {
      this.logger.error('Error analyzing user preferences:', error);
      return null;
    }
  }

  /**
   * Generate smart suggestions for an order
   */
  async generateSmartSuggestions(
    originCity: string,
    originState: string,
    destinationCity: string,
    destinationState: string,
    userId: string,
    currentValues?: any
  ): Promise<SmartSuggestion[]> {
    this.logger.log(`[PATTERN-DEBUG] Generating suggestions for lane: ${originCity}, ${originState} → ${destinationCity}, ${destinationState}`);
    this.logger.log(`[PATTERN-DEBUG] User ID: ${userId}, Current values: ${JSON.stringify(currentValues)}`);
    
    const suggestions: SmartSuggestion[] = [];

    try {
      // Get lane patterns and user preferences in parallel
      this.logger.log('[PATTERN-DEBUG] Fetching lane patterns and user preferences...');
      const [lanePattern, userPreference] = await Promise.all([
        this.analyzeLanePatterns(originCity, originState, destinationCity, destinationState),
        this.analyzeUserPreferences(userId)
      ]);
      
      this.logger.log(`[PATTERN-DEBUG] Lane pattern found: ${!!lanePattern}, User preference found: ${!!userPreference}`);
      if (lanePattern) {
        this.logger.log(`[PATTERN-DEBUG] Lane pattern - Orders: ${lanePattern.orderCount}, Rate confidence: ${lanePattern.rateConfidence}`);
      }
      if (userPreference) {
        this.logger.log(`[PATTERN-DEBUG] User preference - Orders: ${userPreference.orderCount}, Confidence: ${userPreference.confidenceScore}`);
      }

      // Rate Suggestions
      this.logger.log('[PATTERN-DEBUG] Generating rate suggestions...');
      if (lanePattern && lanePattern.rateConfidence > 0.6) {
        let suggestedRate = lanePattern.medianRate;
        
        // Adjust based on user preferences
        if (userPreference && userPreference.confidenceScore > 0.5) {
          suggestedRate *= (1 + userPreference.preferredRateAdjustment);
          this.logger.log(`[PATTERN-DEBUG] Rate adjusted by user preference: ${lanePattern.medianRate} → ${suggestedRate}`);
        }

        const rateSuggestion = {
          type: 'rate' as const,
          confidence: lanePattern.rateConfidence * (userPreference?.confidenceScore || 0.7),
          suggestion: {
            rate: Math.round(suggestedRate),
            range: lanePattern.rateRange,
            reasoning: `Based on ${lanePattern.orderCount} similar orders`
          },
          reasoning: `Historical median: $${lanePattern.medianRate}, typical range: $${lanePattern.rateRange.min}-$${lanePattern.rateRange.max}`,
          source: userPreference ? ('user_preference' as const) : ('historical' as const)
        };
        
        suggestions.push(rateSuggestion);
        this.logger.log(`[PATTERN-DEBUG] Added rate suggestion: $${rateSuggestion.suggestion.rate} (confidence: ${rateSuggestion.confidence})`);
      } else {
        this.logger.log(`[PATTERN-DEBUG] Skipping rate suggestion - Pattern: ${!!lanePattern}, Confidence: ${lanePattern?.rateConfidence || 0}`);
      }

      // Equipment Suggestions
      if (lanePattern && lanePattern.mostCommonEquipment) {
        let suggestedEquipment = lanePattern.mostCommonEquipment;
        
        // Override with user preference if strong enough
        if (userPreference && userPreference.confidenceScore > 0.7) {
          const userFavorite = Object.entries(userPreference.preferredEquipment)
            .sort(([,a], [,b]) => b - a)[0];
          if (userFavorite && userFavorite[1] > 5) {
            suggestedEquipment = userFavorite[0];
          }
        }

        suggestions.push({
          type: 'equipment',
          confidence: 0.8,
          suggestion: {
            equipment: suggestedEquipment,
            alternatives: Object.keys(lanePattern.equipmentDistribution)
              .filter(eq => eq !== suggestedEquipment)
              .slice(0, 2)
          },
          reasoning: `${Math.round(lanePattern.equipmentDistribution[suggestedEquipment] * 100)}% of similar orders use ${suggestedEquipment}`,
          source: (userPreference?.confidenceScore || 0) > 0.7 ? 'user_preference' : 'historical'
        });
      }

      // Weight Suggestions
      if (lanePattern && lanePattern.averageWeight > 0) {
        suggestions.push({
          type: 'weight',
          confidence: 0.7,
          suggestion: {
            weight: Math.round(lanePattern.averageWeight),
            range: lanePattern.typicalWeightRange
          },
          reasoning: `Typical weight for this route: ${Math.round(lanePattern.averageWeight)} lbs`,
          source: 'historical'
        });
      }

      // Timing Suggestions
      if (lanePattern && lanePattern.averageTransitDays > 0) {
        const currentMonth = new Date().getMonth() + 1;
        const seasonalFactor = lanePattern.seasonalFactors[currentMonth] || 1;
        const adjustedTransitDays = Math.ceil(lanePattern.averageTransitDays * seasonalFactor);

        suggestions.push({
          type: 'timing',
          confidence: 0.8,
          suggestion: {
            transitDays: adjustedTransitDays,
            seasonalNote: seasonalFactor > 1.1 ? 'Expect delays due to seasonal factors' :
                         seasonalFactor < 0.9 ? 'Faster transit expected for this time of year' : null
          },
          reasoning: `Historical average: ${lanePattern.averageTransitDays} days, seasonal adjustment applied`,
          source: 'historical'
        });
      }

      // Validation Warnings
      this.logger.log('[PATTERN-DEBUG] Generating validation warnings...');
      const warnings = await this.generateValidationWarnings(currentValues, lanePattern, userPreference);
      suggestions.push(...warnings);
      this.logger.log(`[PATTERN-DEBUG] Added ${warnings.length} validation warnings`);

      this.logger.log(`[PATTERN-DEBUG] SUCCESS: Generated ${suggestions.length} smart suggestions for lane`);
      this.logger.log(`[PATTERN-DEBUG] Suggestion types: ${suggestions.map(s => s.type).join(', ')}`);
      return suggestions;

    } catch (error) {
      this.logger.error('[PATTERN-ERROR] Error generating smart suggestions:', error);
      this.logger.error('[PATTERN-ERROR] Stack trace:', error.stack);
      return [];
    }
  }

  // Private helper methods
  private analyzeRates(rates: number[]) {
    if (rates.length === 0) return { average: 0, median: 0, range: { min: 0, max: 0 }, confidence: 0 };

    const sorted = rates.sort((a, b) => a - b);
    const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
    const median = sorted[Math.floor(sorted.length / 2)];
    const confidence = Math.min(rates.length / 10, 1); // More data = higher confidence

    return {
      average: Math.round(average),
      median: Math.round(median),
      range: { min: sorted[0], max: sorted[sorted.length - 1] },
      confidence
    };
  }

  private analyzeEquipment(orders: any[]) {
    const equipmentCounts: Record<string, number> = {};
    
    orders.forEach(order => {
      if (order.equipmentRequired) {
        equipmentCounts[order.equipmentRequired] = (equipmentCounts[order.equipmentRequired] || 0) + 1;
      }
    });

    const total = orders.length;
    const distribution: Record<string, number> = {};
    Object.entries(equipmentCounts).forEach(([equipment, count]) => {
      distribution[equipment] = count / total;
    });

    const mostCommon = Object.entries(equipmentCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Dry Van';

    return { mostCommon, distribution };
  }

  private analyzeWeights(weights: number[]) {
    if (weights.length === 0) return { average: 0, range: { min: 0, max: 0 } };

    const average = weights.reduce((sum, weight) => sum + weight, 0) / weights.length;
    const sorted = weights.sort((a, b) => a - b);
    
    return {
      average: Math.round(average),
      range: { 
        min: sorted[Math.floor(sorted.length * 0.1)], // 10th percentile
        max: sorted[Math.floor(sorted.length * 0.9)]  // 90th percentile
      }
    };
  }

  private analyzeTimings(orders: any[]) {
    const transitTimes: number[] = [];
    const monthlyFactors: Record<number, number[]> = {};

    orders.forEach(order => {
      if (order.pickupDateUtc && order.deliveryDateUtc) {
        const pickup = new Date(order.pickupDateUtc);
        const delivery = new Date(order.deliveryDateUtc);
        const days = Math.ceil((delivery.getTime() - pickup.getTime()) / (1000 * 60 * 60 * 24));
        
        if (days > 0 && days < 30) { // Reasonable range
          transitTimes.push(days);
          const month = pickup.getMonth() + 1;
          if (!monthlyFactors[month]) monthlyFactors[month] = [];
          monthlyFactors[month].push(days);
        }
      }
    });

    const averageDays = transitTimes.length > 0 
      ? transitTimes.reduce((sum, days) => sum + days, 0) / transitTimes.length 
      : 2;

    // Calculate seasonal factors
    const seasonalFactors: Record<string, number> = {};
    Object.entries(monthlyFactors).forEach(([month, days]) => {
      const monthAverage = days.reduce((sum, d) => sum + d, 0) / days.length;
      seasonalFactors[month] = monthAverage / averageDays;
    });

    return { averageDays, seasonalFactors };
  }

  private analyzeUserEquipmentPreferences(orders: any[]): Record<string, number> {
    const equipmentCounts: Record<string, number> = {};
    
    orders.forEach(order => {
      if (order.equipmentRequired) {
        equipmentCounts[order.equipmentRequired] = (equipmentCounts[order.equipmentRequired] || 0) + 1;
      }
    });

    return equipmentCounts;
  }

  private async analyzeUserRateAdjustments(userOrders: any[]): Promise<number> {
    // This would require comparing user's rates to historical lane averages
    // For now, return 0 (no adjustment pattern detected)
    return 0;
  }

  private analyzeUserWeightPreferences(orders: any[]): Record<string, { min: number; max: number }> {
    const weightsByEquipment: Record<string, number[]> = {};
    
    orders.forEach(order => {
      if (order.equipmentRequired && order.weightLbs) {
        if (!weightsByEquipment[order.equipmentRequired]) {
          weightsByEquipment[order.equipmentRequired] = [];
        }
        weightsByEquipment[order.equipmentRequired].push(order.weightLbs);
      }
    });

    const preferences: Record<string, { min: number; max: number }> = {};
    Object.entries(weightsByEquipment).forEach(([equipment, weights]) => {
      if (weights.length >= 3) {
        const sorted = weights.sort((a, b) => a - b);
        preferences[equipment] = {
          min: sorted[0],
          max: sorted[sorted.length - 1]
        };
      }
    });

    return preferences;
  }

  private calculateConsistencyScore(orders: any[]): number {
    // Measure how consistent user's choices are
    // Higher score = more predictable patterns
    return 0.8; // Placeholder
  }

  private calculateRateModificationFrequency(orders: any[]): number {
    // Calculate how often user modifies suggested rates
    return 0.3; // Placeholder - 30% of the time
  }

  private calculateEquipmentOverrideFrequency(orders: any[]): number {
    // Calculate how often user overrides equipment suggestions
    return 0.2; // Placeholder - 20% of the time
  }

  private calculateDateAdjustmentPattern(orders: any[]): number {
    // Calculate typical date adjustments (+/- days from suggested)
    return 0; // Placeholder - no typical adjustment
  }

  private async generateValidationWarnings(
    currentValues: any,
    lanePattern: LanePattern | null,
    userPreference: UserPreference | null
  ): Promise<SmartSuggestion[]> {
    const warnings: SmartSuggestion[] = [];

    // Rate validation
    if (currentValues?.rate && lanePattern) {
      const rate = parseFloat(currentValues.rate);
      if (rate < lanePattern.rateRange.min * 0.7) {
        warnings.push({
          type: 'warning',
          confidence: 0.9,
          suggestion: {
            message: 'Rate appears unusually low for this route',
            recommendedAction: `Consider rates between $${lanePattern.rateRange.min} - $${lanePattern.rateRange.max}`
          },
          reasoning: `Historical rates for this route range from $${lanePattern.rateRange.min} to $${lanePattern.rateRange.max}`,
          source: 'business_rule'
        });
      } else if (rate > lanePattern.rateRange.max * 1.3) {
        warnings.push({
          type: 'warning',
          confidence: 0.8,
          suggestion: {
            message: 'Rate appears unusually high for this route',
            recommendedAction: 'Double-check rate calculation'
          },
          reasoning: `Rate is significantly above historical maximum of $${lanePattern.rateRange.max}`,
          source: 'business_rule'
        });
      }
    }

    return warnings;
  }
} 