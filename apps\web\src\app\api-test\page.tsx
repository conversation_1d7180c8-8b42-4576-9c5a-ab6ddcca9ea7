'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';

interface ApiTestResult {
  endpoint: string;
  status: 'loading' | 'success' | 'error';
  data?: unknown;
  error?: string;
}

const testEndpoints = [
  '/api/v1/health',
  '/api/v1/debug',
  '/api/v1/admin/users',
  '/api/v1/airtable-orders/assigned'
];

export default function ApiTestPage() {
  const { getToken, isLoading, user } = useAuth();
  const [results, setResults] = useState<ApiTestResult[]>([]);

  const testEndpoint = useCallback(async (endpoint: string): Promise<ApiTestResult> => {
    try {
      const token = await getToken();
      const response = await fetch(endpoint, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      return {
        endpoint,
        status: response.ok ? 'success' : 'error',
        data: response.ok ? data : undefined,
        error: response.ok ? undefined : `${response.status}: ${JSON.stringify(data)}`
      };
    } catch (error) {
      return {
        endpoint,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [getToken]);

  const runTests = useCallback(async () => {
    if (isLoading) return;

    setResults(testEndpoints.map(endpoint => ({ endpoint, status: 'loading' as const })));

    const testPromises = testEndpoints.map(testEndpoint);
    const testResults = await Promise.all(testPromises);
    setResults(testResults);
  }, [isLoading, testEndpoint]);

  useEffect(() => {
    if (!isLoading) {
      runTests();
    }
  }, [isLoading, runTests]);

  if (isLoading) {
    return <div className="p-8">Loading authentication...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Connectivity Test</h1>
      
      <div className="mb-6 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
        <p>User ID: {user?.id || 'Not authenticated'}</p>
        <p>Environment: {process.env.NODE_ENV}</p>
        <p>API Base URL: {process.env.NEXT_PUBLIC_API_BASE_URL || 'Not set'}</p>
      </div>

      <button 
        onClick={runTests}
        className="mb-6 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Run Tests Again
      </button>

      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={index} className="border rounded p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-mono text-sm">{result.endpoint}</span>
              <span className={`px-2 py-1 rounded text-xs ${
                result.status === 'loading' ? 'bg-yellow-100 text-yellow-800' :
                result.status === 'success' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              }`}>
                {result.status}
              </span>
            </div>
            
            {result.error && (
              <div className="text-red-600 text-sm mb-2">
                Error: {result.error}
              </div>
            )}
            
            {result.data !== undefined && (
              <details className="text-sm">
                <summary className="cursor-pointer text-blue-600">View Response</summary>
                <pre className="mt-2 p-2 bg-gray-50 rounded overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </details>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 