-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('PENDING', 'PAID', 'OVERDUE');

-- AlterTable
ALTER TABLE "loads" ADD COLUMN     "bol_file_url" TEXT,
ADD COLUMN     "bol_uploaded_at" TIMESTAMP(3),
ADD COLUMN     "invoice_file_url" TEXT,
ADD COLUMN     "invoice_uploaded_at" TIMESTAMP(3),
ADD COLUMN     "paid_at" TIMESTAMP(3),
ADD COLUMN     "payment_amount" DOUBLE PRECISION,
ADD COLUMN     "payment_due_date" TIMESTAMP(3),
ADD COLUMN     "payment_notes" TEXT,
ADD COLUMN     "payment_status" "PaymentStatus" DEFAULT 'PENDING',
ADD COLUMN     "pod_file_url" TEXT,
ADD COLUMN     "pod_uploaded_at" TIMESTAMP(3);
