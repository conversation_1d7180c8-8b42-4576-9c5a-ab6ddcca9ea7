import { NextRequest, NextResponse } from 'next/server';
import Airtable from 'airtable';

// Helper function to extract location ID from various formats
function extractLocationId(locationValue: any): string {
  if (!locationValue) return '';
  if (typeof locationValue === 'string') return locationValue;
  if (typeof locationValue === 'object') {
    // Try common object structures
    if (locationValue.id) return locationValue.id;
    if (locationValue.value) return locationValue.value;
    if (locationValue.recordId) return locationValue.recordId;
  }
  return String(locationValue);
}

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

// Configuration constants (matching your Airtable extension)
const ORDERS_TABLE_NAME = "Orders";
const STATUS_FIELD = "Status";
const SO_NUMBER_FIELD = "SO Number";
const COYOTE_LOAD_NO_FIELD = "Coyote Load No.";
const PICKUP_DATE_FIELD = "Pickup Date & Time";
const PICKUP_LOCATION_LINK_FIELD = "Pickup Location";
const DELIVERY_DATE_FIELD = "Delivery Date & Time";
const DELIVERY_LOCATION_LINK_FIELD = "Delivery Location";
const RATE_FIELD = "Rate";
const STATUS_VALUE = "New Order";
const DEFAULT_TIME_HOUR = 12;

export async function POST(request: NextRequest) {
  console.log('=== ORDER CREATION ENDPOINT START ===');
  console.log('Request method:', request.method);
  console.log('Request URL:', request.url);
  
  try {
    // Authentication check - simplified and more robust
    console.log('=== AUTHENTICATION DEBUG ===');
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const token = authHeader?.replace('Bearer ', '') || cookieToken;
    console.log('Auth header present:', !!authHeader);
    console.log('Cookie token present:', !!cookieToken);

    if (!token) {
      console.log('ERROR: No authentication token provided');
      return NextResponse.json({ error: 'No authentication token provided' }, { status: 401 });
    }

    // Validate JWT token format
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      console.log('ERROR: Invalid token format');
      return NextResponse.json({ error: 'Invalid token format' }, { status: 401 });
    }

    // Decode JWT payload directly (since we know the structure)
    let payload;
    try {
      payload = JSON.parse(atob(tokenParts[1]));
      console.log('JWT payload decoded:', { id: payload.id, email: payload.email, role: payload.role });
    } catch {
      console.log('ERROR: Invalid token payload');
      return NextResponse.json({ error: 'Invalid token payload' }, { status: 401 });
    }

    // Check if user has operations access (First Cut Produce team members or admins)
    const userRole = (payload.role || '').toUpperCase();
    const userEmail = payload.email || '';
    const companyName = payload.companyName || '';
    
    const hasOperationsAccess = userRole === 'ADMIN' ||
                              companyName === 'First Cut Produce' ||
                              companyName === 'FIRST CUT PRODUCE' ||
                              userEmail.includes('@firstcutproduce.com');
    
    if (!hasOperationsAccess) {
      console.log('ERROR: Access denied - user not part of First Cut Produce team:', { 
        email: userEmail, 
        company: companyName, 
        role: payload.role 
      });
      return NextResponse.json({ error: 'First Cut Produce team access required' }, { status: 403 });
    }

    console.log('SUCCESS: Operations access granted for First Cut Produce team member:', userEmail);

    // Parse request body with error handling
    console.log('=== REQUEST BODY PARSING ===');
    let body;
    try {
      const rawBody = await request.text();
      console.log('Raw request body:', rawBody);
      body = JSON.parse(rawBody);
      console.log('Parsed request body:', JSON.stringify(body, null, 2));
    } catch (error) {
      console.log('ERROR: Failed to parse request body:', error);
      return NextResponse.json({ 
        error: 'Invalid JSON in request body',
        details: error instanceof Error ? error.message : 'Unknown parsing error'
      }, { status: 400 });
    }
    const { 
      pickupLocationId, 
      deliveryLocationId, 
      pickupDate, 
      daysToDelivery, 
      rate, 
      mode,
      orderMode, // Frontend sends this instead of mode
      soNumber,
      coyoteLoadNo,
      batchData
    } = body;
    
    // Use either mode or orderMode (frontend compatibility)
    const actualMode = mode || orderMode;
    
    // Debug location IDs
    console.log('=== LOCATION ID DEBUG ===');
    console.log('Raw pickupLocationId:', JSON.stringify(pickupLocationId));
    console.log('Raw deliveryLocationId:', JSON.stringify(deliveryLocationId));
    console.log('Extracted pickupLocationId:', extractLocationId(pickupLocationId));
    console.log('Extracted deliveryLocationId:', extractLocationId(deliveryLocationId));

    // Validate required fields
    console.log('=== FIELD VALIDATION DEBUG ===');
    console.log('Field values:', {
      pickupLocationId: pickupLocationId || 'MISSING',
      pickupLocationIdType: typeof pickupLocationId,
      deliveryLocationId: deliveryLocationId || 'MISSING', 
      deliveryLocationIdType: typeof deliveryLocationId,
      pickupDate: pickupDate || 'MISSING',
      rate: rate || 'MISSING',
      daysToDelivery: daysToDelivery === undefined ? 'UNDEFINED' : daysToDelivery,
      mode: mode || 'MISSING',
      orderMode: orderMode || 'MISSING',
      actualMode: actualMode || 'MISSING'
    });
    
    if (!pickupLocationId || !deliveryLocationId || !pickupDate || !rate || daysToDelivery === undefined) {
      const missingFields = [];
      if (!pickupLocationId) missingFields.push('pickupLocationId');
      if (!deliveryLocationId) missingFields.push('deliveryLocationId');
      if (!pickupDate) missingFields.push('pickupDate');
      if (!rate) missingFields.push('rate');
      if (daysToDelivery === undefined) missingFields.push('daysToDelivery');
      
      console.log('Missing required fields:', missingFields);
      return NextResponse.json(
        { 
          error: 'Missing required fields',
          missingFields: missingFields,
          receivedData: { pickupLocationId, deliveryLocationId, pickupDate, rate, daysToDelivery, mode, orderMode, actualMode }
        },
        { status: 400 }
      );
    }

    // Calculate dates and convert to ISO strings for Airtable
    const pickupDateTime = new Date(pickupDate + `T${String(DEFAULT_TIME_HOUR).padStart(2,'0')}:00:00.000Z`);
    if (isNaN(pickupDateTime.getTime())) {
      return NextResponse.json(
        { error: 'Invalid pickup date' },
        { status: 400 }
      );
    }

    const deliveryDateTime = new Date(pickupDateTime.getTime());
    deliveryDateTime.setUTCDate(deliveryDateTime.getUTCDate() + parseInt(daysToDelivery));
    
    // Convert to ISO strings for Airtable
    const pickupDateTimeISO = pickupDateTime.toISOString();
    const deliveryDateTimeISO = deliveryDateTime.toISOString();
    
    console.log('=== DATE CONVERSION DEBUG ===');
    console.log('Original pickupDate:', pickupDate);
    console.log('Pickup DateTime Object:', pickupDateTime);
    console.log('Pickup DateTime ISO:', pickupDateTimeISO);
    console.log('Delivery DateTime ISO:', deliveryDateTimeISO);

    // Prepare records to create
    const recordsToCreate: any[] = [];
    const parseErrors: string[] = [];

    const normalizedMode = (actualMode || '').toLowerCase();
    console.log('=== MODE VALIDATION DEBUG ===');
    console.log('Original mode value:', mode);
    console.log('Original orderMode value:', orderMode);
    console.log('Actual mode used:', actualMode);
    console.log('Normalized mode value:', normalizedMode);
    
    if (normalizedMode === 'single') {
      // Single order mode
      console.log('=== SINGLE ORDER MODE DEBUG ===');
      console.log('Single order fields:', { soNumber: soNumber || 'MISSING', coyoteLoadNo: coyoteLoadNo || 'MISSING' });
      
      if (!soNumber || !coyoteLoadNo) {
        console.log('Missing single order required fields');
        return NextResponse.json(
          { 
            error: 'SO Number and Coyote Load Number are required for single orders',
            receivedData: { soNumber, coyoteLoadNo }
          },
          { status: 400 }
        );
      }

      const recordFields = {
        [STATUS_FIELD]: STATUS_VALUE,
        [SO_NUMBER_FIELD]: soNumber,
        [COYOTE_LOAD_NO_FIELD]: coyoteLoadNo,
        [PICKUP_DATE_FIELD]: pickupDateTimeISO,
        [PICKUP_LOCATION_LINK_FIELD]: [extractLocationId(pickupLocationId)],
        [DELIVERY_DATE_FIELD]: deliveryDateTimeISO,
        [DELIVERY_LOCATION_LINK_FIELD]: [extractLocationId(deliveryLocationId)],
        [RATE_FIELD]: parseFloat(rate)
      };
      
      console.log('=== AIRTABLE RECORD DEBUG (SINGLE) ===');
      console.log('Record fields to send:', JSON.stringify(recordFields, null, 2));
      
      recordsToCreate.push({
        fields: recordFields
      });

    } else if (normalizedMode === 'batch') {
      // Batch order mode
      console.log('=== BATCH ORDER MODE DEBUG ===');
      console.log('Batch data:', batchData || 'MISSING');
      
      if (!batchData || !batchData.trim()) {
        console.log('Missing batch data');
        return NextResponse.json(
          { 
            error: 'Batch data is required for batch orders',
            receivedData: { batchData }
          },
          { status: 400 }
        );
      }

      // Parse batch data - handle both newlines and semicolons
      const pairs = batchData.split(/[;\n]/);
      
      for (const pair of pairs) {
        const trimmedPair = pair.trim();
        if (!trimmedPair) continue;
        
        const parts = trimmedPair.split(',');
        if (parts.length !== 2) {
          parseErrors.push(`Invalid format: "${trimmedPair}"`);
          continue;
        }
        
        const soNum = parts[0].trim();
        const coyoteLoadNum = parts[1].trim();
        
        if (!soNum) {
          parseErrors.push(`Missing SO No: "${trimmedPair}"`);
          continue;
        }
        
        if (!coyoteLoadNum) {
          parseErrors.push(`Missing Coyote No: "${trimmedPair}"`);
          continue;
        }

        const batchRecordFields = {
          [STATUS_FIELD]: STATUS_VALUE,
          [SO_NUMBER_FIELD]: soNum,
          [COYOTE_LOAD_NO_FIELD]: coyoteLoadNum,
          [PICKUP_DATE_FIELD]: pickupDateTimeISO,
          [PICKUP_LOCATION_LINK_FIELD]: [extractLocationId(pickupLocationId)],
          [DELIVERY_DATE_FIELD]: deliveryDateTimeISO,
          [DELIVERY_LOCATION_LINK_FIELD]: [extractLocationId(deliveryLocationId)],
          [RATE_FIELD]: parseFloat(rate)
        };
        
        console.log('=== AIRTABLE RECORD DEBUG (BATCH) ===');
        console.log('Batch record fields to send:', JSON.stringify(batchRecordFields, null, 2));
        
        recordsToCreate.push({
          fields: batchRecordFields
        });
      }
    } else {
      console.log('=== UNKNOWN MODE DEBUG ===');
      console.log('Unknown mode received (original):', mode);
      console.log('Unknown mode received (normalized):', normalizedMode);
      console.log('Expected: "single" or "batch" (case-insensitive)');
              return NextResponse.json(
          { 
            error: 'Invalid mode. Must be "single" or "batch" (case-insensitive)',
            receivedMode: mode,
            receivedOrderMode: orderMode,
            actualMode: actualMode,
            normalizedMode: normalizedMode,
            expectedValues: ['single', 'batch']
          },
          { status: 400 }
        );
    }

    if (recordsToCreate.length === 0) {
      console.log('=== NO RECORDS TO CREATE DEBUG ===');
      console.log('Parse errors:', parseErrors);
              console.log('Mode was:', mode);
        console.log('OrderMode was:', orderMode);
        console.log('ActualMode was:', actualMode);
      return NextResponse.json(
                  { 
            error: 'No valid records to create',
            parseErrors,
            mode,
            orderMode,
            actualMode,
            debugInfo: 'This usually means the batch data was invalid or empty'
          },
        { status: 400 }
      );
    }

    // Create records in Airtable (batch processing like your extension)
    const BATCH_SIZE = 50;
    let createdCount = 0;
    let createdIds: string[] = [];
    const creationErrors: string[] = [];

    for (let i = 0; i < recordsToCreate.length; i += BATCH_SIZE) {
      const batch = recordsToCreate.slice(i, i + BATCH_SIZE);
      
      try {
        const newRecordIds = await base(ORDERS_TABLE_NAME).create(batch);
        createdCount += newRecordIds.length;
        createdIds = createdIds.concat(newRecordIds.map(record => record.id));
      } catch (error) {
        console.error(`Error creating batch starting at index ${i}:`, error);
        creationErrors.push(`Batch ${Math.floor(i / BATCH_SIZE) + 1}: ${error}`);
        break; // Stop on first error like your extension
      }
    }

    return NextResponse.json({
      success: true,
      createdCount,
      createdIds,
      parseErrors,
      creationErrors,
      totalAttempted: recordsToCreate.length
    });

  } catch (error) {
    console.error('Error creating orders:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to create orders' },
      { status: 500 }
    );
  }
}
