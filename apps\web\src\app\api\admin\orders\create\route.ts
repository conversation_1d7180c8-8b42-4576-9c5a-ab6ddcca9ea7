import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import Airtable from 'airtable';

// Configure Airtable
const base = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
}).base(process.env.AIRTABLE_BASE_ID!);

// Configuration constants (matching your Airtable extension)
const ORDERS_TABLE_NAME = "Orders";
const STATUS_FIELD = "Status";
const SO_NUMBER_FIELD = "SO Number";
const COYOTE_LOAD_NO_FIELD = "Coyote Load No.";
const PICKUP_DATE_FIELD = "Pickup Date & Time";
const PICKUP_LOCATION_LINK_FIELD = "Pickup Location";
const PICKUP_APPT_FIELD = "Pickup Appt.";
const DELIVERY_DATE_FIELD = "Delivery Date & Time";
const DELIVERY_LOCATION_LINK_FIELD = "Delivery Location";
const DELIVERY_APPT_FIELD = "Delivery Appt.";
const RATE_FIELD = "Rate";
const STATUS_VALUE = "New Order";
const PICKUP_APPT_VALUE = "Not Requested";
const DELIVERY_APPT_VALUE = "Not Requested";
const DEFAULT_TIME_HOUR = 12;

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // TODO: Add admin role check here when user roles are implemented

    const body = await request.json();
    const { 
      pickupLocationId, 
      deliveryLocationId, 
      pickupDate, 
      daysToDelivery, 
      rate, 
      mode,
      soNumber,
      coyoteLoadNo,
      batchData
    } = body;

    // Validate required fields
    if (!pickupLocationId || !deliveryLocationId || !pickupDate || !rate || daysToDelivery === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Calculate dates
    const pickupDateTime = new Date(pickupDate + `T${String(DEFAULT_TIME_HOUR).padStart(2,'0')}:00:00.000Z`);
    if (isNaN(pickupDateTime.getTime())) {
      return NextResponse.json(
        { error: 'Invalid pickup date' },
        { status: 400 }
      );
    }

    const deliveryDateTime = new Date(pickupDateTime.getTime());
    deliveryDateTime.setUTCDate(deliveryDateTime.getUTCDate() + parseInt(daysToDelivery));

    // Prepare records to create
    const recordsToCreate: any[] = [];
    let parseErrors: string[] = [];

    if (mode === 'single') {
      // Single order mode
      if (!soNumber || !coyoteLoadNo) {
        return NextResponse.json(
          { error: 'SO Number and Coyote Load Number are required for single orders' },
          { status: 400 }
        );
      }

      recordsToCreate.push({
        fields: {
          [STATUS_FIELD]: { name: STATUS_VALUE },
          [SO_NUMBER_FIELD]: soNumber,
          [COYOTE_LOAD_NO_FIELD]: coyoteLoadNo,
          [PICKUP_DATE_FIELD]: pickupDateTime,
          [PICKUP_LOCATION_LINK_FIELD]: [{ id: pickupLocationId }],
          [PICKUP_APPT_FIELD]: { name: PICKUP_APPT_VALUE },
          [DELIVERY_DATE_FIELD]: deliveryDateTime,
          [DELIVERY_LOCATION_LINK_FIELD]: [{ id: deliveryLocationId }],
          [DELIVERY_APPT_FIELD]: { name: DELIVERY_APPT_VALUE },
          [RATE_FIELD]: parseFloat(rate)
        }
      });

    } else if (mode === 'batch') {
      // Batch order mode
      if (!batchData || !batchData.trim()) {
        return NextResponse.json(
          { error: 'Batch data is required for batch orders' },
          { status: 400 }
        );
      }

      // Parse batch data (same logic as your Airtable extension)
      const pairs = batchData.split(';');
      
      for (const pair of pairs) {
        const trimmedPair = pair.trim();
        if (!trimmedPair) continue;
        
        const parts = trimmedPair.split(',');
        if (parts.length !== 2) {
          parseErrors.push(`Invalid format: "${trimmedPair}"`);
          continue;
        }
        
        const soNum = parts[0].trim();
        const coyoteLoadNum = parts[1].trim();
        
        if (!soNum) {
          parseErrors.push(`Missing SO No: "${trimmedPair}"`);
          continue;
        }
        
        if (!coyoteLoadNum) {
          parseErrors.push(`Missing Coyote No: "${trimmedPair}"`);
          continue;
        }

        recordsToCreate.push({
          fields: {
            [STATUS_FIELD]: { name: STATUS_VALUE },
            [SO_NUMBER_FIELD]: soNum,
            [COYOTE_LOAD_NO_FIELD]: coyoteLoadNum,
            [PICKUP_DATE_FIELD]: pickupDateTime,
            [PICKUP_LOCATION_LINK_FIELD]: [{ id: pickupLocationId }],
            [PICKUP_APPT_FIELD]: { name: PICKUP_APPT_VALUE },
            [DELIVERY_DATE_FIELD]: deliveryDateTime,
            [DELIVERY_LOCATION_LINK_FIELD]: [{ id: deliveryLocationId }],
            [DELIVERY_APPT_FIELD]: { name: DELIVERY_APPT_VALUE },
            [RATE_FIELD]: parseFloat(rate)
          }
        });
      }
    }

    if (recordsToCreate.length === 0) {
      return NextResponse.json(
        { 
          error: 'No valid records to create',
          parseErrors 
        },
        { status: 400 }
      );
    }

    // Create records in Airtable (batch processing like your extension)
    const BATCH_SIZE = 50;
    let createdCount = 0;
    let createdIds: string[] = [];
    let creationErrors: string[] = [];

    for (let i = 0; i < recordsToCreate.length; i += BATCH_SIZE) {
      const batch = recordsToCreate.slice(i, i + BATCH_SIZE);
      
      try {
        const newRecordIds = await base(ORDERS_TABLE_NAME).create(batch);
        createdCount += newRecordIds.length;
        createdIds = createdIds.concat(newRecordIds.map(record => record.id));
      } catch (error) {
        console.error(`Error creating batch starting at index ${i}:`, error);
        creationErrors.push(`Batch ${Math.floor(i / BATCH_SIZE) + 1}: ${error}`);
        break; // Stop on first error like your extension
      }
    }

    return NextResponse.json({
      success: true,
      createdCount,
      createdIds,
      parseErrors,
      creationErrors,
      totalAttempted: recordsToCreate.length
    });

  } catch (error) {
    console.error('Error creating orders:', error);
    return NextResponse.json(
      { error: 'Failed to create orders' },
      { status: 500 }
    );
  }
}
