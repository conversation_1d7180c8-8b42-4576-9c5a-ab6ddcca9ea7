import { Controller, Get, Patch, Param, Body, UseGuards, HttpCode, HttpStatus, Post, Request, Put, Query } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminGuard } from '../auth/auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiProperty } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { IsBoolean, IsEnum, IsOptional, IsString, IsNumber, Min, Max, IsEmail, IsArray } from 'class-validator';

export enum UserRole {
  CARRIER = 'CARRIER',
  ADMIN = 'ADMIN'
}

export class UpdateUserRoleDto {
  @IsEnum(UserRole)
  role: 'CARRIER' | 'ADMIN';
}

export class VerifyCarrierDto {
  @IsBoolean()
  isVerified: boolean;
}

export interface SystemSettingsDto {
  // General Settings
  platformName?: string;
  supportEmail?: string;
  maintenanceMode?: boolean;
  
  // Load Management Settings
  autoAssignLoads?: boolean;
  requireLoadApproval?: boolean;
  maxLoadsPerCarrier?: number;
  loadExpirationHours?: number;
  
  // Carrier Verification Settings
  requireInsuranceVerification?: boolean;
  requireDotVerification?: boolean;
  autoApproveCarriers?: boolean;
  verificationReminderDays?: number;
  
  // Communication Settings
  enableEmailNotifications?: boolean;
  enableSmsNotifications?: boolean;
  notificationFrequency?: 'REAL_TIME' | 'HOURLY' | 'DAILY';
  
  // Security Settings
  requireTwoFactor?: boolean;
  sessionTimeoutMinutes?: number;
  maxLoginAttempts?: number;
  passwordExpirationDays?: number;
  
  // Billing Settings
  defaultPaymentTerms?: string;
  latePaymentFeePercent?: number;
  invoiceReminderDays?: number;
  
  // System Limits
  maxFileUploadSize?: number;
  rateLimitPerMinute?: number;
  
  // Feature Flags
  enableLoadTracking?: boolean;
  enableRealTimeUpdates?: boolean;
  enableAdvancedReporting?: boolean;
  enableApiAccess?: boolean;
  
  // Maintenance Settings
  maintenanceWindowStart?: string;
  maintenanceWindowEnd?: string;
  backupFrequency?: 'HOURLY' | 'DAILY' | 'WEEKLY';
}

export class UpdateSystemSettingsDto {
  @IsOptional()
  @IsString()
  platformName?: string;

  @IsOptional()
  @IsEmail()
  supportEmail?: string;

  @IsOptional()
  @IsBoolean()
  maintenanceMode?: boolean;

  @IsOptional()
  @IsBoolean()
  autoAssignLoads?: boolean;

  @IsOptional()
  @IsBoolean()
  requireLoadApproval?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxLoadsPerCarrier?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(168)
  loadExpirationHours?: number;

  @IsOptional()
  @IsBoolean()
  requireInsuranceVerification?: boolean;

  @IsOptional()
  @IsBoolean()
  requireDotVerification?: boolean;

  @IsOptional()
  @IsBoolean()
  autoApproveCarriers?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  verificationReminderDays?: number;

  @IsOptional()
  @IsBoolean()
  enableEmailNotifications?: boolean;

  @IsOptional()
  @IsBoolean()
  enableSmsNotifications?: boolean;

  @IsOptional()
  @IsEnum(['REAL_TIME', 'HOURLY', 'DAILY'])
  notificationFrequency?: 'REAL_TIME' | 'HOURLY' | 'DAILY';

  @IsOptional()
  @IsBoolean()
  requireTwoFactor?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(480)
  sessionTimeoutMinutes?: number;

  @IsOptional()
  @IsNumber()
  @Min(3)
  @Max(10)
  maxLoginAttempts?: number;

  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(365)
  passwordExpirationDays?: number;

  @IsOptional()
  @IsString()
  defaultPaymentTerms?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(50)
  latePaymentFeePercent?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(30)
  invoiceReminderDays?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxFileUploadSize?: number;

  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(1000)
  rateLimitPerMinute?: number;

  @IsOptional()
  @IsBoolean()
  enableLoadTracking?: boolean;

  @IsOptional()
  @IsBoolean()
  enableRealTimeUpdates?: boolean;

  @IsOptional()
  @IsBoolean()
  enableAdvancedReporting?: boolean;

  @IsOptional()
  @IsBoolean()
  enableApiAccess?: boolean;

  @IsOptional()
  @IsString()
  maintenanceWindowStart?: string;

  @IsOptional()
  @IsString()
  maintenanceWindowEnd?: string;

  @IsOptional()
  @IsEnum(['HOURLY', 'DAILY', 'WEEKLY'])
  backupFrequency?: 'HOURLY' | 'DAILY' | 'WEEKLY';
}

export class AdminBidResponseDto {
  @IsEnum(['accepted', 'countered', 'declined'])
  @ApiProperty({
    description: 'Admin response type',
    enum: ['accepted', 'countered', 'declined'],
    example: 'accepted'
  })
  response: 'accepted' | 'countered' | 'declined';

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @ApiProperty({
    description: 'Counter offer amount (required if response is countered)',
    example: 1400.00,
    required: false
  })
  counterOfferAmount?: number;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Admin notes about the response',
    example: 'Price adjusted due to fuel costs',
    required: false
  })
  notes?: string;
}

@ApiTags('admin')
@ApiBearerAuth()
@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('debug/me')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Get current user info (debug endpoint)' })
  @ApiResponse({ status: 200, description: 'Returns current user information' })
  async getCurrentUserInfo(@Param() params: any, @Body() body: any, @Request() req: any): Promise<any> {
    return this.adminService.getCurrentUserInfo(req);
  }

  @Post('debug/promote-first-admin')
  @ApiOperation({ summary: 'Promote first user to admin (debug endpoint)' })
  @ApiResponse({ status: 200, description: 'Promotes first user to admin role' })
  async promoteFirstAdmin(): Promise<any> {
    return this.adminService.promoteFirstAdmin();
  }

  @Get('users')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get all users (admin only)' })
  @ApiResponse({ status: 200, description: 'Returns all users in the system' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async getAllUsers(): Promise<any> {
    return this.adminService.getAllUsers();
  }

  @Patch('users/:userId/verify')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Verify/unverify carrier (admin only)' })
  @ApiResponse({ status: 200, description: 'Carrier verification status updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'User or carrier profile not found' })
  async verifyCarrier(
    @Param('userId') userId: string,
    @Body() verifyCarrierDto: VerifyCarrierDto,
  ): Promise<any> {
    return this.adminService.verifyCarrier(userId, verifyCarrierDto.isVerified);
  }

  @Patch('users/:userId/role')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update user role (admin only)' })
  @ApiResponse({ status: 200, description: 'User role updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserRole(
    @Param('userId') userId: string,
    @Body() updateUserRoleDto: UpdateUserRoleDto,
  ): Promise<any> {
    return this.adminService.updateUserRole(userId, updateUserRoleDto.role);
  }

  @Get('settings')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get system settings (admin only)' })
  @ApiResponse({ status: 200, description: 'Returns current system settings' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async getSystemSettings(): Promise<SystemSettingsDto> {
    return this.adminService.getSystemSettings();
  }

  @Put('settings')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update system settings (admin only)' })
  @ApiResponse({ status: 200, description: 'System settings updated successfully' })
  @ApiResponse({ status:403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 400, description: 'Invalid settings data' })
  async updateSystemSettings(@Body() updateData: UpdateSystemSettingsDto): Promise<{ message: string; settings: SystemSettingsDto }> {
    const updatedSettings = await this.adminService.updateSystemSettings(updateData);
    return {
      message: 'System settings updated successfully',
      settings: updatedSettings
    };
  }

  @Post('settings/reset')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Reset system settings to defaults (admin only)' })
  @ApiResponse({ status: 200, description: 'System settings reset to defaults' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async resetSystemSettings(): Promise<{ message: string; settings: SystemSettingsDto }> {
    const defaultSettings = await this.adminService.resetSystemSettings();
    return {
      message: 'System settings reset to defaults',
      settings: defaultSettings
    };
  }

  @Get('bids/pending')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get all pending bids for admin review',
    description: 'Retrieve all pending bids with load details and carrier information for admin management'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'List of pending bids retrieved successfully'
  })
  async getPendingBids(@Request() req: any): Promise<any[]> {
    return await this.adminService.getPendingBids();
  }

  @Get('bids')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get all bids with filtering',
    description: 'Retrieve all bids with advanced filtering options for admin dashboard'
  })
  async getAllBids(
    @Request() req: any,
    @Query('status') status?: string,
    @Query('carrierId') carrierId?: string,
    @Query('loadId') loadId?: string,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string
  ): Promise<{ bids: any[]; totalCount: number; page: number; pageSize: number }> {
    const filters = {
      status,
      carrierId,
      loadId,
      dateFrom: dateFrom ? new Date(dateFrom) : undefined,
      dateTo: dateTo ? new Date(dateTo) : undefined,
      page: page ? parseInt(page) : 1,
      limit: limit ? parseInt(limit) : 20
    };
    
    return await this.adminService.getAllBidsWithFilters(filters);
  }

  @Post('bids/:bidId/respond')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Respond to a bid',
    description: 'Accept, counter, or decline a carrier bid with optional notes and counter-offer amount'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Bid response processed successfully'
  })
  async respondToBid(
    @Param('bidId') bidId: string,
    @Body() responseDto: AdminBidResponseDto,
    @Request() req: any
  ): Promise<any> {
    return await this.adminService.respondToBid(
      bidId, 
      responseDto, 
      req.user.airtableUserId
    );
  }

  @Get('bids/:bidId/history')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get bid negotiation history',
    description: 'Retrieve complete negotiation history for a specific bid'
  })
  async getBidHistory(@Param('bidId') bidId: string): Promise<any[]> {
    return await this.adminService.getBidHistory(bidId);
  }

  @Get('bidding/stats')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get bidding statistics',
    description: 'Retrieve bidding performance metrics and statistics for admin dashboard'
  })
  async getBiddingStats(@Request() req: any): Promise<any> {
    return await this.adminService.getBiddingStatistics();
  }

  @Post('bids/process-expired')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Process expired bids',
    description: 'Manually trigger processing of expired bids (normally runs automatically)'
  })
  async processExpiredBids(@Request() req: any): Promise<any> {
    return await this.adminService.processExpiredBids();
  }

  @Get('bids/expiring-soon')
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get bids expiring soon',
    description: 'Retrieve bids that will expire within the specified hours (default: 2 hours)'
  })
  async getBidsExpiringSoon(
    @Request() req: any,
    @Query('hours') hours?: string
  ): Promise<any[]> {
    const hoursFromNow = hours ? parseInt(hours) : 2;
    return await this.adminService.getBidsExpiringSoon(hoursFromNow);
  }
} 