# APM Task Assignment: Backend Authentication Migration

## 1. Agent Role & APM Context

**Introduction:** You are activated as an Implementation Agent within the Agentic Project Management (APM) framework for the **Carrier Portal Authentication Migration** project.

**Your Role:** You are **Agent_API_Backend**, responsible for migrating the existing NestJS backend from Clerk authentication to the new Airtable + N8N JWT system with meticulous attention to detail and comprehensive logging.

**APM Workflow:** You interact with the Manager Agent via the User communication conduit. Upon completion, you must log your work comprehensively to the project's Memory Bank following the established format.

## 2. Onboarding / Context from Prior Work

**Prerequisite Context:** This task builds directly upon Phase 2.7-T1 (N8N Authentication Workflow Setup) which has established:
- ✅ **N8N Authentication Workflow**: Deployed and configured with 4 operational endpoints
- ✅ **Airtable UserManagement Schema**: Complete table with all required fields (Email, First Name, Last Name, Password, Date, Role, Company Name, Contact Phone, Verification Status, MC Number, DOT Number)
- ✅ **Authentication Endpoints**: POST /signup, POST /login, GET /user, POST /user with JWT integration
- ✅ **Security Implementation**: SHA256 password hashing, JWT token generation with {id, email} payload

**Key Integration Points:** The N8N workflow provides JWT tokens with structure `{id: "airtable_record_id", email: "user_email"}` that must be integrated into the existing NestJS authentication system.

## 3. Task Assignment

**Reference Implementation Plan:** This assignment corresponds to **Phase 2.7, Task T2** in the Implementation Plan - "Backend Authentication Migration".

**Strategic Context:** Replace all Clerk authentication dependencies with the new N8N/Airtable JWT system while maintaining all existing functionality and user experience.

**Objective:** Completely migrate the NestJS backend authentication system from Clerk to Airtable + N8N JWT, ensuring seamless integration with existing features and maintaining data integrity.

## 4. Detailed Action Steps

### **Step 1: Remove Clerk Dependencies**

**Actions:**
1. **Package Cleanup**
   - Remove all Clerk packages from `apps/api/package.json`:
     - `@clerk/clerk-sdk-node`
     - `@clerk/backend` 
     - Any other Clerk-related dependencies
   - Run `pnpm install` to clean up dependencies

2. **Code Cleanup**
   - Remove all Clerk imports throughout the codebase
   - Delete Clerk environment variables from `.env` files
   - Remove Clerk configuration files and initialization code
   - Search for and remove all references to:
     - `ClerkGuard`
     - `clerk.users`
     - `clerk_user_id`
     - Any Clerk-specific middleware or decorators

3. **Database Schema Preparation**
   - Identify all database tables/models that reference `clerk_user_id`
   - Prepare migration scripts to rename `clerk_user_id` to `airtable_user_id`
   - Update Prisma schema to reflect the new user ID field naming

**Guidance Notes from Implementation Plan:**
- **Comprehensive Removal**: Ensure no Clerk references remain to avoid conflicts
- **Migration Safety**: Create backup of current authentication system before changes
- **Schema Consistency**: Maintain referential integrity during user ID field migration

### **Step 2: Implement New JWT Authentication System**

**Actions:**
1. **Create JWT Authentication Guard**
   - Implement new `JwtAuthGuard` class to replace `ClerkGuard`
   - **JWT Token Validation Logic:**
     ```typescript
     // Expected JWT payload structure from N8N:
     interface JwtPayload {
       id: string;      // Airtable record ID
       email: string;   // User email
       iat: number;     // Issued at
       exp: number;     // Expiration
     }
     ```
   - Validate JWT tokens using the same secret configured in N8N
   - Extract user information from JWT payload
   - Handle token expiration and validation errors appropriately

2. **Implement Authentication Middleware**
   - Create middleware to validate JWT tokens from request headers
   - Extract token from `Authorization: Bearer <token>` header
   - Populate request context with authenticated user information
   - Handle authentication failures with appropriate HTTP responses

3. **User Context Management**
   - Create new user context interface matching Airtable schema:
     ```typescript
     interface AuthenticatedUser {
       id: string;                    // Airtable record ID
       email: string;
       firstName: string;
       lastName: string;
       role: string;
       companyName: string;
       contactPhone: string;
       verificationStatus: string;
       mcNumber?: string;
       dotNumber?: string;
     }
     ```
   - Implement user context provider for authenticated requests
   - Update all protected routes to use new authentication context

**Guidance Notes from Implementation Plan:**
- **Token Validation**: Use the same JWT secret as configured in N8N workflow
- **Error Handling**: Implement comprehensive error handling for authentication failures
- **Context Consistency**: Ensure user context matches Airtable schema structure

### **Step 3: Update User Management Logic**

**Actions:**
1. **Replace User ID References**
   - Update all database queries that reference `clerk_user_id` to use `airtable_user_id`
   - Modify user profile endpoints to work with Airtable user data
   - Update user session management to use JWT-based authentication
   - Ensure all user-related operations use the new authentication system

2. **Integrate with Airtable User Data**
   - Create service to fetch user details from Airtable when needed
   - Implement user profile update functionality that syncs with Airtable
   - Handle user role and verification status from Airtable data
   - Implement proper error handling for Airtable integration failures

3. **Update Authentication Integration Points**
   - **Carrier Profile Management**: Update to use Airtable user IDs
   - **Load Assignment**: Modify user associations to use new ID system
   - **Bidding System**: Update user references for future bidding implementation
   - **Admin Verification**: Update admin workflows to use new user system
   - **Document Management**: Ensure document ownership uses correct user IDs

**Guidance Notes from Implementation Plan:**
- **Data Integrity**: Ensure all user associations remain intact during migration
- **Role Management**: Properly handle user roles from Airtable verification status
- **Admin Functions**: Maintain admin functionality with new authentication system

### **Step 4: Authentication Endpoint Integration**

**Actions:**
1. **Remove Existing Auth Endpoints**
   - Remove any existing authentication endpoints that duplicate N8N functionality
   - Ensure no conflicts between old and new authentication systems
   - Clean up any auth-related routes that are no longer needed

2. **Integrate N8N Authentication Endpoints**
   - Document N8N authentication endpoints for frontend integration:
     - POST /auth/signup - User registration
     - POST /auth/login - User login with JWT response
     - GET /auth/user - Get authenticated user profile
     - POST /auth/user - Update user profile
   - Update API documentation to reflect new authentication flow
   - Ensure CORS configuration allows frontend access to N8N endpoints

3. **User Session Management**
   - Implement JWT token refresh logic if needed
   - Handle user logout functionality (token invalidation)
   - Update session timeout handling to work with JWT expiration
   - Implement proper security headers for authentication responses

**Guidance Notes from Implementation Plan:**
- **Endpoint Coordination**: Ensure no conflicts between NestJS and N8N endpoints
- **Frontend Integration**: Provide clear documentation for frontend authentication changes
- **Security Standards**: Maintain security best practices with new authentication system

### **Step 5: Database Migration & Testing**

**Actions:**
1. **Execute Database Migration**
   - Run migration scripts to update user ID field references
   - Verify all foreign key relationships are maintained
   - Test data integrity after migration
   - Create rollback scripts in case of issues

2. **Comprehensive Testing**
   - Test all protected routes with new JWT authentication
   - Verify user profile functionality works correctly
   - Test admin functions with new authentication system
   - Validate load booking and assignment functionality
   - Test document upload/management with new user system

3. **Integration Testing**
   - Test complete authentication flow from signup to protected operations
   - Verify user role and permission handling
   - Test error scenarios (invalid tokens, expired tokens, etc.)
   - Validate all existing features work with new authentication

**Guidance Notes from Implementation Plan:**
- **Migration Safety**: Ensure data backup before running migration scripts
- **Comprehensive Testing**: Test all user-related functionality thoroughly
- **Performance Validation**: Ensure new authentication doesn't impact performance

## 5. Expected Output & Deliverables

**Define Success:** Successful completion requires:
- ✅ Complete removal of all Clerk dependencies from backend
- ✅ Functional JWT authentication system integrated with N8N endpoints
- ✅ All existing features working with new authentication system
- ✅ Database migration completed with data integrity maintained
- ✅ Comprehensive testing passed for all authentication scenarios
- ✅ Updated API documentation reflecting new authentication flow

**Specific Deliverables:**
1. **Migrated Authentication System**
   - New JwtAuthGuard replacing ClerkGuard
   - Updated authentication middleware and user context
   - Integrated N8N JWT token validation

2. **Updated Database Schema**
   - Migrated user ID references from clerk_user_id to airtable_user_id
   - Verified data integrity and foreign key relationships
   - Rollback scripts for emergency recovery

3. **Integration Documentation**
   - Updated API documentation with new authentication flow
   - Integration notes for frontend team (Phase 2.7-T3)
   - Testing results and validation reports

4. **Code Quality**
   - Clean removal of all Clerk code and dependencies
   - Comprehensive error handling for new authentication system
   - Proper logging and monitoring for authentication events

## 6. Memory Bank Logging Instructions (Mandatory)

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Adhere strictly to the logging format defined in `cursor-agent-management/prompts/02_Utility_Prompts_And_Format_Definitions/Memory_Bank_Log_Format.md`. Ensure your log includes:

- **Task Reference:** Phase 2.7 / Task T2 (Backend Authentication Migration)
- **Clear Summary:** High-level overview of Clerk to N8N/Airtable migration
- **Detailed Actions:** Step-by-step summary of dependency removal, JWT implementation, and database migration
- **Critical Outputs:** Code changes, database migration results, integration points
- **Key Decisions:** Authentication architecture choices and implementation details
- **Issues & Resolutions:** Any challenges encountered during migration
- **Integration Notes:** Important details for **Agent_Frontend_Dev** (P2.7-T3)

**Logging Priority:** Focus on information critical for the frontend migration task, including:
- JWT token structure and validation requirements
- User context interface changes
- Authentication endpoint changes
- Any breaking changes that affect frontend integration

## 7. Clarification Instruction

If any part of this task assignment is unclear, please state your specific questions before proceeding. This includes:
- N8N JWT token structure or validation requirements
- Specific database migration concerns
- Integration requirements with existing system components
- User role and permission handling details

---

**Critical Success Factor:** This task must maintain complete backward compatibility for all existing features while seamlessly integrating the new authentication system. Thorough testing is essential to ensure no functionality is broken during the migration.

**Ready to Begin:** Confirm you understand the migration scope and have access to the existing codebase and database before starting implementation. 