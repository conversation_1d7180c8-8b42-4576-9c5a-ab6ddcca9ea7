import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  UseGuards, 
  Request, 
  BadRequestException,
  ForbiddenException,
  Logger,
  Query,
  Param,
  InternalServerErrorException,
  NotFoundException,
  RequestTimeoutException
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { AuthenticatedRequest } from '../auth/authenticated-request.interface';
import { OperationsService, Lane } from './operations.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { AuthService } from '../auth/auth.service';

interface RequestMetrics {
  endpoint: string;
  successCount: number;
  failureCount: number;
  averageResponseTime: number;
  lastFailureTime: number;
  consecutiveFailures: number;
}

@ApiTags('operations')
@ApiBearerAuth()
@Controller('operations')
@UseGuards(AuthGuard)
export class OperationsController {
  private readonly logger = new Logger(OperationsController.name);
  private requestMetrics = new Map<string, RequestMetrics>();
  
  // Circuit breaker configuration
  private readonly CIRCUIT_BREAKER_CONFIG = {
    failureThreshold: 5,
    recoveryTimeout: 30000, // 30 seconds
    requestTimeout: 5000 // 5 second timeout
  };

  constructor(
    private readonly operationsService: OperationsService,
    private readonly authService: AuthService,
  ) {}

  // Circuit breaker helper method
  private checkCircuitBreaker(endpoint: string): void {
    const metrics = this.requestMetrics.get(endpoint);
    if (!metrics) return;

    const now = Date.now();
    
    // If we have too many consecutive failures and it's recent
    if (metrics.consecutiveFailures >= this.CIRCUIT_BREAKER_CONFIG.failureThreshold &&
        now - metrics.lastFailureTime < this.CIRCUIT_BREAKER_CONFIG.recoveryTimeout) {
      const waitTime = Math.ceil((this.CIRCUIT_BREAKER_CONFIG.recoveryTimeout - (now - metrics.lastFailureTime)) / 1000);
      throw new RequestTimeoutException(`Service temporarily unavailable. Please try again in ${waitTime} seconds.`);
    }
  }

  // Record request metrics
  private recordMetrics(endpoint: string, success: boolean, responseTime: number): void {
    const existing = this.requestMetrics.get(endpoint) || {
      endpoint,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      lastFailureTime: 0,
      consecutiveFailures: 0
    };

    if (success) {
      existing.successCount++;
      existing.consecutiveFailures = 0;
      existing.averageResponseTime = (existing.averageResponseTime + responseTime) / 2;
    } else {
      existing.failureCount++;
      existing.consecutiveFailures++;
      existing.lastFailureTime = Date.now();
    }

    this.requestMetrics.set(endpoint, existing);
  }

  // Timeout wrapper for operations
  private async withTimeout<T>(operation: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      operation,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new RequestTimeoutException(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }

  @Get('lanes')
  @ApiOperation({ 
    summary: 'Get available lanes for operations',
    description: 'Get all available pickup→delivery lane combinations for order creation' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns list of available lanes',
    schema: {
      type: 'object',
      properties: {
        lanes: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              originCity: { type: 'string' },
              originState: { type: 'string' },
              destinationCity: { type: 'string' },
              destinationState: { type: 'string' },
              estimatedMiles: { type: 'number' },
              estimatedDuration: { type: 'string' },
              frequencyRank: { type: 'number' },
              lastUsed: { type: 'string', format: 'date-time' }
            }
          }
        },
        total: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Access denied - insufficient permissions' })
  async getLanes(@Request() req: AuthenticatedRequest): Promise<{ lanes: Lane[]; total: number }> {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new BadRequestException('User authentication required');
    }

    try {
      // Verify user has access to operations
      await this.verifyOperationsAccess(airtableUserId);
      
      const lanes = await this.operationsService.getLanes();
      
      this.logger.log(`Retrieved ${lanes.length} lanes for user ${airtableUserId}`);
      
      return {
        lanes,
        total: lanes.length
      };
    } catch (error) {
      this.logger.error(`Error retrieving lanes for user ${airtableUserId}:`, error);
      throw error;
    }
  }

  @Post('lanes/calculate-accurate')
  @ApiOperation({ 
    summary: 'Calculate accurate lane distances (Background Job)',
    description: 'Triggers background calculation of accurate distances using Radar.com API. This should be run periodically to improve distance accuracy.' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Accurate lane calculation started',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        lanesCalculated: { type: 'number' },
        summary: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Access denied - insufficient permissions' })
  async calculateAccurateLanes(@Request() req: AuthenticatedRequest): Promise<{ 
    message: string; 
    lanesCalculated: number; 
    summary: string;
  }> {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new BadRequestException('User authentication required');
    }

    try {
      // Verify user has access to operations
      await this.verifyOperationsAccess(airtableUserId);
      
      this.logger.log(`Starting accurate lane calculation background job for user ${airtableUserId}`);
      
      const lanes = await this.operationsService.calculateAccurateLanes();
      
      const summary = `Calculated accurate distances for ${lanes.length} lanes. Future lane requests will be much faster.`;
      
      this.logger.log(`Completed accurate lane calculation: ${lanes.length} lanes processed for user ${airtableUserId}`);
      
      return {
        message: 'Accurate lane calculation completed successfully',
        lanesCalculated: lanes.length,
        summary
      };
    } catch (error) {
      this.logger.error(`Error calculating accurate lanes for user ${airtableUserId}:`, error);
      throw error;
    }
  }

  @Post('orders')
  @ApiOperation({ 
    summary: 'Create new order',
    description: 'Create a new freight order with lane-based routing' 
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Order created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        airtableRecordId: { type: 'string' },
        message: { type: 'string' },
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid order data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Access denied - insufficient permissions' })
  async createOrder(
    @Request() req: AuthenticatedRequest,
    @Body() createOrderDto: CreateOrderDto
  ) {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new BadRequestException('User authentication required');
    }

    try {
      // Verify user has access to operations
      await this.verifyOperationsAccess(airtableUserId);
      
      this.logger.log(`Creating order for user ${airtableUserId}:`, {
        poNumber: createOrderDto.poNumber,
        lane: `${createOrderDto.originCity}, ${createOrderDto.originState} → ${createOrderDto.destinationCity}, ${createOrderDto.destinationState}`
      });
      
      const result = await this.operationsService.createOrder(createOrderDto, airtableUserId);
      
      this.logger.log(`Successfully created order ${result.id} for user ${airtableUserId}`);
      
      return {
        id: result.id,
        airtableRecordId: result.airtableRecordId,
        message: 'Order created successfully',
        success: true
      };
    } catch (error) {
      this.logger.error(`Error creating order for user ${airtableUserId}:`, error);
      throw error;
    }
  }

  /**
   * Verify user has access to operations functionality
   * Only users with specific MC Numbers and admin users allowed
   */
  private async verifyOperationsAccess(userId: string): Promise<void> {
    try {
      const user = await this.authService.findUserByAirtableId(userId);
      
      if (!user) {
        throw new ForbiddenException('User not found in system');
      }

      // Allow specific MC Numbers (First Cut Produce equivalent) and admin users
      const hasAccess = user.role === 'ADMIN' || 
                       (user.mcNumber && ['123456', '789012'].includes(user.mcNumber)); // Replace with actual MC Numbers
      
      if (!hasAccess) {
        this.logger.warn(`Access denied for user ${userId}: mcNumber=${user.mcNumber}, role=${user.role}`);
        throw new ForbiddenException(
          'Access denied. Operations page is only accessible to authorized carriers and administrators.'
        );
      }

      this.logger.log(`Operations access granted for user ${userId}: mcNumber=${user.mcNumber}, role=${user.role}`);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Error verifying operations access for user ${userId}:`, error);
      throw new ForbiddenException('Unable to verify operations access');
    }
  }

  @Get('suggestions')
  @ApiOperation({ 
    summary: 'Get smart suggestions for order creation',
    description: 'Get AI-powered suggestions based on historical data and user patterns' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns smart suggestions for order creation'
  })
  @ApiResponse({ 
    status: 408, 
    description: 'Request timeout - service temporarily unavailable'
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Internal server error'
  })
  async getSmartSuggestions(
    @Request() req: AuthenticatedRequest,
    @Query('originCity') originCity: string,
    @Query('originState') originState: string,
    @Query('destinationCity') destinationCity: string,
    @Query('destinationState') destinationState: string,
    @Query('currentValues') currentValues?: string
  ) {
    const startTime = Date.now();
    const endpoint = 'get-smart-suggestions';
    
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new BadRequestException('User authentication required');
    }

    try {
      // Circuit breaker check
      this.checkCircuitBreaker(endpoint);
      
      await this.verifyOperationsAccess(airtableUserId);
      
      // Input validation
      if (!originCity || !originState || !destinationCity || !destinationState) {
        throw new BadRequestException('Origin and destination city/state are required');
      }

      const parsedCurrentValues = currentValues ? JSON.parse(currentValues) : undefined;
      
      // Execute with timeout protection
      const suggestions = await this.withTimeout(
        this.operationsService.getSmartSuggestions(
          originCity.trim(),
          originState.trim(),
          destinationCity.trim(),
          destinationState.trim(),
          airtableUserId,
          parsedCurrentValues
        ),
        this.CIRCUIT_BREAKER_CONFIG.requestTimeout
      );
      
      const responseTime = Date.now() - startTime;
      this.recordMetrics(endpoint, true, responseTime);
      
      this.logger.log(`Smart suggestions generated successfully in ${responseTime}ms for user ${airtableUserId}`);
      return suggestions;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordMetrics(endpoint, false, responseTime);
      
      this.logger.error(`Error getting smart suggestions for user ${airtableUserId}:`, error);
      
      // Return appropriate error responses
      if (error instanceof RequestTimeoutException) {
        throw error;
      } else if (error instanceof BadRequestException) {
        throw error;
      } else {
        // Return graceful fallback for any other errors
        throw new InternalServerErrorException({
          message: 'Smart suggestions temporarily unavailable',
          fallback: {
            suggestions: [],
            smartDefaults: {},
            confidence: 0,
            metadata: {
              error: 'Service temporarily unavailable',
              generatedAt: new Date().toISOString(),
              dataPoints: 0,
              learningActive: false
            }
          }
        });
      }
    }
  }

  @Post('validate')
  @ApiOperation({ 
    summary: 'Validate order data with AI-powered rules',
    description: 'Validate order data against business rules and historical patterns' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns validation results'
  })
  @ApiResponse({ 
    status: 408, 
    description: 'Request timeout - service temporarily unavailable'
  })
  async validateOrderWithAI(
    @Request() req: AuthenticatedRequest,
    @Body() validationData: { orderData: any; context: any }
  ) {
    const startTime = Date.now();
    const endpoint = 'validate-order';
    
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new BadRequestException('User authentication required');
    }

    try {
      // Circuit breaker check
      this.checkCircuitBreaker(endpoint);
      
      await this.verifyOperationsAccess(airtableUserId);
      
      // Input validation
      if (!validationData.orderData || !validationData.context) {
        throw new BadRequestException('Order data and context are required');
      }

      // Execute with timeout protection
      const validation = await this.withTimeout(
        this.operationsService.validateOrderWithAI(validationData.orderData, validationData.context),
        this.CIRCUIT_BREAKER_CONFIG.requestTimeout
      );
      
      const responseTime = Date.now() - startTime;
      this.recordMetrics(endpoint, true, responseTime);
      
      this.logger.log(`Order validation completed successfully in ${responseTime}ms for user ${airtableUserId}`);
      return validation;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordMetrics(endpoint, false, responseTime);
      
      this.logger.error(`Error validating order for user ${airtableUserId}:`, error);
      
      if (error instanceof RequestTimeoutException) {
        throw error;
      } else if (error instanceof BadRequestException) {
        throw error;
      } else {
        // Return graceful fallback
        return {
          isValid: true,
          warnings: [],
          suggestions: [],
          criticalIssues: [{
            type: 'system_error',
            message: 'Validation system temporarily unavailable',
            details: 'Please try again in a few moments'
          }]
        };
      }
    }
  }

  @Post('feedback/:orderId')
  @ApiOperation({ 
    summary: 'Record suggestion feedback',
    description: 'Record user feedback on AI suggestions to improve future recommendations' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Feedback recorded successfully'
  })
  async recordFeedback(
    @Request() req: AuthenticatedRequest,
    @Param('orderId') orderId: string,
    @Body() feedbackData: any
  ) {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      throw new BadRequestException('User authentication required');
    }

    try {
      await this.verifyOperationsAccess(airtableUserId);
      
      await this.operationsService.recordSuggestionFeedback(orderId, {
        userId: airtableUserId,
        ...feedbackData
      });
      
      return { success: true, message: 'Feedback recorded successfully' };
    } catch (error) {
      this.logger.error(`Error recording feedback for user ${airtableUserId}:`, error);
      // Don't throw error for feedback - it's non-critical
      return { success: false, message: 'Failed to record feedback' };
    }
  }

  @Get('autocomplete/:field')
  @ApiOperation({ 
    summary: 'Get auto-complete suggestions',
    description: 'Get intelligent auto-complete suggestions for form fields' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns auto-complete suggestions'
  })
  async getAutoComplete(
    @Request() req: AuthenticatedRequest,
    @Param('field') field: string,
    @Query('value') partialValue: string,
    @Query('context') context?: string
  ) {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      throw new BadRequestException('User authentication required');
    }

    try {
      await this.verifyOperationsAccess(airtableUserId);
      
      const parsedContext = context ? JSON.parse(context) : {};
      
      const suggestions = await this.operationsService.getAutoCompleteSuggestions(
        field,
        partialValue,
        { ...parsedContext, userId: airtableUserId }
      );
      
      return { suggestions };
    } catch (error) {
      this.logger.error(`Error getting auto-complete for user ${airtableUserId}:`, error);
      return { suggestions: [] };
    }
  }
} 