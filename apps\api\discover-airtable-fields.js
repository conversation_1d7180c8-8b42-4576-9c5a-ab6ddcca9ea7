// Discover Airtable field names for proper data recovery
const fs = require('fs');
const path = require('path');
const Airtable = require('airtable');

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env');
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          let value = valueParts.join('=');
          // Remove surrounding quotes if present
          if ((value.startsWith('"') && value.endsWith('"')) || 
              (value.startsWith("'") && value.endsWith("'"))) {
            value = value.slice(1, -1);
          }
          envVars[key] = value;
        }
      }
    });
    
    return envVars;
  } catch (error) {
    console.error('Error loading .env file:', error.message);
    return {};
  }
}

async function discoverFields() {
  console.log('🔍 DISCOVERING AIRTABLE FIELD NAMES');
  console.log('===================================\n');
  
  try {
    // Load environment variables
    const envVars = loadEnvFile();
    const apiKey = envVars.AIRTABLE_API_KEY || process.env.AIRTABLE_API_KEY;
    const baseId = envVars.AIRTABLE_BASE_ID || process.env.AIRTABLE_BASE_ID;
    const tableName = envVars.AIRTABLE_TABLE_NAME || process.env.AIRTABLE_TABLE_NAME || 'Orders';
    
    if (!apiKey || !baseId) {
      console.log('❌ Missing Airtable configuration');
      return;
    }
    
    // Initialize Airtable connection
    const airtable = new Airtable({ apiKey });
    const base = airtable.base(baseId);
    
    // Fetch just one record to discover field names
    console.log('📥 Fetching sample record to discover field names...');
    
    const records = await base(tableName)
      .select({
        maxRecords: 1
      })
      .all();
    
    if (records.length === 0) {
      console.log('❌ No records found in the table');
      return;
    }
    
    const sampleRecord = records[0];
    const fieldNames = Object.keys(sampleRecord.fields);
    
    console.log(`✅ Found ${fieldNames.length} fields in Airtable:\n`);
    
    fieldNames.forEach((fieldName, index) => {
      const value = sampleRecord.fields[fieldName];
      const valueType = typeof value;
      const displayValue = value !== null && value !== undefined ? 
        (valueType === 'string' ? `"${value}"` : value.toString()) : 
        'null';
      
      console.log(`${index + 1}. "${fieldName}" (${valueType}): ${displayValue}`);
    });
    
    console.log('\n🎯 MAPPING FOR DATA RECOVERY:');
    console.log('Field names to use in recovery script:');
    
    // Common field patterns to look for
    const commonPatterns = [
      { pattern: /order|id/i, purpose: 'Order ID' },
      { pattern: /status/i, purpose: 'Load Status' },
      { pattern: /origin.*city/i, purpose: 'Origin City' },
      { pattern: /origin.*state/i, purpose: 'Origin State' },
      { pattern: /dest.*city/i, purpose: 'Destination City' },
      { pattern: /dest.*state/i, purpose: 'Destination State' },
      { pattern: /pickup.*date/i, purpose: 'Pickup Date' },
      { pattern: /deliver.*date/i, purpose: 'Delivery Date' },
      { pattern: /equipment/i, purpose: 'Equipment Type' },
      { pattern: /weight/i, purpose: 'Weight' },
      { pattern: /rate/i, purpose: 'Rate' },
      { pattern: /temp/i, purpose: 'Temperature' },
      { pattern: /sync.*api/i, purpose: 'Synced to API' },
      { pattern: /public/i, purpose: 'Is Public' },
      { pattern: /target.*org/i, purpose: 'Target Organizations' }
    ];
    
    commonPatterns.forEach(({ pattern, purpose }) => {
      const matchingField = fieldNames.find(field => pattern.test(field));
      if (matchingField) {
        console.log(`✅ ${purpose}: "${matchingField}"`);
      } else {
        console.log(`❌ ${purpose}: Not found`);
      }
    });
    
    console.log('\n📋 ALL FIELD NAMES (for copy/paste):');
    fieldNames.forEach(field => {
      console.log(`"${field}",`);
    });
    
    return fieldNames;
    
  } catch (error) {
    console.error('❌ Field discovery failed:', error.message);
    console.error('Full error:', error);
    return null;
  }
}

// Execute the discovery
discoverFields().then(fieldNames => {
  if (fieldNames) {
    console.log(`\n✅ Field discovery complete! Found ${fieldNames.length} fields.`);
    console.log('Use these exact field names in the data recovery script.');
  }
}).catch(error => {
  console.error('💥 Discovery script failed:', error);
}); 