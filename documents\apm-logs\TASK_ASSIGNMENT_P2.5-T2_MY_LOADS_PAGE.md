# APM Task Assignment: My Loads Page Complete Implementation

## 1. Agent Role & APM Context

**Introduction:** You are activated as an **Implementation Agent** within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As an Implementation Agent, you are responsible for executing assigned development tasks, implementing backend services, creating frontend components, and integrating complete functionality based on the project's technical architecture. You must execute tasks diligently and log work meticulously.

**Workflow:** You will work directly with the Manager Agent (via the User) and must coordinate with previous agent work through the Memory Bank system.

## 2. Onboarding / Context from Prior Work

**UI/Design Agent Foundation:** This task builds directly upon the work of the UI/Design Agent (P2.5-T1) who has completed UI/UX improvements and user onboarding system implementation. Before proceeding, ensure you:

1. **Review UI Patterns:** Examine the updated UI components and design patterns established by the UI/Design Agent
2. **Maintain Consistency:** Use the same design patterns, spacing, and component styles for the My Loads page
3. **Integration Points:** Understand any onboarding tour integration points that will need to include the My Loads page
4. **Component Reuse:** Leverage any new reusable components created during the UI improvements

**Expected Context from P2.5-T1:**
- Consistent UI patterns and component styling
- Established design system guidelines
- User onboarding flow integration points
- Contextual help system patterns

## 3. Task Assignment

**Reference Implementation Plan:** This assignment corresponds to `Phase 2.5, Task P2.5-T2: My Loads Page Complete Implementation` in the Implementation Plan.

**Objective:** Design and implement a complete "My Loads" page that provides carriers with comprehensive load management functionality, including status tracking, document management, filtering, and mobile-responsive design.

### Detailed Action Steps:

#### A. Backend API Development
1. **Load Status Tracking API:**
   - Create endpoint: `GET /api/v1/airtable-orders/carrier-loads/:carrierProfileId`
   - **Query Parameters:** Support filtering by status, date range, pagination
   - **Response Format:** Include load details, current status, documents, bidding information
   - **Status Integration:** Map Airtable load statuses to user-friendly display names
   - **Guidance:** Use existing `LoadStatus` enum and ensure compatibility with Airtable sync

2. **Document Management API Integration:**
   - Extend existing document endpoints for carrier-specific access
   - **Document Types:** BOL (Bill of Lading), POD (Proof of Delivery), Invoice uploads
   - **File Upload:** Integrate with existing document upload system
   - **Security:** Ensure carriers can only access their own load documents
   - **Status Updates:** Document upload should trigger load status updates if applicable

3. **Load History & Filtering Service:**
   - **Database Queries:** Optimize queries for carrier-specific load history
   - **Filtering Options:** Status, date range, origin/destination, equipment type
   - **Sorting:** By pickup date, delivery date, status, rate
   - **Pagination:** Support for large datasets with efficient pagination
   - **Performance:** Use existing database indexes on `awardedToCarrierProfileId`

#### B. Frontend Page Implementation
1. **My Loads Page Structure:**
   - **Location:** `apps/web/src/app/org/[orgId]/my-loads/page.tsx`
   - **Layout:** Consistent with existing pages using established UI patterns
   - **Responsive Design:** Mobile-first approach with touch-friendly interactions
   - **Navigation:** Integrate with existing navigation system

2. **Load Status Visualization:**
   - **Status Cards/List:** Visual representation of loads by status
   - **Status Badges:** Color-coded status indicators (New, Available, Assigned, In Transit, Delivered, etc.)
   - **Timeline View:** Optional timeline view showing load progression
   - **Quick Stats:** Summary cards showing total loads, active loads, completed loads

3. **Load Details Component:**
   - **Load Information:** Origin, destination, pickup/delivery dates, equipment, rate
   - **Status History:** Timeline of status changes
   - **Documents Section:** Upload/download area for BOL, POD, invoices
   - **Notes/Communication:** Any carrier notes or admin communications
   - **Actions:** Context-specific actions based on load status

4. **Document Upload/Management Interface:**
   - **Drag & Drop Upload:** User-friendly document upload interface
   - **Document Preview:** Preview uploaded documents when possible
   - **Upload Progress:** Progress indicators for file uploads
   - **File Validation:** Ensure proper file types and size limits
   - **Document Status:** Visual indicators for required vs. uploaded documents

#### C. Advanced Features Implementation
1. **Filtering & Search Functionality:**
   - **Filter Panel:** Collapsible filter panel with multiple criteria
   - **Date Range Picker:** For pickup/delivery date filtering
   - **Status Multi-Select:** Filter by multiple statuses simultaneously
   - **Location Search:** Filter by origin/destination
   - **Save Filters:** Allow users to save commonly used filter combinations

2. **Load History Management:**
   - **Pagination:** Efficient pagination for large load histories
   - **Sorting Options:** Multiple sort criteria with clear UI indicators
   - **Export Functionality:** Export load history to CSV/PDF format
   - **Search:** Text search across load details

3. **Status Update Workflow:**
   - **Carrier Actions:** Allow carriers to update certain statuses (e.g., picked up, delivered)
   - **Document Requirements:** Enforce document requirements for status changes
   - **Confirmation Dialogs:** Confirmation for critical status changes
   - **Audit Trail:** Log all status changes with timestamp and user

#### D. Integration & Mobile Optimization
1. **Mobile-Responsive Design:**
   - **Touch Interactions:** Optimize for mobile touch interfaces
   - **Compact Views:** Condensed information display for mobile screens
   - **Swipe Actions:** Consider swipe actions for common operations
   - **Performance:** Optimize for mobile network conditions

2. **Real-time Updates Integration:**
   - **WebSocket Integration:** Connect to existing real-time notification system
   - **Live Status Updates:** Reflect status changes without page refresh
   - **Document Upload Notifications:** Real-time feedback on document processing
   - **Auto-refresh:** Automatic data refresh at appropriate intervals

3. **Error Handling & Loading States:**
   - **Loading Skeletons:** Use skeleton loading for better perceived performance
   - **Error Boundaries:** Implement error boundaries specific to load data
   - **Retry Mechanisms:** Allow users to retry failed operations
   - **Offline Considerations:** Graceful handling of connectivity issues

## 4. Technical Implementation Guidelines

**Architecture Patterns:**
- **Backend:** Follow existing NestJS patterns and service architecture
- **Frontend:** Use Next.js 14 app router patterns consistent with existing pages
- **State Management:** Use React useState/useEffect patterns as established
- **API Integration:** Follow existing API calling patterns with proper error handling

**Key Files and Integration Points:**
- **Backend:**
  - `apps/api/src/airtable-orders/airtable-orders.service.ts` - Extend existing service
  - `apps/api/src/airtable-orders/airtable-orders.controller.ts` - Add new endpoints
  - `apps/api/src/airtable-orders/dto/` - Create necessary DTOs
- **Frontend:**
  - `apps/web/src/app/org/[orgId]/my-loads/` - New page directory
  - `apps/web/src/components/` - Reusable components for load management
  - Existing UI components from the UI/Design Agent work

**Database Considerations:**
- **Existing Schema:** Use current `Load`, `Bid`, `CarrierProfile` models
- **Queries:** Optimize using existing indexes on `awardedToCarrierProfileId`, `status`
- **Document Fields:** Leverage existing document tracking fields (`bolFileUrl`, `podFileUrl`, `invoiceFileUrl`)

## 5. Expected Output & Deliverables

**Define Success:** Successful completion means:
- ✅ Complete My Loads page with all specified functionality
- ✅ Mobile-responsive design that works across all devices
- ✅ Document upload/management fully integrated
- ✅ Real-time status updates operational
- ✅ Advanced filtering and search capabilities
- ✅ Performance optimized for large datasets
- ✅ Consistent with UI patterns established by UI/Design Agent

**Specific Deliverables:**
1. **Backend APIs:** Complete API endpoints for load management and document handling
2. **Frontend Page:** Fully functional My Loads page with all features
3. **Components:** Reusable components for load status, document management, filtering
4. **Mobile Optimization:** Responsive design tested across devices
5. **Integration:** Proper integration with existing authentication, navigation, and real-time systems
6. **Documentation:** API documentation and component usage guidelines

**Quality Standards:**
- **Performance:** Page loads under 2 seconds with typical load data
- **Accessibility:** Proper ARIA labels and keyboard navigation
- **Security:** Proper authorization and data access controls
- **Error Handling:** Comprehensive error handling and user feedback
- **Testing:** Manual testing across different load statuses and data scenarios

## 6. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Ensure your log includes:
- Reference to `Phase 2.5, Task P2.5-T2` in the Implementation Plan
- Detailed description of all API endpoints created or modified
- Complete list of frontend components and pages implemented
- Database queries and performance optimizations applied
- Integration points with existing systems (auth, real-time, documents)
- Mobile responsiveness testing results
- Any technical decisions made and rationale
- Performance metrics and optimization notes
- Security considerations and access control implementation

**Special Instructions:**
- Document any new reusable components created for future use
- Include API endpoint documentation with request/response examples
- Note any UI patterns followed from the UI/Design Agent work
- Provide guidance for future Billing page implementation
- Document any edge cases or limitations discovered

## 7. Clarification Instruction

If any part of this task assignment is unclear, please state your specific questions before proceeding. Key areas to clarify if needed:
- Specific load statuses and workflow requirements
- Document upload requirements and file type restrictions
- Real-time update integration approach
- Mobile responsiveness priorities and breakpoints
- Performance requirements for large datasets

---

**Priority:** HIGH - This completes a core carrier workflow gap and builds on UI/Design Agent foundation.

**Dependencies:** Completion of P2.5-T1 (UI/UX improvements) by UI/Design Agent

**Estimated Duration:** 2-3 days

**Success Metric:** Carriers can efficiently manage their assigned loads, upload required documents, and track load progress through the complete workflow. 