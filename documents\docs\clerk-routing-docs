---
title: Routing in <PERSON>
description: Learn how <PERSON> handles routing in your application.
---

Some of <PERSON>'s components have their own internal routing.

For example, say a user uses their email address to fill out the [`<SignUp />`](/docs/components/authentication/sign-up) form. Once they submit the form, they are redirected from `/sign-up` to `/sign-up/verify-email-address`, which renders <PERSON>'s UI for verifying a user's email address. This redirect is handled by <PERSON>'s internal routing.

## `routing` prop

The following Clerk components accept a `routing` prop in order to define the routing strategy:

- [`<SignUp />`](/docs/components/authentication/sign-up)
- [`<SignIn />`](/docs/components/authentication/sign-in)
- [`<UserProfile />`](/docs/components/user/user-profile)
- [`<CreateOrganization />`](/docs/components/organization/create-organization)
- [`<OrganizationProfile />`](/docs/components/organization/organization-profile)

There following routing strategies can be passed:

- [`path`](#path-routing)
- [`hash`](#hash-routing)

Clerk will attempt to select the routing strategy that best integrates with your framework of choice. If for some reason the default routing strategy doesn't work for you, use the information below to pick a strategy that will work for your setup.

### `path` routing

`path` routing uses the path in the URL to determine the route. This is useful for server-rendered pages where SEO and server-side routing are crucial, such as Next.js or Remix applications.

For example, say you have a Clerk + Next.js application with the `<SignUp />` component on a dedicated `/sign-up` page. A user visit this page and uses their email address to fill out the `<SignUp />` form. Once they submit the form, they are redirected from `/sign-up` to `/sign-up/verify-email-address`.

For the following SDKs, `path` routing is set _by default_ on all Clerk components, as these frameworks support server-side routing out-of-the-box. There is no need to pass the `routing` or `path` props to Clerk components in these frameworks.

- Next.js
- Remix
- Tanstack React Start
- React Router

### `hash` routing

`hash` routing uses [the hash (#) portion of the URL](https://developer.mozilla.org/en-US/docs/Web/API/URL/hash) to determine the route. This is useful for single-page applications that use client-side routing.

For example, say you have a Clerk + React application with the `<SignUp />` component on a dedicated `/sign-up` page. A user visit this page and uses their email address to fill out the `<SignUp />` form. Once they submit the form, they are redirected from `/sign-up` to `/sign-up#verify-email-address`.

In Clerk applications that use any SDK other than Next.js or Remix, `hash` routing is set _by default_ on all Clerk components.
