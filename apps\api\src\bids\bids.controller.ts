import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { BidsService } from './bids.service';
import { AuthGuard } from '../auth/auth.guard';

@Controller('bids')
@UseGuards(AuthGuard)
export class BidsController {
  constructor(private readonly bidsService: BidsService) {}

  @Post('submit')
  async submitBid(@Body() bidData: any, @Request() req: any) {
    // Extract JWT token from the Authorization header
    const authHeader = req.headers.authorization;
    const jwtToken = authHeader?.split(' ')[1]; // Remove 'Bearer ' prefix
    
    return this.bidsService.submitToN8N(bidData, jwtToken);
  }
} 