{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "globalPassThroughEnv": ["AIRTABLE_API_KEY", "AIRTABLE_BASE_ID", "AIRTABLE_TABLE_NAME", "NODE_ENV", "DATABASE_URL", "NEXT_PUBLIC_API_URL", "NEXT_PUBLIC_API_BASE_URL", "N8N_JWT_SECRET", "N8N_JWT_ISSUER", "NEXT_PUBLIC_N8N_BASE_URL", "NEXT_PUBLIC_N8N_API_KEY", "ENABLE_EXPERIMENTAL_COREPACK", "COREPACK_HOME", "DASH0_AUTH_TOKEN", "OTEL_EXPORTER_OTLP_HEADERS", "OTEL_EXPORTER_OTLP_ENDPOINT", "DASH0_LOG_DRAIN_ENDPOINT", "DASH0_DATASET", "OTEL_EXPORTER_OTLP_COMPRESSION"], "tasks": {"@repo/db#build": {"cache": true, "inputs": ["../../apps/api/prisma/schema.prisma"], "outputs": ["generated/client/**"]}, "build": {"dependsOn": ["^build", "@repo/db#build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "dist/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}}}