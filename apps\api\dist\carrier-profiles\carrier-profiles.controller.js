"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CarrierProfilesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarrierProfilesController = void 0;
const common_1 = require("@nestjs/common");
const carrier_profiles_service_1 = require("./carrier-profiles.service");
const dto_1 = require("./dto");
const auth_guard_1 = require("../auth/auth.guard");
const admin_guard_1 = require("../auth/admin.guard");
const swagger_1 = require("@nestjs/swagger");
let CarrierProfilesController = CarrierProfilesController_1 = class CarrierProfilesController {
    carrierProfilesService;
    logger = new common_1.Logger(CarrierProfilesController_1.name);
    constructor(carrierProfilesService) {
        this.carrierProfilesService = carrierProfilesService;
    }
    async create(createCarrierProfileDto, req) {
        this.logger.log(`Creating carrier profile for user: ${req.user?.airtableUserId}`);
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.ForbiddenException('User authentication required');
        }
        try {
            const profile = await this.carrierProfilesService.create(createCarrierProfileDto, airtableUserId);
            this.logger.log(`Carrier profile created successfully for user: ${airtableUserId}, profile ID: ${profile.id}`);
            return profile;
        }
        catch (error) {
            this.logger.error(`Error creating carrier profile for user ${airtableUserId}:`, error);
            if (error.code === 'P2002') {
                throw new common_1.ForbiddenException('A carrier profile already exists for this user');
            }
            throw error;
        }
    }
    async findAll() {
        return this.carrierProfilesService.findAll();
    }
    async findMyProfile(req) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.ForbiddenException('User authentication required');
        }
        try {
            const profile = await this.carrierProfilesService.findMyProfileByAirtableUserId(airtableUserId);
            this.logger.log(`Retrieved carrier profile for user: ${airtableUserId}`);
            return profile;
        }
        catch (error) {
            this.logger.error(`Error retrieving carrier profile for user ${airtableUserId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                this.logger.log(`Auto-creating missing CarrierProfile for user: ${airtableUserId}`);
                try {
                    const createProfileDto = {
                        companyName: 'New Carrier',
                    };
                    const newProfile = await this.carrierProfilesService.create(createProfileDto, airtableUserId);
                    this.logger.log(`Auto-created CarrierProfile: ${newProfile.id} for user: ${airtableUserId}`);
                    return newProfile;
                }
                catch (createError) {
                    this.logger.error(`Failed to auto-create CarrierProfile for user ${airtableUserId}:`, createError);
                    throw new common_1.NotFoundException('No carrier profile found and auto-creation failed');
                }
            }
            throw error;
        }
    }
    async findOne(id) {
        const profile = await this.carrierProfilesService.findOne(id);
        if (!profile) {
            throw new common_1.NotFoundException(`Carrier profile with ID "${id}" not found`);
        }
        return profile;
    }
    async updateMyProfile(updateCarrierProfileDto, req) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.ForbiddenException('User authentication required');
        }
        try {
            this.logger.debug(`Received update DTO for user ${airtableUserId}:`, JSON.stringify(updateCarrierProfileDto, null, 2));
            this.logger.debug(`DTO equipmentTypes type: ${typeof updateCarrierProfileDto.equipmentTypes}, value:`, updateCarrierProfileDto.equipmentTypes);
            const profile = await this.carrierProfilesService.updateMyProfileByAirtableUserId(airtableUserId, updateCarrierProfileDto);
            this.logger.log(`Updated carrier profile for user: ${airtableUserId}`);
            return profile;
        }
        catch (error) {
            this.logger.error(`Error updating carrier profile for user ${airtableUserId}:`, error);
            this.logger.error(`Error details:`, {
                message: error.message,
                stack: error.stack,
                statusCode: error.statusCode,
                response: error.response
            });
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException('No carrier profile found for the authenticated user');
            }
            throw error;
        }
    }
    update(id, updateCarrierProfileDto) {
        return this.carrierProfilesService.update(id, updateCarrierProfileDto);
    }
    async emergencyCreateMyProfile(req) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.ForbiddenException('User authentication required');
        }
        try {
            try {
                const existingProfile = await this.carrierProfilesService.findMyProfileByAirtableUserId(airtableUserId);
                this.logger.log(`Profile already exists for user: ${airtableUserId}`);
                return existingProfile;
            }
            catch (error) {
                if (!(error instanceof common_1.NotFoundException)) {
                    throw error;
                }
            }
            const createProfileDto = {
                companyName: 'New Carrier',
            };
            const profile = await this.carrierProfilesService.create(createProfileDto, airtableUserId);
            this.logger.log(`Emergency CarrierProfile created: ${profile.id} for user: ${airtableUserId}`);
            return profile;
        }
        catch (error) {
            this.logger.error(`Error in emergency profile creation for user ${airtableUserId}:`, error);
            throw error;
        }
    }
    remove(id) {
        return this.carrierProfilesService.remove(id);
    }
};
exports.CarrierProfilesController = CarrierProfilesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new carrier profile',
        description: 'Create a new carrier profile for the authenticated user'
    }),
    (0, swagger_1.ApiBody)({ type: dto_1.CreateCarrierProfileDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCarrierProfileDto, Object]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get carrier profiles',
        description: 'Get all carrier profiles (admin only) or your own profile'
    }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get my carrier profile',
        description: 'Get the carrier profile for the authenticated user'
    }),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "findMyProfile", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a specific carrier profile by ID (Admin Only)',
        description: 'Requires Admin role. RBAC enforced.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Carrier Profile ID (CUID)', type: String }),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)('me'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Update my carrier profile',
        description: 'Update the carrier profile for the authenticated user'
    }),
    (0, swagger_1.ApiBody)({ type: dto_1.UpdateCarrierProfileDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.UpdateCarrierProfileDto, Object]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "updateMyProfile", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a specific carrier profile by ID (Admin Only)',
        description: 'Requires Admin role. RBAC enforced.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Carrier Profile ID (CUID)', type: String }),
    (0, swagger_1.ApiBody)({ type: dto_1.UpdateCarrierProfileDto }),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.AdminUpdateCarrierProfileDto]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "update", null);
__decorate([
    (0, common_1.Post)('emergency/create'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Emergency create my carrier profile',
        description: 'Emergency endpoint to create a carrier profile for authenticated user if missing'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "emergencyCreateMyProfile", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a specific carrier profile by ID (Admin Only)',
        description: 'Requires Admin role. RBAC enforced.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Carrier Profile ID (CUID)', type: String }),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CarrierProfilesController.prototype, "remove", null);
exports.CarrierProfilesController = CarrierProfilesController = CarrierProfilesController_1 = __decorate([
    (0, swagger_1.ApiTags)('carrier-profiles'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Controller)('carrier-profiles'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        disableErrorMessages: false
    })),
    __metadata("design:paramtypes", [carrier_profiles_service_1.CarrierProfilesService])
], CarrierProfilesController);
//# sourceMappingURL=carrier-profiles.controller.js.map