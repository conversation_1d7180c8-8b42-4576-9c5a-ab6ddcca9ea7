# Deployment Fix Guide

## Current Issue
The application is experiencing 500 errors on API endpoints after deployment. The main issues are:

1. **Missing Environment Variables**: The API deployment doesn't have required environment variables
2. **API Routing**: The API subdomain routing needs to be properly configured
3. **CORS Issues**: Cross-origin requests between frontend and API

## Immediate Fixes Applied

### 1. Created API Proxy Route
- **File**: `apps/web/src/app/api/v1/[...pathParts]/route.ts`
- **Purpose**: Proxies all `/api/v1/*` requests from the frontend to the backend API
- **Benefits**: 
  - Eliminates CORS issues
  - Provides better error handling
  - Adds authentication context

### 2. Updated Next.js Configuration
- **File**: `apps/web/next.config.js`
- **Change**: Removed the rewrite rule since we're now handling API routing with the proxy route
- **Reason**: The proxy route provides better control and error handling

### 3. Updated Middleware
- **File**: `apps/web/src/middleware.ts`
- **Change**: Added `/api/(.*)` to public routes
- **Reason**: Allow API routes to be handled by their own handlers without Clerk middleware interference

### 4. Enhanced API Health Checks
- **File**: `apps/api/src/app.controller.ts`
- **Added**: Debug endpoint to help diagnose environment variable issues
- **Purpose**: Easier troubleshooting of deployment issues

### 5. Created API Test Page
- **File**: `apps/web/src/app/api-test/page.tsx`
- **Purpose**: Test API connectivity and debug issues
- **Access**: Visit `/api-test` on your deployed frontend

## Required Actions for Full Fix

### 1. Configure Environment Variables in Vercel

You need to set these environment variables in your **API project** on Vercel:

```bash
# Required for API functionality
DATABASE_URL="postgresql://fcp_carrier_portal_user:<EMAIL>/fcp_carrier_portal"
CLERK_SECRET_KEY="**************************************************"
CLERK_JWT_ISSUER="https://clerk.fcp-portal.com"

# Airtable Configuration
AIRTABLE_API_KEY="**********************************************************************************"
AIRTABLE_BASE_ID="appXKT0rHSLqOPrSA"
AIRTABLE_TABLE_NAME="tbl4OnEHrzUNG2V2X"

# Optional
NODE_ENV="production"
ENABLE_DEBUG="true"  # Temporarily enable for debugging
```

### 2. Configure Environment Variables in Frontend

Ensure your **web project** on Vercel has:

```bash
NEXT_PUBLIC_API_BASE_URL="https://api.fcp-portal.com"
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_live_Y2xlcmsuZmNwLXBvcnRhbC5jb20k"
```

### 3. Verify Domain Configuration

Ensure that:
- `api.fcp-portal.com` points to your API Vercel deployment
- `www.fcp-portal.com` points to your web Vercel deployment

### 4. Test the Fix

1. **Deploy the changes** to both projects
2. **Visit** `https://www.fcp-portal.com/api-test` to test API connectivity
3. **Check the debug endpoint** at `https://api.fcp-portal.com/api/v1/debug` to verify environment variables

## Debugging Steps

### Step 1: Test API Directly
```bash
curl https://api.fcp-portal.com/api/v1/health
```
Expected response:
```json
{
  "status": "ok",
  "timestamp": "2025-01-30T...",
  "environment": "production",
  "version": "1.0.0"
}
```

### Step 2: Test API Debug Endpoint
```bash
curl https://api.fcp-portal.com/api/v1/debug
```
This should show which environment variables are set/missing.

### Step 3: Test Frontend API Proxy
Visit `https://www.fcp-portal.com/api-test` and check the results.

### Step 4: Check Vercel Function Logs
- Go to Vercel dashboard
- Check function logs for both projects
- Look for error messages in the logs

## Alternative Solutions

### Option 1: Single Deployment
If the dual-deployment approach continues to cause issues, consider:
1. Moving the API code into the Next.js app as API routes
2. This eliminates CORS and domain routing issues
3. Simpler deployment and environment management

### Option 2: Direct API Calls
If you prefer to keep separate deployments:
1. Ensure CORS is properly configured in the API
2. Use direct API calls instead of the proxy
3. Handle authentication tokens properly

## Monitoring

After deployment, monitor:
1. **Vercel Function Logs**: Check for errors in both projects
2. **API Response Times**: Ensure the proxy doesn't add significant latency
3. **Error Rates**: Monitor 500 errors in production

## Security Notes

- The debug endpoint is disabled in production unless `ENABLE_DEBUG=true`
- Remove `ENABLE_DEBUG` after fixing the issues
- Ensure all environment variables are properly secured in Vercel
- Regularly rotate API keys and secrets

## Next Steps

1. **Apply environment variables** in Vercel dashboard
2. **Redeploy both projects**
3. **Test using the API test page**
4. **Remove debug flags** once everything works
5. **Monitor production logs** for any remaining issues

# Admin Settings API Deployment Fix

## Issue
The admin settings endpoints are returning 404, indicating they may not be deployed to the backend API.

## Backend Deployment Checklist

### 1. Verify Admin Module is Included
Ensure the admin module is properly imported in the main app module:

```typescript
// apps/api/src/app.module.ts
import { AdminModule } from './admin/admin.module';

@Module({
  imports: [
    // ... other modules
    AdminModule,
  ],
})
export class AppModule {}
```

### 2. Check Admin Controller Routes
Verify these endpoints exist in `apps/api/src/admin/admin.controller.ts`:
- `GET /api/v1/admin/settings`
- `PUT /api/v1/admin/settings`
- `POST /api/v1/admin/settings/reset`

### 3. Database Migration
Ensure the SystemSettings table exists by running:
```bash
cd apps/api
npx prisma migrate deploy
```

### 4. Environment Variables
Verify these are set in the backend deployment:
- `DATABASE_URL`
- `CLERK_SECRET_KEY`
- `CLERK_PUBLISHABLE_KEY`

### 5. Redeploy Backend
After verifying the above, redeploy the backend API to Vercel:
```bash
# From the root directory
vercel --prod
```

## Testing After Deployment

1. Visit the admin panel at: `https://www.fcp-portal.com/org/[orgId]/admin`
2. Go to the "Settings" tab
3. Click "Run Tests" in the API Integration Tests panel
4. All tests should pass (green status)

## Expected Test Results
- ✅ Authentication Token: Valid Clerk token obtained
- ✅ API Proxy Health: Health check passed (200)
- ✅ Admin Settings GET: Settings retrieved successfully
- ✅ Backend API Direct: Endpoint exists (auth required)

## Troubleshooting

### If tests still fail:
1. Check Vercel deployment logs for the backend
2. Verify the admin module is properly registered
3. Check database connection and migrations
4. Ensure admin guard is working correctly

### Common Issues:
- **404 errors**: Admin module not imported or routes not registered
- **403 errors**: User doesn't have admin permissions
- **500 errors**: Database connection or migration issues 