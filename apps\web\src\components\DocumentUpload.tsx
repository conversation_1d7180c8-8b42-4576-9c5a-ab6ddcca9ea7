'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Upload, FileText, Image, X, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface UploadedFile {
  id?: string;
  name: string;
  url: string;
  size: number;
  type: string;
}

interface DocumentUploadProps {
  loadId: string;
  onUploadComplete?: (files: UploadedFile[]) => void;
  className?: string;
  disabled?: boolean;
}

export function DocumentUpload({ 
  loadId, 
  onUploadComplete,
  className = '',
  disabled = false
}: DocumentUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    if (disabled) return;

    setUploading(true);
    setUploadProgress(0);
    setUploadStatus('uploading');

    try {
      const formData = new FormData();
      acceptedFiles.forEach(file => {
        formData.append('documents', file);
      });

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch(`/api/loads/${loadId}/documents`, {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Upload failed');
      }

      const result = await response.json();
      setUploadedFiles(prev => [...prev, ...result.files]);
      setUploadStatus('success');
      
      toast.success(result.message || 'Upload completed successfully', {
        description: `${result.files.length} file(s) uploaded`
      });

      onUploadComplete?.(result.files);

      // Reset status after a delay
      setTimeout(() => setUploadStatus('idle'), 2000);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus('error');
      
      toast.error('Upload failed', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      // Reset status after a delay
      setTimeout(() => setUploadStatus('idle'), 3000);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [loadId, onUploadComplete, disabled]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 5,
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: uploading || disabled
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const getFileIcon = (type: string) => {
    return type.startsWith('image/') ? Image : FileText;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = () => {
    switch (uploadStatus) {
      case 'uploading':
        return <Loader2 className="h-6 w-6 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      default:
        return <Upload className="h-6 w-6 text-gray-400" />;
    }
  };

  const getStatusMessage = () => {
    switch (uploadStatus) {
      case 'uploading':
        return 'Uploading files...';
      case 'success':
        return 'Upload completed!';
      case 'error':
        return 'Upload failed';
      default:
        return isDragActive 
          ? 'Drop files here...' 
          : 'Drag & drop files here, or click to select';
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer',
          'transition-colors duration-200',
          isDragActive && 'border-blue-400 bg-blue-50',
          uploadStatus === 'success' && 'border-green-400 bg-green-50',
          uploadStatus === 'error' && 'border-red-400 bg-red-50',
          !isDragActive && uploadStatus === 'idle' && 'border-gray-300 hover:border-gray-400',
          (uploading || disabled) && 'opacity-50 cursor-not-allowed'
        )}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          {getStatusIcon()}
          
          <div>
            <p className="text-lg text-gray-700 mb-2">
              {getStatusMessage()}
            </p>
            {uploadStatus === 'idle' && (
              <p className="text-sm text-gray-500">
                Supports PDF, JPG, PNG files up to 10MB each (max 5 files)
              </p>
            )}
          </div>

          {uploading && (
            <div className="w-full max-w-xs">
              <Progress value={uploadProgress} className="w-full" />
              <p className="text-xs text-gray-500 mt-1">
                {uploadProgress}% complete
              </p>
            </div>
          )}
        </div>
      </div>

      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">Uploaded Files</h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {uploadedFiles.map((file) => {
              const IconComponent = getFileIcon(file.type);
              return (
                <div 
                  key={file.id || file.name}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <IconComponent className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <span>{formatFileSize(file.size)}</span>
                        <span>•</span>
                        <span className="capitalize">
                          {file.type.split('/')[1]}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {file.id && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id!)}
                        disabled={uploading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {uploadedFiles.length === 0 && uploadStatus === 'idle' && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            No documents uploaded yet. Upload your BOL, invoice, or other load documents above.
          </p>
        </div>
      )}
    </div>
  );
} 