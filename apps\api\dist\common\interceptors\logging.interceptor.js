"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var LoggingInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const crypto_1 = require("crypto");
let LoggingInterceptor = LoggingInterceptor_1 = class LoggingInterceptor {
    logger = new common_1.Logger(LoggingInterceptor_1.name);
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        const correlationId = request.correlationId || (0, crypto_1.randomUUID)();
        request.correlationId = correlationId;
        response.setHeader('X-Correlation-Id', correlationId);
        const { method, url, headers, body, query, params } = request;
        const userAgent = headers['user-agent'] || '';
        const userId = request.auth?.sub;
        const startTime = Date.now();
        this.logger.log(`Incoming Request [${correlationId}]`, {
            correlationId,
            method,
            url,
            userAgent,
            userId,
            timestamp: new Date().toISOString(),
            ip: request.ip,
            query: this.sanitizeObject(query),
            params: this.sanitizeObject(params),
            bodySize: body ? JSON.stringify(body).length : 0,
            hasBody: !!body,
        });
        return next.handle().pipe((0, operators_1.tap)((data) => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            this.logger.log(`Outgoing Response [${correlationId}]`, {
                correlationId,
                method,
                url,
                statusCode: response.statusCode,
                duration: `${duration}ms`,
                userId,
                timestamp: new Date().toISOString(),
                responseSize: data ? JSON.stringify(data).length : 0,
                hasResponse: !!data,
            });
        }), (0, operators_1.catchError)((error) => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            this.logger.error(`Request Error [${correlationId}]`, {
                correlationId,
                method,
                url,
                error: error.message,
                errorName: error.constructor.name,
                duration: `${duration}ms`,
                userId,
                timestamp: new Date().toISOString(),
                stack: error.stack,
            });
            throw error;
        }));
    }
    sanitizeObject(obj) {
        if (!obj || typeof obj !== 'object')
            return obj;
        const sanitized = { ...obj };
        const sensitiveFields = ['password', 'token', 'authorization', 'auth', 'secret', 'key'];
        for (const key in sanitized) {
            if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
                sanitized[key] = '[REDACTED]';
            }
        }
        return sanitized;
    }
};
exports.LoggingInterceptor = LoggingInterceptor;
exports.LoggingInterceptor = LoggingInterceptor = LoggingInterceptor_1 = __decorate([
    (0, common_1.Injectable)()
], LoggingInterceptor);
//# sourceMappingURL=logging.interceptor.js.map