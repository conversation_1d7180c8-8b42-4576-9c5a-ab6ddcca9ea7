// Test script to verify Vercel Blob setup
// Run with: node test-blob-setup.js

import { put, list } from '@vercel/blob';

async function testBlobSetup() {
  try {
    console.log('🧪 Testing Vercel Blob setup...');
    
    // Check if environment variable is set
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error('❌ BLOB_READ_WRITE_TOKEN environment variable not found');
      console.log('   Please add it to your .env.local file');
      return;
    }
    
    console.log('✅ BLOB_READ_WRITE_TOKEN found');
    
    // Test connection by listing blobs
    const { blobs } = await list();
    console.log('✅ Successfully connected to Vercel Blob');
    console.log(`   Found ${blobs.length} existing blobs`);
    
    // Test upload with a small file
    const testContent = 'Hello from carrier portal file upload test!';
    const blob = await put('test/setup-test.txt', testContent, {
      access: 'private',
    });
    
    console.log('✅ Test upload successful');
    console.log(`   URL: ${blob.url}`);
    console.log('');
    console.log('🎉 Vercel Blob setup is working correctly!');
    console.log('   You can now implement the file upload feature.');
    
  } catch (error) {
    console.error('❌ Vercel Blob setup failed:');
    console.error('   Error:', error.message);
    
    if (error.message.includes('unauthorized')) {
      console.log('   → Check your BLOB_READ_WRITE_TOKEN value');
    } else if (error.message.includes('network')) {
      console.log('   → Check your internet connection');
    } else {
      console.log('   → Please check your Vercel Blob configuration');
    }
  }
}

testBlobSetup(); 