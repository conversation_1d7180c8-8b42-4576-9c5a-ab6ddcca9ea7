import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class PerformanceMiddleware implements NestMiddleware {
  private readonly logger = new Logger(PerformanceMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const { method, originalUrl } = req;

    // Skip performance logging for health checks and static assets
    if (originalUrl.includes('/health') || originalUrl.includes('/favicon')) {
      return next();
    }

    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const { statusCode } = res;
      
      // Log slow requests (>1000ms) as warnings
      if (duration > 1000) {
        this.logger.warn(
          `SLOW REQUEST: ${method} ${originalUrl} - ${statusCode} - ${duration}ms`
        );
      } 
      // Log medium requests (>500ms) as info
      else if (duration > 500) {
        this.logger.log(
          `MEDIUM REQUEST: ${method} ${originalUrl} - ${statusCode} - ${duration}ms`
        );
      }
      // Log fast requests only in debug mode
      else {
        this.logger.debug(
          `${method} ${originalUrl} - ${statusCode} - ${duration}ms`
        );
      }

      // Track specific endpoint performance
      if (originalUrl.includes('/airtable-orders/available')) {
        this.logger.log(`LOADBOARD PERFORMANCE: ${duration}ms`);
      } else if (originalUrl.includes('/airtable-orders/assigned')) {
        this.logger.log(`ASSIGNED LOADS PERFORMANCE: ${duration}ms`);
      } else if (originalUrl.includes('/auth/') || originalUrl.includes('/clerk')) {
        this.logger.log(`AUTH PERFORMANCE: ${duration}ms`);
      }
    });

    next();
  }
} 