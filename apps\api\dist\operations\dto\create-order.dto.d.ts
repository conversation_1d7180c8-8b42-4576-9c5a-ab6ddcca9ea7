export declare enum EquipmentType {
    DRY_VAN = "Dry Van",
    REEFER = "Reefer",
    FLATBED = "Flatbed",
    STEP_DECK = "Step Deck",
    LOWBOY = "Lowboy",
    TANKER = "Tanker",
    BOX_TRUCK = "Box Truck",
    STRAIGHT_TRUCK = "Straight Truck"
}
export declare enum PriorityLevel {
    NORMAL = "normal",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class CreateOrderDto {
    laneId: string;
    originCity: string;
    originState: string;
    destinationCity: string;
    destinationState: string;
    estimatedMiles: number;
    poNumber: string;
    soNumber?: string;
    pickupDate: string;
    deliveryDate?: string;
    equipmentRequired: EquipmentType;
    weightLbs?: number;
    rate?: number;
    temperature?: string;
    notes?: string;
    priority?: PriorityLevel;
}
