"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = exports.UpdateUserProfileDto = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const auth_guard_1 = require("./auth.guard");
const auth_service_1 = require("./auth.service");
class UpdateUserProfileDto {
    firstName;
    lastName;
    email;
}
exports.UpdateUserProfileDto = UpdateUserProfileDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateUserProfileDto.prototype, "firstName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateUserProfileDto.prototype, "lastName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], UpdateUserProfileDto.prototype, "email", void 0);
let AuthController = AuthController_1 = class AuthController {
    authService;
    logger = new common_1.Logger(AuthController_1.name);
    constructor(authService) {
        this.authService = authService;
    }
    async getUserProfile(req) {
        const userId = req.user?.airtableUserId;
        if (!userId) {
            this.logger.error('No user ID found in request user data');
            throw new Error('User authentication required');
        }
        try {
            this.logger.log(`Retrieving user profile for: ${userId}`);
            const userProfile = await this.authService.findUserByAirtableUserId(userId);
            return {
                id: userProfile.id,
                airtableUserId: userProfile.airtableUserId,
                email: userProfile.email,
                firstName: userProfile.firstName,
                lastName: userProfile.lastName,
                role: userProfile.role,
                createdAt: userProfile.createdAt,
                updatedAt: userProfile.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Error retrieving user profile for ${userId}:`, error.message, error.stack);
            throw new Error('Unable to retrieve user profile');
        }
    }
    async updateUserProfile(req, updateData) {
        const userId = req.user?.airtableUserId;
        if (!userId) {
            this.logger.error('No user ID found in request user data');
            throw new Error('User authentication required');
        }
        try {
            this.logger.log(`Updating user profile for: ${userId}`);
            this.logger.debug(`Update data:`, updateData);
            const updatedProfile = await this.authService.updateUserProfile(userId, updateData);
            this.logger.debug(`Updated profile result:`, updatedProfile);
            return {
                id: updatedProfile.id,
                airtableUserId: updatedProfile.airtableUserId,
                email: updatedProfile.email,
                firstName: updatedProfile.firstName,
                lastName: updatedProfile.lastName,
                role: updatedProfile.role,
                createdAt: updatedProfile.createdAt,
                updatedAt: updatedProfile.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Error updating user profile for ${userId}:`, error.message, error.stack);
            this.logger.error(`Full error object:`, error);
            throw new Error('Unable to update user profile');
        }
    }
    async getCurrentUser(req) {
        const userId = req.user?.airtableUserId;
        if (!userId) {
            this.logger.error('No user ID found in request user data');
            throw new Error('User authentication required');
        }
        try {
            this.logger.log(`Attempting to retrieve user info for: ${userId}`);
            const userProfile = await Promise.race([
                this.authService.findUserByAirtableUserId(userId),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Database timeout - using degraded mode')), 5000))
            ]);
            this.logger.log(`Successfully retrieved user info for: ${userId}`);
            return {
                id: userProfile.airtableUserId,
                email: userProfile.email,
                firstName: userProfile.firstName,
                lastName: userProfile.lastName,
                companyName: userProfile.companyName,
                mcNumber: userProfile.mcNumber,
                dotNumber: userProfile.dotNumber,
                role: userProfile.role,
                verificationStatus: userProfile.verificationStatus,
            };
        }
        catch (error) {
            this.logger.error(`Error retrieving user info for ${userId}:`, error.message, error.stack);
            if (error.message.includes('timeout') || error.message.includes('connection')) {
                this.logger.warn(`Database unavailable, returning degraded user info for ${userId}`);
                return {
                    id: userId,
                    email: req.user?.email || '<EMAIL>',
                    firstName: 'Unknown',
                    lastName: 'User',
                    companyName: 'Unknown Company',
                    mcNumber: req.user?.mcNumber || null,
                    role: req.user?.role || 'CARRIER',
                    verificationStatus: 'Pending',
                    _degradedMode: true,
                    _message: 'Database temporarily unavailable - showing basic user info from JWT'
                };
            }
            if (error.message === 'Database query timeout') {
                throw new Error('Service temporarily unavailable. Please try again.');
            }
            if (error.message.includes('not found')) {
                throw new Error('User profile not found. Please contact support.');
            }
            if (error.message.includes('connection') || error.message.includes('closed')) {
                throw new Error('Database connection error. Please try again shortly.');
            }
            throw new Error('Unable to retrieve user information. Please try again.');
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Get)('profile'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: 'Get current user profile',
        description: 'Get the current authenticated user\'s profile information'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns current user profile',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                airtableUserId: { type: 'string' },
                email: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string', enum: ['ADMIN', 'CARRIER'] },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getUserProfile", null);
__decorate([
    (0, common_1.Patch)('profile'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: 'Update current user profile',
        description: 'Update the current authenticated user\'s profile information'
    }),
    (0, swagger_1.ApiBody)({ type: UpdateUserProfileDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns updated user profile'
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, UpdateUserProfileDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "updateUserProfile", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: 'Get current user information',
        description: 'Get the current authenticated user\'s profile and role information'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns current user information including role',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                clerkUserId: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' },
                role: { type: 'string', enum: ['ADMIN', 'CARRIER'] },
                orgName: { type: 'string' },
                orgRole: { type: 'string' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal Server Error' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getCurrentUser", null);
exports.AuthController = AuthController = AuthController_1 = __decorate([
    (0, swagger_1.ApiTags)('auth'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map