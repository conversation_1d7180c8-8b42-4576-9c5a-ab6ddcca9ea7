Multiple select
Array of selected option names.

When creating or updating records, if a choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

Similar to multipleAttachments and multipleCollaborators, this array-type field will override the current cell value when being updated. Be sure to spread the current cell value if you want to keep the currently selected choices.

Cell format V1

array of strings
Array of selected option names.

Cell format V2 (webhooks)

array of the below object
id
string
color
optional<string>
Optional when the select field is configured to not use colors.

Allowed values: "blueLight2", "cyanLight2", "tealLight2", "greenLight2", "yellowLight2", "orangeLight2", "redLight2", "pinkLight2", "purpleLight2", "grayLight2", "blueLight1", "cyanLight1", "tealLight1", "greenLight1", "yellowLight1", "orangeLight1", "redLight1", "pinkLight1", "purpleLight1", "grayLight1", "blueBright", "cyanBright", "tealBright", "greenBright", "yellowBright", "orangeBright", "redBright", "pinkBright", "purpleBright", "grayBright", "blueDark1", "cyanDark1", "tealDark1", "greenDark1", "yellowDark1", "orangeDark1", "redDark1", "pinkDark1", "purpleDark1", "grayDark1"

name
string
Field type and options (read)

type
"multipleSelects"
options
object
choices
array of the below object
id
string
color
optional<string>
Optional when the select field is configured to not use colors.

Allowed values: "blueLight2", "cyanLight2", "tealLight2", "greenLight2", "yellowLight2", "orangeLight2", "redLight2", "pinkLight2", "purpleLight2", "grayLight2", "blueLight1", "cyanLight1", "tealLight1", "greenLight1", "yellowLight1", "orangeLight1", "redLight1", "pinkLight1", "purpleLight1", "grayLight1", "blueBright", "cyanBright", "tealBright", "greenBright", "yellowBright", "orangeBright", "redBright", "pinkBright", "purpleBright", "grayBright", "blueDark1", "cyanDark1", "tealDark1", "greenDark1", "yellowDark1", "orangeDark1", "redDark1", "pinkDark1", "purpleDark1", "grayDark1"

name
string
Field type and options (write)

type
"multipleSelects"
options
object
choices
array of the below object
id
optional<string>
This is not specified when creating new options, useful when specifing existing options (for example: reordering options, keeping old options and adding new ones, etc)

color
optional<string>
Optional when creating an option.

name
string
Number
A integer(whole number, e.g. 1, 32, 99) or decimal number showing decimal digits. Precision set with the field config.

Cell format

number
Field type and options

type
"number"
options
object
precision
number
Indicates the number of digits shown to the right of the decimal point for this field. (0-8 inclusive)

Percent
Decimal number representing a percentage value. For example, the underlying cell value for 12.3% is 0.123.

Cell format

number
Field type and options

type
"percent"
options
object
precision
number
Indicates the number of digits shown to the right of the decimal point for this field. (0-8 inclusive)

Phone
A telephone number, e.g. "(*************".

Cell format

string
Field type and options

type
"phoneNumber"
Rating
A positive integer (e.g. "3 stars" is 3). A rating cannot be 0.

Cell format

number
Field type and options

Bases on a free or plus plan are limited to using the 'star' icon and 'yellowBright' color.

type
"rating"
options
object
color
"yellowBright" | "orangeBright" | "redBright" | "pinkBright" | "purpleBright" | "blueBright" | "cyanBright" | "tealBright" | "greenBright" | "grayBright"
The color of selected icons.

icon
"star" | "heart" | "thumbsUp" | "flag" | "dot"
The icon name used to display the rating.

max
number
The maximum value for the rating, from 1 to 10 inclusive.

Rich text
Long text (with rich text formatting enabled)

A Markdown-inspired markup language. Learn more about using Markdown in long text's rich text formatting API.

Cell format

string
Field type and options

type
"richText"
Rollup
A rollup allows you to summarize data from records that are linked to this table.

Cell format V1 (read only)

string | number | true
Cell format V2 (webhooks)

any
Field type and options (read only)

type
"rollup"
options
object
fieldIdInLinkedTable
optional<string>
The id of the field in the linked table

recordLinkFieldId
optional<string>
The linked field id

result
optional<Field type and options | null>
The resulting field type and options for the rollup. See other field type configs on this page for the possible values. Can be null if invalid.

isValid
optional<boolean>
referencedFieldIds
optional<array of strings>
The ids of any fields referenced in the rollup formula

Single line text
A single line of text.

Cell format

string
Field type and options

type
"singleLineText"
Single select
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

Cell format V1

string
Cell format V2 (webhooks)

id
string
color
optional<string>
Optional when the select field is configured to not use colors.

Allowed values: "blueLight2", "cyanLight2", "tealLight2", "greenLight2", "yellowLight2", "orangeLight2", "redLight2", "pinkLight2", "purpleLight2", "grayLight2", "blueLight1", "cyanLight1", "tealLight1", "greenLight1", "yellowLight1", "orangeLight1", "redLight1", "pinkLight1", "purpleLight1", "grayLight1", "blueBright", "cyanBright", "tealBright", "greenBright", "yellowBright", "orangeBright", "redBright", "pinkBright", "purpleBright", "grayBright", "blueDark1", "cyanDark1", "tealDark1", "greenDark1", "yellowDark1", "orangeDark1", "redDark1", "pinkDark1", "purpleDark1", "grayDark1"

name
string
Field type and options (read)

type
"singleSelect"
options
object
choices
array of the below object
id
string
color
optional<string>
Optional when the select field is configured to not use colors.

Allowed values: "blueLight2", "cyanLight2", "tealLight2", "greenLight2", "yellowLight2", "orangeLight2", "redLight2", "pinkLight2", "purpleLight2", "grayLight2", "blueLight1", "cyanLight1", "tealLight1", "greenLight1", "yellowLight1", "orangeLight1", "redLight1", "pinkLight1", "purpleLight1", "grayLight1", "blueBright", "cyanBright", "tealBright", "greenBright", "yellowBright", "orangeBright", "redBright", "pinkBright", "purpleBright", "grayBright", "blueDark1", "cyanDark1", "tealDark1", "greenDark1", "yellowDark1", "orangeDark1", "redDark1", "pinkDark1", "purpleDark1", "grayDark1"

name
string
Field type and options (write)

type
"singleSelect"
options
object
choices
array of the below object
id
optional<string>
This is not specified when creating new options, useful when specifing existing options (for example: reordering options, keeping old options and adding new ones, etc)

color
optional<string>
Optional when creating an option.

name
string
Sync source
Shows the name of the source that a record is synced from. This field is only available on synced tables.

Cell format V1 (read only)

string
The sync source name.

Cell format V2 (webhooks)

id
string
The id unique for this source within this base. Not the baseId.

name
string
The sync source name.

color
optional<string>
Field type and options

type
"externalSyncSource"
options
object
choices
array of the below object
id
string
color
optional<string>
Optional when the select field is configured to not use colors.

Allowed values: "blueLight2", "cyanLight2", "tealLight2", "greenLight2", "yellowLight2", "orangeLight2", "redLight2", "pinkLight2", "purpleLight2", "grayLight2", "blueLight1", "cyanLight1", "tealLight1", "greenLight1", "yellowLight1", "orangeLight1", "redLight1", "pinkLight1", "purpleLight1", "grayLight1", "blueBright", "cyanBright", "tealBright", "greenBright", "yellowBright", "orangeBright", "redBright", "pinkBright", "purpleBright", "grayBright", "blueDark1", "cyanDark1", "tealDark1", "greenDark1", "yellowDark1", "orangeDark1", "redDark1", "pinkDark1", "purpleDark1", "grayDark1"

name
string
Url
A valid URL (e.g. airtable.com or https://airtable.com/universe).

Cell format

string
Field type and options

type
"url"