"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = exports.AdminBidResponseDto = exports.UpdateSystemSettingsDto = exports.VerifyCarrierDto = exports.UpdateUserRoleDto = exports.UserRole = void 0;
const common_1 = require("@nestjs/common");
const admin_service_1 = require("./admin.service");
const auth_guard_1 = require("../auth/auth.guard");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_2 = require("../auth/auth.guard");
const class_validator_1 = require("class-validator");
var UserRole;
(function (UserRole) {
    UserRole["CARRIER"] = "CARRIER";
    UserRole["ADMIN"] = "ADMIN";
})(UserRole || (exports.UserRole = UserRole = {}));
class UpdateUserRoleDto {
    role;
}
exports.UpdateUserRoleDto = UpdateUserRoleDto;
__decorate([
    (0, class_validator_1.IsEnum)(UserRole),
    __metadata("design:type", String)
], UpdateUserRoleDto.prototype, "role", void 0);
class VerifyCarrierDto {
    isVerified;
}
exports.VerifyCarrierDto = VerifyCarrierDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], VerifyCarrierDto.prototype, "isVerified", void 0);
class UpdateSystemSettingsDto {
    platformName;
    supportEmail;
    maintenanceMode;
    autoAssignLoads;
    requireLoadApproval;
    maxLoadsPerCarrier;
    loadExpirationHours;
    requireInsuranceVerification;
    requireDotVerification;
    autoApproveCarriers;
    verificationReminderDays;
    enableEmailNotifications;
    enableSmsNotifications;
    notificationFrequency;
    requireTwoFactor;
    sessionTimeoutMinutes;
    maxLoginAttempts;
    passwordExpirationDays;
    defaultPaymentTerms;
    latePaymentFeePercent;
    invoiceReminderDays;
    maxFileUploadSize;
    rateLimitPerMinute;
    enableLoadTracking;
    enableRealTimeUpdates;
    enableAdvancedReporting;
    enableApiAccess;
    maintenanceWindowStart;
    maintenanceWindowEnd;
    backupFrequency;
}
exports.UpdateSystemSettingsDto = UpdateSystemSettingsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "platformName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "supportEmail", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "maintenanceMode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "autoAssignLoads", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "requireLoadApproval", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "maxLoadsPerCarrier", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(168),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "loadExpirationHours", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "requireInsuranceVerification", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "requireDotVerification", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "autoApproveCarriers", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "verificationReminderDays", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "enableEmailNotifications", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "enableSmsNotifications", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['REAL_TIME', 'HOURLY', 'DAILY']),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "notificationFrequency", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "requireTwoFactor", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(5),
    (0, class_validator_1.Max)(480),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "sessionTimeoutMinutes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(3),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "maxLoginAttempts", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "passwordExpirationDays", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "defaultPaymentTerms", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "latePaymentFeePercent", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "invoiceReminderDays", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "maxFileUploadSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(10),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], UpdateSystemSettingsDto.prototype, "rateLimitPerMinute", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "enableLoadTracking", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "enableRealTimeUpdates", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "enableAdvancedReporting", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSystemSettingsDto.prototype, "enableApiAccess", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "maintenanceWindowStart", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "maintenanceWindowEnd", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['HOURLY', 'DAILY', 'WEEKLY']),
    __metadata("design:type", String)
], UpdateSystemSettingsDto.prototype, "backupFrequency", void 0);
class AdminBidResponseDto {
    response;
    counterOfferAmount;
    notes;
}
exports.AdminBidResponseDto = AdminBidResponseDto;
__decorate([
    (0, class_validator_1.IsEnum)(['accepted', 'countered', 'declined']),
    (0, swagger_1.ApiProperty)({
        description: 'Admin response type',
        enum: ['accepted', 'countered', 'declined'],
        example: 'accepted'
    }),
    __metadata("design:type", String)
], AdminBidResponseDto.prototype, "response", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.01),
    (0, swagger_1.ApiProperty)({
        description: 'Counter offer amount (required if response is countered)',
        example: 1400.00,
        required: false
    }),
    __metadata("design:type", Number)
], AdminBidResponseDto.prototype, "counterOfferAmount", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        description: 'Admin notes about the response',
        example: 'Price adjusted due to fuel costs',
        required: false
    }),
    __metadata("design:type", String)
], AdminBidResponseDto.prototype, "notes", void 0);
let AdminController = class AdminController {
    adminService;
    constructor(adminService) {
        this.adminService = adminService;
    }
    async getCurrentUserInfo(params, body, req) {
        return this.adminService.getCurrentUserInfo(req);
    }
    async promoteFirstAdmin() {
        return this.adminService.promoteFirstAdmin();
    }
    async getAllUsers() {
        return this.adminService.getAllUsers();
    }
    async verifyCarrier(userId, verifyCarrierDto) {
        return this.adminService.verifyCarrier(userId, verifyCarrierDto.isVerified);
    }
    async updateUserRole(userId, updateUserRoleDto) {
        return this.adminService.updateUserRole(userId, updateUserRoleDto.role);
    }
    async getSystemSettings() {
        return this.adminService.getSystemSettings();
    }
    async updateSystemSettings(updateData) {
        const updatedSettings = await this.adminService.updateSystemSettings(updateData);
        return {
            message: 'System settings updated successfully',
            settings: updatedSettings
        };
    }
    async resetSystemSettings() {
        const defaultSettings = await this.adminService.resetSystemSettings();
        return {
            message: 'System settings reset to defaults',
            settings: defaultSettings
        };
    }
    async getPendingBids(req) {
        return await this.adminService.getPendingBids();
    }
    async getAllBids(req, status, carrierId, loadId, dateFrom, dateTo, page, limit) {
        const filters = {
            status,
            carrierId,
            loadId,
            dateFrom: dateFrom ? new Date(dateFrom) : undefined,
            dateTo: dateTo ? new Date(dateTo) : undefined,
            page: page ? parseInt(page) : 1,
            limit: limit ? parseInt(limit) : 20
        };
        return await this.adminService.getAllBidsWithFilters(filters);
    }
    async respondToBid(bidId, responseDto, req) {
        return await this.adminService.respondToBid(bidId, responseDto, req.user.airtableUserId);
    }
    async getBidHistory(bidId) {
        return await this.adminService.getBidHistory(bidId);
    }
    async getBiddingStats(req) {
        return await this.adminService.getBiddingStatistics();
    }
    async processExpiredBids(req) {
        return await this.adminService.processExpiredBids();
    }
    async getBidsExpiringSoon(req, hours) {
        const hoursFromNow = hours ? parseInt(hours) : 2;
        return await this.adminService.getBidsExpiringSoon(hoursFromNow);
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.Get)('debug/me'),
    (0, common_1.UseGuards)(auth_guard_2.AuthGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user info (debug endpoint)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns current user information' }),
    __param(0, (0, common_1.Param)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getCurrentUserInfo", null);
__decorate([
    (0, common_1.Post)('debug/promote-first-admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Promote first user to admin (debug endpoint)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Promotes first user to admin role' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "promoteFirstAdmin", null);
__decorate([
    (0, common_1.Get)('users'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns all users in the system' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.Patch)('users/:userId/verify'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Verify/unverify carrier (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Carrier verification status updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User or carrier profile not found' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, VerifyCarrierDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "verifyCarrier", null);
__decorate([
    (0, common_1.Patch)('users/:userId/role'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Update user role (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User role updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, UpdateUserRoleDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateUserRole", null);
__decorate([
    (0, common_1.Get)('settings'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Get system settings (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns current system settings' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getSystemSettings", null);
__decorate([
    (0, common_1.Put)('settings'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Update system settings (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System settings updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid settings data' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [UpdateSystemSettingsDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateSystemSettings", null);
__decorate([
    (0, common_1.Post)('settings/reset'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Reset system settings to defaults (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System settings reset to defaults' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "resetSystemSettings", null);
__decorate([
    (0, common_1.Get)('bids/pending'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all pending bids for admin review',
        description: 'Retrieve all pending bids with load details and carrier information for admin management'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of pending bids retrieved successfully'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getPendingBids", null);
__decorate([
    (0, common_1.Get)('bids'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all bids with filtering',
        description: 'Retrieve all bids with advanced filtering options for admin dashboard'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('carrierId')),
    __param(3, (0, common_1.Query)('loadId')),
    __param(4, (0, common_1.Query)('dateFrom')),
    __param(5, (0, common_1.Query)('dateTo')),
    __param(6, (0, common_1.Query)('page')),
    __param(7, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAllBids", null);
__decorate([
    (0, common_1.Post)('bids/:bidId/respond'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Respond to a bid',
        description: 'Accept, counter, or decline a carrier bid with optional notes and counter-offer amount'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Bid response processed successfully'
    }),
    __param(0, (0, common_1.Param)('bidId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, AdminBidResponseDto, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "respondToBid", null);
__decorate([
    (0, common_1.Get)('bids/:bidId/history'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get bid negotiation history',
        description: 'Retrieve complete negotiation history for a specific bid'
    }),
    __param(0, (0, common_1.Param)('bidId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getBidHistory", null);
__decorate([
    (0, common_1.Get)('bidding/stats'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get bidding statistics',
        description: 'Retrieve bidding performance metrics and statistics for admin dashboard'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getBiddingStats", null);
__decorate([
    (0, common_1.Post)('bids/process-expired'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Process expired bids',
        description: 'Manually trigger processing of expired bids (normally runs automatically)'
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "processExpiredBids", null);
__decorate([
    (0, common_1.Get)('bids/expiring-soon'),
    (0, common_1.UseGuards)(auth_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get bids expiring soon',
        description: 'Retrieve bids that will expire within the specified hours (default: 2 hours)'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('hours')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getBidsExpiringSoon", null);
exports.AdminController = AdminController = __decorate([
    (0, swagger_1.ApiTags)('admin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin'),
    __metadata("design:paramtypes", [admin_service_1.AdminService])
], AdminController);
//# sourceMappingURL=admin.controller.js.map