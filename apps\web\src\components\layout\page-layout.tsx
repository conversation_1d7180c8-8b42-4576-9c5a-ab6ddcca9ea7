'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Home, ListCollapse, ScrollText, Shield, CreditCard, Package } from 'lucide-react';
import { OnboardingTour, useOnboardingTour } from '@/components/ui/onboarding-tour';
import { MultiOrgErrorBoundary } from '@/components/MultiOrgErrorBoundary';
import { useAuth } from '@/contexts/auth-context';
import TopNavigation from './TopNavigation';

interface PageLayoutProps {
  children: React.ReactNode;
  orgId?: string;
  title?: string;
  description?: string;
}

const baseNavItems = [
  { linkPath: '/dashboard', label: 'Dashboard', icon: Home },
  { linkPath: '/loadboard', label: 'Loadboard', icon: ListCollapse },
  { linkPath: '/my-loads', label: 'My Loads', icon: ScrollText },
  // { linkPath: '/billing', label: 'Billing', icon: CreditCard }, // Temporarily disabled
];

const operationsNavItem = { linkPath: '/operations', label: 'Operations', icon: Package };

const adminNavItems = [
  { linkPath: '/admin', label: 'Admin', icon: Shield },
];

export function PageLayout({ children }: PageLayoutProps) {
  const pathname = usePathname();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isCheckingAdmin, setIsCheckingAdmin] = useState(true);
  const [hasOperationsAccess, setHasOperationsAccess] = useState(false);
  
  const { user, isLoading: authLoading } = useAuth();
  
  // Use full width layout for all pages
  
  // Onboarding tour
  const { isTourOpen, hasCompletedTour, startTour, completeTour, skipTour } = useOnboardingTour();

  // Update admin status when auth data changes
  useEffect(() => {
    if (user) {
      setIsAdmin(user.role === 'ADMIN');
      setHasOperationsAccess(user.companyName === 'First Cut Produce' || user.role === 'ADMIN');
      setIsCheckingAdmin(false);
    } else if (!authLoading) {
      setIsAdmin(false);
      setHasOperationsAccess(false);
      setIsCheckingAdmin(false);
    }
  }, [user, authLoading]);

  const navItems = [
    ...baseNavItems,
    ...(hasOperationsAccess ? [operationsNavItem] : []),
    ...(isAdmin ? adminNavItems : []),
  ].map(item => ({
    ...item,
    href: item.linkPath,
  }));

  const settingsHref = '/settings';

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background">
        <TopNavigation
          navItems={[]}
          settingsHref="#"
          isCheckingAdmin={true}
          hasCompletedTour={true}
          startTour={() => {}}
        />
        
        <main className="pt-16 min-h-screen top-nav-layout">
          <div className="w-full px-4 lg:px-8 py-8">
            <div className="space-y-4 animate-pulse">
              <div className="h-8 bg-muted rounded-lg max-w-md"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-20 bg-muted rounded-lg"></div>
                ))}
              </div>
              <div className="h-64 bg-muted rounded-lg"></div>
            </div>
          </div>
        </main>
        
        {/* Onboarding Tour */}
        <OnboardingTour
          isOpen={isTourOpen}
          onComplete={completeTour}
          onSkip={skipTour}
        />
      </div>
    );
  }

  return (
    <MultiOrgErrorBoundary>
      <div className="min-h-screen bg-background">
              <TopNavigation
        navItems={navItems}
          settingsHref={settingsHref}
          isCheckingAdmin={isCheckingAdmin}
          hasCompletedTour={hasCompletedTour}
          startTour={startTour}
        />
        
        <main className="pt-16 min-h-screen top-nav-layout">
          <div className="w-full px-4 lg:px-8 py-8">
            {children}
          </div>
        </main>

        {/* Onboarding Tour */}
        <OnboardingTour
          isOpen={isTourOpen}
          onComplete={completeTour}
          onSkip={skipTour}
        />
      </div>
    </MultiOrgErrorBoundary>
  );
} 