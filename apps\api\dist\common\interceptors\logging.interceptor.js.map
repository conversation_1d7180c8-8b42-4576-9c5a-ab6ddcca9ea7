{"version": 3, "file": "logging.interceptor.js", "sourceRoot": "", "sources": ["../../../src/common/interceptors/logging.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAMwB;AAExB,8CAAiD;AAEjD,mCAAoC;AAG7B,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IACZ,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9D,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAG7C,MAAM,aAAa,GAAI,OAAe,CAAC,aAAa,IAAI,IAAA,mBAAU,GAAE,CAAC;QACpE,OAAe,CAAC,aAAa,GAAG,aAAa,CAAC;QAG/C,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAEtD,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAC9D,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAI,OAAe,CAAC,IAAI,EAAE,GAAG,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG7B,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,qBAAqB,aAAa,GAAG,EACrC;YACE,aAAa;YACb,MAAM;YACN,GAAG;YACH,SAAS;YACT,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YACjC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YACnC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC,CAAC,IAAI;SAChB,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAGrC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sBAAsB,aAAa,GAAG,EACtC;gBACE,aAAa;gBACb,MAAM;gBACN,GAAG;gBACH,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACpD,WAAW,EAAE,CAAC,CAAC,IAAI;aACpB,CACF,CAAC;QACJ,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAGrC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kBAAkB,aAAa,GAAG,EAClC;gBACE,aAAa;gBACb,MAAM;gBACN,GAAG;gBACH,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;gBACjC,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CACF,CAAC;YAGF,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,GAAQ;QAC7B,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,GAAG,CAAC;QAEhD,MAAM,SAAS,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;QAG7B,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAExF,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC5B,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrE,SAAS,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AArGY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CAqG9B"}