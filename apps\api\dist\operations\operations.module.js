"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperationsModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const operations_controller_1 = require("./operations.controller");
const operations_service_1 = require("./operations.service");
const pattern_analysis_service_1 = require("./services/pattern-analysis.service");
const smart_suggestions_service_1 = require("./services/smart-suggestions.service");
const radar_distance_service_1 = require("./services/radar-distance.service");
const prisma_module_1 = require("../prisma/prisma.module");
const auth_module_1 = require("../auth/auth.module");
let OperationsModule = class OperationsModule {
};
exports.OperationsModule = OperationsModule;
exports.OperationsModule = OperationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            config_1.ConfigModule,
        ],
        controllers: [operations_controller_1.OperationsController],
        providers: [
            operations_service_1.OperationsService,
            pattern_analysis_service_1.PatternAnalysisService,
            smart_suggestions_service_1.SmartSuggestionsService,
            radar_distance_service_1.RadarDistanceService
        ],
        exports: [operations_service_1.OperationsService],
    })
], OperationsModule);
//# sourceMappingURL=operations.module.js.map