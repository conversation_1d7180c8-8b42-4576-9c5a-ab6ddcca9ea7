import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { PrismaService } from '../prisma/prisma.service';
import { CircuitBreakerService } from '../common/services/circuit-breaker.service';
import { AirtableLoadWebhookDto } from './dto/airtable-load.dto';
import { AdvancedFiltersDto } from './dto/advanced-filters.dto';
import { SavedSearchDto } from './dto/saved-search.dto';
import { Load, SavedSearch } from '@repo/db';
import { RequestBookingDetailsDto } from './dto/request-booking-details.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { AuthService } from '../auth/auth.service';
export interface LoadBookingConfirmation {
    message: string;
    loadId: string;
    carrierUserId: string;
}
export declare class AirtableOrdersService {
    private configService;
    private prisma;
    private cacheManager;
    private notificationsService;
    private circuitBreakerService;
    private authService;
    private readonly logger;
    private base;
    private tableName;
    constructor(configService: ConfigService, prisma: PrismaService, cacheManager: Cache, notificationsService: NotificationsService, circuitBreakerService: CircuitBreakerService, authService: AuthService);
    private getAirtableRecordById;
    private mapAirtableStatusToEnum;
    private parseTargetOrganizations;
    private calculateMilesForLoad;
    private calculateSingleRouteDistance;
    private calculateFallbackDistance;
    private mapAirtableInvStatusToEnum;
    processLoadWebhook(payload: AirtableLoadWebhookDto): Promise<Load>;
    private sendLoadNotifications;
    getAvailableLoads(userAirtableId?: string): Promise<any[]>;
    getAvailableLoadsWithFilters(userAirtableId?: string, filters?: AdvancedFiltersDto): Promise<{
        loads: any[];
        totalCount: number;
        page: number;
        pageSize: number;
    }>;
    private applyGeographicFilter;
    private applySorting;
    syncAllLoadsFromAirtable(syncAll?: boolean): Promise<{
        syncedCount: number;
        errors: any[];
    }>;
    private processSingleRecord;
    requestLoadBooking(loadId: string, requestingUserClerkId: string, bookingDetailsDto: RequestBookingDetailsDto): Promise<LoadBookingConfirmation>;
    getAssignedLoadsForCarrier(airtableUserId: string): Promise<any[]>;
    uploadLoadDocument(loadId: string, documentType: 'bol' | 'pod' | 'invoice', fileUrl: string, airtableUserId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    assignLoadToCarrier(airtableRecordId: string, clerkUserId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    updateLoadStatus(airtableRecordId: string, newStatus: string, clerkUserId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    unassignLoadFromCarrier(airtableRecordId: string, adminClerkUserId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    carrierCancelLoad(airtableRecordId: string, airtableUserId: string, reason?: string): Promise<{
        success: boolean;
        message: string;
    }>;
    createBid(loadAirtableRecordId: string, airtableUserId: string, bidAmount: number, carrierNotes?: string): Promise<{
        success: boolean;
        bid: any;
        message: string;
    }>;
    getCarrierBids(airtableUserId: string): Promise<any[]>;
    private ensureBidsFieldExists;
    private updateAirtableBids;
    withdrawBid(bidId: string, clerkUserId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    debugAirtableBidUpdate(loadAirtableRecordId: string): Promise<any>;
    diagnoseLoadForBidding(loadAirtableRecordId: string): Promise<any>;
    getSavedSearches(userAirtableId: string): Promise<any[]>;
    saveSearch(userAirtableId: string, savedSearchDto: SavedSearchDto): Promise<SavedSearch>;
    updateSavedSearch(userAirtableId: string, searchId: string, savedSearchDto: SavedSearchDto): Promise<SavedSearch>;
    deleteSavedSearch(userAirtableId: string, searchId: string): Promise<void>;
    respondToCounterOffer(bidId: string, carrierUserId: string, response: 'accepted' | 'declined', notes?: string): Promise<{
        success: boolean;
        message: string;
        bid?: any;
    }>;
    private sendCounterOfferResponseNotifications;
    getDashboardMetrics(airtableUserId: string): Promise<any>;
    getMyLoadsFromAirtable(airtableUserId: string, filters?: {
        status?: string;
        search?: string;
        dateRange?: string;
    }): Promise<any[]>;
}
