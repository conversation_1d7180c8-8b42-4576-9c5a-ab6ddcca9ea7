import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service'; // Adjust path if needed
import { CreateCarrierProfileDto, UpdateCarrierProfileDto } from './dto';
import { Prisma, CarrierProfile, Role } from '@repo/db'; // Import Prisma types from @repo/db

@Injectable()
export class CarrierProfilesService {
  constructor(private prisma: PrismaService) {}

  async create(
    createCarrierProfileDto: CreateCarrierProfileDto,
    airtableUserId: string, // Updated to use N8N auth
  ): Promise<CarrierProfile> {
    // 1. Find the User record corresponding to the airtableUserId (which is the N8N 'id' claim)
    let user = await this.prisma.user.findUnique({
      where: { airtableUserId: airtableUserId }, // Changed here
    });

    if (!user) {
      // User doesn't exist in local database yet, but they should exist in Airtable
      // Create the user record using minimal required fields
      try {
                  user = await this.prisma.user.create({
            data: {
              airtableUserId: airtableUserId,
              email: createCarrierProfileDto.contact_email || `${airtableUserId}@placeholder.com`,
              firstName: 'Unknown',
              lastName: 'User',
              role: Role.CARRIER,
            },
          });
        console.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
      } catch (error) {
        throw new NotFoundException(`Unable to create User record for Airtable ID ${airtableUserId}: ${error.message}`);
      }
    }

    // 2. Check if a profile already exists for this user
    const existingProfile = await this.prisma.carrierProfile.findUnique({
      where: { userId: user.id },
    });

    if (existingProfile) {
      // Decide on behavior: throw error or update existing?
      // For now, let's throw an error, assuming POST is only for initial creation.
      // Could also return the existing profile or update it.
      throw new ConflictException(`Carrier profile already exists for user ${user.id}`);
      // Or return existingProfile;
      // Or update: return this.updateByUserId(user.id, createCarrierProfileDto);
    }

    // 3. Create the new carrier profile
    // We spread the DTO and explicitly add the userId from the found User record.
    const data: Prisma.CarrierProfileCreateInput = {
      ...createCarrierProfileDto,
      user: {
        connect: { id: user.id }, // Connect to the existing user record
      },
      // Ensure JSON fields are handled correctly if needed (Prisma usually handles JS objects)
      // equipmentTypes: createCarrierProfileDto.equipmentTypes as Prisma.InputJsonValue | undefined,
      // serviceableRegions: createCarrierProfileDto.serviceableRegions as Prisma.InputJsonValue | undefined,
    };

    return this.prisma.carrierProfile.create({ data });
  }

  async findAll(): Promise<CarrierProfile[]> {
    // TODO: Add pagination, filtering, sorting later
    return this.prisma.carrierProfile.findMany();
  }

  async findOne(id: string): Promise<CarrierProfile> {
    const profile = await this.prisma.carrierProfile.findUnique({
      where: { id },
      // include: { user: true } // Optionally include related user data
    });
    if (!profile) {
      throw new NotFoundException(`CarrierProfile with ID "${id}" not found`);
    }
    return profile;
  }

  // Find profile by user ID (useful for letting a user fetch their own profile)
  async findOneByUserId(userId: string): Promise<CarrierProfile> {
    const profile = await this.prisma.carrierProfile.findUnique({
      where: { userId },
      // include: { user: true } // Optionally include related user data
    });
    if (!profile) {
      // Use a specific error or maybe return null depending on expected usage
      throw new NotFoundException(`CarrierProfile for user ID "${userId}" not found`);
    }
    return profile;
  }

  // Find profile by Airtable User ID
  async findMyProfileByAirtableUserId(airtableUserId: string): Promise<CarrierProfile> {
    let user = await this.prisma.user.findUnique({
      where: { airtableUserId: airtableUserId }, // Updated to use correct field
    });

    if (!user) {
      // User doesn't exist in local database yet, but they should exist in Airtable
      // Create the user record using minimal required fields
      try {
        user = await this.prisma.user.create({
          data: {
            airtableUserId: airtableUserId,
            email: `${airtableUserId}@placeholder.com`, // Placeholder email
            firstName: 'Unknown',
            lastName: 'User',
            role: Role.CARRIER,
          },
        });
        console.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
      } catch (error) {
        throw new NotFoundException(`Unable to create User record for Airtable ID ${airtableUserId}: ${error.message}`);
      }
    }
    // Now call the existing method that finds a profile by the internal user ID
    return this.findOneByUserId(user.id);
  }

  async update(
    id: string, // Profile ID to update
    updateCarrierProfileDto: UpdateCarrierProfileDto,
  ): Promise<CarrierProfile> {
    // Check if profile exists first
    await this.findOne(id); // Throws NotFoundException if not found

    const data: Prisma.CarrierProfileUpdateInput = {
      ...updateCarrierProfileDto,
      // Ensure JSON fields are handled correctly if needed
      // equipmentTypes: updateCarrierProfileDto.equipmentTypes as Prisma.InputJsonValue | undefined,
      // serviceableRegions: updateCarrierProfileDto.serviceableRegions as Prisma.InputJsonValue | undefined,
    };

    // Prevent changing userId during update if necessary
    // delete data.userId; // Or handle more gracefully if userId is in Update DTO (it isn't currently)

    return this.prisma.carrierProfile.update({
      where: { id },
      data,
    });
  }
  
  // It might be more logical for a user to update *their own* profile
  // based on their user ID rather than the profile's CUID.
  async updateByUserId(
    userId: string,
    updateCarrierProfileDto: UpdateCarrierProfileDto,
  ): Promise<CarrierProfile> {
    // Find the profile ID associated with the user ID
    const profile = await this.findOneByUserId(userId); // Throws NotFoundException if not found
    
    const data: Prisma.CarrierProfileUpdateInput = {
        ...updateCarrierProfileDto,
    };

    return this.prisma.carrierProfile.update({
      where: { id: profile!.id }, // Update using the found profile ID - Added non-null assertion !
      data,
    });
  }

  // Update profile by Airtable User ID
  async updateMyProfileByAirtableUserId(
    airtableUserId: string,
    updateCarrierProfileDto: UpdateCarrierProfileDto,
  ): Promise<CarrierProfile> {
    let user = await this.prisma.user.findUnique({
      where: { airtableUserId: airtableUserId }, // Updated to use correct field
    });

    if (!user) {
      // User doesn't exist in local database yet, but they should exist in Airtable
      // Create the user record using minimal required fields
      try {
        user = await this.prisma.user.create({
          data: {
            airtableUserId: airtableUserId,
            email: updateCarrierProfileDto.contact_email || `${airtableUserId}@placeholder.com`,
            firstName: 'Unknown',
            lastName: 'User',
            role: Role.CARRIER,
          },
        });
        console.log(`Created missing User record for airtableUserId: ${airtableUserId}`);
      } catch (error) {
        console.error(`Error creating User record for ${airtableUserId}:`, error);
        throw new NotFoundException(`Unable to create User record for Airtable ID ${airtableUserId}: ${error.message}`);
      }
    }

    // Check if carrier profile exists for this user
    let carrierProfile = await this.prisma.carrierProfile.findUnique({
      where: { userId: user.id },
    });

    if (!carrierProfile) {
      // Create the carrier profile if it doesn't exist
      try {
        const createData: Prisma.CarrierProfileCreateInput = {
          companyName: updateCarrierProfileDto.companyName || 'New Carrier',
          user: {
            connect: { id: user.id },
          },
        };
        carrierProfile = await this.prisma.carrierProfile.create({ data: createData });
        console.log(`Created missing CarrierProfile for user: ${user.id}`);
      } catch (error) {
        console.error(`Error creating CarrierProfile for user ${user.id}:`, error);
        throw new NotFoundException(`Unable to create CarrierProfile for user ${user.id}: ${error.message}`);
      }
    }

    // Now call the existing method that updates a profile by the internal user ID
    return this.updateByUserId(user.id, updateCarrierProfileDto);
  }

  async remove(id: string): Promise<CarrierProfile> {
    // Check if profile exists first
    await this.findOne(id); // Throws NotFoundException if not found
    // TODO: Consider implications - should bids/loads be handled? Prisma relations might cascade or prevent deletion.
    // Check Prisma schema for onDelete behavior for Bid and Load relations.
    return this.prisma.carrierProfile.delete({
      where: { id },
    });
  }
} 