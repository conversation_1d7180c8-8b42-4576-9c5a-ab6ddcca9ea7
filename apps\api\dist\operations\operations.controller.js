"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OperationsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../auth/auth.guard");
const operations_service_1 = require("./operations.service");
const create_order_dto_1 = require("./dto/create-order.dto");
const auth_service_1 = require("../auth/auth.service");
let OperationsController = OperationsController_1 = class OperationsController {
    operationsService;
    authService;
    logger = new common_1.Logger(OperationsController_1.name);
    requestMetrics = new Map();
    CIRCUIT_BREAKER_CONFIG = {
        failureThreshold: 5,
        recoveryTimeout: 30000,
        requestTimeout: 5000
    };
    constructor(operationsService, authService) {
        this.operationsService = operationsService;
        this.authService = authService;
    }
    checkCircuitBreaker(endpoint) {
        const metrics = this.requestMetrics.get(endpoint);
        if (!metrics)
            return;
        const now = Date.now();
        if (metrics.consecutiveFailures >= this.CIRCUIT_BREAKER_CONFIG.failureThreshold &&
            now - metrics.lastFailureTime < this.CIRCUIT_BREAKER_CONFIG.recoveryTimeout) {
            const waitTime = Math.ceil((this.CIRCUIT_BREAKER_CONFIG.recoveryTimeout - (now - metrics.lastFailureTime)) / 1000);
            throw new common_1.RequestTimeoutException(`Service temporarily unavailable. Please try again in ${waitTime} seconds.`);
        }
    }
    recordMetrics(endpoint, success, responseTime) {
        const existing = this.requestMetrics.get(endpoint) || {
            endpoint,
            successCount: 0,
            failureCount: 0,
            averageResponseTime: 0,
            lastFailureTime: 0,
            consecutiveFailures: 0
        };
        if (success) {
            existing.successCount++;
            existing.consecutiveFailures = 0;
            existing.averageResponseTime = (existing.averageResponseTime + responseTime) / 2;
        }
        else {
            existing.failureCount++;
            existing.consecutiveFailures++;
            existing.lastFailureTime = Date.now();
        }
        this.requestMetrics.set(endpoint, existing);
    }
    async withTimeout(operation, timeoutMs) {
        return Promise.race([
            operation,
            new Promise((_, reject) => setTimeout(() => reject(new common_1.RequestTimeoutException(`Operation timed out after ${timeoutMs}ms`)), timeoutMs))
        ]);
    }
    async getLanes(req) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            await this.verifyOperationsAccess(airtableUserId);
            const lanes = await this.operationsService.getLanes();
            this.logger.log(`Retrieved ${lanes.length} lanes for user ${airtableUserId}`);
            return {
                lanes,
                total: lanes.length
            };
        }
        catch (error) {
            this.logger.error(`Error retrieving lanes for user ${airtableUserId}:`, error);
            throw error;
        }
    }
    async calculateAccurateLanes(req) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            await this.verifyOperationsAccess(airtableUserId);
            this.logger.log(`Starting accurate lane calculation background job for user ${airtableUserId}`);
            const lanes = await this.operationsService.calculateAccurateLanes();
            const summary = `Calculated accurate distances for ${lanes.length} lanes. Future lane requests will be much faster.`;
            this.logger.log(`Completed accurate lane calculation: ${lanes.length} lanes processed for user ${airtableUserId}`);
            return {
                message: 'Accurate lane calculation completed successfully',
                lanesCalculated: lanes.length,
                summary
            };
        }
        catch (error) {
            this.logger.error(`Error calculating accurate lanes for user ${airtableUserId}:`, error);
            throw error;
        }
    }
    async createOrder(req, createOrderDto) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            await this.verifyOperationsAccess(airtableUserId);
            this.logger.log(`Creating order for user ${airtableUserId}:`, {
                poNumber: createOrderDto.poNumber,
                lane: `${createOrderDto.originCity}, ${createOrderDto.originState} → ${createOrderDto.destinationCity}, ${createOrderDto.destinationState}`
            });
            const result = await this.operationsService.createOrder(createOrderDto, airtableUserId);
            this.logger.log(`Successfully created order ${result.id} for user ${airtableUserId}`);
            return {
                id: result.id,
                airtableRecordId: result.airtableRecordId,
                message: 'Order created successfully',
                success: true
            };
        }
        catch (error) {
            this.logger.error(`Error creating order for user ${airtableUserId}:`, error);
            throw error;
        }
    }
    async verifyOperationsAccess(userId) {
        try {
            const user = await this.authService.findUserByAirtableId(userId);
            if (!user) {
                throw new common_1.ForbiddenException('User not found in system');
            }
            const hasAccess = user.role === 'ADMIN' ||
                (user.mcNumber && ['123456', '789012'].includes(user.mcNumber));
            if (!hasAccess) {
                this.logger.warn(`Access denied for user ${userId}: mcNumber=${user.mcNumber}, role=${user.role}`);
                throw new common_1.ForbiddenException('Access denied. Operations page is only accessible to authorized carriers and administrators.');
            }
            this.logger.log(`Operations access granted for user ${userId}: mcNumber=${user.mcNumber}, role=${user.role}`);
        }
        catch (error) {
            if (error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Error verifying operations access for user ${userId}:`, error);
            throw new common_1.ForbiddenException('Unable to verify operations access');
        }
    }
    async getSmartSuggestions(req, originCity, originState, destinationCity, destinationState, currentValues) {
        const startTime = Date.now();
        const endpoint = 'get-smart-suggestions';
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            this.checkCircuitBreaker(endpoint);
            await this.verifyOperationsAccess(airtableUserId);
            if (!originCity || !originState || !destinationCity || !destinationState) {
                throw new common_1.BadRequestException('Origin and destination city/state are required');
            }
            const parsedCurrentValues = currentValues ? JSON.parse(currentValues) : undefined;
            const suggestions = await this.withTimeout(this.operationsService.getSmartSuggestions(originCity.trim(), originState.trim(), destinationCity.trim(), destinationState.trim(), airtableUserId, parsedCurrentValues), this.CIRCUIT_BREAKER_CONFIG.requestTimeout);
            const responseTime = Date.now() - startTime;
            this.recordMetrics(endpoint, true, responseTime);
            this.logger.log(`Smart suggestions generated successfully in ${responseTime}ms for user ${airtableUserId}`);
            return suggestions;
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.recordMetrics(endpoint, false, responseTime);
            this.logger.error(`Error getting smart suggestions for user ${airtableUserId}:`, error);
            if (error instanceof common_1.RequestTimeoutException) {
                throw error;
            }
            else if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            else {
                throw new common_1.InternalServerErrorException({
                    message: 'Smart suggestions temporarily unavailable',
                    fallback: {
                        suggestions: [],
                        smartDefaults: {},
                        confidence: 0,
                        metadata: {
                            error: 'Service temporarily unavailable',
                            generatedAt: new Date().toISOString(),
                            dataPoints: 0,
                            learningActive: false
                        }
                    }
                });
            }
        }
    }
    async validateOrderWithAI(req, validationData) {
        const startTime = Date.now();
        const endpoint = 'validate-order';
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            this.logger.error('No user ID found in request user data');
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            this.checkCircuitBreaker(endpoint);
            await this.verifyOperationsAccess(airtableUserId);
            if (!validationData.orderData || !validationData.context) {
                throw new common_1.BadRequestException('Order data and context are required');
            }
            const validation = await this.withTimeout(this.operationsService.validateOrderWithAI(validationData.orderData, validationData.context), this.CIRCUIT_BREAKER_CONFIG.requestTimeout);
            const responseTime = Date.now() - startTime;
            this.recordMetrics(endpoint, true, responseTime);
            this.logger.log(`Order validation completed successfully in ${responseTime}ms for user ${airtableUserId}`);
            return validation;
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.recordMetrics(endpoint, false, responseTime);
            this.logger.error(`Error validating order for user ${airtableUserId}:`, error);
            if (error instanceof common_1.RequestTimeoutException) {
                throw error;
            }
            else if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            else {
                return {
                    isValid: true,
                    warnings: [],
                    suggestions: [],
                    criticalIssues: [{
                            type: 'system_error',
                            message: 'Validation system temporarily unavailable',
                            details: 'Please try again in a few moments'
                        }]
                };
            }
        }
    }
    async recordFeedback(req, orderId, feedbackData) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            await this.verifyOperationsAccess(airtableUserId);
            await this.operationsService.recordSuggestionFeedback(orderId, {
                userId: airtableUserId,
                ...feedbackData
            });
            return { success: true, message: 'Feedback recorded successfully' };
        }
        catch (error) {
            this.logger.error(`Error recording feedback for user ${airtableUserId}:`, error);
            return { success: false, message: 'Failed to record feedback' };
        }
    }
    async getAutoComplete(req, field, partialValue, context) {
        const airtableUserId = req.user?.airtableUserId;
        if (!airtableUserId) {
            throw new common_1.BadRequestException('User authentication required');
        }
        try {
            await this.verifyOperationsAccess(airtableUserId);
            const parsedContext = context ? JSON.parse(context) : {};
            const suggestions = await this.operationsService.getAutoCompleteSuggestions(field, partialValue, { ...parsedContext, userId: airtableUserId });
            return { suggestions };
        }
        catch (error) {
            this.logger.error(`Error getting auto-complete for user ${airtableUserId}:`, error);
            return { suggestions: [] };
        }
    }
};
exports.OperationsController = OperationsController;
__decorate([
    (0, common_1.Get)('lanes'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available lanes for operations',
        description: 'Get all available pickup→delivery lane combinations for order creation'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns list of available lanes',
        schema: {
            type: 'object',
            properties: {
                lanes: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            originCity: { type: 'string' },
                            originState: { type: 'string' },
                            destinationCity: { type: 'string' },
                            destinationState: { type: 'string' },
                            estimatedMiles: { type: 'number' },
                            estimatedDuration: { type: 'string' },
                            frequencyRank: { type: 'number' },
                            lastUsed: { type: 'string', format: 'date-time' }
                        }
                    }
                },
                total: { type: 'number' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - insufficient permissions' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "getLanes", null);
__decorate([
    (0, common_1.Post)('lanes/calculate-accurate'),
    (0, swagger_1.ApiOperation)({
        summary: 'Calculate accurate lane distances (Background Job)',
        description: 'Triggers background calculation of accurate distances using Radar.com API. This should be run periodically to improve distance accuracy.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Accurate lane calculation started',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
                lanesCalculated: { type: 'number' },
                summary: { type: 'string' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - insufficient permissions' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "calculateAccurateLanes", null);
__decorate([
    (0, common_1.Post)('orders'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create new order',
        description: 'Create a new freight order with lane-based routing'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Order created successfully',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                airtableRecordId: { type: 'string' },
                message: { type: 'string' },
                success: { type: 'boolean' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid order data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - insufficient permissions' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_order_dto_1.CreateOrderDto]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "createOrder", null);
__decorate([
    (0, common_1.Get)('suggestions'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get smart suggestions for order creation',
        description: 'Get AI-powered suggestions based on historical data and user patterns'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns smart suggestions for order creation'
    }),
    (0, swagger_1.ApiResponse)({
        status: 408,
        description: 'Request timeout - service temporarily unavailable'
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('originCity')),
    __param(2, (0, common_1.Query)('originState')),
    __param(3, (0, common_1.Query)('destinationCity')),
    __param(4, (0, common_1.Query)('destinationState')),
    __param(5, (0, common_1.Query)('currentValues')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "getSmartSuggestions", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, swagger_1.ApiOperation)({
        summary: 'Validate order data with AI-powered rules',
        description: 'Validate order data against business rules and historical patterns'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns validation results'
    }),
    (0, swagger_1.ApiResponse)({
        status: 408,
        description: 'Request timeout - service temporarily unavailable'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "validateOrderWithAI", null);
__decorate([
    (0, common_1.Post)('feedback/:orderId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Record suggestion feedback',
        description: 'Record user feedback on AI suggestions to improve future recommendations'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Feedback recorded successfully'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('orderId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "recordFeedback", null);
__decorate([
    (0, common_1.Get)('autocomplete/:field'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get auto-complete suggestions',
        description: 'Get intelligent auto-complete suggestions for form fields'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns auto-complete suggestions'
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('field')),
    __param(2, (0, common_1.Query)('value')),
    __param(3, (0, common_1.Query)('context')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], OperationsController.prototype, "getAutoComplete", null);
exports.OperationsController = OperationsController = OperationsController_1 = __decorate([
    (0, swagger_1.ApiTags)('operations'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('operations'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [operations_service_1.OperationsService,
        auth_service_1.AuthService])
], OperationsController);
//# sourceMappingURL=operations.controller.js.map