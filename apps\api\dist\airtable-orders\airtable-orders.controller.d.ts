import { AirtableOrdersService } from './airtable-orders.service';
import { AirtableLoadWebhookDto } from './dto/airtable-load.dto';
import { RequestBookingDetailsDto } from './dto/request-booking-details.dto';
import { CreateBidDto } from './dto/create-bid.dto';
import { UploadDocumentDto } from './dto/upload-document.dto';
import { AdvancedFiltersDto, SavedSearchDto } from './dto/advanced-filters.dto';
import { Load } from '@repo/db';
export declare class AirtableOrdersController {
    private readonly airtableOrdersService;
    private readonly logger;
    constructor(airtableOrdersService: AirtableOrdersService);
    getAvailableLoads(req: any, filters?: AdvancedFiltersDto): Promise<{
        loads: any[];
        totalCount: number;
        page: number;
        pageSize: number;
    }>;
    getMyLoads(req: any, status?: string, search?: string, dateRange?: string): Promise<{
        loads: any[];
        totalCount: number;
        companyName: string;
    }>;
    getAssignedLoadsForCarrier(req: any): Promise<any[]>;
    getDashboardMetrics(req: any): Promise<any>;
    requestLoadBooking(loadId: string, bookingDetailsDto: RequestBookingDetailsDto, req: any): Promise<any>;
    processWebhook(payload: AirtableLoadWebhookDto): Promise<Load>;
    syncAllLoads(syncAll?: string): Promise<{
        syncedCount: number;
        errors: any[];
    }>;
    uploadLoadDocument(loadId: string, uploadData: UploadDocumentDto, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    assignLoadToCarrier(airtableRecordId: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    updateLoadStatus(loadId: string, body: {
        status: string;
    }, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    unassignLoadFromCarrier(airtableRecordId: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    cancelAssignedLoad(loadId: string, body: {
        reason?: string;
    }, req: any): Promise<any>;
    createBid(loadId: string, createBidDto: CreateBidDto, req: any): Promise<any>;
    respondToCounterOffer(bidId: string, body: {
        response: 'accepted' | 'declined';
        notes?: string;
    }, req: any): Promise<any>;
    getCarrierBids(req: any): Promise<any[]>;
    withdrawBid(bidId: string, req: any): Promise<any>;
    withdrawBidPost(bidId: string, req: any): Promise<any>;
    withdrawBidFrontendRoute(bidId: string, req: any): Promise<any>;
    debugAirtableBids(loadId: string, req: any): Promise<any>;
    getSavedSearches(req: any): Promise<any[]>;
    saveSearch(req: any, savedSearchDto: SavedSearchDto): Promise<any>;
    updateSavedSearch(req: any, searchId: string, savedSearchDto: SavedSearchDto): Promise<any>;
    deleteSavedSearch(req: any, searchId: string): Promise<{
        success: boolean;
    }>;
    debugLoadForBidding(loadId: string, req: any): Promise<any>;
}
