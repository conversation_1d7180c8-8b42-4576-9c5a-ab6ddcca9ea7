import { NextRequest, NextResponse } from 'next/server';
import Airtable from 'airtable';

export async function GET(request: NextRequest) {
  try {
    // Configure Airtable
    const base = new Airtable({
      apiKey: process.env.AIRTABLE_API_KEY
    }).base(process.env.AIRTABLE_BASE_ID!);

    console.log('Fetching sample record to discover Airtable schema...');
    
    // Fetch just one record to see all available fields
    const records = await base("Orders")
      .select({
        maxRecords: 1
      })
      .all();

    if (records.length === 0) {
      return NextResponse.json({ 
        error: 'No records found in Orders table',
        fields: [],
        message: 'Cannot determine schema from empty table'
      });
    }

    const sampleRecord = records[0]!;
    const fields = sampleRecord.fields;
    const fieldNames = Object.keys(fields);
    
    const schemaInfo = {
      totalFields: fieldNames.length,
      recordId: sampleRecord.id,
      fields: fieldNames.map(fieldName => ({
        name: fieldName,
        type: typeof fields[fieldName],
        sampleValue: fields[fieldName],
        isArray: Array.isArray(fields[fieldName]),
        isObject: typeof fields[fieldName] === 'object' && !Array.isArray(fields[fieldName])
      }))
    };

    console.log('Airtable schema discovered:', schemaInfo);

    return NextResponse.json({
      success: true,
      message: `Found ${fieldNames.length} fields in Airtable Orders table`,
      schema: schemaInfo
    });

  } catch (error) {
    console.error('Error fetching Airtable schema:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch Airtable schema',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 