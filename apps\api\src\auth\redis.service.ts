import { Injectable, OnModuleDestroy } from '@nestjs/common';

interface CacheEntry {
  value: any;
  timeout?: NodeJS.Timeout;
}

@Injectable()
export class RedisService implements OnModuleDestroy {
  private cache = new Map<string, CacheEntry>();

  async get<T = any>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    return entry ? (entry.value as T) : null;
  }

  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    this.del(key); // Clear any existing timeout
    const entry: CacheEntry = { value };
    if (ttlSeconds) {
      entry.timeout = setTimeout(() => {
        this.cache.delete(key);
      }, ttlSeconds * 1000);
    }
    this.cache.set(key, entry);
  }

  async del(key: string): Promise<void> {
    const entry = this.cache.get(key);
    if (entry && entry.timeout) {
      clearTimeout(entry.timeout);
    }
    this.cache.delete(key);
  }

  async onModuleDestroy() {
    for (const entry of this.cache.values()) {
      if (entry.timeout) clearTimeout(entry.timeout);
    }
    this.cache.clear();
  }
} 