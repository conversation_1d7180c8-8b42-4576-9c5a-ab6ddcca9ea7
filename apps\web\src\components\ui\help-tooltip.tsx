'use client';

import React, { useState } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { HelpCircle, Info, AlertCircle, CheckCircle, Lightbulb } from 'lucide-react';

interface HelpTooltipProps {
  content: string;
  title?: string;
  type?: 'info' | 'help' | 'warning' | 'success' | 'tip';
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  className?: string;
  iconSize?: 'sm' | 'md' | 'lg';
  children?: React.ReactNode;
  maxWidth?: string;
}

const typeConfig = {
  info: {
    icon: Info,
    color: 'text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300',
    bgColor: 'bg-blue-50 dark:bg-blue-950/50',
    borderColor: 'border-blue-200 dark:border-blue-800',
    textColor: 'text-blue-900 dark:text-blue-100'
  },
  help: {
    icon: HelpCircle,
    color: 'text-muted-foreground hover:text-primary',
    bgColor: 'bg-background',
    borderColor: 'border-border',
    textColor: 'text-foreground'
  },
  warning: {
    icon: AlertCircle,
    color: 'text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300',
    bgColor: 'bg-amber-50 dark:bg-amber-950/50',
    borderColor: 'border-amber-200 dark:border-amber-800',
    textColor: 'text-amber-900 dark:text-amber-100'
  },
  success: {
    icon: CheckCircle,
    color: 'text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300',
    bgColor: 'bg-green-50 dark:bg-green-950/50',
    borderColor: 'border-green-200 dark:border-green-800',
    textColor: 'text-green-900 dark:text-green-100'
  },
  tip: {
    icon: Lightbulb,
    color: 'text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300',
    bgColor: 'bg-purple-50 dark:bg-purple-950/50',
    borderColor: 'border-purple-200 dark:border-purple-800',
    textColor: 'text-purple-900 dark:text-purple-100'
  }
};

const sizeConfig = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-5 w-5'
};

export function HelpTooltip({
  content,
  title,
  type = 'help',
  side = 'top',
  align = 'center',
  className = '',
  iconSize = 'md',
  children,
  maxWidth = '300px'
}: HelpTooltipProps) {
  const [isOpen, setIsOpen] = useState(false);
  const config = typeConfig[type];
  const Icon = config.icon;
  const iconSizeClass = sizeConfig[iconSize];

  return (
    <TooltipProvider>
      <Tooltip open={isOpen} onOpenChange={setIsOpen}>
        <TooltipTrigger asChild>
          {children || (
            <Button
              variant="ghost"
              size="sm"
              className={`h-auto p-1 ${config.color} ${className}`}
              onMouseEnter={() => setIsOpen(true)}
              onMouseLeave={() => setIsOpen(false)}
              onClick={() => setIsOpen(!isOpen)}
            >
              <Icon className={iconSizeClass} />
            </Button>
          )}
        </TooltipTrigger>
        <TooltipContent
          side={side}
          align={align}
          className={`${config.bgColor} ${config.borderColor} border shadow-lg ${config.textColor}`}
          style={{ maxWidth }}
        >
          <div className="space-y-1">
            {title && (
              <div className={`font-medium text-sm ${config.textColor}`}>{title}</div>
            )}
            <div className={`text-sm leading-relaxed ${config.textColor}`}>
              {content}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Specialized components for common use cases
export function ProfileFieldHelp({ field, children }: { field: string; children?: React.ReactNode }) {
  const fieldHelp: Record<string, { title: string; content: string }> = {
    companyName: {
      title: 'Company Name',
      content: 'Enter your legal business name exactly as it appears on your MC authority. This name will appear on load assignments and documentation.'
    },
    dotNumber: {
      title: 'DOT Number',
      content: 'Your Department of Transportation number is required for interstate commerce. Format: 12345678 (numbers only). This must be active and in good standing.'
    },
    mcNumber: {
      title: 'MC Number',
      content: 'Your Motor Carrier authority number from FMCSA. Format: MC-123456. Required to transport freight for hire. Must be active and properly insured.'
    },
    equipmentTypes: {
      title: 'Equipment Types',
      content: 'Select all equipment types you can provide. This helps match you with appropriate loads. Update this list as your fleet changes.'
    },
    contactPhone: {
      title: 'Contact Phone',
      content: 'Primary phone number for load coordination and emergency contact. Include area code. Format: (*************'
    },
    contactEmail: {
      title: 'Contact Email',
      content: 'Primary email for load notifications and documentation. Make sure you check this regularly for time-sensitive communications.'
    },
    serviceAreas: {
      title: 'Service Areas',
      content: 'States and regions where you operate. This helps filter loads in your preferred lanes and reduces irrelevant load offers.'
    }
  };

  const help = fieldHelp[field];
  if (!help) return children || null;

  return (
    <HelpTooltip
      title={help.title}
      content={help.content}
      type="info"
      side="right"
    >
      {children}
    </HelpTooltip>
  );
}

export function LoadboardHelp({ feature, children }: { feature: string; children?: React.ReactNode }) {
  const featureHelp: Record<string, { title: string; content: string }> = {
    filters: {
      title: 'Loadboard Filters',
      content: 'Use filters to find loads that match your operation. Set pickup/delivery locations, equipment type, dates, and minimum rates to see only relevant freight.'
    },
    bidding: {
      title: 'Bidding Process',
      content: 'Submit competitive bids on loads. Include your all-in rate and any relevant notes. The shipper will review all bids and select the best carrier.'
    },
    loadDetails: {
      title: 'Load Details',
      content: 'Click on any load to view full details including commodity, weight, special requirements, and pickup/delivery information.'
    },
    targetedLoads: {
      title: 'Targeted Loads',
      content: 'Blue badges indicate loads specifically targeted to your organization. These are premium opportunities with higher award probability.'
    },
    statusMeanings: {
      title: 'Load Status',
      content: 'Available: Open for bidding | Pending: Under review | Awarded: Assigned to carrier | In Transit: Being delivered | Delivered: Completed'
    }
  };

  const help = featureHelp[feature];
  if (!help) return children || null;

  return (
    <HelpTooltip
      title={help.title}
      content={help.content}
      type="help"
      side="bottom"
    >
      {children}
    </HelpTooltip>
  );
}

export function OperationsHelp({ feature, children }: { feature: string; children?: React.ReactNode }) {
  const featureHelp: Record<string, { title: string; content: string }> = {
    aiFeatures: {
      title: 'AI-Powered Operations',
      content: 'Use AI to optimize load planning, route suggestions, and carrier matching. The system learns from your preferences to provide better recommendations.'
    },
    orderCreation: {
      title: 'Create New Orders',
      content: 'Add new freight orders to the system. Include all pickup/delivery details, commodity information, and special requirements for accurate carrier matching.'
    },
    carrierAssignment: {
      title: 'Carrier Assignment',
      content: 'Assign loads to specific carriers or post to the loadboard. The system suggests best-fit carriers based on equipment, location, and performance history.'
    }
  };

  const help = featureHelp[feature];
  if (!help) return children || null;

  return (
    <HelpTooltip
      title={help.title}
      content={help.content}
      type="tip"
      side="left"
    >
      {children}
    </HelpTooltip>
  );
} 