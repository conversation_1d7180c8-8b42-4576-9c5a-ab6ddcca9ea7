# TASK ASSIGNMENT: Implement File Upload & Airtable Sync Feature
**Priority**: P3 (Feature Enhancement)  
**Agent Type**: Implementation Agent  
**Complexity**: Medium  
**Estimated Time**: 3-4 days  
**Tags**: #file-upload #airtable-sync #next.js #vercel-blob #implementation

## OVERVIEW
Implement a complete file upload system for carriers to upload BOL/Invoice documents after completing loads, with automatic Airtable synchronization. Use the pragmatic middle-ground approach: Next.js Server Actions + Vercel Blob + Sharp image optimization.

## REQUIREMENTS

### **Functional Requirements**
1. **File Upload Interface**
   - Drag & drop file upload component
   - Support multiple files (up to 5 per upload)
   - File type validation: PDF, JPG, JPEG, PNG
   - File size limit: 10MB per file
   - Progress indication during upload
   - Preview of uploaded files

2. **File Processing**
   - Automatic image optimization using Sharp
   - Resize images to max 2048x2048 pixels
   - Compress JPEG to 85% quality
   - Preserve PDF files as-is
   - Generate unique filenames to prevent conflicts

3. **Storage & Database**
   - Store files in Vercel Blob with private access
   - Save file metadata in PostgreSQL database
   - Associate files with specific load records
   - Track upload timestamps and file sizes

4. **Airtable Integration**
   - Sync uploaded document URLs to Airtable loads
   - Update document count in Airtable
   - Handle sync failures gracefully
   - Log sync activities for debugging

### **Technical Requirements**
- Use Next.js Server Actions for file handling
- Implement TypeScript throughout
- Follow existing project patterns and conventions
- Add proper error handling and validation
- Include loading states and user feedback
- Write unit tests for core functions

## CHOSEN TECHNICAL APPROACH

### **Technology Stack**
- **Next.js Server Actions** - Built-in file handling
- **Vercel Blob** - Managed storage solution
- **Sharp** - Image optimization library
- **Prisma** - Database operations
- **Airtable API** - Direct API calls for sync

### **Why This Approach**
- Fast implementation (3-4 days vs 2 weeks)
- Native Vercel integration
- Minimal external dependencies
- Clear migration path for future scaling
- Cost-effective for current scale

## IMPLEMENTATION TASKS

### **Task 1: Database Schema Updates**
Add document storage capability to the existing schema.

```sql
-- Add to existing Prisma schema
model Document {
  id        String   @id @default(cuid())
  loadId    String
  filename  String
  url       String
  type      String   // 'image/jpeg', 'application/pdf', etc.
  size      Int      // file size in bytes
  uploadedAt DateTime @default(now())
  
  load      Load     @relation(fields: [loadId], references: [id], onDelete: Cascade)
  
  @@map("documents")
}

// Update Load model to include documents relationship
model Load {
  // ... existing fields
  documents Document[]
}
```

**Deliverables:**
- [ ] Update `prisma/schema.prisma`
- [ ] Generate and run migration
- [ ] Verify database schema in development

### **Task 2: Server-Side Upload API**
Create the file upload endpoint using Next.js Server Actions.

**File: `apps/web/src/app/api/loads/[loadId]/documents/route.ts`**

```typescript
import { put } from '@vercel/blob';
import sharp from 'sharp';
import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs';
import { prisma } from '@/lib/prisma';
import { updateAirtableRecord } from '@/lib/airtable';

export async function POST(
  request: NextRequest,
  { params }: { params: { loadId: string } }
) {
  try {
    // Authenticate user
    const { userId } = auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify load ownership
    const load = await prisma.load.findFirst({
      where: { 
        id: params.loadId,
        carrierId: userId // Assuming carrierId matches Clerk userId
      }
    });

    if (!load) {
      return Response.json({ error: 'Load not found' }, { status: 404 });
    }

    const formData = await request.formData();
    const files = formData.getAll('documents') as File[];

    if (files.length === 0) {
      return Response.json({ error: 'No files provided' }, { status: 400 });
    }

    if (files.length > 5) {
      return Response.json({ error: 'Maximum 5 files allowed' }, { status: 400 });
    }

    const uploadedFiles = [];

    for (const file of files) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        return Response.json({ 
          error: `Unsupported file type: ${file.type}` 
        }, { status: 400 });
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        return Response.json({ 
          error: `File too large: ${file.name}` 
        }, { status: 400 });
      }

      let buffer = Buffer.from(await file.arrayBuffer());
      let processedSize = buffer.length;
      
      // Optimize images with Sharp
      if (file.type.startsWith('image/')) {
        buffer = await sharp(buffer)
          .resize(2048, 2048, { 
            fit: 'inside', 
            withoutEnlargement: true 
          })
          .jpeg({ quality: 85 })
          .toBuffer();
        processedSize = buffer.length;
      }

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

      // Upload to Vercel Blob
      const blob = await put(
        `loads/${params.loadId}/${filename}`, 
        buffer, 
        {
          access: 'private',
          contentType: file.type,
        }
      );

      // Save to database
      const document = await prisma.document.create({
        data: {
          loadId: params.loadId,
          filename: file.name,
          url: blob.url,
          type: file.type,
          size: processedSize,
        },
      });

      uploadedFiles.push({
        id: document.id,
        name: file.name,
        url: blob.url,
        size: processedSize,
        type: file.type,
      });
    }

    // Sync with Airtable
    try {
      await syncDocumentsToAirtable(params.loadId, uploadedFiles);
    } catch (airtableError) {
      console.error('Airtable sync failed:', airtableError);
      // Continue - don't fail the upload if Airtable sync fails
    }

    return Response.json({ 
      success: true, 
      files: uploadedFiles,
      message: `${uploadedFiles.length} file(s) uploaded successfully`
    });

  } catch (error) {
    console.error('Upload error:', error);
    return Response.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

async function syncDocumentsToAirtable(loadId: string, files: any[]) {
  // Get load with airtable ID
  const load = await prisma.load.findUnique({
    where: { id: loadId },
    select: { airtableId: true }
  });

  if (!load?.airtableId) {
    throw new Error('Load not found or missing Airtable ID');
  }

  // Update Airtable record
  await updateAirtableRecord(load.airtableId, {
    'Documents': files.map(f => f.url).join('\n'),
    'Document Count': files.length,
    'Last Document Upload': new Date().toISOString()
  });
}
```

**Deliverables:**
- [ ] Create upload API endpoint
- [ ] Add file validation logic
- [ ] Implement Sharp image optimization
- [ ] Add Vercel Blob integration
- [ ] Create Airtable sync function
- [ ] Add comprehensive error handling

### **Task 3: Document Upload React Component**
Create a reusable drag-and-drop upload component.

**File: `apps/web/src/components/DocumentUpload.tsx`**

```typescript
'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/use-toast';
import { Upload, FileText, Image, X } from 'lucide-react';

interface UploadedFile {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
}

interface DocumentUploadProps {
  loadId: string;
  onUploadComplete?: (files: UploadedFile[]) => void;
  className?: string;
}

export function DocumentUpload({ 
  loadId, 
  onUploadComplete,
  className = '' 
}: DocumentUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      acceptedFiles.forEach(file => {
        formData.append('documents', file);
      });

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch(`/api/loads/${loadId}/documents`, {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Upload failed');
      }

      const result = await response.json();
      setUploadedFiles(prev => [...prev, ...result.files]);
      
      toast({
        title: 'Upload Successful',
        description: result.message,
      });

      onUploadComplete?.(result.files);

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload Failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [loadId, onUploadComplete]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 5,
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: uploading
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const getFileIcon = (type: string) => {
    return type.startsWith('image/') ? Image : FileText;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-colors duration-200
          ${isDragActive 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${uploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        
        {uploading ? (
          <div className="space-y-2">
            <p className="text-gray-600">Uploading files...</p>
            <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
          </div>
        ) : (
          <div>
            <p className="text-lg text-gray-700 mb-2">
              {isDragActive 
                ? 'Drop files here...' 
                : 'Drag & drop files here, or click to select'
              }
            </p>
            <p className="text-sm text-gray-500">
              Supports PDF, JPG, PNG files up to 10MB each (max 5 files)
            </p>
          </div>
        )}
      </div>

      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">Uploaded Files</h4>
          {uploadedFiles.map((file) => {
            const IconComponent = getFileIcon(file.type);
            return (
              <div 
                key={file.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <IconComponent className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(file.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
```

**Deliverables:**
- [ ] Create DocumentUpload component
- [ ] Add drag-and-drop functionality
- [ ] Implement progress indication
- [ ] Add file preview/management
- [ ] Style with Tailwind CSS

### **Task 4: Airtable Integration Module**
Create reusable Airtable sync functions.

**File: `apps/web/src/lib/airtable.ts`**

```typescript
import Airtable from 'airtable';

const airtable = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY,
});

const base = airtable.base(process.env.AIRTABLE_BASE_ID!);

export async function updateAirtableRecord(
  recordId: string, 
  fields: Record<string, any>
) {
  try {
    await base('Loads').update(recordId, fields);
    console.log(`Updated Airtable record ${recordId}:`, fields);
  } catch (error) {
    console.error('Airtable update failed:', error);
    throw error;
  }
}

export async function syncLoadDocuments(
  loadId: string,
  documents: Array<{ url: string; name: string; type: string }>
) {
  // Get load with Airtable ID
  const load = await prisma.load.findUnique({
    where: { id: loadId },
    select: { airtableId: true }
  });

  if (!load?.airtableId) {
    throw new Error('Load not found or missing Airtable ID');
  }

  const documentUrls = documents.map(doc => doc.url).join('\n');
  const documentNames = documents.map(doc => doc.name).join('\n');

  await updateAirtableRecord(load.airtableId, {
    'Documents': documentUrls,
    'Document Names': documentNames,
    'Document Count': documents.length,
    'Last Document Upload': new Date().toISOString()
  });
}
```

**Deliverables:**
- [ ] Create Airtable helper functions
- [ ] Add error handling for API calls
- [ ] Add proper TypeScript types
- [ ] Add environment variable validation

### **Task 5: Integration with Load Management**
Add document upload to existing load views.

**File: Update `apps/web/src/app/org/[orgId]/my-loads/[loadId]/page.tsx`**

```typescript
import { DocumentUpload } from '@/components/DocumentUpload';

// Add to existing load detail page
export default function LoadDetailPage({ params }: { params: { loadId: string } }) {
  // ... existing load data fetching

  return (
    <div className="space-y-6">
      {/* ... existing load details */}
      
      {load.status === 'COMPLETED' && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Upload Documents</h3>
          <p className="text-gray-600 mb-4">
            Upload your Bill of Lading (BOL) and invoice documents for this completed load.
          </p>
          <DocumentUpload 
            loadId={params.loadId}
            onUploadComplete={(files) => {
              // Refresh page or update state
              window.location.reload();
            }}
          />
        </div>
      )}
    </div>
  );
}
```

**Deliverables:**
- [ ] Add DocumentUpload to load detail pages
- [ ] Show upload section only for completed loads
- [ ] Display existing documents
- [ ] Add proper loading states

### **Task 6: Environment Configuration**
Add required environment variables and configuration.

**File: `.env.local` additions:**
```
# Airtable Configuration
AIRTABLE_API_KEY=your_airtable_api_key
AIRTABLE_BASE_ID=your_base_id

# Vercel Blob (automatically configured in Vercel)
BLOB_READ_WRITE_TOKEN=your_blob_token
```

**File: `package.json` dependencies:**
```json
{
  "dependencies": {
    "sharp": "^0.33.0",
    "react-dropzone": "^14.2.3",
    "airtable": "^0.12.2"
  }
}
```

**Deliverables:**
- [ ] Add environment variables
- [ ] Install required dependencies
- [ ] Update deployment configuration
- [ ] Add environment validation

### **Task 7: Testing & Error Handling**
Create comprehensive tests and error handling.

**File: `apps/web/src/app/api/loads/[loadId]/documents/route.test.ts`**

```typescript
import { POST } from './route';
import { NextRequest } from 'next/server';

describe('/api/loads/[loadId]/documents', () => {
  it('should upload files successfully', async () => {
    // Test implementation
  });

  it('should reject invalid file types', async () => {
    // Test implementation
  });

  it('should handle file size limits', async () => {
    // Test implementation
  });

  it('should sync with Airtable', async () => {
    // Test implementation
  });
});
```

**Deliverables:**
- [ ] Write unit tests for API endpoints
- [ ] Test file validation logic
- [ ] Test Airtable sync functionality
- [ ] Add error logging and monitoring

## TESTING REQUIREMENTS

### **Manual Testing Checklist**
- [ ] Upload single PDF file
- [ ] Upload multiple image files
- [ ] Test drag and drop functionality
- [ ] Verify image optimization works
- [ ] Test file size validation
- [ ] Test file type validation
- [ ] Verify Airtable sync
- [ ] Test error scenarios
- [ ] Test on different browsers
- [ ] Test mobile responsiveness

### **Automated Testing**
- [ ] API endpoint tests
- [ ] File validation tests
- [ ] Database integration tests
- [ ] Airtable sync tests

## SUCCESS CRITERIA

### **Functional Success**
- [ ] Carriers can upload BOL/Invoice documents
- [ ] Files are stored securely in Vercel Blob
- [ ] Images are automatically optimized
- [ ] Document metadata is saved to database
- [ ] Airtable records are updated with document info
- [ ] Upload progress is shown to users
- [ ] Error states are handled gracefully

### **Technical Success**
- [ ] All TypeScript code is properly typed
- [ ] Error handling covers edge cases
- [ ] Performance is acceptable (<3s upload time)
- [ ] Code follows project conventions
- [ ] Tests have >80% coverage
- [ ] No security vulnerabilities

## DEPLOYMENT CHECKLIST

### **Pre-deployment**
- [ ] Run database migrations
- [ ] Add environment variables to Vercel
- [ ] Test in staging environment
- [ ] Verify Airtable integration works
- [ ] Check file upload limits in production

### **Post-deployment**
- [ ] Monitor error logs
- [ ] Test file uploads in production
- [ ] Verify Airtable sync is working
- [ ] Check storage usage in Vercel Blob
- [ ] Monitor API response times

## FUTURE ENHANCEMENTS (NOT IN SCOPE)

These features can be added later:
- OCR text extraction from documents
- Virus scanning for uploaded files
- Document thumbnails/previews
- Bulk upload capabilities
- Document version control
- Advanced Airtable webhook sync
- Migration to Multer if scale requires

## GUIDING NOTES

1. **Keep it simple** - Focus on core functionality first
2. **Error handling** - Graceful degradation if Airtable sync fails
3. **Security** - Validate all file inputs thoroughly
4. **Performance** - Optimize images to reduce storage costs
5. **User experience** - Clear progress indication and error messages
6. **Testing** - Test with real BOL/Invoice files from carriers
7. **Documentation** - Add clear comments for future maintainers

## CONTACT & ESCALATION

- **Technical questions**: Escalate to senior developer
- **Airtable integration issues**: Check with operations team
- **File storage concerns**: Verify Vercel Blob quotas/pricing
- **Security questions**: Follow existing authentication patterns 