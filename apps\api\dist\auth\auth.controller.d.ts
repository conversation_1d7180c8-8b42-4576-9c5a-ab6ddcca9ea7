import { AuthService } from './auth.service';
import { AuthenticatedRequest } from './authenticated-request.interface';
export declare class UpdateUserProfileDto {
    firstName?: string;
    lastName?: string;
    email?: string;
}
export declare class AuthController {
    private readonly authService;
    private readonly logger;
    constructor(authService: AuthService);
    getUserProfile(req: AuthenticatedRequest): Promise<{
        id: any;
        airtableUserId: any;
        email: any;
        firstName: any;
        lastName: any;
        role: any;
        createdAt: any;
        updatedAt: any;
    }>;
    updateUserProfile(req: AuthenticatedRequest, updateData: UpdateUserProfileDto): Promise<{
        id: any;
        airtableUserId: any;
        email: any;
        firstName: any;
        lastName: any;
        role: any;
        createdAt: any;
        updatedAt: any;
    }>;
    getCurrentUser(req: AuthenticatedRequest): Promise<{
        id: any;
        email: any;
        firstName: any;
        lastName: any;
        companyName: any;
        mcNumber: any;
        dotNumber: any;
        role: any;
        verificationStatus: any;
        _degradedMode?: undefined;
        _message?: undefined;
    } | {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        companyName: string;
        mcNumber: string | null;
        role: "CARRIER" | "ADMIN";
        verificationStatus: string;
        _degradedMode: boolean;
        _message: string;
        dotNumber?: undefined;
    }>;
}
