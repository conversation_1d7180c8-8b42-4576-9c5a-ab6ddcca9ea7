# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build/
 apps/api/dist/


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
Thumbs.db

# IDE configuration files (Uncomment if you want to ignore them)
# .vscode/
# .idea/
.cursor/ # Cursor editor specific files

# Prisma Client: Generally, the generated Prisma Client (often in node_modules/.prisma/client or a custom output path)
# should be committed to your repository. If your `prisma generate` output is configured elsewhere
# and you DON'T want to commit it, add that specific path here.
