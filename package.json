{"name": "carrier-portal", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.5", "turbo": "^2.5.3", "typescript": "5.8.2"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@9.1.0", "dependencies": {"@opentelemetry/api-logs": "^0.49.1", "@opentelemetry/instrumentation": "^0.49.1", "@opentelemetry/sdk-logs": "^0.49.1", "airtable": "^0.12.2", "clsx": "^2.1.1", "framer-motion": "^12.10.5", "lucide-react": "^0.509.0", "svix": "^1.65.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "pnpm": {"overrides": {"@opentelemetry/resources": "^1.22.0", "@opentelemetry/sdk-metrics": "^1.22.0", "@opentelemetry/sdk-trace-base": "^1.22.0", "@opentelemetry/api": "1.9.0"}}}