"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Truck,
  MapPin,
  Calendar,
  DollarSign,
  Package,
  RefreshCw,
  Search,
  Filter,
  Loader2,
  AlertCircle,
  FileText,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react";
import { toast } from "sonner";

interface Load {
  id: string;
  airtableRecordId: string;
  proNumber?: string;
  soNumber?: string;
  status?: string;
  
  // Location information
  origin?: string;
  destination?: string;
  originCity?: string;
  originState?: string;
  destinationCity?: string;
  destinationState?: string;
  
  // Date/time information
  pickupDateTime?: string;
  deliveryDateTime?: string;
  pickupAppointmentTime?: string;
  deliveryAppointmentTime?: string;
  
  // Load details
  rate?: number;
  weight?: number;
  equipment?: string;
  temperature?: string;
  distance?: number;
  ratePerMile?: string;
  
  // Additional information
  carrier?: string;
  commodity?: string;
  
  // Document tracking
  bolUploaded?: boolean;
  podUploaded?: boolean;
  invoiceUploaded?: boolean;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  
  // Legacy fields for backward compatibility
  pickupDateUtc?: string;
  deliveryDateUtc?: string;
  equipmentRequired?: string;
  weightLbs?: number;
  awardedToCarrierProfileId?: string;
  miles?: number;
  bid?: {
    id: string;
    bidAmount: number;
    status: string;
    carrierNotes?: string;
    createdAt: string;
  };
}

interface LoadFilters {
  status: string;
  searchQuery: string;
  dateRange: string;
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'delivered':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'in_transit':
    case 'picked_up':
      return <Truck className="h-4 w-4 text-blue-600" />;
    case 'pending':
    case 'assigned':
      return <Clock className="h-4 w-4 text-yellow-600" />;
    case 'cancelled':
      return <XCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Package className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'delivered':
      return 'bg-green-100 text-green-800';
    case 'in_transit':
    case 'picked_up':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
    case 'assigned':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function MyLoadsPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const { getToken } = useAuth();
  
  const [loads, setLoads] = useState<Load[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LoadFilters>({
    status: '',
    searchQuery: '',
    dateRange: ''
  });
  const [selectedLoad, setSelectedLoad] = useState<Load | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);

  const fetchMyLoads = useCallback(async () => {
    if (!isUserLoaded || !user) return;
    
    setIsLoading(true);
    setError(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (filters.status) params.append('status', filters.status);
      if (filters.searchQuery) params.append('search', filters.searchQuery);
      if (filters.dateRange) params.append('dateRange', filters.dateRange);

      const response = await fetch(`/api/v1/airtable-orders/my-loads?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch loads: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Handle new API response format
      if (data.loads) {
        setLoads(data.loads);
        console.log(`Loaded ${data.totalCount} loads for company: ${data.companyName}`);
      } else {
        // Fallback for old response format
        setLoads(data || []);
      }

    } catch (error) {
      console.error('Error fetching my loads:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
      toast.error('Failed to load your loads');
    } finally {
      setIsLoading(false);
    }
  }, [isUserLoaded, user, getToken, filters]);

  useEffect(() => {
    fetchMyLoads();
  }, [fetchMyLoads]);

  const handleFilterChange = (key: keyof LoadFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleStatusUpdate = async (loadId: string, newStatus: string) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/v1/airtable-orders/${loadId}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.statusText}`);
      }

      toast.success('Status updated successfully!');
      fetchMyLoads(); // Refresh the list

    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update status');
    }
  };

  const handleViewDetails = (load: Load) => {
    setSelectedLoad(load);
    setIsDetailsOpen(true);
  };

  // Show loading state
  if (!isUserLoaded || isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading your loads...</span>
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Unable to Load Your Loads</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchMyLoads} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
                  </div>
      </div>

      {/* Load Details Modal */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Load Details</DialogTitle>
            <DialogDescription>
              Complete information for {selectedLoad?.proNumber || selectedLoad?.soNumber || 'this load'}
            </DialogDescription>
          </DialogHeader>
          
          {selectedLoad && (
            <div className="space-y-6">
              {/* Route Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Route Information
                  </h3>
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">Origin:</span> {selectedLoad.origin || `${selectedLoad.originCity}, ${selectedLoad.originState}`}
                    </div>
                    <div>
                      <span className="font-medium">Destination:</span> {selectedLoad.destination || `${selectedLoad.destinationCity}, ${selectedLoad.destinationState}`}
                    </div>
                    {(selectedLoad.distance || selectedLoad.miles) && (
                      <div>
                        <span className="font-medium">Distance:</span> {selectedLoad.distance || selectedLoad.miles} miles
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Schedule
                  </h3>
                  <div className="space-y-2">
                    {(selectedLoad.pickupDateTime || selectedLoad.pickupDateUtc) && (
                      <div>
                        <span className="font-medium">Pickup:</span> {new Date(selectedLoad.pickupDateTime || selectedLoad.pickupDateUtc!).toLocaleDateString()}
                        {selectedLoad.pickupAppointmentTime && (
                          <div className="text-sm text-muted-foreground ml-4">
                            Appointment: {selectedLoad.pickupAppointmentTime}
                          </div>
                        )}
                      </div>
                    )}
                    {(selectedLoad.deliveryDateTime || selectedLoad.deliveryDateUtc) && (
                      <div>
                        <span className="font-medium">Delivery:</span> {new Date(selectedLoad.deliveryDateTime || selectedLoad.deliveryDateUtc!).toLocaleDateString()}
                        {selectedLoad.deliveryAppointmentTime && (
                          <div className="text-sm text-muted-foreground ml-4">
                            Appointment: {selectedLoad.deliveryAppointmentTime}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Load Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <Package className="h-5 w-5 mr-2" />
                    Load Details
                  </h3>
                  <div className="space-y-2">
                    {selectedLoad.commodity && (
                      <div>
                        <span className="font-medium">Commodity:</span> {selectedLoad.commodity}
                      </div>
                    )}
                    {(selectedLoad.weight || selectedLoad.weightLbs) && (
                      <div>
                        <span className="font-medium">Weight:</span> {(selectedLoad.weight || selectedLoad.weightLbs)!.toLocaleString()} lbs
                      </div>
                    )}
                    {(selectedLoad.equipment || selectedLoad.equipmentRequired) && (
                      <div>
                        <span className="font-medium">Equipment:</span> {selectedLoad.equipment || selectedLoad.equipmentRequired}
                      </div>
                    )}
                    {selectedLoad.temperature && (
                      <div>
                        <span className="font-medium">Temperature:</span> {selectedLoad.temperature}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    Financial Information
                  </h3>
                  <div className="space-y-2">
                    {selectedLoad.rate && (
                      <div>
                        <span className="font-medium">Rate:</span> ${selectedLoad.rate.toLocaleString()}
                      </div>
                    )}
                    {(selectedLoad.ratePerMile || (selectedLoad.rate && (selectedLoad.distance || selectedLoad.miles))) && (
                      <div>
                        <span className="font-medium">Rate per Mile:</span> ${selectedLoad.ratePerMile || (
                          (selectedLoad.distance || selectedLoad.miles) 
                            ? (selectedLoad.rate! / (selectedLoad.distance || selectedLoad.miles!)).toFixed(2)
                            : '0.00'
                        )}/mile
                      </div>
                    )}
                    {selectedLoad.bid && (
                      <div>
                        <span className="font-medium">Your Bid:</span> ${selectedLoad.bid.bidAmount.toLocaleString()}
                        {selectedLoad.bid.carrierNotes && (
                          <div className="text-sm text-muted-foreground ml-4">
                            Notes: {selectedLoad.bid.carrierNotes}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Reference Numbers */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Reference Numbers</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {selectedLoad.proNumber && (
                    <div>
                      <span className="font-medium">PRO Number:</span> {selectedLoad.proNumber}
                    </div>
                  )}
                  {selectedLoad.soNumber && (
                    <div>
                      <span className="font-medium">SO Number:</span> {selectedLoad.soNumber}
                    </div>
                  )}
                  {selectedLoad.airtableRecordId && (
                    <div>
                      <span className="font-medium">Record ID:</span> {selectedLoad.airtableRecordId}
                    </div>
                  )}
                </div>
              </div>

              {/* Status & Documents */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Status</h3>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedLoad.status || '')}
                    <Badge className={getStatusColor(selectedLoad.status || '')}>
                      {selectedLoad.status || 'Unknown'}
                    </Badge>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">Document Status</h3>
                  <div className="flex space-x-2">
                    <Badge variant={selectedLoad.bolUploaded ? "default" : "outline"}>
                      BOL {selectedLoad.bolUploaded ? '✓' : '○'}
                    </Badge>
                    <Badge variant={selectedLoad.podUploaded ? "default" : "outline"}>
                      POD {selectedLoad.podUploaded ? '✓' : '○'}
                    </Badge>
                    <Badge variant={selectedLoad.invoiceUploaded ? "default" : "outline"}>
                      INV {selectedLoad.invoiceUploaded ? '✓' : '○'}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Timestamps</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                  <div>
                    <span className="font-medium">Created:</span> {new Date(selectedLoad.createdAt).toLocaleString()}
                  </div>
                  <div>
                    <span className="font-medium">Updated:</span> {new Date(selectedLoad.updatedAt).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </PageLayout>
  );
}

  const groupedLoads = {
    active: loads.filter(load => {
      const status = load.status?.toLowerCase().replace(/\s+/g, '_') || '';
      return ['assigned', 'in_transit', 'picked_up', 'new_order', 'pending'].includes(status);
    }),
    completed: loads.filter(load => {
      const status = load.status?.toLowerCase().replace(/\s+/g, '_') || '';
      return ['completed', 'delivered', 'delivered_empty'].includes(status);
    }),
    bids: loads.filter(load => {
      const status = load.status?.toLowerCase().replace(/\s+/g, '_') || '';
      return load.bid && !['accepted', 'completed', 'delivered', 'delivered_empty'].includes(status);
    })
  };

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Loads</h1>
            <p className="text-muted-foreground">
              Manage your assigned loads and track their progress
            </p>
          </div>
          
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Button onClick={fetchMyLoads} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Loads</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groupedLoads.active.length}</div>
              <p className="text-xs text-muted-foreground">In progress</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groupedLoads.completed.length}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${groupedLoads.completed.reduce((sum, load) => sum + (load.rate || 0), 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">From completed loads</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="search" className="text-sm font-medium">Search</label>
                <Input
                  id="search"
                  placeholder="Search loads..."
                  value={filters.searchQuery}
                  onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                />
              </div>
              
              <div>
                <label htmlFor="status" className="text-sm font-medium">Status</label>
                <Select value={filters.status || "all"} onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="dateRange" className="text-sm font-medium">Date Range</label>
                <Select value={filters.dateRange || "all"} onValueChange={(value) => handleFilterChange('dateRange', value === 'all' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All dates" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All dates</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="quarter">This Quarter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loads List */}
        <div className="space-y-4">
          {loads.length > 0 ? (
            loads.map((load) => (
              <Card key={load.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
                    {/* Route and Basic Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center space-x-2 mb-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="font-semibold">
                          {load.origin || `${load.originCity}, ${load.originState}`} → {load.destination || `${load.destinationCity}, ${load.destinationState}`}
                        </span>
                      </div>
                      <div className="space-y-1">
                        {(load.proNumber || load.soNumber) && (
                          <p className="text-sm text-muted-foreground">
                            {load.proNumber && `PRO: ${load.proNumber}`}
                            {load.proNumber && load.soNumber && ' • '}
                            {load.soNumber && `SO: ${load.soNumber}`}
                          </p>
                        )}
                        {(load.distance || load.miles) && (
                          <p className="text-sm text-muted-foreground">
                            {load.distance || load.miles} miles
                          </p>
                        )}
                        {load.commodity && (
                          <p className="text-sm text-muted-foreground">
                            Commodity: {load.commodity}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Details */}
                    <div>
                      <div className="space-y-2">
                        {(load.pickupDateTime || load.pickupDateUtc) && (
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm font-medium">Pickup:</span>
                            </div>
                            <p className="text-sm text-muted-foreground ml-6">
                              {new Date(load.pickupDateTime || load.pickupDateUtc!).toLocaleDateString()}
                              {load.pickupAppointmentTime && (
                                <span className="block">Appt: {load.pickupAppointmentTime}</span>
                              )}
                            </p>
                          </div>
                        )}
                        
                        {(load.deliveryDateTime || load.deliveryDateUtc) && (
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm font-medium">Delivery:</span>
                            </div>
                            <p className="text-sm text-muted-foreground ml-6">
                              {new Date(load.deliveryDateTime || load.deliveryDateUtc!).toLocaleDateString()}
                              {load.deliveryAppointmentTime && (
                                <span className="block">Appt: {load.deliveryAppointmentTime}</span>
                              )}
                            </p>
                          </div>
                        )}
                        
                        {(load.equipment || load.equipmentRequired) && (
                          <Badge variant="outline">{load.equipment || load.equipmentRequired}</Badge>
                        )}
                        
                        {load.temperature && (
                          <Badge variant="secondary">Temp: {load.temperature}</Badge>
                        )}
                        
                        {(load.weight || load.weightLbs) && (
                          <p className="text-sm text-muted-foreground">
                            Weight: {(load.weight || load.weightLbs)!.toLocaleString()} lbs
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Status */}
                    <div className="flex flex-col space-y-2">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(load.status || '')}
                        <Badge className={getStatusColor(load.status || '')}>
                          {load.status || 'Unknown'}
                        </Badge>
                      </div>
                      {load.bid && (
                        <div className="text-sm text-muted-foreground">
                          Bid: ${load.bid.bidAmount.toLocaleString()}
                        </div>
                      )}
                    </div>

                    {/* Rate and Actions */}
                    <div className="flex flex-col items-end space-y-3">
                      {load.rate && (
                        <div className="text-right">
                          <div className="flex items-center justify-end space-x-1">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            <span className="text-2xl font-bold text-green-600">
                              ${load.rate.toLocaleString()}
                            </span>
                          </div>
                          {(load.ratePerMile || (load.rate && (load.distance || load.miles))) && (
                            <p className="text-sm text-muted-foreground">
                              ${load.ratePerMile || (
                                (load.distance || load.miles) 
                                  ? (load.rate / (load.distance || load.miles!)).toFixed(2)
                                  : '0.00'
                              )}/mile
                            </p>
                          )}
                        </div>
                      )}
                      
                      {/* Document Status */}
                      <div className="flex space-x-1">
                        <Badge variant={load.bolUploaded ? "default" : "outline"} className="text-xs">
                          BOL {load.bolUploaded ? '✓' : '○'}
                        </Badge>
                        <Badge variant={load.podUploaded ? "default" : "outline"} className="text-xs">
                          POD {load.podUploaded ? '✓' : '○'}
                        </Badge>
                        <Badge variant={load.invoiceUploaded ? "default" : "outline"} className="text-xs">
                          INV {load.invoiceUploaded ? '✓' : '○'}
                        </Badge>
                      </div>
                      
                      <div className="flex flex-col space-y-2">
                        {load.status && ['assigned', 'picked_up', 'in_transit', 'new_order', 'pending'].includes(load.status.toLowerCase().replace(/\s+/g, '_')) && (
                          <Button
                            size="sm"
                            onClick={() => {
                              let newStatus = 'delivered';
                              const currentStatus = load.status?.toLowerCase().replace(/\s+/g, '_');
                              
                              if (currentStatus === 'new_order' || currentStatus === 'pending') {
                                newStatus = 'assigned';
                              } else if (currentStatus === 'assigned') {
                                newStatus = 'picked_up';
                              } else if (currentStatus === 'picked_up') {
                                newStatus = 'in_transit';
                              }
                              
                              handleStatusUpdate(load.id, newStatus);
                            }}
                          >
                            {(() => {
                              const status = load.status?.toLowerCase().replace(/\s+/g, '_');
                              if (status === 'new_order' || status === 'pending') return 'Accept Load';
                              if (status === 'assigned') return 'Mark Picked Up';
                              if (status === 'picked_up') return 'Mark In Transit';
                              if (status === 'in_transit') return 'Mark Delivered';
                              return 'Update Status';
                            })()}
                          </Button>
                        )}
                        
                        <Button variant="outline" size="sm" onClick={() => handleViewDetails(load)}>
                          <FileText className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No loads found</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any loads matching the current filters.
                </p>
                <Button variant="outline" onClick={() => setFilters({ status: '', searchQuery: '', dateRange: '' })}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </PageLayout>
  );
} 