"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth, useUser } from '@/contexts/auth-context';
import { PageLayout } from "@/components/layout/page-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Truck,
  MapPin,
  Calendar,
  DollarSign,
  Package,
  RefreshCw,
  Search,
  Filter,
  Loader2,
  AlertCircle,
  FileText,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react";
import { toast } from "sonner";

interface Load {
  id: string;
  airtableRecordId: string;
  proNumber?: string;
  originCity?: string;
  originState?: string;
  destinationCity?: string;
  destinationState?: string;
  pickupDateUtc?: string;
  deliveryDateUtc?: string;
  equipmentRequired?: string;
  weightLbs?: number;
  rate?: number;
  status?: string;
  awardedToCarrierProfileId?: string;
  createdAt: string;
  updatedAt: string;
  miles?: number;
  ratePerMile?: number;
  bid?: {
    id: string;
    bidAmount: number;
    status: string;
    carrierNotes?: string;
    createdAt: string;
  };
}

interface LoadFilters {
  status: string;
  searchQuery: string;
  dateRange: string;
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'delivered':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'in_transit':
    case 'picked_up':
      return <Truck className="h-4 w-4 text-blue-600" />;
    case 'pending':
    case 'assigned':
      return <Clock className="h-4 w-4 text-yellow-600" />;
    case 'cancelled':
      return <XCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Package className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'delivered':
      return 'bg-green-100 text-green-800';
    case 'in_transit':
    case 'picked_up':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
    case 'assigned':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function MyLoadsPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const { getToken } = useAuth();
  
  const [loads, setLoads] = useState<Load[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LoadFilters>({
    status: '',
    searchQuery: '',
    dateRange: ''
  });

  const fetchMyLoads = useCallback(async () => {
    if (!isUserLoaded || !user) return;
    
    setIsLoading(true);
    setError(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (filters.status) params.append('status', filters.status);
      if (filters.searchQuery) params.append('search', filters.searchQuery);
      if (filters.dateRange) params.append('dateRange', filters.dateRange);

      const response = await fetch(`/api/v1/airtable-orders/my-loads?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch loads: ${response.statusText}`);
      }

      const data = await response.json();
      setLoads(data.loads || []);

    } catch (error) {
      console.error('Error fetching my loads:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
      toast.error('Failed to load your loads');
    } finally {
      setIsLoading(false);
    }
  }, [isUserLoaded, user, getToken, filters]);

  useEffect(() => {
    fetchMyLoads();
  }, [fetchMyLoads]);

  const handleFilterChange = (key: keyof LoadFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleStatusUpdate = async (loadId: string, newStatus: string) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/v1/airtable-orders/${loadId}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.statusText}`);
      }

      toast.success('Status updated successfully!');
      fetchMyLoads(); // Refresh the list

    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update status');
    }
  };

  // Show loading state
  if (!isUserLoaded || isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading your loads...</span>
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Unable to Load Your Loads</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchMyLoads} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </PageLayout>
    );
  }

  const groupedLoads = {
    active: loads.filter(load => ['assigned', 'in_transit', 'picked_up'].includes(load.status?.toLowerCase() || '')),
    completed: loads.filter(load => ['completed', 'delivered'].includes(load.status?.toLowerCase() || '')),
    bids: loads.filter(load => load.bid && !['accepted', 'completed', 'delivered'].includes(load.status?.toLowerCase() || ''))
  };

  return (
    <PageLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Loads</h1>
            <p className="text-muted-foreground">
              Manage your assigned loads and track their progress
            </p>
          </div>
          
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Button onClick={fetchMyLoads} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Loads</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groupedLoads.active.length}</div>
              <p className="text-xs text-muted-foreground">In progress</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groupedLoads.completed.length}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${groupedLoads.completed.reduce((sum, load) => sum + (load.rate || 0), 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">From completed loads</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="search" className="text-sm font-medium">Search</label>
                <Input
                  id="search"
                  placeholder="Search loads..."
                  value={filters.searchQuery}
                  onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                />
              </div>
              
              <div>
                <label htmlFor="status" className="text-sm font-medium">Status</label>
                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="dateRange" className="text-sm font-medium">Date Range</label>
                <Select value={filters.dateRange} onValueChange={(value) => handleFilterChange('dateRange', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All dates" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All dates</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="quarter">This Quarter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loads List */}
        <div className="space-y-4">
          {loads.length > 0 ? (
            loads.map((load) => (
              <Card key={load.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
                    {/* Route and Basic Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center space-x-2 mb-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="font-semibold">
                          {load.originCity}, {load.originState} → {load.destinationCity}, {load.destinationState}
                        </span>
                      </div>
                      {load.proNumber && (
                        <p className="text-sm text-muted-foreground">PRO: {load.proNumber}</p>
                      )}
                      {load.miles && (
                        <p className="text-sm text-muted-foreground">{load.miles} miles</p>
                      )}
                    </div>

                    {/* Details */}
                    <div>
                      <div className="space-y-2">
                        {load.pickupDateUtc && (
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {new Date(load.pickupDateUtc).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                        {load.equipmentRequired && (
                          <Badge variant="outline">{load.equipmentRequired}</Badge>
                        )}
                        {load.weightLbs && (
                          <p className="text-sm text-muted-foreground">
                            Weight: {load.weightLbs.toLocaleString()} lbs
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Status */}
                    <div className="flex flex-col space-y-2">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(load.status || '')}
                        <Badge className={getStatusColor(load.status || '')}>
                          {load.status || 'Unknown'}
                        </Badge>
                      </div>
                      {load.bid && (
                        <div className="text-sm text-muted-foreground">
                          Bid: ${load.bid.bidAmount.toLocaleString()}
                        </div>
                      )}
                    </div>

                    {/* Rate and Actions */}
                    <div className="flex flex-col items-end space-y-2">
                      {load.rate && (
                        <div className="text-right">
                          <div className="flex items-center space-x-1">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            <span className="text-2xl font-bold text-green-600">
                              ${load.rate.toLocaleString()}
                            </span>
                          </div>
                          {load.ratePerMile && (
                            <p className="text-sm text-muted-foreground">
                              ${load.ratePerMile.toFixed(2)}/mile
                            </p>
                          )}
                        </div>
                      )}
                      
                      <div className="flex space-x-2">
                        {load.status && ['assigned', 'picked_up'].includes(load.status.toLowerCase()) && (
                          <Button
                            size="sm"
                            onClick={() => {
                              const newStatus = load.status?.toLowerCase() === 'assigned' ? 'picked_up' : 'delivered';
                              handleStatusUpdate(load.id, newStatus);
                            }}
                          >
                            {load.status.toLowerCase() === 'assigned' ? 'Mark Picked Up' : 'Mark Delivered'}
                          </Button>
                        )}
                        
                        <Button variant="outline" size="sm">
                          <FileText className="h-4 w-4 mr-2" />
                          Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No loads found</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any loads matching the current filters.
                </p>
                <Button variant="outline" onClick={() => setFilters({ status: '', searchQuery: '', dateRange: '' })}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </PageLayout>
  );
} 