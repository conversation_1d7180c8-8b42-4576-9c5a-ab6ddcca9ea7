const { execSync } = require('child_process');
const path = require('path');

// Set the working directory to the API folder
const apiDir = path.join(__dirname, 'apps', 'api');
const schemaPath = path.join(apiDir, 'prisma', 'schema.prisma');

console.log('🔄 Running Prisma migration...');
console.log('API Directory:', apiDir);
console.log('Schema Path:', schemaPath);

try {
  // Change to API directory and run migration
  process.chdir(apiDir);
  
  console.log('📁 Current directory:', process.cwd());
  
  // Run the migration
  execSync('npx prisma migrate deploy --schema=prisma/schema.prisma', {
    stdio: 'inherit',
    cwd: apiDir
  });
  
  console.log('✅ Migration completed successfully!');
  
  // Generate Prisma client
  console.log('🔄 Generating Prisma client...');
  execSync('npx prisma generate --schema=prisma/schema.prisma', {
    stdio: 'inherit',
    cwd: apiDir
  });
  
  console.log('✅ Prisma client generated successfully!');
  
} catch (error) {
  console.error('❌ Migration failed:', error.message);
  process.exit(1);
}
