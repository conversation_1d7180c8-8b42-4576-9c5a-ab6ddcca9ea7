import { IsOptional, IsString, <PERSON>N<PERSON>ber, IsDate, IsA<PERSON>y, IsE<PERSON>, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

export enum SortField {
  PICKUP_DATE = 'pickupDate',
  DELIVERY_DATE = 'deliveryDate',
  RATE = 'rate',
  DISTANCE = 'distance',
  CREATED_AT = 'createdAt',
  WEIGHT = 'weight',
  EQUIPMENT = 'equipment'
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

export class AdvancedFiltersDto {
  @ApiPropertyOptional({ description: 'Filter by origin city/state' })
  @IsOptional()
  @IsString()
  origin?: string;

  @ApiPropertyOptional({ description: 'Filter by destination city/state' })
  @IsOptional()
  @IsString()
  destination?: string;

  @ApiPropertyOptional({ description: 'Filter by equipment type (R, V, F, etc.)' })
  @IsOptional()
  @IsString()
  equipment?: string;

  @ApiPropertyOptional({ description: 'Equipment types array for multiple selection' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  equipmentTypes?: string[];

  @ApiPropertyOptional({ description: 'Minimum rate' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minRate?: number;

  @ApiPropertyOptional({ description: 'Maximum rate' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maxRate?: number;

  @ApiPropertyOptional({ description: 'Minimum weight in lbs' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minWeight?: number;

  @ApiPropertyOptional({ description: 'Maximum weight in lbs' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maxWeight?: number;

  @ApiPropertyOptional({ description: 'Pickup date start (ISO 8601 format)' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  pickupDateStart?: Date;

  @ApiPropertyOptional({ description: 'Pickup date end (ISO 8601 format)' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  pickupDateEnd?: Date;

  @ApiPropertyOptional({ description: 'Delivery date start (ISO 8601 format)' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  deliveryDateStart?: Date;

  @ApiPropertyOptional({ description: 'Delivery date end (ISO 8601 format)' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  deliveryDateEnd?: Date;

  @ApiPropertyOptional({ description: 'Geographic search center point (lat,lng format: "40.7128,-74.0060")' })
  @IsOptional()
  @IsString()
  geoCenter?: string;

  @ApiPropertyOptional({ description: 'Search radius in miles from geo center', minimum: 1, maximum: 3000 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(3000)
  @Type(() => Number)
  geoRadius?: number;

  @ApiPropertyOptional({ description: 'Use geographic filtering for origin (true) or destination (false)', default: true })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  geoFilterOrigin?: boolean = true;

  @ApiPropertyOptional({ description: 'Free text search across multiple fields' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ enum: SortField, description: 'Field to sort by' })
  @IsOptional()
  @IsEnum(SortField)
  sortBy?: SortField;

  @ApiPropertyOptional({ enum: SortOrder, description: 'Sort order', default: SortOrder.ASC })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.ASC;

  @ApiPropertyOptional({ description: 'Page number for pagination', minimum: 1, default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Page size for pagination', minimum: 1, maximum: 100, default: 25 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  pageSize?: number = 25;
}

export class SavedSearchDto {
  @ApiPropertyOptional({ description: 'Name for the saved search' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Search criteria as JSON' })
  @IsOptional()
  criteria?: AdvancedFiltersDto;

  @ApiPropertyOptional({ description: 'Whether this search should be set as default' })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  isDefault?: boolean = false;
} 