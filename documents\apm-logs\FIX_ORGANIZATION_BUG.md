# 🚨 **IMMEDIATE FIX: Organization Assignment Bug**

**Issue:** All users assigned to "MVT Logistics" instead of correct organization  
**Root Cause:** `AuthService` always selects first organization (`data[0]`)  
**File to Fix:** `apps/api/src/auth/auth.service.ts`  
**Lines:** 119-127  

---

## **🔧 EXACT FIX REQUIRED**

### **Step 1: Apply the Fix**

Replace the existing code in `apps/api/src/auth/auth.service.ts` lines 119-127:

**FIND THIS CODE (lines 119-127):**
```typescript
const orgMemberships = await this.clerkClient.users.getOrganizationMembershipList({
    userId: clerkUserId
});

if (orgMemberships.data && orgMemberships.data.length > 0) {
    // Use the first organization membership as default
    const primaryMembership = orgMemberships.data[0];
    organizationInfo.clerkOrgId = primaryMembership.organization.id;
    organizationInfo.orgName = primaryMembership.organization.name;
    organizationInfo.orgRole = primaryMembership.role;
    this.logger.log(`User ${clerkUserId} has organization membership: ${primaryMembership.organization.name}`);
}
```

**REPLACE WITH THIS CODE:**
```typescript
const orgMemberships = await this.clerkClient.users.getOrganizationMembershipList({
    userId: clerkUserId
});

if (orgMemberships.data && orgMemberships.data.length > 0) {
    // Log all organizations for debugging
    this.logger.log(`User ${clerkUserId} has ${orgMemberships.data.length} organization memberships: ${
        orgMemberships.data.map(m => `${m.organization.name} (${m.role})`).join(', ')
    }`);
    
    // Smart organization selection - avoid defaulting to "MVT Logistics"
    let primaryMembership = orgMemberships.data[0];
    
    // If user has multiple orgs and first one is "MVT Logistics", try to find a better one
    if (orgMemberships.data.length > 1 && primaryMembership.organization.name === 'MVT Logistics') {
        // Look for non-MVT organization first
        const nonMVTOrg = orgMemberships.data.find(m => m.organization.name !== 'MVT Logistics');
        if (nonMVTOrg) {
            primaryMembership = nonMVTOrg;
            this.logger.log(`User ${clerkUserId} has MVT as first org, switching to: ${primaryMembership.organization.name}`);
        }
    }
    
    // If still multiple options, prefer admin role
    if (orgMemberships.data.length > 1) {
        const adminOrg = orgMemberships.data.find(m => 
            m.role === 'org:admin' && m.organization.name !== 'MVT Logistics'
        );
        if (adminOrg) {
            primaryMembership = adminOrg;
            this.logger.log(`User ${clerkUserId} using admin organization: ${primaryMembership.organization.name}`);
        }
    }
    
    organizationInfo.clerkOrgId = primaryMembership.organization.id;
    organizationInfo.orgName = primaryMembership.organization.name;
    organizationInfo.orgRole = primaryMembership.role;
    this.logger.log(`User ${clerkUserId} final organization assignment: ${primaryMembership.organization.name} (${primaryMembership.role})`);
}
```

### **Step 2: Test the Fix**

1. **Deploy the fix:**
```bash
cd apps/api
pnpm run build
# Deploy to your environment
```

2. **Test with the affected user:**
```bash
# Check the API logs when the user logs in
# Look for the new debug messages showing organization selection
```

3. **Verify targeted loads now work:**
```bash
# Login as the user and check if loads targeted to "First Cut Produce" now appear
```

### **Step 3: Verify Database State**

If needed, manually fix the user's organization in the database:
```sql
-- Check current organization
SELECT clerk_user_id, org_name FROM users 
WHERE clerk_user_id = 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE';

-- If it's still wrong after the fix, manually update:
UPDATE users 
SET org_name = 'First Cut Produce' 
WHERE clerk_user_id = 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE';
```

---

## **🎯 SUCCESS CRITERIA**

**✅ Fix Complete When:**
- [ ] User logs show multiple organizations being fetched
- [ ] User shows correct organization (not "MVT Logistics")
- [ ] Targeted loads for "First Cut Produce" are visible
- [ ] User can bid with correct organization
- [ ] Other users maintain their correct organizations

**🚨 CRITICAL:** This fix needs to be applied immediately as it affects all user authentication and organization-based functionality. 