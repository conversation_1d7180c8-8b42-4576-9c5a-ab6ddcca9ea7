import { 
  ExceptionFilter, 
  Catch, 
  ArgumentsHost, 
  HttpException, 
  HttpStatus, 
  Logger, 
  Injectable 
} from '@nestjs/common';
import { Request, Response } from 'express';
import { randomUUID } from 'crypto';

@Injectable()
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    
    // Generate correlation ID for request tracing
    const correlationId = randomUUID();
    
    // Add correlation ID to request for other middleware
    (request as any).correlationId = correlationId;

    const errorResponse = this.getErrorResponse(exception, correlationId, request);
    
    // Log error with structured information
    this.logError(exception, errorResponse, request, correlationId);
    
    response.status(errorResponse.statusCode).json(errorResponse.body);
  }

  private getErrorResponse(exception: unknown, correlationId: string, request: Request) {
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'Internal Server Error';
    let details: any = null;

    // Handle different types of exceptions
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || exception.message;
        error = (exceptionResponse as any).error || error;
        details = (exceptionResponse as any).details || null;
      }
    } else if (exception instanceof Error) {
      // Handle specific error patterns
      if (exception.message.includes('timeout')) {
        statusCode = HttpStatus.REQUEST_TIMEOUT;
        message = 'Request timeout - please try again';
        error = 'Request Timeout';
      } else if (exception.message.includes('connection') || exception.message.includes('ECONNREFUSED')) {
        statusCode = HttpStatus.SERVICE_UNAVAILABLE;
        message = 'Service temporarily unavailable';
        error = 'Service Unavailable';
      } else if (exception.message.includes('AIRTABLE') || exception.message.includes('External API')) {
        statusCode = HttpStatus.BAD_GATEWAY;
        message = 'External service error - please try again';
        error = 'External Service Error';
      } else if (exception.message.includes('P2002')) {
        // Basic Prisma unique constraint error handling without instanceof
        statusCode = HttpStatus.CONFLICT;
        message = 'This record already exists';
        error = 'Duplicate Entry';
      } else if (exception.message.includes('P2025')) {
        // Basic Prisma record not found error handling
        statusCode = HttpStatus.NOT_FOUND;
        message = 'Record not found';
        error = 'Not Found';
      } else if (exception.message.includes('P1001') || exception.message.includes('P1002')) {
        // Basic Prisma connection error handling
        statusCode = HttpStatus.SERVICE_UNAVAILABLE;
        message = 'Database temporarily unavailable';
        error = 'Database Connection Error';
      } else {
        message = process.env.NODE_ENV === 'production' 
          ? 'An unexpected error occurred' 
          : exception.message;
      }
    } else if (typeof exception === 'object' && exception !== null && 'message' in exception) {
      // Handle unknown exceptions that have a message property
      message = process.env.NODE_ENV === 'production' 
        ? 'An unexpected error occurred' 
        : String((exception as any).message);
    }

    // Classify error type for monitoring
    const errorClassification = this.classifyError(exception, statusCode);

    return {
      statusCode,
      body: {
        statusCode,
        error,
        message,
        correlationId,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
        ...(details && { details }),
        ...(errorClassification && { classification: errorClassification }),
        ...(process.env.NODE_ENV === 'development' && { 
          stack: exception instanceof Error ? exception.stack : undefined 
        })
      }
    };
  }

  private classifyError(exception: unknown, statusCode: number): string {
    // Classify errors for monitoring and analytics
    if (statusCode >= 500) {
      if (exception instanceof Error && exception.message.includes('P')) {
        return 'database_error';
      }
      if (exception instanceof Error) {
        if (exception.message.includes('timeout')) return 'timeout_error';
        if (exception.message.includes('connection')) return 'connection_error';
        if (exception.message.includes('AIRTABLE')) return 'external_service_error';
      }
      return 'system_error';
    } else if (statusCode >= 400 && statusCode < 500) {
      return 'user_error';
    }
    return 'unknown_error';
  }

  private logError(
    exception: unknown, 
    errorResponse: any, 
    request: Request, 
    correlationId: string
  ): void {
    const { statusCode, body } = errorResponse;
    const isServerError = statusCode >= 500;
    
    const logData = {
      correlationId,
      statusCode,
      error: body.error,
      message: body.message,
      classification: body.classification,
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      userId: (request as any).auth?.sub,
      timestamp: body.timestamp,
      stack: exception instanceof Error ? exception.stack : undefined
    };

    if (isServerError) {
      this.logger.error(`Server Error [${correlationId}]`, logData);
    } else {
      this.logger.warn(`Client Error [${correlationId}]`, logData);
    }
  }
} 