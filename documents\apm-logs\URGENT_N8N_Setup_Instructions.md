# 🚨 URGENT: N8N Authentication Setup Required

## Current Issue
The "Create Account" button is failing because the N8N authentication system is not configured. The frontend is trying to call `${NEXT_PUBLIC_N8N_BASE_URL}/webhook/auth/register` but this environment variable is missing.

## Quick Fix Options

### Option 1: Set N8N Environment Variable (Recommended)

Add this to your Vercel environment variables:

```bash
NEXT_PUBLIC_N8N_BASE_URL=https://firstcutproduce.app.n8n.cloud
```

**Steps:**
1. Go to Vercel dashboard
2. Navigate to your project settings
3. Go to Environment Variables
4. Add `NEXT_PUBLIC_N8N_BASE_URL` with value `https://firstcutproduce.app.n8n.cloud`
5. Redeploy the application

### Option 2: Temporary Mock Authentication (Development Only)

If you need immediate testing functionality, I can create a temporary mock authentication system.

## Required N8N Workflows

You need these two webhook endpoints configured in N8N:

### 1. Registration Endpoint
- **URL**: `https://firstcutproduce.app.n8n.cloud/webhook/auth/register`
- **Method**: POST
- **Input**: 
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe", 
  "companyName": "ABC Trucking",
  "mcNumber": "123456"
}
```
- **Output**:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "rec1234567890",
    "email": "<EMAIL>",
    "role": "CARRIER",
    "mcNumber": "123456",
    "firstName": "John",
    "lastName": "Doe",
    "companyName": "ABC Trucking"
  }
}
```

### 2. Login Endpoint
- **URL**: `https://firstcutproduce.app.n8n.cloud/webhook/auth/login`
- **Method**: POST
- **Input**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- **Output**: Same as registration

## Current Error Details

When users click "Create Account":
1. ❌ Frontend tries to call N8N webhook
2. ❌ `NEXT_PUBLIC_N8N_BASE_URL` is undefined
3. ❌ Network request fails
4. ❌ User sees error in developer console

## Status

- ✅ Frontend authentication forms are ready
- ✅ Error handling is implemented
- ✅ User interface is working
- ❌ N8N backend endpoints not configured
- ❌ Environment variables not set

## Next Steps

**IMMEDIATE (Choose One):**

1. **Production Ready**: Configure N8N workflows and set environment variable
2. **Development**: Let me create a temporary mock auth system for testing

**RECOMMENDED**: Set up the N8N authentication system as designed, as this aligns with your migration plan from Clerk to N8N authentication.

## Testing After Setup

Once configured, test:
1. Click "Create Account" → Should open registration form
2. Fill out form → Should create user and redirect to dashboard
3. Click "Sign In" → Should open login form
4. Enter credentials → Should authenticate and redirect

The authentication system is fully implemented on the frontend side - it just needs the N8N backend to be configured. 