{"name": "Admin Notification Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "admin/bid-alert", "responseMode": "responseNode", "options": {}}, "id": "5a1b2c3d-4e5f-6a7b-8c9d-0e1f2a3b4c5d", "name": "Webhook - <PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "admin-bid-alert"}, {"parameters": {"functionCode": "const jwt = require('jsonwebtoken');\nconst crypto = require('crypto');\nconst bidData = $json.body;\n\n// Validate that this is an authenticated bid notification\nif (!bidData.authenticatedUser || !bidData.carrierUserId) {\n  throw new Error('Invalid bid notification - missing authentication data');\n}\n\n// Generate secure action tokens with HMAC\nconst secret = process.env.N8N_JWT_SECRET || 'fallback-secret';\nconst timestamp = Date.now();\n\n// Create secure tokens for each action\nconst createActionToken = (action) => {\n  const tokenData = {\n    bidId: bidData.bidId,\n    action: action,\n    timestamp: timestamp,\n    carrierMcNumber: bidData.carrierMcNumber,\n    adminAction: true\n  };\n  \n  // Add HMAC signature for security\n  const signature = crypto.createHmac('sha256', secret)\n    .update(JSON.stringify(tokenData))\n    .digest('hex');\n  \n  tokenData.signature = signature;\n  return Buffer.from(JSON.stringify(tokenData)).toString('base64');\n};\n\n// Generate one-click action URLs with secure tokens\nconst baseUrl = 'https://firstcutproduce.app.n8n.cloud/webhook/admin/respond';\nconst acceptUrl = `${baseUrl}?action=accept&bidId=${bidData.bidId}&token=${createActionToken('accept')}`;\nconst counterUrl = `${baseUrl}?action=counter&bidId=${bidData.bidId}&token=${createActionToken('counter')}`;\nconst declineUrl = `${baseUrl}?action=decline&bidId=${bidData.bidId}&token=${createActionToken('decline')}`;\n\n// Format currency\nconst formattedAmount = new Intl.NumberFormat('en-US', {\n  style: 'currency',\n  currency: 'USD'\n}).format(bidData.bidAmount);\n\n// Format rate per mile if available\nlet ratePerMile = '';\nif (bidData.loadDetails?.miles && bidData.bidAmount) {\n  const rpm = bidData.bidAmount / parseInt(bidData.loadDetails.miles);\n  ratePerMile = ` (${new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(rpm)}/mile)`;\n}\n\nreturn {\n  ...bidData,\n  formattedAmount,\n  ratePerMile,\n  acceptUrl,\n  counterUrl,\n  declineUrl,\n  urgencyLevel: 'high',\n  securityInfo: {\n    tokenTimestamp: timestamp,\n    carrierAuthenticated: true,\n    mcNumber: bidData.carrierMcNumber\n  }\n};"}, "id": "6b2c3d4e-5f6a-7b8c-9d0e-1f2a3b4c5d6e", "name": "Prepare Secure Admin Notification", "type": "n8n-nodes-base.function", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "=🚛 New Bid Alert: {{ $json.formattedAmount }} - Action Required", "emailType": "html", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #1f2937; color: white; padding: 20px; border-radius: 8px 8px 0 0;\">\n    <h1 style=\"margin: 0; font-size: 24px;\">🚛 New Bid Received</h1>\n    <p style=\"margin: 5px 0 0 0; opacity: 0.8;\">Immediate Action Required</p>\n  </div>\n  \n  <div style=\"background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0;\">\n    <div style=\"background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h2 style=\"color: #1f2937; margin-top: 0;\">Bid Details</h2>\n      <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 15px;\">\n        <div>\n          <strong>Company:</strong> {{ $json.carrierCompanyName }}<br>\n          <strong>MC Number:</strong> {{ $json.carrierMcNumber }}<br>\n          <strong>Bid Amount:</strong> <span style=\"color: #059669; font-size: 18px; font-weight: bold;\">{{ $json.formattedAmount }}{{ $json.ratePerMile }}</span>\n        </div>\n        <div>\n          <strong>Load:</strong> {{ $json.loadDetails.origin }} → {{ $json.loadDetails.destination }}<br>\n          <strong>Distance:</strong> {{ $json.loadDetails.miles }} miles<br>\n          <strong>Equipment:</strong> {{ $json.loadDetails.equipment }}\n        </div>\n      </div>\n      \n      {{ $json.carrierNotes ? `<div style=\"margin-top: 15px; padding: 15px; background: #f1f5f9; border-radius: 6px;\"><strong>Carrier Notes:</strong><br>${$json.carrierNotes}</div>` : '' }}\n    </div>\n\n    <div style=\"text-align: center; margin: 30px 0;\">\n      <h3 style=\"color: #1f2937;\">One-Click Actions</h3>\n      <div style=\"display: inline-block; margin: 10px;\">\n        <a href=\"{{ $json.acceptUrl }}\" style=\"background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;\">\n          ✅ Accept Bid\n        </a>\n      </div>\n      <div style=\"display: inline-block; margin: 10px;\">\n        <a href=\"{{ $json.counterUrl }}\" style=\"background: #d97706; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;\">\n          💬 Counter Offer\n        </a>\n      </div>\n      <div style=\"display: inline-block; margin: 10px;\">\n        <a href=\"{{ $json.declineUrl }}\" style=\"background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;\">\n          ❌ Decline\n        </a>\n      </div>\n    </div>\n\n    <div style=\"background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #fbbf24;\">\n      <strong>⏰ Time Sensitive:</strong> This bid expires in 24 hours ({{ $json.expiresAt }})\n    </div>\n  </div>\n</div>", "options": {}}, "id": "7c3d4e5f-6a7b-8c9d-0e1f-2a3b4c5d6e7f", "name": "Send Admin <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [680, 300], "credentials": {"smtp": {"id": "EMAIL_CREDENTIAL_ID", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Admin notification sent successfully\",\n  \"bidId\": \"{{ $json.bidId }}\"\n}"}, "id": "8d4e5f6a-7b8c-9d0e-1f2a-3b4c5d6e7f8g", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 2, "position": [900, 300]}], "connections": {"Webhook - Admin Alert": {"main": [[{"node": "Prepare Secure Admin Notification", "type": "main", "index": 0}]]}, "Prepare Secure Admin Notification": {"main": [[{"node": "Send Admin <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Send Admin Email Alert": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "id": "admin-notification-workflow", "meta": {"instanceId": "n8n-instance"}}