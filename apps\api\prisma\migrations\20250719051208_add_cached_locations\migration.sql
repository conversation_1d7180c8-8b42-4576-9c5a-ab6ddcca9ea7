-- CreateTable
CREATE TABLE "cached_locations" (
    "id" TEXT NOT NULL,
    "airtableRecordId" TEXT NOT NULL,
    "name" TEXT,
    "city" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "zipCode" TEXT,
    "address" TEXT,
    "last_synced_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "cached_locations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cached_locations_airtableRecordId_key" ON "cached_locations"("airtableRecordId");

-- CreateIndex
CREATE INDEX "cached_locations_last_synced_at_idx" ON "cached_locations"("last_synced_at");

-- CreateIndex
CREATE INDEX "cached_locations_city_state_idx" ON "cached_locations"("city", "state");
