import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class CarrierProfileGuard implements CanActivate {
  private readonly logger = new Logger(CarrierProfileGuard.name);

  constructor(private prisma: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const userAirtableId = request.auth?.id;

    if (!userAirtableId) {
      this.logger.error('CarrierProfileGuard: No user ID found in request');
      throw new UnauthorizedException('Authentication required');
    }

    this.logger.log(`CarrierProfileGuard: Validating carrier profile for user ${userAirtableId}`);

    try {
      // Check if user exists and has organization assignment
      const user = await this.prisma.user.findUnique({
        where: { airtableUserId: userAirtableId },
        select: {
          id: true,
          airtableUserId: true,
          role: true
        }
      });

      if (!user) {
        this.logger.error(`CarrierProfileGuard: User ${userAirtableId} not found in database`);
        throw new UnauthorizedException('User account not found. Please contact support.');
      }

      // Organization assignments are no longer used in N8N authentication system

      // Check if user has carrier profile (unless admin)
      if (user.role !== 'ADMIN') {
        const carrierProfile = await this.prisma.carrierProfile.findUnique({
          where: { userId: user.id },
          select: {
            id: true,
            companyName: true,
            mcNumber: true
          }
        });

        if (!carrierProfile) {
          this.logger.warn(`CarrierProfileGuard: User ${userAirtableId} has no carrier profile - allowing access to create one`);
          // FIXED: Allow access for new users who need to create their carrier profile
          // They will be guided through profile completion in the UI
          return true;
        }

        // FIXED: More permissive validation - only require either company name OR MC number
        // Users can still access the system to complete missing fields
        const hasBasicInfo = carrierProfile.companyName || carrierProfile.mcNumber;
        
        if (!hasBasicInfo) {
          this.logger.warn(`CarrierProfileGuard: User ${userAirtableId} has empty carrier profile - allowing access to complete it`);
          // Still allow access so they can complete their profile
          return true;
        }

        this.logger.log(`CarrierProfileGuard: User ${userAirtableId} profile validation passed`);
      } else {
        this.logger.log(`CarrierProfileGuard: Admin user ${userAirtableId} - bypassing profile checks`);
      }

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      
      this.logger.error(`CarrierProfileGuard: Database error validating user ${userAirtableId}: ${error.message}`, error.stack);
      throw new UnauthorizedException('Profile validation failed. Please try again.');
    }
  }
} 