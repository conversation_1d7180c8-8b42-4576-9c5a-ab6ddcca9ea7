# Loadboard Full-Width Layout Fix

## Problem
The loadboard table was not utilizing the full width of the screen, leaving whitespace on the sides regardless of screen size. This reduced the available space for displaying load data columns effectively.

## Root Cause
The loadboard page had multiple padding/margin constraints that prevented full-width utilization:

1. **Container Padding**: The main container had `p-4 lg:p-6` padding creating internal spacing
2. **Table Layout**: The table was using `table-fixed` which limited flexibility 
3. **Overflow Handling**: `overflow-visible` didn't allow proper horizontal scrolling on smaller screens

## Solution Implemented

### 1. Container Width Optimization
**File**: `apps/web/src/app/org/[orgId]/loadboard/page.tsx`

**Before**:
```jsx
<div className="min-h-full bg-background/50 -m-6 p-4 lg:p-6">
<div className="w-full">
```

**After**:
```jsx
<div className="min-h-full bg-background/50 -m-6 -mx-4 lg:-mx-6 xl:-mx-8 p-0">
<div className="w-full px-4 lg:px-6 xl:px-8 py-4">
```

**Changes**:
- Added negative horizontal margins (`-mx-4 lg:-mx-6 xl:-mx-8`) to break out of parent container constraints
- Removed all padding (`p-0`) from outer container
- Added controlled horizontal padding (`px-4 lg:px-6 xl:px-8`) and vertical padding (`py-4`) to inner container
- This creates a full-width breakout while maintaining proper content padding

### 2. Table Responsiveness Enhancement
**Before**:
```jsx
<div className="loadboard-scroll w-full overflow-visible">
<Table className="w-full table-fixed loadboard-table top-nav-layout">
```

**After**:
```jsx
<div className="loadboard-scroll w-full overflow-x-auto">
<Table className="w-full min-w-full loadboard-table top-nav-layout">
```

**Changes**:
- Changed `overflow-visible` to `overflow-x-auto` for proper horizontal scrolling on smaller screens
- Removed `table-fixed` constraint and added `min-w-full` to ensure table takes full available width
- Table now expands to use all available space while still being scrollable on mobile

## Benefits

1. **Full Screen Utilization**: Table now uses 100% of available screen width
2. **Responsive Design**: Maintains proper behavior across all screen sizes
3. **Better Data Visibility**: More horizontal space for displaying load information
4. **Mobile Friendly**: Horizontal scroll available when needed on smaller screens
5. **Consistent Layout**: Maintains visual consistency with other full-width components

## Technical Notes

- The PageLayout component already had conditional full-width logic for loadboard pages
- The fix builds upon existing responsive design patterns
- Changes are backwards compatible and don't affect other pages
- No breaking changes to existing functionality

## Result
The loadboard now takes up the full width of the screen regardless of device size, providing maximum space for load data display while maintaining proper responsive behavior and scroll functionality. 