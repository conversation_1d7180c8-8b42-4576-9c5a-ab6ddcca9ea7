import { PrismaService } from '../prisma/prisma.service';
export interface NotificationData {
    id: string;
    type: string;
    title: string;
    message: string;
    data?: any;
    timestamp: Date;
    userId?: string;
    organizationId?: string;
}
export interface LoadNotificationData {
    loadId: string;
    airtableRecordId: string;
    originCity?: string;
    originState?: string;
    destinationCity?: string;
    destinationState?: string;
    rate?: number;
    equipmentRequired?: string;
    pickupDate?: Date;
    deliveryDate?: Date;
    status?: string;
    targetOrganizations?: string[];
    isPublic?: boolean;
}
export interface BidNotificationData {
    bidId: string;
    loadId: string;
    loadAirtableRecordId: string;
    carrierUserId: string;
    carrierCompanyName?: string;
    bidAmount: number;
    status: string;
    carrierNotes?: string;
}
export interface LoadStatusChangeData {
    loadId: string;
    airtableRecordId: string;
    oldStatus: string;
    newStatus: string;
    assignedCarrierUserId?: string;
    assignedCarrierCompanyName?: string;
}
export declare class NotificationsService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    broadcastNewLoad(loadData: LoadNotificationData): Promise<void>;
    broadcastLoadStatusChange(statusData: LoadStatusChangeData): Promise<void>;
    broadcastBidUpdate(bidData: BidNotificationData): Promise<void>;
    broadcastToUser(userId: string, notification: any): Promise<void>;
    broadcastToOrganization(organizationId: string, notification: Omit<NotificationData, 'id' | 'timestamp'>): Promise<void>;
    broadcastBookingConfirmation(loadId: string, carrierUserId: string, loadDetails: any): Promise<void>;
    broadcastLoadAssignment(loadId: string, carrierUserId: string, loadDetails: any): Promise<void>;
    broadcastSystemAnnouncement(title: string, message: string, data?: any): Promise<void>;
    getConnectionStats(): {
        connectedUsers: number;
    };
    isUserConnected(userId: string): boolean;
    broadcastToAdmins(notification: any): Promise<void>;
    private getBidStatusMessage;
}
