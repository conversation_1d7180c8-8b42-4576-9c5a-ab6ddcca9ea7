"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var PerformanceMiddleware_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMiddleware = void 0;
const common_1 = require("@nestjs/common");
let PerformanceMiddleware = PerformanceMiddleware_1 = class PerformanceMiddleware {
    logger = new common_1.Logger(PerformanceMiddleware_1.name);
    use(req, res, next) {
        const startTime = Date.now();
        const { method, originalUrl } = req;
        if (originalUrl.includes('/health') || originalUrl.includes('/favicon')) {
            return next();
        }
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            const { statusCode } = res;
            if (duration > 1000) {
                this.logger.warn(`SLOW REQUEST: ${method} ${originalUrl} - ${statusCode} - ${duration}ms`);
            }
            else if (duration > 500) {
                this.logger.log(`MEDIUM REQUEST: ${method} ${originalUrl} - ${statusCode} - ${duration}ms`);
            }
            else {
                this.logger.debug(`${method} ${originalUrl} - ${statusCode} - ${duration}ms`);
            }
            if (originalUrl.includes('/airtable-orders/available')) {
                this.logger.log(`LOADBOARD PERFORMANCE: ${duration}ms`);
            }
            else if (originalUrl.includes('/airtable-orders/assigned')) {
                this.logger.log(`ASSIGNED LOADS PERFORMANCE: ${duration}ms`);
            }
            else if (originalUrl.includes('/auth/') || originalUrl.includes('/clerk')) {
                this.logger.log(`AUTH PERFORMANCE: ${duration}ms`);
            }
        });
        next();
    }
};
exports.PerformanceMiddleware = PerformanceMiddleware;
exports.PerformanceMiddleware = PerformanceMiddleware = PerformanceMiddleware_1 = __decorate([
    (0, common_1.Injectable)()
], PerformanceMiddleware);
//# sourceMappingURL=performance.middleware.js.map