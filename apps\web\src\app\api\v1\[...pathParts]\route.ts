import { NextRequest, NextResponse } from 'next/server';

// Configuration for the backend API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ pathParts: string[] }> }
) {
  const params = await context.params;
  return handleRequest(request, params);
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ pathParts: string[] }> }
) {
  const params = await context.params;
  return handleRequest(request, params);
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ pathParts: string[] }> }
) {
  const params = await context.params;
  return handleRequest(request, params);
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ pathParts: string[] }> }
) {
  const params = await context.params;
  return handleRequest(request, params);
}

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ pathParts: string[] }> }
) {
  const params = await context.params;
  return handleRequest(request, params);
}

async function handleRequest(
  request: NextRequest,
  params: { pathParts: string[] }
) {
  try {
    // Get N8N JWT token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('n8n_auth_token')?.value;
    const n8nToken = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!n8nToken) {
      console.log('[API Proxy] No N8N token found - user not authenticated');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Validate JWT token format (basic check)
    const tokenParts = n8nToken.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      console.log('[API Proxy] Invalid JWT token format');
      return NextResponse.json({ error: 'Invalid token format' }, { status: 401 });
    }

    // Decode payload to get user info
    let userId;
    try {
      const payload = JSON.parse(atob(tokenParts[1]));
      userId = payload.id;
      console.log('[API Proxy] N8N token validated for user:', userId);
    } catch (error) {
      console.error('[API Proxy] Failed to decode JWT token:', error);
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Construct the target URL
    const pathParts = params.pathParts || [];
    const targetPath = `/api/v1/${pathParts.join('/')}`;
    const searchParams = request.nextUrl.searchParams.toString();
    const targetUrl = `${API_BASE_URL}${targetPath}${searchParams ? `?${searchParams}` : ''}`;

    console.log(`[API Proxy] ${request.method} ${targetUrl}`);

    // Prepare headers for the backend request
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      // Use the N8N JWT token for backend authentication
      'Authorization': `Bearer ${n8nToken}`,
      // Add user context
      'X-User-ID': userId,
    };

    // Prepare the request options
    const requestOptions: RequestInit = {
      method: request.method,
      headers,
    };

    // Add body for non-GET requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      try {
        const body = await request.text();
        if (body) {
          requestOptions.body = body;
        }
      } catch (error) {
        console.warn('[API Proxy] Failed to read request body:', error);
      }
    }

    // Make the request to the backend API
    const response = await fetch(targetUrl, requestOptions);

    // Log the response status for debugging
    console.log(`[API Proxy] Response: ${response.status} ${response.statusText}`);

    // Handle different response types
    const contentType = response.headers.get('content-type');
    
    if (!response.ok) {
      let errorBody;
      try {
        if (contentType?.includes('application/json')) {
          errorBody = await response.json();
        } else {
          errorBody = await response.text();
        }
      } catch {
        errorBody = `API Error: ${response.status} ${response.statusText}`;
      }

      console.error(`[API Proxy] Backend error:`, {
        status: response.status,
        statusText: response.statusText,
        body: errorBody,
        url: targetUrl
      });

      return NextResponse.json(
        { 
          error: 'Backend API Error',
          details: errorBody,
          status: response.status
        },
        { status: response.status }
      );
    }

    // Forward successful responses
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      return NextResponse.json(data, { status: response.status });
    } else {
      const data = await response.text();
      return new NextResponse(data, { 
        status: response.status,
        headers: {
          'Content-Type': contentType || 'text/plain'
        }
      });
    }

  } catch (error) {
    console.error('[API Proxy] Error:', error);
    
    return NextResponse.json(
      { 
        error: 'Proxy Error',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: 'Failed to proxy request to backend API'
      },
      { status: 500 }
    );
  }
} 