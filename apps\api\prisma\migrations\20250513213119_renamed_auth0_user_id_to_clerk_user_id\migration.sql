/*
  Warnings:

  - You are about to drop the column `auth0_user_id` on the `users` table. All the data in the column will be lost. (This warning is now inaccurate due to manual edit)
  - A unique constraint covering the columns `[clerk_user_id]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `clerk_user_id` to the `users` table without a default value. This is not possible if the table is not empty. (This warning is now inaccurate)

*/
-- DropIndex
DROP INDEX "users_auth0_user_id_key";

-- AlterTable
ALTER TABLE "users" RENAME COLUMN "auth0_user_id" TO "clerk_user_id";

-- CreateIndex
CREATE UNIQUE INDEX "users_clerk_user_id_key" ON "users"("clerk_user_id");