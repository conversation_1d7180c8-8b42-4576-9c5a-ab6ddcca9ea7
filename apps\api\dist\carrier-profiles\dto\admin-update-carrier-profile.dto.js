"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUpdateCarrierProfileDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_carrier_profile_dto_1 = require("./create-carrier-profile.dto");
const class_validator_1 = require("class-validator");
class AdminUpdateCarrierProfileDto extends (0, swagger_1.PartialType)(create_carrier_profile_dto_1.CreateCarrierProfileDto) {
    isVerifiedByAdmin;
    adminNotes;
}
exports.AdminUpdateCarrierProfileDto = AdminUpdateCarrierProfileDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], AdminUpdateCarrierProfileDto.prototype, "isVerifiedByAdmin", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AdminUpdateCarrierProfileDto.prototype, "adminNotes", void 0);
//# sourceMappingURL=admin-update-carrier-profile.dto.js.map