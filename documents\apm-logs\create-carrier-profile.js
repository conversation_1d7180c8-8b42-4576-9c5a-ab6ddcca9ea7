const { PrismaClient } = require('../../packages/db/generated/client');

async function createCarrierProfile() {
  const prisma = new PrismaClient();
  const userClerkId = 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE';
  
  try {
    console.log('🔍 Creating carrier profile for:', userClerkId);
    
    // Get user record
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userClerkId },
      select: { id: true }
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    // Create carrier profile with admin-appropriate values
    const carrierProfile = await prisma.carrierProfile.create({
      data: {
        userId: user.id,
        companyName: 'First Cut Produce',
        mcNumber: 'ADMIN-001', // Admin placeholder
        dotNumber: 'ADMIN-001', // Admin placeholder
        phoneNumber: '555-ADMIN',
        equipmentTypes: ['DRY_VAN', 'REEFER'], // Default equipment types
        serviceableRegions: ['US'], // Default to US
        isVerifiedByAdmin: true, // Auto-verify admin
        adminNotes: 'Admin user carrier profile - created for system access'
      }
    });
    
    console.log('✅ Carrier profile created:', JSON.stringify(carrierProfile, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createCarrierProfile(); 