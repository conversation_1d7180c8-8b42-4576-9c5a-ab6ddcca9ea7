-- CreateTable
CREATE TABLE "system_settings" (
    "id" TEXT NOT NULL,
    "platform_name" TEXT,
    "support_email" TEXT,
    "maintenance_mode" BOOLEAN DEFAULT false,
    "auto_assign_loads" BOOLEAN DEFAULT false,
    "require_load_approval" BOOLEAN DEFAULT true,
    "max_loads_per_carrier" INTEGER DEFAULT 10,
    "load_expiration_hours" INTEGER DEFAULT 72,
    "require_insurance_verification" BOOLEAN DEFAULT true,
    "require_dot_verification" BOOLEAN DEFAULT true,
    "auto_approve_carriers" BOOLEAN DEFAULT false,
    "verification_reminder_days" INTEGER DEFAULT 30,
    "enable_email_notifications" BOOLEAN DEFAULT true,
    "enable_sms_notifications" BOOLEAN DEFAULT false,
    "notification_frequency" TEXT DEFAULT 'DAILY',
    "require_two_factor" BOOLEAN DEFAULT false,
    "session_timeout_minutes" INTEGER DEFAULT 60,
    "max_login_attempts" INTEGER DEFAULT 5,
    "password_expiration_days" INTEGER DEFAULT 90,
    "default_payment_terms" TEXT DEFAULT 'Net 30',
    "late_payment_fee_percent" DOUBLE PRECISION DEFAULT 2.5,
    "invoice_reminder_days" INTEGER DEFAULT 7,
    "max_file_upload_size" INTEGER DEFAULT 10,
    "rate_limit_per_minute" INTEGER DEFAULT 100,
    "enable_load_tracking" BOOLEAN DEFAULT true,
    "enable_real_time_updates" BOOLEAN DEFAULT true,
    "enable_advanced_reporting" BOOLEAN DEFAULT false,
    "enable_api_access" BOOLEAN DEFAULT false,
    "maintenance_window_start" TEXT DEFAULT '02:00',
    "maintenance_window_end" TEXT DEFAULT '04:00',
    "backup_frequency" TEXT DEFAULT 'DAILY',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "system_settings_pkey" PRIMARY KEY ("id")
);
