"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var AirtableOrdersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AirtableOrdersService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const cache_manager_1 = require("@nestjs/cache-manager");
const airtable_1 = __importDefault(require("airtable"));
const prisma_service_1 = require("../prisma/prisma.service");
const circuit_breaker_service_1 = require("../common/services/circuit-breaker.service");
const advanced_filters_dto_1 = require("./dto/advanced-filters.dto");
const geographic_utils_1 = require("./utils/geographic.utils");
const db_1 = require("@repo/db");
const notifications_service_1 = require("../notifications/notifications.service");
let AirtableOrdersService = AirtableOrdersService_1 = class AirtableOrdersService {
    configService;
    prisma;
    cacheManager;
    notificationsService;
    circuitBreakerService;
    logger = new common_1.Logger(AirtableOrdersService_1.name);
    base;
    tableName;
    constructor(configService, prisma, cacheManager, notificationsService, circuitBreakerService) {
        this.configService = configService;
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.notificationsService = notificationsService;
        this.circuitBreakerService = circuitBreakerService;
        const apiKey = this.configService.get('AIRTABLE_API_KEY');
        const baseId = this.configService.get('AIRTABLE_BASE_ID');
        this.tableName = this.configService.get('AIRTABLE_TABLE_NAME', 'Orders');
        if (!apiKey || !baseId) {
            this.logger.error('Airtable API Key or Base ID is missing in environment variables.');
            throw new Error('Airtable configuration is incomplete.');
        }
        const airtable = new airtable_1.default({ apiKey });
        this.base = airtable.base(baseId);
        this.logger.log(`Airtable configured for base ID: ${baseId}, table: ${this.tableName}`);
    }
    async getAirtableRecordById(recordId) {
        this.logger.debug(`Fetching record ${recordId} directly from Airtable table: ${this.tableName}`);
        return this.circuitBreakerService.execute('airtable-get-record', async () => {
            const record = await this.base(this.tableName).find(recordId);
            if (!record) {
                this.logger.warn(`Record with ID ${recordId} not found in Airtable table ${this.tableName}.`);
                throw new common_1.NotFoundException(`Load with ID ${recordId} not found in Airtable.`);
            }
            return record;
        }, async () => {
            this.logger.warn(`Airtable unavailable, attempting database fallback for record ${recordId}`);
            const cachedLoad = await this.prisma.load.findFirst({
                where: { airtableRecordId: recordId }
            });
            if (cachedLoad) {
                const fallbackRecord = {
                    id: recordId,
                    fields: {
                        'Order ID.': cachedLoad.proNumber,
                        'Status': cachedLoad.status,
                        'Rate to Carrier': cachedLoad.rate,
                        'Pickup City Lookup': [cachedLoad.originCity],
                        'Pickup State Lookup': [cachedLoad.originState],
                        'Delivery City Lookup': [cachedLoad.destinationCity],
                        'Delivery State Lookup': [cachedLoad.destinationState],
                        'Pickup Date & Time': cachedLoad.pickupDateUtc?.toISOString(),
                        'Delivery Date & Time': cachedLoad.deliveryDateUtc?.toISOString(),
                        'Weight (lbs)': cachedLoad.weightLbs,
                        'Equipment Required': cachedLoad.equipmentRequired,
                        'Temperature': cachedLoad.temperature
                    }
                };
                this.logger.log(`Using database fallback for record ${recordId}`);
                return fallbackRecord;
            }
            throw new common_1.NotFoundException(`Load with ID ${recordId} not found in Airtable or local cache.`);
        }, {
            failureThreshold: 3,
            recoveryTimeout: 30000,
            monitoringPeriod: 300000
        });
    }
    mapAirtableStatusToEnum(airtableStatus) {
        if (!airtableStatus)
            return undefined;
        const key = airtableStatus.toUpperCase().replace(/\s+\/\s+|\s+/g, '_');
        if (db_1.LoadStatus[key]) {
            return db_1.LoadStatus[key];
        }
        this.logger.warn(`Unknown Airtable status: "${airtableStatus}". Cannot map to LoadStatus enum.`);
        return undefined;
    }
    parseTargetOrganizations(targetOrganizations) {
        if (!targetOrganizations)
            return undefined;
        if (Array.isArray(targetOrganizations)) {
            return targetOrganizations.filter(org => typeof org === 'string');
        }
        if (typeof targetOrganizations === 'string') {
            return targetOrganizations.split(',').map(org => org.trim());
        }
        return undefined;
    }
    async calculateMilesForLoad(originCity, originState, destinationCity, destinationState) {
        const logPrefix = `MILES_CALC[${originCity}, ${originState} → ${destinationCity}, ${destinationState}]`;
        try {
            if (typeof destinationCity !== 'string' || typeof destinationState !== 'string') {
                throw new Error(`Invalid destination data types: city=${typeof destinationCity}, state=${typeof destinationState}`);
            }
            if (typeof originCity !== 'string' || typeof originState !== 'string') {
                throw new Error(`Invalid origin data types: city=${typeof originCity}, state=${typeof originState}`);
            }
            const destinations = destinationCity.split(',').map(city => city.trim()).filter(city => city.length > 0);
            const states = destinationState.split(',').map(state => state.trim()).filter(state => state.length > 0);
            if (destinations.length === 0) {
                throw new Error('No valid destination cities found');
            }
            if (destinations.length > 1) {
                this.logger.log(`${logPrefix} Multiple destinations detected: ${destinations.length} cities`);
                let totalDistance = 0;
                let currentOriginCity = originCity;
                let currentOriginState = originState;
                for (let i = 0; i < destinations.length; i++) {
                    const destCity = destinations[i];
                    const destState = states[i] || destinationState.split(',')[0]?.trim() || destinationState;
                    const segmentDistance = await this.calculateSingleRouteDistance(currentOriginCity, currentOriginState, destCity, destState);
                    totalDistance += segmentDistance;
                    this.logger.debug(`${logPrefix} Segment ${i + 1}: ${currentOriginCity}, ${currentOriginState} → ${destCity}, ${destState} = ${segmentDistance} miles`);
                    currentOriginCity = destCity;
                    currentOriginState = destState;
                }
                this.logger.log(`${logPrefix} Multi-destination total distance: ${totalDistance} miles`);
                return Math.round(totalDistance);
            }
            else {
                return await this.calculateSingleRouteDistance(originCity, originState, destinations[0], states[0] || destinationState);
            }
        }
        catch (error) {
            this.logger.error(`${logPrefix} Critical error during calculation: ${error.message}`);
            throw error;
        }
    }
    async calculateSingleRouteDistance(originCity, originState, destinationCity, destinationState) {
        const logPrefix = `MILES_CALC[${originCity}, ${originState} → ${destinationCity}, ${destinationState}]`;
        try {
            const originAddress = `${originCity}, ${originState}`;
            const destinationAddress = `${destinationCity}, ${destinationState}`;
            this.logger.debug(`${logPrefix} Starting distance calculation...`);
            let cachedDistance = await this.prisma.distanceCache.findFirst({
                where: {
                    AND: [
                        { originAddress: { equals: originAddress, mode: 'insensitive' } },
                        { destinationAddress: { equals: destinationAddress, mode: 'insensitive' } }
                    ]
                }
            });
            if (cachedDistance) {
                this.logger.log(`${logPrefix} Found exact case-insensitive match: ${cachedDistance.distanceMiles} miles`);
                return Math.round(cachedDistance.distanceMiles);
            }
            cachedDistance = await this.prisma.distanceCache.findFirst({
                where: {
                    AND: [
                        { originAddress: { equals: destinationAddress, mode: 'insensitive' } },
                        { destinationAddress: { equals: originAddress, mode: 'insensitive' } }
                    ]
                }
            });
            if (cachedDistance) {
                this.logger.log(`${logPrefix} Found reverse case-insensitive match: ${cachedDistance.distanceMiles} miles`);
                return Math.round(cachedDistance.distanceMiles);
            }
            cachedDistance = await this.prisma.distanceCache.findFirst({
                where: {
                    AND: [
                        { originAddress: { contains: originCity, mode: 'insensitive' } },
                        { destinationAddress: { contains: destinationCity, mode: 'insensitive' } }
                    ]
                }
            });
            if (cachedDistance) {
                this.logger.log(`${logPrefix} Found partial match: ${cachedDistance.originAddress} → ${cachedDistance.destinationAddress} = ${cachedDistance.distanceMiles} miles`);
                return Math.round(cachedDistance.distanceMiles);
            }
            cachedDistance = await this.prisma.distanceCache.findFirst({
                where: {
                    AND: [
                        { originAddress: { contains: destinationCity, mode: 'insensitive' } },
                        { destinationAddress: { contains: originCity, mode: 'insensitive' } }
                    ]
                }
            });
            if (cachedDistance) {
                this.logger.log(`${logPrefix} Found reverse partial match: ${cachedDistance.originAddress} → ${cachedDistance.destinationAddress} = ${cachedDistance.distanceMiles} miles`);
                return Math.round(cachedDistance.distanceMiles);
            }
            this.logger.log(`${logPrefix} No cached distance found, attempting Radar API calculation...`);
            try {
                const { RadarDistanceService } = await Promise.resolve().then(() => __importStar(require('../operations/services/radar-distance.service')));
                const radarService = new RadarDistanceService(this.configService, this.prisma);
                const distanceResult = await radarService.calculateDistanceByAddress(originAddress, destinationAddress);
                if (distanceResult.success) {
                    this.logger.log(`${logPrefix} Radar API successful: ${distanceResult.distanceMiles} miles`);
                    return Math.round(distanceResult.distanceMiles);
                }
                else {
                    this.logger.warn(`${logPrefix} Radar API failed, using fallback calculation`);
                }
            }
            catch (radarError) {
                this.logger.error(`${logPrefix} Radar API error: ${radarError.message}`);
            }
            this.logger.warn(`${logPrefix} No cached or API data available, using fallback estimation`);
            const fallbackDistance = this.calculateFallbackDistance(originCity, originState, destinationCity, destinationState);
            this.logger.log(`${logPrefix} Fallback estimation: ${fallbackDistance} miles`);
            return fallbackDistance;
        }
        catch (error) {
            this.logger.error(`${logPrefix} Critical error during calculation: ${error.message}`);
            this.logger.warn(`${logPrefix} Using fallback calculation due to error`);
            return this.calculateFallbackDistance(originCity, originState, destinationCity, destinationState);
        }
    }
    calculateFallbackDistance(originCity, originState, destinationCity, destinationState) {
        if (originState.toLowerCase().trim() === destinationState.toLowerCase().trim()) {
            return 200;
        }
        const stateDistances = {
            'TX-CA': 1200, 'CA-TX': 1200,
            'TX-FL': 1000, 'FL-TX': 1000,
            'CA-FL': 2400, 'FL-CA': 2400,
            'NY-CA': 2800, 'CA-NY': 2800,
            'TX-NY': 1600, 'NY-TX': 1600,
            'FL-NY': 1100, 'NY-FL': 1100,
        };
        const routeKey = `${originState.toUpperCase()}-${destinationState.toUpperCase()}`;
        const reverseKey = `${destinationState.toUpperCase()}-${originState.toUpperCase()}`;
        return stateDistances[routeKey] || stateDistances[reverseKey] || 800;
    }
    mapAirtableInvStatusToEnum(airtableInvStatus) {
        if (!airtableInvStatus)
            return undefined;
        const invStatusMap = {
            'Not Sent': db_1.InvStatus.NOT_SENT,
            'Sent': db_1.InvStatus.SENT,
            'Paid': db_1.InvStatus.PAID,
        };
        return invStatusMap[airtableInvStatus];
    }
    async processLoadWebhook(payload) {
        this.logger.log(`SERVICE: processLoadWebhook called with payload for Airtable ID: ${payload.airtable_record_id}`);
        const mappedData = {
            airtableRecordId: payload.airtable_record_id,
            originCity: payload.origin_city,
            originState: payload.origin_state,
            destinationCity: payload.destination_city,
            destinationState: payload.destination_state,
            equipmentRequired: payload.equipment_required,
            weightLbs: payload.weight_lbs,
            rate: payload.rate,
            status: this.mapAirtableStatusToEnum(payload.status),
            temperature: payload.temperature,
            rawAirtableData: payload.raw_airtable_data || payload,
        };
        if (payload.pickup_date_utc) {
            const pickupDate = new Date(payload.pickup_date_utc);
            if (!isNaN(pickupDate.getTime())) {
                mappedData.pickupDateUtc = pickupDate;
            }
            else {
                this.logger.warn(`Invalid pickup_date_utc format: ${payload.pickup_date_utc}`);
            }
        }
        if (payload.delivery_date_utc) {
            const deliveryDate = new Date(payload.delivery_date_utc);
            if (!isNaN(deliveryDate.getTime())) {
                mappedData.deliveryDateUtc = deliveryDate;
            }
            else {
                this.logger.warn(`Invalid delivery_date_utc format: ${payload.delivery_date_utc}`);
            }
        }
        try {
            const airtableRecord = await this.getAirtableRecordById(payload.airtable_record_id);
            const fields = airtableRecord.fields;
            this.logger.log(`SERVICE: Processing load ${payload.airtable_record_id} - visibility controlled by "Synced to API" at query time`);
            const targetOrganizations = payload.target_organizations || fields['Target Organizations'];
            const isPublic = payload.is_public !== undefined ? payload.is_public : fields['Is Public'];
            this.logger.log(`SERVICE: Processing targeting fields - Target Organizations: ${JSON.stringify(targetOrganizations)}, Is Public: ${isPublic}`);
            if (targetOrganizations !== undefined) {
                let targetOrgArray = [];
                if (Array.isArray(targetOrganizations)) {
                    targetOrgArray = targetOrganizations;
                }
                else if (typeof targetOrganizations === 'string') {
                    targetOrgArray = [targetOrganizations];
                }
                mappedData.targetOrganizations = targetOrgArray.length > 0 ? targetOrgArray : null;
                mappedData.isTargeted = targetOrgArray.length > 0;
                this.logger.log(`SERVICE: Set targetOrganizations: ${JSON.stringify(targetOrgArray)}, isTargeted: ${mappedData.isTargeted}`);
            }
            if (isPublic !== undefined) {
                mappedData.isPublic = !!isPublic;
                this.logger.log(`SERVICE: Set isPublic: ${mappedData.isPublic}`);
            }
            if (payload.status === 'Assigned') {
                const carrierCompanyName = fields['Booking Request - User ID'];
                if (carrierCompanyName) {
                    this.logger.log(`SERVICE: Load ${payload.airtable_record_id} assigned to carrier: ${carrierCompanyName}`);
                    const carrierProfile = await this.prisma.carrierProfile.findFirst({
                        where: {
                            companyName: carrierCompanyName
                        }
                    });
                    if (carrierProfile) {
                        mappedData.awardedToCarrierProfileId = carrierProfile.id;
                        this.logger.log(`SERVICE: Successfully linked load to carrier profile ID: ${carrierProfile.id}`);
                    }
                    else {
                        this.logger.warn(`SERVICE: Could not find carrier profile for company: ${carrierCompanyName}`);
                    }
                }
                else {
                    this.logger.warn(`SERVICE: Load status is "Assigned" but no booking request information found for ${payload.airtable_record_id}`);
                }
            }
        }
        catch (airtableError) {
            this.logger.error(`SERVICE: Error fetching Airtable record for targeting fields: ${airtableError.message}`, airtableError.stack);
            mappedData.isPublic = true;
            mappedData.isTargeted = false;
            mappedData.targetOrganizations = null;
        }
        Object.keys(mappedData).forEach(key => {
            if (mappedData[key] === undefined) {
                delete mappedData[key];
            }
        });
        try {
            const existingLoad = await this.prisma.load.findUnique({
                where: { airtableRecordId: payload.airtable_record_id },
            });
            let result;
            let isNewLoad = false;
            let oldStatus;
            if (existingLoad) {
                this.logger.log(`SERVICE: Found existing load (ID: ${existingLoad.id}). Updating...`);
                oldStatus = existingLoad.status;
                result = await this.prisma.load.update({
                    where: { id: existingLoad.id },
                    data: mappedData,
                });
            }
            else {
                this.logger.log(`SERVICE: No existing load found. Creating new load...`);
                isNewLoad = true;
                result = await this.prisma.load.create({
                    data: mappedData,
                });
            }
            try {
                await this.sendLoadNotifications(result, isNewLoad, oldStatus, payload);
            }
            catch (notificationError) {
                this.logger.error(`Failed to send notifications for load ${result.id}: ${notificationError.message}`, notificationError.stack);
            }
            return result;
        }
        catch (error) {
            this.logger.error(`SERVICE: Database error processing webhook for Airtable ID ${payload.airtable_record_id}: ${error.message}`, error.stack);
            if (error.code === 'P2002') {
                throw new common_1.BadRequestException('Error due to duplicate entry, potentially race condition.');
            }
            throw new common_1.InternalServerErrorException(`DATABASE: Database operation failed: ${error.message}`);
        }
    }
    async sendLoadNotifications(load, isNewLoad, oldStatus, payload) {
        this.logger.log(`Sending notifications for load ${load.id}, isNew: ${isNewLoad}, oldStatus: ${oldStatus}, newStatus: ${load.status}`);
        if (isNewLoad) {
            const notificationData = {
                loadId: load.id,
                airtableRecordId: load.airtableRecordId,
                originCity: load.originCity || undefined,
                originState: load.originState || undefined,
                destinationCity: load.destinationCity || undefined,
                destinationState: load.destinationState || undefined,
                rate: load.rate || undefined,
                equipmentRequired: load.equipmentRequired || undefined,
                pickupDate: load.pickupDateUtc || undefined,
                deliveryDate: load.deliveryDateUtc || undefined,
                status: load.status || undefined,
                targetOrganizations: this.parseTargetOrganizations(load.targetOrganizations),
                isPublic: load.isPublic || false,
            };
            await this.notificationsService.broadcastNewLoad(notificationData);
        }
        else if (oldStatus && oldStatus !== load.status) {
            const statusChangeData = {
                loadId: load.id,
                airtableRecordId: load.airtableRecordId,
                oldStatus: oldStatus || 'Unknown',
                newStatus: load.status || 'Unknown',
            };
            if (load.awardedToCarrierProfileId) {
                try {
                    const carrierProfile = await this.prisma.carrierProfile.findUnique({
                        where: { id: load.awardedToCarrierProfileId },
                        include: { user: true }
                    });
                    if (carrierProfile) {
                        statusChangeData.assignedCarrierUserId = carrierProfile.user.airtableUserId || undefined;
                        statusChangeData.assignedCarrierCompanyName = carrierProfile.companyName || undefined;
                    }
                }
                catch (error) {
                    this.logger.warn(`Failed to fetch carrier details for load assignment notification: ${error.message}`);
                }
            }
            await this.notificationsService.broadcastLoadStatusChange(statusChangeData);
        }
    }
    async getAvailableLoads(userAirtableId) {
        const startTime = Date.now();
        this.logger.log('SERVICE: getAvailableLoads called (N8N JWT - MC Number Based Targeting)');
        try {
            await this.prisma.$queryRaw `SELECT 1`;
        }
        catch (dbError) {
            this.logger.error('DATABASE: Connection health check failed:', dbError);
            throw new common_1.HttpException('Database connection unavailable', common_1.HttpStatus.SERVICE_UNAVAILABLE);
        }
        const baseCacheKey = 'available_loads_mc_number_v1';
        let cacheKey = baseCacheKey;
        let userMcNumber = null;
        let isAdmin = false;
        if (userAirtableId) {
            try {
                this.logger.log(`SERVICE: Fetching user profile for ${userAirtableId}`);
                const userCacheKey = `user_profile_${userAirtableId}`;
                let userProfile = await this.cacheManager.get(userCacheKey);
                if (!userProfile) {
                    this.logger.log(`SERVICE: User profile cache miss, fetching from database`);
                    try {
                        userProfile = await this.prisma.userProfile.findUnique({
                            where: { airtableUserId: userAirtableId },
                            select: {
                                mcNumber: true,
                                role: true,
                                airtableUserId: true
                            }
                        });
                        if (userProfile) {
                            await this.cacheManager.set(userCacheKey, userProfile, 300000);
                        }
                    }
                    catch (dbError) {
                        this.logger.error('DATABASE: Failed to fetch user profile:', dbError);
                        throw new common_1.HttpException(`Database error while fetching user profile: ${dbError.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                    }
                }
                else {
                    this.logger.debug(`SERVICE: User profile cache hit for ${userAirtableId}`);
                }
                if (userProfile) {
                    userMcNumber = userProfile.mcNumber;
                    isAdmin = userProfile.role?.toLowerCase() === 'admin';
                    cacheKey = `${baseCacheKey}_${isAdmin ? 'admin' : (userMcNumber || 'no_mc')}`;
                    this.logger.log(`SERVICE: User ${userAirtableId} MC Number: "${userMcNumber}", isAdmin: ${isAdmin}, role: ${userProfile.role}`);
                }
                else {
                    this.logger.warn(`SERVICE: User profile ${userAirtableId} not found in database`);
                }
            }
            catch (error) {
                this.logger.error(`SERVICE: Failed to fetch user profile: ${error.message}`, error.stack);
                throw new common_1.HttpException(`Database error while fetching user profile: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
        else {
            this.logger.warn('SERVICE: No userAirtableId provided for MC Number filtering');
        }
        try {
            const cachedLoads = await this.cacheManager.get(cacheKey);
            if (cachedLoads) {
                const cacheTime = Date.now() - startTime;
                this.logger.log(`SERVICE: Cache hit for ${cacheKey}, returning cached loads in ${cacheTime}ms`);
                return cachedLoads;
            }
        }
        catch (cacheError) {
            this.logger.warn(`SERVICE: Cache retrieval failed: ${cacheError.message}`);
        }
        const recordsData = [];
        try {
            this.logger.log(`SERVICE: Querying Airtable for loads with "Synced to API" = true`);
            const fieldsToFetch = [
                'Order ID.',
                'Rate to Carrier',
                'Pickup Date & Time',
                'Pickup Appt.',
                'Delivery Date & Time',
                'Delivery Appt.',
                'Weight (lbs)',
                'Status',
                'Equipment Required',
                'Pickup City Lookup',
                'Pickup State Lookup',
                'Delivery City Lookup',
                'Delivery State Lookup',
                'Target MC Numbers',
                'Target Organizations'
            ];
            this.logger.log(`SERVICE: Fetching records from Airtable with "Synced to API" filter`);
            const records = await this.base(this.tableName)
                .select({
                view: 'Grid view',
                fields: fieldsToFetch,
                filterByFormula: `AND({Synced to API}, {Status} = "Available")`
            })
                .all();
            this.logger.log(`SERVICE: Fetched ${records.length} records from Airtable with "Synced to API" = true`);
            for (const record of records) {
                try {
                    const pickupCityRaw = record.get('Pickup City Lookup');
                    const pickupStateRaw = record.get('Pickup State Lookup');
                    const deliveryCityRaw = record.get('Delivery City Lookup');
                    const deliveryStateRaw = record.get('Delivery State Lookup');
                    const pickupCity = Array.isArray(pickupCityRaw) ? pickupCityRaw.join(', ') : pickupCityRaw;
                    const pickupState = Array.isArray(pickupStateRaw) ? pickupStateRaw.join(', ') : pickupStateRaw;
                    const deliveryCity = Array.isArray(deliveryCityRaw) ? deliveryCityRaw.join(', ') : deliveryCityRaw;
                    const deliveryState = Array.isArray(deliveryStateRaw) ? deliveryStateRaw.join(', ') : deliveryStateRaw;
                    const targetMcNumbers = record.get('Target MC Numbers');
                    const targetOrganizations = record.get('Target Organizations');
                    let isTargetedToUser = false;
                    let canUserSeeLoad = false;
                    let isPublic = false;
                    this.logger.debug(`SERVICE: Processing record ${record.id}, targetMcNumbers: ${JSON.stringify(targetMcNumbers)}, targetOrganizations: ${JSON.stringify(targetOrganizations)}`);
                    const hasTargeting = targetMcNumbers && (Array.isArray(targetMcNumbers) ? targetMcNumbers.length > 0 : targetMcNumbers.trim() !== '');
                    if (hasTargeting) {
                        isPublic = false;
                        if (userMcNumber) {
                            const mcArray = Array.isArray(targetMcNumbers)
                                ? targetMcNumbers
                                : [targetMcNumbers];
                            isTargetedToUser = mcArray.some(mc => mc.toLowerCase().trim() === userMcNumber.toLowerCase().trim());
                            this.logger.debug(`SERVICE: Load ${record.id} is TARGETED - user MC: "${userMcNumber}", target MCs: ${JSON.stringify(mcArray)}, isTargeted: ${isTargetedToUser}`);
                            canUserSeeLoad = isTargetedToUser;
                        }
                        else {
                            this.logger.debug(`SERVICE: Load ${record.id} is TARGETED but user has no MC Number - denying access`);
                            canUserSeeLoad = false;
                        }
                    }
                    else {
                        isPublic = true;
                        canUserSeeLoad = true;
                        this.logger.debug(`SERVICE: Load ${record.id} is PUBLIC (no MC targeting) - allowing access to all users`);
                    }
                    if (!isTargetedToUser && !hasTargeting && targetOrganizations && userMcNumber) {
                        this.logger.debug(`SERVICE: Load ${record.id} falling back to legacy organization targeting`);
                        this.logger.warn(`SERVICE: Load ${record.id} uses legacy organization targeting - recommend migrating to MC Number targeting`);
                        isPublic = false;
                    }
                    if (isAdmin) {
                        const wasBlocked = !canUserSeeLoad;
                        canUserSeeLoad = true;
                        this.logger.log(`SERVICE: Admin override - user can see load ${record.id}${wasBlocked ? ' (was blocked)' : ''}`);
                    }
                    if (!canUserSeeLoad) {
                        this.logger.warn(`SECURITY: Access DENIED to load ${record.id} for user ${userAirtableId} (MC: "${userMcNumber}") - isPublic: ${isPublic}, targets: ${JSON.stringify(targetMcNumbers)}`);
                        continue;
                    }
                    else {
                        this.logger.log(`SECURITY: Access GRANTED to load ${record.id} for user ${userAirtableId} (MC: "${userMcNumber}") - isPublic: ${isPublic}, targeted: ${isTargetedToUser}`);
                    }
                    let miles;
                    try {
                        if (!pickupCity || !pickupState || !deliveryCity || !deliveryState) {
                            this.logger.warn(`SERVICE: Missing location data for load ${record.id} - pickup: ${pickupCity}, ${pickupState} → delivery: ${deliveryCity}, ${deliveryState}`);
                            continue;
                        }
                        miles = await this.calculateMilesForLoad(pickupCity, pickupState, deliveryCity, deliveryState);
                    }
                    catch (milesError) {
                        this.logger.error(`SERVICE: Failed to calculate miles for load ${record.id}: ${milesError.message}`);
                        continue;
                    }
                    const equipmentValue = record.get('Equipment Required');
                    const equipment = (typeof equipmentValue === 'string' ? equipmentValue : 'Dry Van');
                    const temp = equipment?.toUpperCase().includes('REEFER') ? '-10°F' : null;
                    const commodity = 'Frozen Fruit';
                    const formatLocationString = (cities, states) => {
                        const cityArray = cities.split(',').map(city => city.trim());
                        const stateArray = states.split(',').map(state => state.trim());
                        if (cityArray.length === 1) {
                            return `${cityArray[0]}, ${stateArray[0] || ''}`;
                        }
                        return cityArray.map((city, index) => {
                            const state = stateArray[index] || stateArray[0] || '';
                            const position = index === 0 ? '1st' : index === 1 ? '2nd' : index === 2 ? '3rd' : `${index + 1}th`;
                            return `${position} ${city}, ${state}`;
                        }).join(' / ');
                    };
                    const loadData = {
                        id: record.id,
                        proNumber: record.get('Order ID.'),
                        rate: record.get('Rate to Carrier'),
                        origin: formatLocationString(pickupCity, pickupState),
                        destination: formatLocationString(deliveryCity, deliveryState),
                        pickupDateTime: record.get('Pickup Date & Time'),
                        pickupAppointmentTime: record.get('Pickup Appt.'),
                        deliveryDateTime: record.get('Delivery Date & Time'),
                        deliveryAppointmentTime: record.get('Delivery Appt.'),
                        weight: record.get('Weight (lbs)'),
                        status: record.get('Status'),
                        equipment: equipment,
                        miles: miles,
                        temp: temp,
                        commodity: commodity,
                        isPublic: isPublic,
                        isTargeted: isTargetedToUser,
                        targetOrganizations: targetOrganizations,
                        originCity: pickupCity,
                        originState: pickupState,
                        destinationCity: deliveryCity,
                        destinationState: deliveryState,
                        specialInstructions: record.get('Special Instructions'),
                        brokerCompany: record.get('Broker Company'),
                        shipperCompany: record.get('Shipper Company'),
                        receiverCompany: record.get('Receiver Company')
                    };
                    this.logger.debug(`SERVICE: Load ${record.id} final result - isPublic: ${isPublic}, isTargeted: ${isTargetedToUser}, visible: ${canUserSeeLoad}`);
                    recordsData.push(loadData);
                }
                catch (recordError) {
                    this.logger.error(`SERVICE: Error processing record ${record.id}:`, recordError);
                }
            }
            this.logger.log(`SERVICE: Filtered to ${recordsData.length} loads visible to user (ONLY loads with "Synced to API" = true)`);
            const sortedLoads = recordsData.sort((a, b) => {
                if (a.isTargeted && !b.isTargeted)
                    return -1;
                if (!a.isTargeted && b.isTargeted)
                    return 1;
                const aPickupDate = a.pickupDateTime ? new Date(a.pickupDateTime).getTime() : 0;
                const bPickupDate = b.pickupDateTime ? new Date(b.pickupDateTime).getTime() : 0;
                return aPickupDate - bPickupDate;
            });
            const targetedCount = sortedLoads.filter(load => load.isTargeted).length;
            const publicCount = sortedLoads.length - targetedCount;
            this.logger.log(`SERVICE: Load sorting completed - ${targetedCount} targeted loads (top), ${publicCount} public loads (bottom)`);
            try {
                await this.cacheManager.set(cacheKey, sortedLoads, 120000);
                this.logger.debug(`SERVICE: Cached sorted loads under key ${cacheKey}`);
            }
            catch (cacheError) {
                this.logger.warn(`SERVICE: Failed to cache loads: ${cacheError.message}`);
            }
            const totalTime = Date.now() - startTime;
            this.logger.log(`SERVICE: getAvailableLoads (EMERGENCY FIX - Airtable Filtered) completed in ${totalTime}ms`);
            return sortedLoads;
        }
        catch (error) {
            this.logger.error('SERVICE: Error fetching data from Airtable:', error);
            throw new common_1.HttpException(`Failed to fetch loads from Airtable: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAvailableLoadsWithFilters(userAirtableId, filters) {
        const startTime = Date.now();
        this.logger.log(`SERVICE: getAvailableLoadsWithFilters called for user ${userAirtableId} with filters:`, filters);
        const baseLoads = await this.getAvailableLoads(userAirtableId);
        let filteredLoads = [...baseLoads];
        if (filters?.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredLoads = filteredLoads.filter(load => load.proNumber?.toLowerCase().includes(searchTerm) ||
                load.origin?.toLowerCase().includes(searchTerm) ||
                load.destination?.toLowerCase().includes(searchTerm) ||
                load.equipment?.toLowerCase().includes(searchTerm) ||
                load.status?.toLowerCase().includes(searchTerm));
            this.logger.debug(`SERVICE: Text search "${searchTerm}" filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.origin) {
            const originTerm = filters.origin.toLowerCase();
            filteredLoads = filteredLoads.filter(load => load.origin?.toLowerCase().includes(originTerm));
            this.logger.debug(`SERVICE: Origin filter "${originTerm}" filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.destination) {
            const destTerm = filters.destination.toLowerCase();
            filteredLoads = filteredLoads.filter(load => load.destination?.toLowerCase().includes(destTerm));
            this.logger.debug(`SERVICE: Destination filter "${destTerm}" filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.equipment) {
            filteredLoads = filteredLoads.filter(load => load.equipment === filters.equipment);
            this.logger.debug(`SERVICE: Equipment filter "${filters.equipment}" filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.equipmentTypes && filters.equipmentTypes.length > 0) {
            filteredLoads = filteredLoads.filter(load => filters.equipmentTypes.includes(load.equipment));
            this.logger.debug(`SERVICE: Equipment types filter [${filters.equipmentTypes.join(', ')}] filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.minRate !== undefined) {
            filteredLoads = filteredLoads.filter(load => {
                const rate = typeof load.rate === 'string' ? parseFloat(load.rate) : load.rate;
                return rate >= filters.minRate;
            });
            this.logger.debug(`SERVICE: Min rate filter ${filters.minRate} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.maxRate !== undefined) {
            filteredLoads = filteredLoads.filter(load => {
                const rate = typeof load.rate === 'string' ? parseFloat(load.rate) : load.rate;
                return rate <= filters.maxRate;
            });
            this.logger.debug(`SERVICE: Max rate filter ${filters.maxRate} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.minWeight !== undefined) {
            filteredLoads = filteredLoads.filter(load => load.weight >= filters.minWeight);
            this.logger.debug(`SERVICE: Min weight filter ${filters.minWeight} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.maxWeight !== undefined) {
            filteredLoads = filteredLoads.filter(load => load.weight <= filters.maxWeight);
            this.logger.debug(`SERVICE: Max weight filter ${filters.maxWeight} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.pickupDateStart) {
            filteredLoads = filteredLoads.filter(load => {
                if (!load.pickupDateTime)
                    return false;
                const pickupDate = new Date(load.pickupDateTime);
                return pickupDate >= filters.pickupDateStart;
            });
            this.logger.debug(`SERVICE: Pickup date start filter ${filters.pickupDateStart.toISOString()} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.pickupDateEnd) {
            filteredLoads = filteredLoads.filter(load => {
                if (!load.pickupDateTime)
                    return false;
                const pickupDate = new Date(load.pickupDateTime);
                return pickupDate <= filters.pickupDateEnd;
            });
            this.logger.debug(`SERVICE: Pickup date end filter ${filters.pickupDateEnd.toISOString()} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.deliveryDateStart) {
            filteredLoads = filteredLoads.filter(load => {
                if (!load.deliveryDateTime)
                    return false;
                const deliveryDate = new Date(load.deliveryDateTime);
                return deliveryDate >= filters.deliveryDateStart;
            });
            this.logger.debug(`SERVICE: Delivery date start filter ${filters.deliveryDateStart.toISOString()} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.deliveryDateEnd) {
            filteredLoads = filteredLoads.filter(load => {
                if (!load.deliveryDateTime)
                    return false;
                const deliveryDate = new Date(load.deliveryDateTime);
                return deliveryDate <= filters.deliveryDateEnd;
            });
            this.logger.debug(`SERVICE: Delivery date end filter ${filters.deliveryDateEnd.toISOString()} filtered to ${filteredLoads.length} loads`);
        }
        if (filters?.geoCenter && filters?.geoRadius) {
            const centerCoords = geographic_utils_1.GeographicUtils.parseCoordinates(filters.geoCenter);
            if (centerCoords) {
                filteredLoads = await this.applyGeographicFilter(filteredLoads, centerCoords, filters.geoRadius, filters.geoFilterOrigin ?? true);
                this.logger.debug(`SERVICE: Geographic filter (center: ${filters.geoCenter}, radius: ${filters.geoRadius} miles) filtered to ${filteredLoads.length} loads`);
            }
            else {
                this.logger.warn(`SERVICE: Invalid geographic center coordinates: ${filters.geoCenter}`);
            }
        }
        const totalCount = filteredLoads.length;
        if (filters?.sortBy) {
            filteredLoads = this.applySorting(filteredLoads, filters.sortBy, filters.sortOrder);
            this.logger.debug(`SERVICE: Applied sorting by ${filters.sortBy} ${filters.sortOrder}`);
        }
        const page = filters?.page || 1;
        const pageSize = filters?.pageSize || 25;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedLoads = filteredLoads.slice(startIndex, endIndex);
        const totalTime = Date.now() - startTime;
        this.logger.log(`SERVICE: getAvailableLoadsWithFilters completed in ${totalTime}ms - ${totalCount} total, ${paginatedLoads.length} on page ${page}`);
        return {
            loads: paginatedLoads,
            totalCount,
            page,
            pageSize
        };
    }
    async applyGeographicFilter(loads, centerCoords, radiusMiles, filterOrigin = true) {
        const filteredLoads = [];
        for (const load of loads) {
            try {
                const locationToCheck = filterOrigin ? load.origin : load.destination;
                if (!locationToCheck)
                    continue;
                const locationParts = geographic_utils_1.GeographicUtils.parseLocation(locationToCheck);
                if (!locationParts)
                    continue;
                let locationCoords = geographic_utils_1.GeographicUtils.getCityCoordinates(locationParts.city, locationParts.state);
                if (!locationCoords) {
                    this.logger.debug(`SERVICE: No coordinates found for ${locationToCheck}, skipping geographic filter`);
                    continue;
                }
                if (geographic_utils_1.GeographicUtils.isWithinRadius(centerCoords, locationCoords, radiusMiles)) {
                    const distance = geographic_utils_1.GeographicUtils.calculateDistance(centerCoords, locationCoords);
                    filteredLoads.push({
                        ...load,
                        distance: Math.round(distance * 10) / 10
                    });
                }
            }
            catch (error) {
                this.logger.warn(`SERVICE: Error applying geographic filter to load ${load.id}: ${error.message}`);
            }
        }
        return filteredLoads;
    }
    applySorting(loads, sortBy, sortOrder = advanced_filters_dto_1.SortOrder.ASC) {
        return loads.sort((a, b) => {
            let valueA;
            let valueB;
            switch (sortBy) {
                case advanced_filters_dto_1.SortField.PICKUP_DATE:
                    valueA = a.pickupDateTime ? new Date(a.pickupDateTime).getTime() : 0;
                    valueB = b.pickupDateTime ? new Date(b.pickupDateTime).getTime() : 0;
                    break;
                case advanced_filters_dto_1.SortField.DELIVERY_DATE:
                    valueA = a.deliveryDateTime ? new Date(a.deliveryDateTime).getTime() : 0;
                    valueB = b.deliveryDateTime ? new Date(b.deliveryDateTime).getTime() : 0;
                    break;
                case advanced_filters_dto_1.SortField.RATE:
                    valueA = typeof a.rate === 'string' ? parseFloat(a.rate) || 0 : a.rate || 0;
                    valueB = typeof b.rate === 'string' ? parseFloat(b.rate) || 0 : b.rate || 0;
                    break;
                case advanced_filters_dto_1.SortField.DISTANCE:
                    valueA = a.distance || 0;
                    valueB = b.distance || 0;
                    break;
                case advanced_filters_dto_1.SortField.WEIGHT:
                    valueA = a.weight || 0;
                    valueB = b.weight || 0;
                    break;
                case advanced_filters_dto_1.SortField.EQUIPMENT:
                    valueA = a.equipment || '';
                    valueB = b.equipment || '';
                    break;
                case advanced_filters_dto_1.SortField.CREATED_AT:
                default:
                    valueA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                    valueB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                    break;
            }
            if (typeof valueA === 'string' && typeof valueB === 'string') {
                valueA = valueA.toLowerCase();
                valueB = valueB.toLowerCase();
            }
            let comparison = 0;
            if (valueA < valueB)
                comparison = -1;
            else if (valueA > valueB)
                comparison = 1;
            return sortOrder === advanced_filters_dto_1.SortOrder.DESC ? -comparison : comparison;
        });
    }
    async syncAllLoadsFromAirtable(syncAll = false) {
        this.logger.log(`SERVICE: syncAllLoadsFromAirtable called with syncAll=${syncAll}`);
        this.logger.log(`Starting ${syncAll ? 'full' : 'optimized'} batch sync from Airtable table: ${this.tableName}`);
        let syncedCount = 0;
        const errors = [];
        try {
            const selectOptions = {
                view: 'Grid view',
                fields: [
                    'Order ID.',
                    'Status',
                    'Pickup City Lookup',
                    'Pickup State Lookup',
                    'Delivery City Lookup',
                    'Delivery State Lookup',
                    'Equipment Required',
                    'Weight (lbs)',
                    'Rate to Carrier',
                    'Pickup Date & Time',
                    'Delivery Date & Time',
                    'Temperature',
                    'Synced to API',
                    'PO Number',
                    'SO Number',
                    'Pickup Number',
                    'Delivery No.',
                    'Shipper Address',
                    'Receiver Name',
                    'Receiver Address',
                    'Inv Status',
                    'Carrier',
                    'Cases',
                    'Pallets'
                ],
                maxRecords: syncAll ? 5000 : 500
            };
            if (!syncAll) {
                selectOptions.filterByFormula = `AND({Synced to API}, OR({Status} = "Available", {Status} = "Booking Requested"))`;
                this.logger.log('SERVICE: Using filtered sync (Synced to API = true only)');
            }
            else {
                selectOptions.filterByFormula = `NOT({Order ID.} = BLANK())`;
                this.logger.log('SERVICE: Using full sync (all loads with Order ID)');
            }
            const records = await this.base(this.tableName).select(selectOptions).all();
            this.logger.log(`SERVICE: Fetched ${records.length} records from Airtable (${syncAll ? 'all loads' : 'Synced to API = true only'}).`);
            const BATCH_SIZE = syncAll ? 20 : 10;
            for (let i = 0; i < records.length; i += BATCH_SIZE) {
                const batch = records.slice(i, i + BATCH_SIZE);
                this.logger.log(`SERVICE: Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(records.length / BATCH_SIZE)} (${batch.length} records)`);
                const batchPromises = batch.map(async (record) => {
                    return Promise.race([
                        this.processSingleRecord(record),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Record processing timeout')), 8000))
                    ]);
                });
                const batchResults = await Promise.allSettled(batchPromises);
                batchResults.forEach((result, index) => {
                    if (result.status === 'fulfilled') {
                        syncedCount++;
                    }
                    else {
                        const record = batch[index];
                        this.logger.error(`SERVICE: Error processing record ${record.id}: ${result.reason}`);
                        errors.push({ airtableRecordId: record.id, error: result.reason });
                    }
                });
                if (i + BATCH_SIZE < records.length) {
                    await new Promise(resolve => setTimeout(resolve, syncAll ? 50 : 100));
                }
            }
            this.logger.log(`SERVICE: Optimized sync completed. Synced ${syncedCount} records. Encountered ${errors.length} errors.`);
            return { syncedCount, errors };
        }
        catch (error) {
            this.logger.error('SERVICE: Major error during optimized sync from Airtable:', error);
            throw new common_1.InternalServerErrorException(`Failed to sync loads from Airtable: ${error.message}`);
        }
    }
    async processSingleRecord(record) {
        const airtableRecordId = record.id;
        const mappedData = {
            airtableRecordId: airtableRecordId,
            proNumber: record.fields['Order ID.'] || undefined,
            originCity: record.fields['Pickup City Lookup'] ? record.fields['Pickup City Lookup'].join(', ') : undefined,
            originState: record.fields['Pickup State Lookup'] ? record.fields['Pickup State Lookup'].join(', ') : undefined,
            destinationCity: record.fields['Delivery City Lookup'] ? record.fields['Delivery City Lookup'].join(', ') : undefined,
            destinationState: record.fields['Delivery State Lookup'] ? record.fields['Delivery State Lookup'].join(', ') : undefined,
            equipmentRequired: record.fields['Equipment Required'] || undefined,
            weightLbs: record.fields['Weight (lbs)'] || undefined,
            rate: record.fields['Rate to Carrier'] || undefined,
            status: this.mapAirtableStatusToEnum(record.fields['Status']),
            temperature: record.fields['Temperature'] || undefined,
            rawAirtableData: record.fields,
            poNumber: record.fields['PO Number'] || undefined,
            soNumber: record.fields['SO Number'] || undefined,
            pickupNumber: record.fields['Pickup Number'] || undefined,
            deliveryNumber: record.fields['Delivery No.'] || undefined,
            shipperAddress: record.fields['Shipper Address'] || undefined,
            receiverName: record.fields['Receiver Name'] || undefined,
            receiverAddress: record.fields['Receiver Address'] || undefined,
            invStatus: this.mapAirtableInvStatusToEnum(record.fields['Inv Status']),
            carrier: record.fields['Carrier'] || undefined,
            cases: record.fields['Cases'] || undefined,
            pallets: record.fields['Pallets'] || undefined,
        };
        if (record.fields['Pickup Date & Time']) {
            const pickupDate = new Date(record.fields['Pickup Date & Time']);
            if (!isNaN(pickupDate.getTime())) {
                mappedData.pickupDateUtc = pickupDate;
            }
        }
        if (record.fields['Delivery Date & Time']) {
            const deliveryDate = new Date(record.fields['Delivery Date & Time']);
            if (!isNaN(deliveryDate.getTime())) {
                mappedData.deliveryDateUtc = deliveryDate;
            }
        }
        Object.keys(mappedData).forEach(key => {
            if (mappedData[key] === undefined) {
                delete mappedData[key];
            }
        });
        await this.prisma.load.upsert({
            where: { airtableRecordId: airtableRecordId },
            update: mappedData,
            create: mappedData,
        });
    }
    async requestLoadBooking(loadId, requestingUserClerkId, bookingDetailsDto) {
        this.logger.log(`SERVICE: User ${requestingUserClerkId} requesting to book load ${loadId} with details: ${JSON.stringify(bookingDetailsDto)}`);
        const airtableRecord = await this.getAirtableRecordById(loadId);
        const loadFields = airtableRecord.fields;
        const airtableStatus = loadFields['Status'];
        if (airtableStatus !== 'Available') {
            this.logger.warn(`SERVICE: Load ${loadId} is not available for booking. Current Airtable status: ${airtableStatus}`);
            throw new common_1.BadRequestException(`Load is not available for booking. Status: ${airtableStatus}`);
        }
        const requestingUser = await this.prisma.user.findUnique({
            where: { airtableUserId: requestingUserClerkId },
            include: { carrierProfile: true },
        });
        if (!requestingUser) {
            this.logger.error(`SERVICE: User with Clerk ID ${requestingUserClerkId} not found.`);
            throw new common_1.NotFoundException('Requesting user not found.');
        }
        if (requestingUser.role !== db_1.Role.CARRIER && requestingUser.role !== db_1.Role.ADMIN) {
            this.logger.warn(`SERVICE: User ${requestingUserClerkId} is not a CARRIER or ADMIN. Role: ${requestingUser.role}. Cannot book load.`);
            throw new common_1.ForbiddenException('Only users with the CARRIER or ADMIN role can book loads.');
        }
        if (!requestingUser.carrierProfile) {
            this.logger.warn(`SERVICE: User ${requestingUserClerkId} (Role: CARRIER) has no carrier profile. Cannot book load.`);
            throw new common_1.ForbiddenException('Your carrier profile is incomplete. Please complete your carrier profile to book loads.');
        }
        const fieldsToUpdate = {
            'Status': 'Booking Requested',
            'Booking Request - User ID': requestingUser.carrierProfile.companyName ?? 'Unknown Company',
            'Booking Request - Timestamp': new Date().toISOString(),
            'Booking Request - Carrier MC Number': requestingUser.carrierProfile.mcNumber ?? undefined,
            'Booking Request - Carrier Contact Name': requestingUser.carrierProfile.contact_name ??
                (`${requestingUser.firstName || ''} ${requestingUser.lastName || ''}`.trim() ||
                    undefined),
            'Booking Request - Carrier Contact Email': requestingUser.carrierProfile.contact_email ?? requestingUser.email ?? undefined,
            'Booking Request - Carrier Contact Phone': requestingUser.carrierProfile.contact_phone ?? undefined,
            'Booking Request - Is Truck Empty': bookingDetailsDto.isTruckEmpty,
            'Booking Request - Truck Empty Location': bookingDetailsDto.truckEmptyLocation,
            'Booking Request - ETA to Shipper': bookingDetailsDto.etaToShipper,
            'Booking Request - Notes': bookingDetailsDto.notes ?? undefined,
            'Booking Request - Rate Accepted': loadFields['Rate to Carrier'] ?? undefined,
        };
        Object.keys(fieldsToUpdate).forEach(key => {
            if (fieldsToUpdate[key] === undefined) {
                delete fieldsToUpdate[key];
            }
        });
        try {
            await this.base(this.tableName).update([
                {
                    id: loadId,
                    fields: fieldsToUpdate,
                },
            ], { typecast: true });
            this.logger.log(`SERVICE: Successfully updated Airtable record ${loadId} for booking request by user ${requestingUserClerkId}.`);
            try {
                const loadDetails = {
                    airtableRecordId: airtableRecord.id,
                    proNumber: airtableRecord.fields['Order ID.'],
                    rate: airtableRecord.fields['Rate to Carrier'],
                    origin: `${airtableRecord.fields['Pickup City Lookup']?.[0] || ''}, ${airtableRecord.fields['Pickup State Lookup']?.[0] || ''}`,
                    destination: `${airtableRecord.fields['Delivery City Lookup']?.[0] || ''}, ${airtableRecord.fields['Delivery State Lookup']?.[0] || ''}`,
                };
                await this.notificationsService.broadcastBookingConfirmation(loadId, requestingUserClerkId, loadDetails);
            }
            catch (notificationError) {
                this.logger.error(`Failed to send booking confirmation notification: ${notificationError.message}`, notificationError.stack);
            }
            return {
                message: 'Booking request submitted successfully! Awaiting admin approval.',
                loadId: airtableRecord.id,
                carrierUserId: requestingUserClerkId,
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Failed to update Airtable record ${loadId} for booking request: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to log booking request. Please try again or contact support.');
        }
    }
    async getAssignedLoadsForCarrier(airtableUserId) {
        this.logger.log(`SERVICE: getAssignedLoadsForCarrier called for user ${airtableUserId}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId: airtableUserId },
            include: { carrierProfile: true },
        });
        if (!user) {
            this.logger.error(`SERVICE: User with Airtable ID ${airtableUserId} not found.`);
            throw new common_1.NotFoundException('User not found.');
        }
        if (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN) {
            this.logger.warn(`SERVICE: User ${airtableUserId} is not a CARRIER or ADMIN. Role: ${user.role}`);
            throw new common_1.ForbiddenException('Only users with the CARRIER or ADMIN role can view assigned loads.');
        }
        if (user.role === db_1.Role.ADMIN) {
            this.logger.log(`SERVICE: Admin user ${airtableUserId} requesting all assigned loads`);
            const allAssignedLoads = await this.prisma.load.findMany({
                where: {
                    status: {
                        in: [db_1.LoadStatus.ASSIGNED, db_1.LoadStatus.DELIVERED_EMPTY],
                    },
                    awardedToCarrierProfileId: { not: null },
                },
                include: {
                    awardedToCarrierProfile: {
                        select: {
                            companyName: true,
                            contact_name: true,
                            contact_email: true,
                            contact_phone: true,
                        },
                    },
                },
                orderBy: {
                    pickupDateUtc: 'asc',
                },
            });
            this.logger.log(`SERVICE: Found ${allAssignedLoads.length} assigned loads for admin user`);
            return allAssignedLoads;
        }
        if (!user.carrierProfile) {
            this.logger.warn(`SERVICE: User ${airtableUserId} (Role: CARRIER) has no carrier profile.`);
            throw new common_1.ForbiddenException('Carrier profile not found.');
        }
        const assignedLoads = await this.prisma.load.findMany({
            where: {
                awardedToCarrierProfileId: user.carrierProfile.id,
                status: {
                    in: [db_1.LoadStatus.ASSIGNED, db_1.LoadStatus.DELIVERED_EMPTY],
                },
            },
            include: {
                awardedToCarrierProfile: {
                    select: {
                        companyName: true,
                        contact_name: true,
                        contact_email: true,
                        contact_phone: true,
                    },
                },
            },
            orderBy: {
                pickupDateUtc: 'asc',
            },
        });
        const loadsWithAirtableData = await Promise.allSettled(assignedLoads.map(async (load) => {
            try {
                const airtableRecord = await Promise.race([
                    this.getAirtableRecordById(load.airtableRecordId),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Airtable timeout')), 5000))
                ]);
                const fields = airtableRecord.fields;
                return {
                    id: load.id,
                    airtableRecordId: load.airtableRecordId,
                    proNumber: fields['Order ID.'] || load.proNumber,
                    status: fields['Status'] || load.status,
                    rate: fields['Rate to Carrier'] || load.rate,
                    origin: `${fields['Pickup City Lookup']?.[0] || load.originCity}, ${fields['Pickup State Lookup']?.[0] || load.originState}`,
                    destination: `${fields['Delivery City Lookup']?.[0] || load.destinationCity}, ${fields['Delivery State Lookup']?.[0] || load.destinationState}`,
                    pickupDateTime: fields['Pickup Date & Time'] || load.pickupDateUtc?.toISOString(),
                    deliveryDateTime: fields['Delivery Date & Time'] || load.deliveryDateUtc?.toISOString(),
                    pickupAppointmentTime: fields['Pickup Appt.'],
                    deliveryAppointmentTime: fields['Delivery Appt.'],
                    weight: fields['Weight (lbs)'] || load.weightLbs,
                    equipment: fields['Equipment Required'] || load.equipmentRequired,
                    temperature: fields['Temperature'] || load.temperature,
                    distance: fields['Distance (Miles)'],
                    brokerCompany: fields['Broker Company'],
                    shipperCompany: fields['Shipper Company'],
                    receiverCompany: fields['Receiver Company'],
                    specialInstructions: fields['Special Instructions'],
                    awardedToCarrier: load.awardedToCarrierProfile,
                    bolUploaded: !!load.bolFileUrl,
                    podUploaded: !!load.podFileUrl,
                    invoiceUploaded: !!load.invoiceFileUrl,
                    bolUploadedAt: load.bolUploadedAt?.toISOString(),
                    podUploadedAt: load.podUploadedAt?.toISOString(),
                    invoiceUploadedAt: load.invoiceUploadedAt?.toISOString(),
                    paymentStatus: load.paymentStatus?.toLowerCase(),
                    paymentDueDate: load.paymentDueDate?.toISOString(),
                    paidAt: load.paidAt?.toISOString(),
                    paymentAmount: load.paymentAmount,
                    paymentNotes: load.paymentNotes,
                };
            }
            catch (error) {
                this.logger.warn(`SERVICE: Could not fetch Airtable data for load ${load.airtableRecordId}: ${error.message}`);
                return {
                    id: load.id,
                    airtableRecordId: load.airtableRecordId,
                    proNumber: load.proNumber,
                    status: load.status,
                    rate: load.rate,
                    origin: `${load.originCity}, ${load.originState}`,
                    destination: `${load.destinationCity}, ${load.destinationState}`,
                    pickupDateTime: load.pickupDateUtc?.toISOString(),
                    deliveryDateTime: load.deliveryDateUtc?.toISOString(),
                    weight: load.weightLbs,
                    equipment: load.equipmentRequired,
                    temperature: load.temperature,
                    awardedToCarrier: load.awardedToCarrierProfile,
                    bolUploaded: !!load.bolFileUrl,
                    podUploaded: !!load.podFileUrl,
                    invoiceUploaded: !!load.invoiceFileUrl,
                    bolUploadedAt: load.bolUploadedAt?.toISOString(),
                    podUploadedAt: load.podUploadedAt?.toISOString(),
                    invoiceUploadedAt: load.invoiceUploadedAt?.toISOString(),
                    paymentStatus: load.paymentStatus?.toLowerCase(),
                    paymentDueDate: load.paymentDueDate?.toISOString(),
                    paidAt: load.paidAt?.toISOString(),
                    paymentAmount: load.paymentAmount,
                    paymentNotes: load.paymentNotes,
                };
            }
        }));
        const successfulLoads = loadsWithAirtableData
            .filter((result) => result.status === 'fulfilled')
            .map((result) => result.value);
        const failures = loadsWithAirtableData.filter((result) => result.status === 'rejected');
        if (failures.length > 0) {
            this.logger.warn(`SERVICE: ${failures.length} loads failed to fetch Airtable data, using database fallback`);
        }
        this.logger.log(`SERVICE: Found ${successfulLoads.length} assigned loads for carrier ${user.carrierProfile.companyName}`);
        return successfulLoads;
    }
    async uploadLoadDocument(loadId, documentType, fileUrl, airtableUserId) {
        this.logger.log(`SERVICE: uploadLoadDocument called for load ${loadId}, type ${documentType} by user ${airtableUserId}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId },
            include: { carrierProfile: true },
        });
        if (!user || (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN)) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (user.role === db_1.Role.ADMIN) {
            this.logger.log(`SERVICE: Admin user ${airtableUserId} uploading ${documentType} for load ${loadId}`);
        }
        else {
            if (!user.carrierProfile) {
                throw new common_1.ForbiddenException('Carrier profile not found');
            }
            const load = await this.prisma.load.findFirst({
                where: {
                    id: loadId,
                    awardedToCarrierProfileId: user.carrierProfile.id,
                },
            });
            if (!load) {
                throw new common_1.ForbiddenException('Load not found or not assigned to you');
            }
        }
        const updateData = {
            updatedAt: new Date(),
        };
        switch (documentType) {
            case 'bol':
                updateData.bolFileUrl = fileUrl;
                updateData.bolUploadedAt = new Date();
                break;
            case 'pod':
                updateData.podFileUrl = fileUrl;
                updateData.podUploadedAt = new Date();
                break;
            case 'invoice':
                updateData.invoiceFileUrl = fileUrl;
                updateData.invoiceUploadedAt = new Date();
                break;
        }
        try {
            await this.prisma.load.update({
                where: { id: loadId },
                data: updateData,
            });
            this.logger.log(`SERVICE: Successfully uploaded ${documentType} for load ${loadId}`);
            return {
                success: true,
                message: `${documentType.toUpperCase()} uploaded successfully`,
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Failed to update load ${loadId} with ${documentType}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to update document information');
        }
    }
    async assignLoadToCarrier(airtableRecordId, clerkUserId) {
        this.logger.log(`SERVICE: assignLoadToCarrier called for load ${airtableRecordId} to user ${clerkUserId}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId: clerkUserId },
            include: { carrierProfile: true },
        });
        if (!user || (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN)) {
            throw new common_1.ForbiddenException('User not found or not a valid carrier or admin');
        }
        if (!user.carrierProfile) {
            throw new common_1.ForbiddenException('Carrier profile not found. Cannot assign load without a carrier profile.');
        }
        const load = await this.prisma.load.findUnique({
            where: { airtableRecordId },
        });
        if (!load) {
            throw new common_1.NotFoundException('Load not found');
        }
        if (load.status !== db_1.LoadStatus.ASSIGNED) {
            throw new common_1.BadRequestException(`Load status must be "Assigned" to assign to carrier. Current status: ${load.status}`);
        }
        try {
            const updatedLoad = await this.prisma.load.update({
                where: { airtableRecordId },
                data: {
                    awardedToCarrierProfileId: user.carrierProfile.id,
                    updatedAt: new Date(),
                },
            });
            this.logger.log(`SERVICE: Successfully assigned load ${airtableRecordId} to carrier ${user.carrierProfile.companyName}`);
            return {
                success: true,
                message: `Load successfully assigned to ${user.carrierProfile.companyName}`,
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Failed to assign load ${airtableRecordId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to assign load to carrier');
        }
    }
    async unassignLoadFromCarrier(airtableRecordId, adminClerkUserId) {
        this.logger.log(`SERVICE: unassignLoadFromCarrier called for load ${airtableRecordId} by admin ${adminClerkUserId}`);
        const adminUser = await this.prisma.user.findUnique({
            where: { airtableUserId: adminClerkUserId },
        });
        if (!adminUser || adminUser.role !== db_1.Role.ADMIN) {
            throw new common_1.ForbiddenException('Only admins can unassign loads');
        }
        const load = await this.prisma.load.findUnique({
            where: { airtableRecordId },
            include: {
                awardedToCarrierProfile: {
                    select: {
                        companyName: true,
                    },
                },
            },
        });
        if (!load) {
            throw new common_1.NotFoundException('Load not found');
        }
        if (!load.awardedToCarrierProfileId) {
            throw new common_1.BadRequestException('Load is not currently assigned to any carrier');
        }
        const previousCarrier = load.awardedToCarrierProfile?.companyName;
        try {
            const updatedLoad = await this.prisma.load.update({
                where: { airtableRecordId },
                data: {
                    awardedToCarrierProfileId: null,
                    updatedAt: new Date(),
                },
            });
            try {
                await this.base(this.tableName).update([
                    {
                        id: airtableRecordId,
                        fields: {
                            'Status': 'Available',
                            'Booking Request - User ID': '',
                            'Booking Request - Timestamp': '',
                            'Booking Request - Carrier MC Number': '',
                            'Booking Request - Carrier Contact Name': '',
                            'Booking Request - Carrier Contact Email': '',
                            'Booking Request - Carrier Contact Phone': '',
                            'Booking Request - Is Truck Empty': '',
                            'Booking Request - Truck Empty Location': '',
                            'Booking Request - ETA to Shipper': '',
                            'Booking Request - Notes': '',
                            'Booking Request - Rate Accepted': '',
                        },
                    },
                ], { typecast: true });
                this.logger.log(`SERVICE: Successfully updated Airtable status to Available for load ${airtableRecordId}`);
            }
            catch (airtableError) {
                this.logger.warn(`SERVICE: Failed to update Airtable status: ${airtableError.message}`);
            }
            this.logger.log(`SERVICE: Successfully unassigned load ${airtableRecordId} from carrier ${previousCarrier}`);
            return {
                success: true,
                message: `Load successfully unassigned from ${previousCarrier} and made available again`,
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Failed to unassign load ${airtableRecordId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to unassign load from carrier');
        }
    }
    async carrierCancelLoad(airtableRecordId, airtableUserId, reason) {
        this.logger.log(`SERVICE: carrierCancelLoad called for load ${airtableRecordId} by user ${airtableUserId}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId },
            include: { carrierProfile: true },
        });
        if (!user || (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN)) {
            throw new common_1.ForbiddenException('Only carriers and admins can cancel loads');
        }
        const existingLoad = await this.prisma.load.findUnique({
            where: { airtableRecordId },
            include: { awardedToCarrierProfile: true },
        });
        if (!existingLoad) {
            throw new common_1.NotFoundException(`Load with Airtable ID ${airtableRecordId} not found`);
        }
        if (user.role === db_1.Role.ADMIN) {
            this.logger.log(`SERVICE: Admin user ${airtableUserId} canceling load ${airtableRecordId}`);
        }
        else {
            if (!user.carrierProfile) {
                throw new common_1.ForbiddenException('Carrier profile not found');
            }
            if (existingLoad.awardedToCarrierProfileId !== user.carrierProfile.id) {
                throw new common_1.ForbiddenException('You can only cancel loads assigned to you');
            }
        }
        try {
            const updatedLoad = await this.prisma.load.update({
                where: { airtableRecordId },
                data: {
                    awardedToCarrierProfileId: null,
                    updatedAt: new Date(),
                },
            });
            try {
                await this.base(this.tableName).update([
                    {
                        id: airtableRecordId,
                        fields: {
                            'Status': 'Available',
                            'Booking Request - User ID': '',
                            'Booking Request - Timestamp': '',
                            'Booking Request - Carrier MC Number': '',
                            'Booking Request - Carrier Contact Name': '',
                            'Booking Request - Carrier Contact Email': '',
                            'Booking Request - Carrier Contact Phone': '',
                            'Booking Request - Is Truck Empty': '',
                            'Booking Request - Truck Empty Location': '',
                            'Booking Request - ETA to Shipper': '',
                            'Booking Request - Notes': reason ? `CANCELLED BY CARRIER: ${reason}` : 'CANCELLED BY CARRIER',
                            'Booking Request - Rate Accepted': '',
                        },
                    },
                ], { typecast: true });
                this.logger.log(`SERVICE: Successfully updated Airtable status to Available for cancelled load ${airtableRecordId}`);
            }
            catch (airtableError) {
                this.logger.warn(`SERVICE: Failed to update Airtable status: ${airtableError.message}`);
            }
            this.logger.log(`SERVICE: ${user.role === db_1.Role.ADMIN ? 'Admin' : 'Carrier'} ${user.carrierProfile?.companyName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || airtableUserId} successfully cancelled load ${airtableRecordId}`);
            return {
                success: true,
                message: 'Load successfully cancelled and made available for other carriers',
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Error cancelling load: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to cancel load: ${error.message}`);
        }
    }
    async createBid(loadAirtableRecordId, airtableUserId, bidAmount, carrierNotes) {
        this.logger.log(`SERVICE: createBid called for load ${loadAirtableRecordId} by user ${airtableUserId} with amount $${bidAmount}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId },
            include: { carrierProfile: true },
        });
        if (!user || (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN)) {
            throw new common_1.ForbiddenException('Only carriers and admins can submit bids');
        }
        if (user.role === db_1.Role.ADMIN) {
            this.logger.log(`SERVICE: Admin user ${airtableUserId} creating bid for load ${loadAirtableRecordId}`);
            if (!user.carrierProfile) {
                throw new common_1.ForbiddenException('Admin users must have a carrier profile to submit bids');
            }
        }
        else {
            if (!user.carrierProfile) {
                throw new common_1.ForbiddenException('Carrier profile not found');
            }
        }
        let load = await this.prisma.load.findUnique({
            where: { airtableRecordId: loadAirtableRecordId },
        });
        if (!load) {
            this.logger.warn(`SERVICE: Load ${loadAirtableRecordId} not found in database, attempting to sync from Airtable...`);
            try {
                const airtableRecord = await this.getAirtableRecordById(loadAirtableRecordId);
                const syncedToApi = airtableRecord.get('Synced to API');
                const status = airtableRecord.get('Status');
                if (!syncedToApi) {
                    throw new common_1.NotFoundException(`Load ${loadAirtableRecordId} exists in Airtable but "Synced to API" is not checked. Please check the "Synced to API" checkbox in Airtable.`);
                }
                if (status !== 'Available' && status !== 'Booking Requested') {
                    throw new common_1.NotFoundException(`Load ${loadAirtableRecordId} exists but status is "${status}". Only loads with status "Available" or "Booking Requested" can receive bids.`);
                }
                this.logger.log(`SERVICE: Syncing load ${loadAirtableRecordId} from Airtable to database...`);
                await this.processSingleRecord(airtableRecord);
                load = await this.prisma.load.findUnique({
                    where: { airtableRecordId: loadAirtableRecordId },
                });
                if (!load) {
                    throw new Error('Failed to sync load to database');
                }
                this.logger.log(`SERVICE: Successfully synced load ${loadAirtableRecordId} to database for bidding`);
            }
            catch (syncError) {
                this.logger.error(`SERVICE: Failed to sync load ${loadAirtableRecordId}: ${syncError.message}`);
                if (syncError instanceof common_1.NotFoundException) {
                    throw syncError;
                }
                throw new common_1.NotFoundException(`Load ${loadAirtableRecordId} not found or could not be synced. ` +
                    `Error: ${syncError.message}. Please try running a manual sync or contact support.`);
            }
        }
        if (load.status !== db_1.LoadStatus.AVAILABLE) {
            throw new common_1.BadRequestException(`Load is not available for bidding. Current status: ${load.status}`);
        }
        try {
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 24);
            const bid = await this.prisma.bid.upsert({
                where: {
                    load_carrier_unique_bid: {
                        loadId: load.id,
                        carrierProfileId: user.carrierProfile.id,
                    },
                },
                update: {
                    bidAmount,
                    carrierNotes,
                    status: 'ACTIVE',
                    updatedAt: new Date(),
                    expires_at: expiresAt,
                    negotiation_status: 'OPEN',
                    admin_response: 'PENDING'
                },
                create: {
                    loadId: load.id,
                    carrierProfileId: user.carrierProfile.id,
                    bidAmount,
                    carrierNotes,
                    status: 'ACTIVE',
                    expires_at: expiresAt,
                    negotiation_status: 'OPEN',
                    admin_response: 'PENDING'
                },
                include: {
                    carrierProfile: true,
                    load: true,
                },
            });
            try {
                await this.updateAirtableBids(loadAirtableRecordId);
                this.logger.log(`SERVICE: Successfully synced bid to Airtable for load ${loadAirtableRecordId}`);
            }
            catch (airtableError) {
                this.logger.warn(`SERVICE: Airtable sync failed for bid on load ${loadAirtableRecordId}: ${airtableError.message}`);
                this.logger.warn(`SERVICE: Bid was saved successfully to database, but Airtable sync failed - this is non-critical`);
                this.logger.warn(`SERVICE: Airtable sync error details:`, airtableError.stack);
            }
            this.logger.log(`SERVICE: Successfully created/updated bid for load ${loadAirtableRecordId} by carrier ${user.carrierProfile.companyName}`);
            try {
                const bidNotificationData = {
                    bidId: bid.id,
                    loadId: load.id,
                    loadAirtableRecordId: loadAirtableRecordId,
                    carrierUserId: airtableUserId,
                    carrierCompanyName: user.carrierProfile.companyName || undefined,
                    bidAmount,
                    status: 'pending',
                    carrierNotes,
                };
                await this.notificationsService.broadcastBidUpdate(bidNotificationData);
            }
            catch (notificationError) {
                this.logger.error(`Failed to send bid notification: ${notificationError.message}`, notificationError.stack);
            }
            return {
                success: true,
                bid: {
                    id: bid.id,
                    bidAmount: bid.bidAmount,
                    carrierNotes: bid.carrierNotes,
                    status: bid.status,
                    createdAt: bid.createdAt,
                    updatedAt: bid.updatedAt,
                    carrierName: bid.carrierProfile.companyName,
                },
                message: 'Bid submitted successfully',
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Error creating bid: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to submit bid: ${error.message}`);
        }
    }
    async getCarrierBids(airtableUserId) {
        this.logger.log(`SERVICE: getCarrierBids called for user ${airtableUserId}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId },
            include: { carrierProfile: true },
        });
        if (!user || (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN)) {
            throw new common_1.ForbiddenException('Only carriers and admins can view bids');
        }
        try {
            if (user.role === db_1.Role.ADMIN) {
                this.logger.log(`SERVICE: Admin user ${airtableUserId} requesting all bids`);
                const allBids = await this.prisma.bid.findMany({
                    where: {
                        status: 'ACTIVE',
                    },
                    include: {
                        load: {
                            include: {
                                awardedToCarrierProfile: true,
                                bids: {
                                    include: {
                                        carrierProfile: true,
                                    },
                                    where: {
                                        status: 'ACTIVE',
                                    },
                                    orderBy: {
                                        bidAmount: 'desc',
                                    },
                                },
                            },
                        },
                        carrierProfile: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                });
                const transformedAdminBids = await Promise.allSettled(allBids.map(async (bid) => {
                    let airtableData = null;
                    try {
                        const airtableRecord = await Promise.race([
                            this.getAirtableRecordById(bid.load.airtableRecordId),
                            new Promise((_, reject) => setTimeout(() => reject(new Error('Airtable timeout')), 3000))
                        ]);
                        airtableData = airtableRecord.fields;
                    }
                    catch (error) {
                        this.logger.warn(`Could not fetch Airtable data for load ${bid.load.airtableRecordId}: ${error.message}`);
                    }
                    const allLoadBids = bid.load.bids;
                    const isHighestBid = allLoadBids.length > 0 && allLoadBids[0].id === bid.id;
                    const isOutbid = !isHighestBid && allLoadBids.length > 1;
                    const loadIsAssigned = !!bid.load.awardedToCarrierProfileId;
                    const wonLoad = loadIsAssigned && bid.load.awardedToCarrierProfileId === bid.carrierProfileId;
                    let bidStatus = 'ACTIVE';
                    if (wonLoad) {
                        bidStatus = 'WON';
                    }
                    else if (loadIsAssigned) {
                        bidStatus = 'LOST';
                    }
                    else if (isOutbid) {
                        bidStatus = 'OUTBID';
                    }
                    else if (isHighestBid) {
                        bidStatus = 'LEADING';
                    }
                    return {
                        id: bid.id,
                        bidAmount: bid.bidAmount,
                        carrierNotes: bid.carrierNotes,
                        status: bidStatus,
                        createdAt: bid.createdAt,
                        updatedAt: bid.updatedAt,
                        carrier: {
                            id: bid.carrierProfile.id,
                            companyName: bid.carrierProfile.companyName,
                            mcNumber: bid.carrierProfile.mcNumber,
                            contactName: bid.carrierProfile.contact_name,
                            contactEmail: bid.carrierProfile.contact_email,
                            contactPhone: bid.carrierProfile.contact_phone,
                        },
                        carrierName: bid.carrierProfile.companyName,
                        load: {
                            id: bid.load.id,
                            airtableRecordId: bid.load.airtableRecordId,
                            proNumber: airtableData?.['Order ID.'] || bid.load.proNumber,
                            status: airtableData?.['Status'] || bid.load.status,
                            rate: airtableData?.['Rate to Carrier'] || bid.load.rate,
                            origin: airtableData ?
                                `${airtableData['Pickup City Lookup']?.[0] || ''}, ${airtableData['Pickup State Lookup']?.[0] || ''}` :
                                `${bid.load.originCity}, ${bid.load.originState}`,
                            destination: airtableData ?
                                `${airtableData['Delivery City Lookup']?.[0] || ''}, ${airtableData['Delivery State Lookup']?.[0] || ''}` :
                                `${bid.load.destinationCity}, ${bid.load.destinationState}`,
                            pickupDateTime: airtableData?.['Pickup Date & Time'] || bid.load.pickupDateUtc?.toISOString(),
                            deliveryDateTime: airtableData?.['Delivery Date & Time'] || bid.load.deliveryDateUtc?.toISOString(),
                            weight: airtableData?.['Weight (lbs)'] || bid.load.weightLbs,
                            equipment: airtableData?.['Equipment Required'] || bid.load.equipmentRequired,
                        },
                        competingBids: {
                            total: allLoadBids.length,
                            highest: allLoadBids.length > 0 ? allLoadBids[0].bidAmount : null,
                            myRank: allLoadBids.findIndex(b => b.id === bid.id) + 1,
                        },
                    };
                }));
                const successfulAdminBids = transformedAdminBids
                    .filter((result) => result.status === 'fulfilled')
                    .map((result) => result.value);
                const adminFailures = transformedAdminBids.filter((result) => result.status === 'rejected');
                if (adminFailures.length > 0) {
                    this.logger.warn(`SERVICE: ${adminFailures.length} admin bids failed to fetch Airtable data`);
                }
                this.logger.log(`SERVICE: Found ${successfulAdminBids.length} total bids from all carriers for admin user ${airtableUserId}`);
                return successfulAdminBids;
            }
            if (!user.carrierProfile) {
                throw new common_1.ForbiddenException('Carrier profile not found');
            }
            const bids = await this.prisma.bid.findMany({
                where: {
                    carrierProfileId: user.carrierProfile.id,
                    status: 'ACTIVE',
                },
                include: {
                    load: {
                        include: {
                            awardedToCarrierProfile: true,
                            bids: {
                                include: {
                                    carrierProfile: true,
                                },
                                orderBy: {
                                    bidAmount: 'desc',
                                },
                            },
                        },
                    },
                    carrierProfile: true,
                },
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const transformedBids = await Promise.allSettled(bids.map(async (bid) => {
                let airtableData = null;
                try {
                    const airtableRecord = await Promise.race([
                        this.getAirtableRecordById(bid.load.airtableRecordId),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Airtable timeout')), 3000))
                    ]);
                    airtableData = airtableRecord.fields;
                }
                catch (error) {
                    this.logger.warn(`Could not fetch Airtable data for load ${bid.load.airtableRecordId}: ${error.message}`);
                }
                const allBids = bid.load.bids;
                const isHighestBid = allBids.length > 0 && allBids[0].id === bid.id;
                const isOutbid = !isHighestBid && allBids.length > 1;
                const loadIsAssigned = !!bid.load.awardedToCarrierProfileId;
                const wonLoad = loadIsAssigned && user.carrierProfile && bid.load.awardedToCarrierProfileId === user.carrierProfile.id;
                let bidStatus = 'ACTIVE';
                if (wonLoad) {
                    bidStatus = 'WON';
                }
                else if (loadIsAssigned) {
                    bidStatus = 'LOST';
                }
                else if (isOutbid) {
                    bidStatus = 'OUTBID';
                }
                else if (isHighestBid) {
                    bidStatus = 'LEADING';
                }
                return {
                    id: bid.id,
                    bidAmount: bid.bidAmount,
                    carrierNotes: bid.carrierNotes,
                    status: bidStatus,
                    createdAt: bid.createdAt,
                    updatedAt: bid.updatedAt,
                    load: {
                        id: bid.load.id,
                        airtableRecordId: bid.load.airtableRecordId,
                        proNumber: airtableData?.['Order ID.'] || bid.load.proNumber,
                        status: airtableData?.['Status'] || bid.load.status,
                        rate: airtableData?.['Rate to Carrier'] || bid.load.rate,
                        origin: airtableData ?
                            `${airtableData['Pickup City Lookup']?.[0] || ''}, ${airtableData['Pickup State Lookup']?.[0] || ''}` :
                            `${bid.load.originCity}, ${bid.load.originState}`,
                        destination: airtableData ?
                            `${airtableData['Delivery City Lookup']?.[0] || ''}, ${airtableData['Delivery State Lookup']?.[0] || ''}` :
                            `${bid.load.destinationCity}, ${bid.load.destinationState}`,
                        pickupDateTime: airtableData?.['Pickup Date & Time'] || bid.load.pickupDateUtc?.toISOString(),
                        deliveryDateTime: airtableData?.['Delivery Date & Time'] || bid.load.deliveryDateUtc?.toISOString(),
                        weight: airtableData?.['Weight (lbs)'] || bid.load.weightLbs,
                        equipment: airtableData?.['Equipment Required'] || bid.load.equipmentRequired,
                    },
                    competingBids: {
                        total: allBids.length,
                        highest: allBids.length > 0 ? allBids[0].bidAmount : null,
                        myRank: allBids.findIndex(b => b.id === bid.id) + 1,
                    },
                };
            }));
            const successfulBids = transformedBids
                .filter((result) => result.status === 'fulfilled')
                .map((result) => result.value);
            const bidFailures = transformedBids.filter((result) => result.status === 'rejected');
            if (bidFailures.length > 0) {
                this.logger.warn(`SERVICE: ${bidFailures.length} bids failed to fetch Airtable data`);
            }
            this.logger.log(`SERVICE: Found ${successfulBids.length} bids for carrier ${user.carrierProfile?.companyName || 'Unknown'}`);
            return successfulBids;
        }
        catch (error) {
            this.logger.error(`SERVICE: Error fetching carrier bids: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to fetch bids: ${error.message}`);
        }
    }
    async ensureBidsFieldExists(loadAirtableRecordId) {
        this.logger.log(`SERVICE: ensureBidsFieldExists called for load ${loadAirtableRecordId}`);
        try {
            const currentRecord = await this.getAirtableRecordById(loadAirtableRecordId);
            const availableFields = Object.keys(currentRecord.fields);
            this.logger.log(`SERVICE: Total available fields count: ${availableFields.length}`);
            this.logger.log(`SERVICE: Complete Airtable fields list: ${JSON.stringify(availableFields, null, 2)}`);
            const possibleBidFieldNames = ['Bids', 'Carrier Bids', 'Bid', 'bids', 'carrier_bids', 'CarrierBids', 'BIDS'];
            this.logger.log(`SERVICE: Searching for bid field names: ${JSON.stringify(possibleBidFieldNames)}`);
            for (const fieldName of possibleBidFieldNames) {
                this.logger.debug(`SERVICE: Checking for exact match: "${fieldName}"`);
                if (availableFields.includes(fieldName)) {
                    this.logger.log(`SERVICE: ✅ FOUND exact match for bid field: "${fieldName}"`);
                    return fieldName;
                }
            }
            this.logger.log(`SERVICE: No exact match found, trying case-insensitive search...`);
            for (const fieldName of possibleBidFieldNames) {
                const matchedField = availableFields.find(field => field.toLowerCase() === fieldName.toLowerCase());
                if (matchedField) {
                    this.logger.log(`SERVICE: ✅ FOUND case-insensitive match: "${matchedField}" (searched for "${fieldName}")`);
                    return matchedField;
                }
            }
            this.logger.log(`SERVICE: No case-insensitive match found, trying partial matching...`);
            const bidRelatedFields = availableFields.filter(field => field.toLowerCase().includes('bid'));
            if (bidRelatedFields.length > 0) {
                this.logger.log(`SERVICE: ✅ FOUND bid-related fields: ${JSON.stringify(bidRelatedFields)}`);
                this.logger.log(`SERVICE: Using first bid-related field: "${bidRelatedFields[0]}"`);
                return bidRelatedFields[0];
            }
            this.logger.error(`SERVICE: ❌ NO BID FIELD FOUND in Airtable for load ${loadAirtableRecordId}`);
            this.logger.error(`SERVICE: Available fields (${availableFields.length} total):`);
            availableFields.forEach((field, index) => {
                this.logger.error(`SERVICE:   ${index + 1}. "${field}"`);
            });
            this.logger.error(`SERVICE: Searched for field names: ${possibleBidFieldNames.join(', ')}`);
            this.logger.error(`SERVICE: RECOMMENDATION: Create a field named 'Bids' (Multiple Select type) in your Airtable base`);
            this.logger.error(`SERVICE: Field settings: Type = Multiple Select, Allow users to add new options = ON`);
            return 'Bids';
        }
        catch (error) {
            this.logger.error(`SERVICE: Error checking Airtable fields: ${error.message}`, error.stack);
            return 'Bids';
        }
    }
    async updateAirtableBids(loadAirtableRecordId) {
        this.logger.log(`SERVICE: updateAirtableBids called for load ${loadAirtableRecordId}`);
        try {
            const load = await this.prisma.load.findUnique({
                where: { airtableRecordId: loadAirtableRecordId },
                include: {
                    bids: {
                        include: {
                            carrierProfile: true,
                        },
                        where: {
                            status: 'ACTIVE',
                        },
                        orderBy: {
                            bidAmount: 'desc',
                        },
                    },
                },
            });
            if (!load) {
                throw new common_1.NotFoundException(`Load with Airtable ID ${loadAirtableRecordId} not found`);
            }
            this.logger.log(`SERVICE: Found ${load.bids.length} active bids for load ${loadAirtableRecordId}`);
            if (load.bids.length === 0) {
                this.logger.log(`SERVICE: No active bids found, clearing Airtable bid field`);
            }
            const bidOptions = load.bids.map(bid => `${bid.carrierProfile.companyName} - $${bid.bidAmount.toLocaleString()}`);
            this.logger.log(`SERVICE: Formatted bid options for Airtable: ${JSON.stringify(bidOptions)}`);
            const bidFieldName = await this.ensureBidsFieldExists(loadAirtableRecordId);
            const updateFields = {};
            updateFields[bidFieldName] = bidOptions;
            this.logger.log(`SERVICE: Updating Airtable field '${bidFieldName}' with ${bidOptions.length} bid options`);
            this.logger.log(`SERVICE: Bid options array: ${JSON.stringify(bidOptions)}`);
            this.logger.log(`SERVICE: Attempting Airtable update for record ${loadAirtableRecordId}`);
            this.logger.log(`SERVICE: Update payload: ${JSON.stringify({
                id: loadAirtableRecordId,
                fields: updateFields
            }, null, 2)}`);
            const updateResult = await this.base(this.tableName).update([
                {
                    id: loadAirtableRecordId,
                    fields: updateFields,
                },
            ], { typecast: true });
            this.logger.log(`SERVICE: ✅ Airtable update successful for load ${loadAirtableRecordId}`);
            if (updateResult && updateResult.length > 0) {
                this.logger.log(`SERVICE: Updated record ID: ${updateResult[0].id}`);
                const updatedRecord = updateResult[0];
                if (updatedRecord.fields[bidFieldName]) {
                    this.logger.log(`SERVICE: ✅ Updated ${bidFieldName} field value: ${JSON.stringify(updatedRecord.fields[bidFieldName])}`);
                }
                else {
                    this.logger.warn(`SERVICE: ⚠️ Field ${bidFieldName} was updated but value not returned in response`);
                }
            }
            else {
                this.logger.warn(`SERVICE: ⚠️ Airtable update returned unexpected result: ${JSON.stringify(updateResult)}`);
            }
        }
        catch (error) {
            this.logger.error(`SERVICE: ❌ ERROR updating Airtable bids for load ${loadAirtableRecordId}: ${error.message}`, error.stack);
            this.logger.error(`SERVICE: Full error object: ${JSON.stringify({
                message: error.message,
                statusCode: error.statusCode,
                error: error.error,
                type: error.type,
                stack: error.stack?.substring(0, 500)
            }, null, 2)}`);
            let errorReportingFieldName = 'Bids';
            try {
                errorReportingFieldName = await this.ensureBidsFieldExists(loadAirtableRecordId);
            }
            catch (fieldError) {
                this.logger.warn(`SERVICE: Could not determine field name for error reporting: ${fieldError.message}`);
            }
            if (error.statusCode) {
                this.logger.error(`SERVICE: Airtable HTTP status code: ${error.statusCode}`);
            }
            const errorMessage = error.message?.toLowerCase() || '';
            if (errorMessage.includes('invalid_multiple_choice_options') || error.type === 'INVALID_MULTIPLE_CHOICE_OPTIONS') {
                this.logger.error(`SERVICE: ❌ MULTIPLE SELECT FIELD PERMISSION ERROR`);
                this.logger.error(`SERVICE: The 'Bids' field exists but doesn't allow new options`);
                this.logger.error(`SERVICE: SOLUTION: Go to Airtable > ${errorReportingFieldName} field > Field Options > Allow users to add new options = ON`);
                this.logger.error(`SERVICE: NOTE: API now includes 'typecast: true' parameter to auto-create new options`);
            }
            if (errorMessage.includes('unknown_field_name') || errorMessage.includes('does not exist') || error.type === 'UNKNOWN_FIELD_NAME') {
                this.logger.error(`SERVICE: ❌ FIELD NOT FOUND ERROR`);
                this.logger.error(`SERVICE: Field '${errorReportingFieldName}' not found in Airtable base`);
                this.logger.error(`SERVICE: SOLUTION: Create a 'Bids' field (Multiple Select type) in your Airtable base`);
                this.logger.error(`SERVICE: DEBUG: Use endpoint POST /api/v1/airtable-orders/{loadId}/debug-bids to see available fields`);
            }
            if (errorMessage.includes('unauthorized') || errorMessage.includes('forbidden') || error.statusCode === 401 || error.statusCode === 403) {
                this.logger.error(`SERVICE: ❌ AIRTABLE API PERMISSION ERROR`);
                this.logger.error(`SERVICE: API key may not have write permissions to this base/table`);
                this.logger.error(`SERVICE: SOLUTION: Check Airtable API key permissions and base access`);
            }
            if (errorMessage.includes('not_found') || error.statusCode === 404) {
                this.logger.error(`SERVICE: ❌ AIRTABLE RECORD/BASE NOT FOUND ERROR`);
                this.logger.error(`SERVICE: Record ${loadAirtableRecordId} or base may not exist`);
                this.logger.error(`SERVICE: SOLUTION: Check Airtable base ID and record ID are correct`);
            }
            const detailedErrorMessage = `Failed to sync bid to Airtable: ${error.message}. ` +
                `Field: '${errorReportingFieldName}'. ` +
                `Status: ${error.statusCode || 'Unknown'}. ` +
                `Type: ${error.type || 'Unknown'}. ` +
                `Recommendation: Check Airtable 'Bids' Multiple Select field permissions and API access.`;
            throw new common_1.InternalServerErrorException(detailedErrorMessage);
        }
    }
    async withdrawBid(bidId, clerkUserId) {
        this.logger.log(`SERVICE: withdrawBid called for bid ${bidId} by user ${clerkUserId}`);
        const user = await this.prisma.user.findUnique({
            where: { airtableUserId: clerkUserId },
            include: { carrierProfile: true },
        });
        if (!user || (user.role !== db_1.Role.CARRIER && user.role !== db_1.Role.ADMIN)) {
            throw new common_1.ForbiddenException('Only carriers and admins can withdraw bids');
        }
        if (!user.carrierProfile) {
            throw new common_1.ForbiddenException('Carrier profile not found');
        }
        try {
            const bid = await this.prisma.bid.findUnique({
                where: { id: bidId },
                include: {
                    load: true,
                    carrierProfile: true,
                },
            });
            if (!bid) {
                throw new common_1.NotFoundException(`Bid with ID ${bidId} not found`);
            }
            if (user.role !== db_1.Role.ADMIN && bid.carrierProfileId !== user.carrierProfile.id) {
                throw new common_1.ForbiddenException('You can only withdraw your own bids');
            }
            if (user.role === db_1.Role.ADMIN && bid.carrierProfileId !== user.carrierProfile?.id) {
                this.logger.log(`SERVICE: Admin ${(`${user.firstName || ''} ${user.lastName || ''}`.trim() || clerkUserId)} withdrawing bid ${bidId} on behalf of carrier ${bid.carrierProfile.companyName}`);
            }
            if (bid.load.awardedToCarrierProfileId) {
                throw new common_1.BadRequestException('Cannot withdraw bid - load has already been awarded');
            }
            await this.prisma.bid.update({
                where: { id: bidId },
                data: {
                    status: 'WITHDRAWN',
                    updatedAt: new Date(),
                },
            });
            try {
                await this.updateAirtableBids(bid.load.airtableRecordId);
                this.logger.log(`SERVICE: Successfully synced bid withdrawal to Airtable for load ${bid.load.airtableRecordId}`);
            }
            catch (airtableError) {
                this.logger.warn(`SERVICE: Airtable sync failed for bid withdrawal on load ${bid.load.airtableRecordId}: ${airtableError.message}`);
                this.logger.warn(`SERVICE: Bid was withdrawn successfully in database, but Airtable sync failed - this is non-critical`);
                this.logger.warn(`SERVICE: Airtable sync error details:`, airtableError.stack);
            }
            this.logger.log(`SERVICE: Successfully withdrew bid ${bidId} for carrier ${user.carrierProfile?.companyName || 'Unknown'}`);
            return {
                success: true,
                message: 'Bid withdrawn successfully',
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Error withdrawing bid: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to withdraw bid: ${error.message}`);
        }
    }
    async debugAirtableBidUpdate(loadAirtableRecordId) {
        try {
            const testResults = [];
            this.logger.log(`SERVICE: Checking if Airtable record ${loadAirtableRecordId} exists...`);
            let airtableRecord;
            try {
                airtableRecord = await this.base(this.tableName).find(loadAirtableRecordId);
                testResults.push({
                    fieldName: 'Record Exists in Airtable',
                    success: true,
                    message: `Record ${loadAirtableRecordId} found in Airtable`
                });
            }
            catch (error) {
                testResults.push({
                    fieldName: 'Record Exists in Airtable',
                    success: false,
                    error: error.message,
                    message: `Record ${loadAirtableRecordId} NOT found in Airtable`
                });
                return { testResults, summary: 'Record does not exist in Airtable' };
            }
            const syncedToApi = airtableRecord.get('Synced to API');
            testResults.push({
                fieldName: 'Synced to API',
                success: !!syncedToApi,
                message: `Synced to API = ${syncedToApi} (needs to be true for bidding)`
            });
            const dbRecord = await this.prisma.load.findUnique({
                where: { airtableRecordId: loadAirtableRecordId }
            });
            testResults.push({
                fieldName: 'Record in Database',
                success: !!dbRecord,
                message: dbRecord ? `Record found in database with ID ${dbRecord.id}` : 'Record NOT found in database'
            });
            const status = airtableRecord.get('Status');
            testResults.push({
                fieldName: 'Status',
                success: status === 'Available',
                message: `Status = "${status}" (needs to be "Available" for bidding)`
            });
            const isEligibleForSync = syncedToApi && (status === 'Available' || status === 'Booking Requested');
            testResults.push({
                fieldName: 'Eligible for Sync',
                success: isEligibleForSync,
                message: `Eligible for sync = ${isEligibleForSync} (needs "Synced to API" = true AND status = "Available" or "Booking Requested")`
            });
            const allPassed = testResults.every(test => test.success);
            const summary = allPassed ?
                'All checks passed - record should be available for bidding' :
                'Some checks failed - record may not be properly synced';
            const recommendedActions = [];
            if (!syncedToApi) {
                recommendedActions.push('✅ Check the "Synced to API" checkbox in Airtable');
            }
            if (!dbRecord && syncedToApi) {
                recommendedActions.push('🔄 Run manual sync to sync this record to database');
            }
            if (status !== 'Available') {
                recommendedActions.push(`📝 Change status from "${status}" to "Available" in Airtable`);
            }
            return {
                loadAirtableRecordId,
                testResults,
                summary,
                recommendedActions,
                rawAirtableData: {
                    'Synced to API': syncedToApi,
                    'Status': status,
                    'Order ID.': airtableRecord.get('Order ID.'),
                    'Rate to Carrier': airtableRecord.get('Rate to Carrier')
                }
            };
        }
        catch (error) {
            this.logger.error(`SERVICE: Error debugging Airtable record ${loadAirtableRecordId}: ${error.message}`);
            return {
                error: error.message,
                loadAirtableRecordId,
                summary: 'Error occurred during diagnostics'
            };
        }
    }
    async diagnoseLoadForBidding(loadAirtableRecordId) {
        this.logger.log(`SERVICE: Diagnosing load ${loadAirtableRecordId} for bidding eligibility...`);
        try {
            const diagnostics = await this.debugAirtableBidUpdate(loadAirtableRecordId);
            const biddingDiagnostics = {
                ...diagnostics,
                biddingRequirements: {
                    'Record exists in Airtable': true,
                    'Synced to API checkbox checked': true,
                    'Status is "Available"': true,
                    'Record synced to database': true
                },
                nextSteps: [
                    '1. Verify "Synced to API" is checked in Airtable',
                    '2. Verify Status is "Available"',
                    '3. Run manual sync if record exists but not in database',
                    '4. Wait a few minutes for automatic sync, then try again'
                ]
            };
            return biddingDiagnostics;
        }
        catch (error) {
            this.logger.error(`SERVICE: Error diagnosing load for bidding: ${error.message}`);
            throw error;
        }
    }
    async getSavedSearches(userAirtableId) {
        this.logger.log(`SERVICE: getSavedSearches called for user ${userAirtableId}`);
        try {
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: userAirtableId },
                include: {
                    savedSearches: {
                        orderBy: [
                            { isDefault: 'desc' },
                            { createdAt: 'desc' }
                        ]
                    }
                }
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
            }
            return user.savedSearches.map(search => ({
                id: search.id,
                name: search.name,
                criteria: search.criteria,
                isDefault: search.isDefault,
                createdAt: search.createdAt,
                updatedAt: search.updatedAt
            }));
        }
        catch (error) {
            this.logger.error(`SERVICE: Error fetching saved searches for user ${userAirtableId}:`, error);
            throw error;
        }
    }
    async saveSearch(userAirtableId, savedSearchDto) {
        this.logger.log(`SERVICE: saveSearch called for user ${userAirtableId} with name "${savedSearchDto.name}"`);
        try {
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: userAirtableId }
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
            }
            if (savedSearchDto.isDefault) {
                await this.prisma.savedSearch.updateMany({
                    where: {
                        userId: user.id,
                        isDefault: true
                    },
                    data: { isDefault: false }
                });
            }
            const savedSearch = await this.prisma.savedSearch.create({
                data: {
                    userId: user.id,
                    name: savedSearchDto.name,
                    criteria: savedSearchDto.criteria || {},
                    isDefault: savedSearchDto.isDefault || false
                }
            });
            this.logger.log(`SERVICE: Saved search "${savedSearchDto.name}" created successfully for user ${userAirtableId}`);
            return savedSearch;
        }
        catch (error) {
            this.logger.error(`SERVICE: Error saving search for user ${userAirtableId}:`, error);
            throw error;
        }
    }
    async updateSavedSearch(userAirtableId, searchId, savedSearchDto) {
        this.logger.log(`SERVICE: updateSavedSearch called for user ${userAirtableId}, search ${searchId}`);
        try {
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: userAirtableId }
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
            }
            const existingSearch = await this.prisma.savedSearch.findFirst({
                where: {
                    id: searchId,
                    userId: user.id
                }
            });
            if (!existingSearch) {
                throw new common_1.NotFoundException(`Saved search ${searchId} not found for user ${userAirtableId}`);
            }
            if (savedSearchDto.isDefault) {
                await this.prisma.savedSearch.updateMany({
                    where: {
                        userId: user.id,
                        isDefault: true,
                        id: { not: searchId }
                    },
                    data: { isDefault: false }
                });
            }
            const updatedSearch = await this.prisma.savedSearch.update({
                where: { id: searchId },
                data: {
                    name: savedSearchDto.name,
                    criteria: savedSearchDto.criteria || {},
                    isDefault: savedSearchDto.isDefault || false
                }
            });
            this.logger.log(`SERVICE: Saved search "${savedSearchDto.name}" updated successfully for user ${userAirtableId}`);
            return updatedSearch;
        }
        catch (error) {
            this.logger.error(`SERVICE: Error updating saved search for user ${userAirtableId}:`, error);
            throw error;
        }
    }
    async deleteSavedSearch(userAirtableId, searchId) {
        this.logger.log(`SERVICE: deleteSavedSearch called for user ${userAirtableId}, search ${searchId}`);
        try {
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: userAirtableId }
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with Airtable ID ${userAirtableId} not found`);
            }
            const result = await this.prisma.savedSearch.deleteMany({
                where: {
                    id: searchId,
                    userId: user.id
                }
            });
            if (result.count === 0) {
                throw new common_1.NotFoundException(`Saved search ${searchId} not found for user ${userAirtableId}`);
            }
            this.logger.log(`SERVICE: Saved search ${searchId} deleted successfully for user ${userAirtableId}`);
        }
        catch (error) {
            this.logger.error(`SERVICE: Error deleting saved search for user ${userAirtableId}:`, error);
            throw error;
        }
    }
    async respondToCounterOffer(bidId, carrierUserId, response, notes) {
        this.logger.log(`SERVICE: Carrier ${carrierUserId} responding to counter-offer on bid ${bidId}: ${response}`);
        try {
            const user = await this.prisma.user.findUnique({
                where: { airtableUserId: carrierUserId },
                include: { carrierProfile: true },
            });
            if (!user || user.role !== db_1.Role.CARRIER) {
                throw new common_1.ForbiddenException('Only carriers can respond to counter-offers');
            }
            if (!user.carrierProfile) {
                throw new common_1.ForbiddenException('Carrier profile not found');
            }
            const existingBid = await this.prisma.bid.findUnique({
                where: { id: bidId },
                include: {
                    load: true,
                    carrierProfile: true
                }
            });
            if (!existingBid) {
                throw new common_1.NotFoundException(`Bid ${bidId} not found`);
            }
            if (existingBid.carrierProfileId !== user.carrierProfile.id) {
                throw new common_1.ForbiddenException('You can only respond to your own bids');
            }
            if (existingBid.admin_response !== db_1.AdminResponse.COUNTERED) {
                throw new common_1.BadRequestException(`Bid is not in countered state. Current status: ${existingBid.admin_response}`);
            }
            if (existingBid.negotiation_status !== db_1.NegotiationStatus.OPEN) {
                throw new common_1.BadRequestException(`Bid negotiation is no longer open. Current status: ${existingBid.negotiation_status}`);
            }
            let newNegotiationStatus;
            let newAdminResponse;
            let finalAmount = null;
            if (response === 'accepted') {
                newNegotiationStatus = db_1.NegotiationStatus.CLOSED;
                newAdminResponse = db_1.AdminResponse.ACCEPTED;
                finalAmount = existingBid.counter_offer_amount;
            }
            else {
                newNegotiationStatus = db_1.NegotiationStatus.CLOSED;
                newAdminResponse = db_1.AdminResponse.DECLINED;
            }
            const result = await this.prisma.$transaction(async (tx) => {
                const updatedBid = await tx.bid.update({
                    where: { id: bidId },
                    data: {
                        negotiation_status: newNegotiationStatus,
                        response_timestamp: new Date(),
                        carrierNotes: notes || existingBid.carrierNotes,
                        ...(response === 'accepted' && finalAmount && { bidAmount: finalAmount })
                    },
                    include: {
                        load: true,
                        carrierProfile: true
                    }
                });
                await tx.bid_responses.create({
                    data: {
                        id: `carrier_response_${bidId}_${Date.now()}`,
                        bid_id: bidId,
                        response_type: db_1.BidResponseType.CARRIER_RESPONSE,
                        amount: finalAmount,
                        notes: notes || null,
                        created_by: carrierUserId
                    }
                });
                if (response === 'accepted') {
                    await tx.load.update({
                        where: { id: existingBid.loadId },
                        data: {
                            awardedToCarrierProfileId: existingBid.carrierProfileId,
                            status: 'ASSIGNED'
                        }
                    });
                    await tx.bid.updateMany({
                        where: {
                            loadId: existingBid.loadId,
                            id: { not: bidId },
                            negotiation_status: db_1.NegotiationStatus.OPEN
                        },
                        data: {
                            negotiation_status: db_1.NegotiationStatus.CLOSED,
                            admin_response: db_1.AdminResponse.DECLINED,
                            response_timestamp: new Date(),
                            admin_notes: 'Load awarded to another carrier'
                        }
                    });
                }
                return updatedBid;
            });
            await this.sendCounterOfferResponseNotifications(result, response, carrierUserId);
            const message = response === 'accepted'
                ? `Counter-offer accepted! You have been assigned to load ${result.load.airtableRecordId} for $${finalAmount}`
                : `Counter-offer declined. The negotiation has been closed.`;
            this.logger.log(`SERVICE: Carrier ${carrierUserId} ${response} counter-offer on bid ${bidId}`);
            return {
                success: true,
                message,
                bid: {
                    id: result.id,
                    loadId: result.loadId,
                    bidAmount: result.bidAmount,
                    negotiationStatus: result.negotiation_status,
                    adminResponse: result.admin_response,
                    finalAmount: finalAmount,
                    assignedLoad: response === 'accepted' ? result.load : null
                }
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`SERVICE: Error responding to counter-offer ${bidId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to respond to counter-offer');
        }
    }
    async sendCounterOfferResponseNotifications(bid, response, carrierUserId) {
        try {
            const adminNotification = {
                type: 'counter_offer_response',
                title: `Counter-offer ${response}`,
                message: `${bid.carrierProfile.companyName} has ${response} your counter-offer for load ${bid.load.airtableRecordId}`,
                data: {
                    bidId: bid.id,
                    loadId: bid.loadId,
                    carrierResponse: response,
                    carrierName: bid.carrierProfile.companyName,
                    finalAmount: bid.bidAmount,
                    loadDetails: bid.load
                }
            };
            await this.notificationsService.broadcastToAdmins(adminNotification);
            if (response === 'accepted') {
                await this.notificationsService.broadcastToUser(carrierUserId, {
                    type: 'load_assigned',
                    title: 'Load Assigned!',
                    message: `Congratulations! You have been assigned to load ${bid.load.airtableRecordId}`,
                    data: {
                        loadId: bid.loadId,
                        bidId: bid.id,
                        finalAmount: bid.bidAmount,
                        loadDetails: bid.load
                    }
                });
            }
        }
        catch (error) {
            this.logger.error(`Failed to send counter-offer response notifications: ${error.message}`, error.stack);
        }
    }
    async getDashboardMetrics(airtableUserId) {
        this.logger.log(`Fetching dashboard metrics for user ${airtableUserId}`);
        try {
            const carrierProfile = await this.prisma.carrierProfile.findFirst({
                where: {
                    user: { airtableUserId }
                },
                include: { user: true }
            });
            if (!carrierProfile) {
                throw new common_1.NotFoundException('Carrier profile not found');
            }
            const assignedLoads = await this.prisma.load.findMany({
                where: {
                    status: 'ASSIGNED',
                    awardedToCarrierProfileId: carrierProfile.id
                }
            });
            const activeBids = await this.prisma.bid.findMany({
                where: {
                    carrierProfileId: carrierProfile.id,
                    admin_response: 'PENDING'
                }
            });
            const completedLoads = await this.prisma.load.findMany({
                where: {
                    status: 'DELIVERED_EMPTY',
                    awardedToCarrierProfileId: carrierProfile.id
                }
            });
            const totalEarnings = completedLoads.reduce((sum, load) => sum + (load.rate || 0), 0);
            const recentLoads = await this.prisma.load.findMany({
                where: {
                    awardedToCarrierProfileId: carrierProfile.id
                },
                orderBy: { createdAt: 'desc' },
                take: 10,
                select: {
                    id: true,
                    airtableRecordId: true,
                    proNumber: true,
                    originCity: true,
                    originState: true,
                    destinationCity: true,
                    destinationState: true,
                    rate: true,
                    status: true,
                    pickupDateUtc: true,
                    deliveryDateUtc: true
                }
            });
            const recentBids = await this.prisma.bid.findMany({
                where: {
                    carrierProfileId: carrierProfile.id
                },
                orderBy: { createdAt: 'desc' },
                take: 10,
                include: {
                    load: {
                        select: {
                            airtableRecordId: true,
                            proNumber: true,
                            originCity: true,
                            originState: true,
                            destinationCity: true,
                            destinationState: true,
                            rate: true
                        }
                    }
                }
            });
            const loadsByStatus = await this.prisma.load.groupBy({
                by: ['status'],
                where: {
                    awardedToCarrierProfileId: carrierProfile.id
                },
                _count: {
                    _all: true
                }
            });
            const statusCounts = {};
            loadsByStatus.forEach(group => {
                if (group._count && group.status) {
                    statusCounts[group.status] = group._count._all;
                }
            });
            const now = new Date();
            const thisWeekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
            const lastWeekStart = new Date(thisWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000);
            const lastWeekEnd = new Date(thisWeekStart.getTime() - 1);
            const thisWeekLoads = await this.prisma.load.count({
                where: {
                    awardedToCarrierProfileId: carrierProfile.id,
                    createdAt: {
                        gte: thisWeekStart
                    }
                }
            });
            const lastWeekLoads = await this.prisma.load.count({
                where: {
                    awardedToCarrierProfileId: carrierProfile.id,
                    createdAt: {
                        gte: lastWeekStart,
                        lte: lastWeekEnd
                    }
                }
            });
            const metrics = {
                totalAssignedLoads: assignedLoads.length,
                totalActiveBids: activeBids.length,
                totalEarnings: totalEarnings,
                recentLoads: recentLoads,
                recentBids: recentBids.map(bid => ({
                    id: bid.id,
                    bidAmount: bid.bidAmount,
                    status: bid.admin_response,
                    createdAt: bid.createdAt,
                    load: bid.load
                })),
                loadsByStatus: statusCounts,
                thisWeekLoads: thisWeekLoads,
                lastWeekLoads: lastWeekLoads
            };
            this.logger.log(`Dashboard metrics fetched successfully for user ${airtableUserId}`);
            return metrics;
        }
        catch (error) {
            this.logger.error(`Error fetching dashboard metrics for user ${airtableUserId}: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.AirtableOrdersService = AirtableOrdersService;
exports.AirtableOrdersService = AirtableOrdersService = AirtableOrdersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService, Object, notifications_service_1.NotificationsService,
        circuit_breaker_service_1.CircuitBreakerService])
], AirtableOrdersService);
//# sourceMappingURL=airtable-orders.service.js.map