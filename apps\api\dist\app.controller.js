"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AppController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const app_service_1 = require("./app.service");
const auth_guard_1 = require("./auth/auth.guard");
const prisma_service_1 = require("./prisma/prisma.service");
const circuit_breaker_service_1 = require("./common/services/circuit-breaker.service");
let AppController = AppController_1 = class AppController {
    appService;
    prismaService;
    circuitBreakerService;
    logger = new common_1.Logger(AppController_1.name);
    constructor(appService, prismaService, circuitBreakerService) {
        this.appService = appService;
        this.prismaService = prismaService;
        this.circuitBreakerService = circuitBreakerService;
    }
    getSimpleTest() {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            message: 'Simple test endpoint working'
        };
    }
    getHello() {
        return this.appService.getHello();
    }
    async getHealth() {
        const startTime = Date.now();
        try {
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                responseTime: `${responseTime}ms`,
                version: process.env.npm_package_version || 'unknown',
                environment: process.env.NODE_ENV || 'unknown',
                message: 'API server is running'
            };
        }
        catch (error) {
            this.logger.error('Health check failed:', error);
            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                responseTime: `${Date.now() - startTime}ms`,
                error: error.message
            };
        }
    }
    async getDetailedHealth() {
        const startTime = Date.now();
        try {
            const dbHealth = await this.prismaService.getDetailedHealthCheck();
            const circuitBreakerHealth = this.circuitBreakerService.getHealthCheck();
            const responseTime = Date.now() - startTime;
            const overallStatus = this.determineOverallStatus([dbHealth.status, circuitBreakerHealth.status]);
            return {
                status: overallStatus,
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                responseTime: `${responseTime}ms`,
                version: process.env.npm_package_version || 'unknown',
                environment: process.env.NODE_ENV || 'unknown',
                services: {
                    database: dbHealth,
                    circuitBreakers: circuitBreakerHealth
                }
            };
        }
        catch (error) {
            this.logger.error('Detailed health check failed:', error);
            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                responseTime: `${Date.now() - startTime}ms`,
                error: error.message
            };
        }
    }
    async getReadiness() {
        try {
            const dbHealth = await this.prismaService.healthCheck();
            if (!dbHealth) {
                return {
                    status: 'not ready',
                    reason: 'Database connection failed'
                };
            }
            return {
                status: 'ready',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            return {
                status: 'not ready',
                reason: error.message
            };
        }
    }
    getLiveness() {
        return {
            status: 'alive',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        };
    }
    async getDatabaseHealth() {
        const dbStatus = await this.prismaService.verifyConnection();
        const isHealthy = await this.prismaService.healthCheck();
        return {
            connected: dbStatus.connected,
            healthy: isHealthy,
            error: dbStatus.error,
            timestamp: new Date().toISOString()
        };
    }
    getDebug() {
        const envVars = [
            'NODE_ENV',
            'DATABASE_URL',
            'N8N_JWT_SECRET',
            'N8N_JWT_ISSUER',
            'N8N_BASE_URL',
            'AIRTABLE_API_KEY',
            'AIRTABLE_BASE_ID',
            'AIRTABLE_TABLE_NAME'
        ];
        const envStatus = {};
        envVars.forEach(varName => {
            const value = process.env[varName];
            if (value) {
                if (varName.includes('SECRET') || varName.includes('KEY') || varName.includes('URL')) {
                    envStatus[varName] = `${value.substring(0, 8)}...${value.substring(value.length - 4)}`;
                }
                else {
                    envStatus[varName] = value;
                }
            }
            else {
                envStatus[varName] = 'NOT_SET';
            }
        });
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV,
            environmentVariables: envStatus,
            allEnvKeys: Object.keys(process.env).filter(key => key.startsWith('N8N_') ||
                key.startsWith('DATABASE_') ||
                key.startsWith('AIRTABLE_') ||
                key.startsWith('VERCEL_') ||
                key === 'NODE_ENV')
        };
    }
    getProfile(req) {
        return req.user;
    }
    async getDebugUser(req) {
        const userId = req.user?.airtableUserId;
        if (!userId) {
            return { error: 'No user ID in auth payload' };
        }
        try {
            const user = await this.appService.findUserByAirtableId(userId);
            return {
                n8nPayload: req.auth,
                userContext: req.user,
                databaseUser: user,
                authSync: {
                    hasUserId: !!user?.airtableUserId,
                    hasEmail: !!user?.email,
                    hasMcNumber: !!user?.mcNumber,
                    hasRole: !!user?.role,
                }
            };
        }
        catch (error) {
            return {
                error: error.message,
                n8nPayload: req.auth,
                userContext: req.user,
                stack: error.stack
            };
        }
    }
    determineOverallStatus(statuses) {
        if (statuses.some(status => status === 'unhealthy')) {
            return 'unhealthy';
        }
        if (statuses.some(status => status === 'degraded')) {
            return 'degraded';
        }
        return 'healthy';
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)('test-simple'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getSimpleTest", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], AppController.prototype, "getHello", null);
__decorate([
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getHealth", null);
__decorate([
    (0, common_1.Get)('health/detailed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getDetailedHealth", null);
__decorate([
    (0, common_1.Get)('health/ready'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getReadiness", null);
__decorate([
    (0, common_1.Get)('health/live'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getLiveness", null);
__decorate([
    (0, common_1.Get)('health/db'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getDatabaseHealth", null);
__decorate([
    (0, common_1.Get)('debug'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getDebug", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getProfile", null);
__decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Get)('debug/user'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getDebugUser", null);
exports.AppController = AppController = AppController_1 = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [app_service_1.AppService,
        prisma_service_1.PrismaService,
        circuit_breaker_service_1.CircuitBreakerService])
], AppController);
//# sourceMappingURL=app.controller.js.map