Okay, here's a comprehensive Convention File for your "Carrier Loadboard & Bidding App" project. This file aims to establish consistency across the codebase, making it easier to read, maintain, and collaborate on.

---

## Project Conventions: Carrier Loadboard & Bidding App

**Version:** 1.0
**Last Updated:** \[Current Date]

**Purpose:** This document outlines the coding, naming, and structural conventions to be followed throughout the development of the Carrier Loadboard & Bidding App. Adherence to these conventions will ensure code consistency, readability, and maintainability.

### 1. General Principles

1.  **Clarity over Brevity:** Write code that is easy to understand.
2.  **DRY (Don't Repeat Yourself):** Avoid redundant code by using functions, components, and services.
3.  **KISS (Keep It Simple, Stupid):** Favor simpler solutions over complex ones.
4.  **Consistency:** Follow the established conventions even if you have a personal preference for another style.
5.  **Comments:**
    *   Comment complex logic, non-obvious decisions, or workarounds.
    *   Use `// TODO:` for tasks to be completed and `// FIXME:` for known issues needing fixes.
    *   JSDoc/TSDoc for public API functions, classes, and complex types.

### 2. Version Control (Git)

1.  **Branching Strategy:**
    *   `main`: Production-ready code. Protected branch. Merges only from `develop` via Pull Requests.
    *   `develop`: Integration branch for features. Nightly/staging builds can be deployed from here.
    *   `feature/<feature-name>`: For new features (e.g., `feature/loadboard-filters`). Branched from `develop`.
    *   `bugfix/<issue-number>`: For fixing bugs (e.g., `bugfix/login-error-52`). Branched from `develop` or `main` (for hotfixes).
    *   `hotfix/<issue-number>`: For critical production bugs. Branched from `main`, merged back to `main` and `develop`.
2.  **Commit Messages:**
    *   Use Conventional Commits format: `<type>(<scope>): <subject>`
        *   **Types:** `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `ci`, `perf`.
        *   **Scope (Optional):** Module or component affected (e.g., `auth`, `loadboard`, `api-bids`).
        *   **Subject:** Concise description of the change, imperative mood (e.g., "Add user login endpoint").
    *   Example: `feat(auth): implement password reset functionality`
    *   Example: `fix(loadboard): correct filter logic for date ranges`
3.  **Pull Requests (PRs):**
    *   All code changes (except urgent hotfixes) must go through PRs to `develop` or `main`.
    *   PRs must be reviewed by at least one other team member.
    *   PR descriptions should clearly explain the changes and link to any relevant issues.
    *   Ensure CI checks (linting, tests) pass before merging.

### 3. Project Structure (Monorepo using Turborepo/Nx or similar is implied)

*   **Root Directory:**
    *   `apps/`: Contains individual applications.
        *   `web/`: Next.js frontend application.
        *   `api/`: NestJS backend application.
    *   `packages/`: Contains shared libraries/utilities.
        *   `ui/`: Shared React components (if any).
        *   `eslint-config-custom/`: Custom ESLint configurations.
        *   `tsconfig/`: Shared TypeScript configurations.
        *   `types/`: Shared TypeScript type definitions (e.g., API payloads, data models).
    *   `README.md`
    *   `CONTRIBUTING.md`
    *   `CONVENTIONS.md` (This file)
    *   `package.json` (Root monorepo config)
    *   `turbo.json` or `nx.json`

### 4. Naming Conventions

1.  **General:**
    *   Use **English** for all names.
    *   Be descriptive and avoid abbreviations where possible (e.g., `userProfile` instead of `usrPrfl`).
2.  **Files & Directories:**
    *   **`kebab-case`**: All lowercase, words separated by hyphens.
        *   Examples: `user-profile.tsx`, `load-details-modal/`, `auth.service.ts`
    *   Component files: `ComponentName.tsx` (PascalCase for React components).
3.  **TypeScript/JavaScript Variables & Functions:**
    *   **`camelCase`**: `let itemCount; function calculateTotal() {}`
    *   **Constants:** `UPPER_SNAKE_CASE` (e.g., `const API_BASE_URL = '...';`). For configuration values.
4.  **TypeScript Classes, Interfaces, Types, Enums:**
    *   **`PascalCase`**: `class UserService {}`, `interface LoadData {}`, `type UserRole = 'ADMIN' | 'CARRIER';`, `enum HttpStatus {}`
5.  **CSS (Tailwind CSS is primary, but for custom CSS if any):**
    *   Classes: `kebab-case` (e.g., `.custom-button-style`).
    *   BEM (Block Element Modifier) can be used for complex custom components if not handled by Tailwind.
6.  **Database (PostgreSQL):**
    *   **Tables:** `PascalCase` (singular or plural based on ORM default, aim for consistency - e.g., `Users`, `CarrierProfiles` or `User`, `CarrierProfile`). *Decision from previous step: `Users`, `CarrierProfiles`, `Loads`, `Bids` (PascalCase plural).*
    *   **Columns:** `snake_case` (e.g., `user_id`, `created_at`, `company_name`).
    *   **Primary Keys:** `id`.
    *   **Foreign Keys:** `<referenced_table_singular>_id` (e.g., `user_id` in `CarrierProfiles` table referencing `Users.id`).
7.  **API Endpoints (RESTful):**
    *   Use **`kebab-case`** for paths.
    *   Plural nouns for collections (e.g., `/users`, `/loads`).
    *   Use HTTP verbs appropriately: `GET`, `POST`, `PUT`, `PATCH`, `DELETE`.
    *   Example: `GET /api/v1/loads/:loadId/bids`
    *   Example: `POST /api/v1/auth/login`

### 5. Coding Style

1.  **Formatting:**
    *   Use **Prettier** for automatic code formatting. Configuration will be in the root `package.json` or `.prettierrc.js`.
    *   **Key Prettier Settings (Example):**
        *   `printWidth`: 100
        *   `tabWidth`: 4 (or 2, be consistent)
        *   `useTabs`: false
        *   `semi`: true
        *   `singleQuote`: true
        *   `trailingComma`: `es5`
        *   `bracketSpacing`: true
        *   `arrowParens`: `always`
2.  **Linting:**
    *   Use **ESLint** with appropriate plugins (TypeScript, React, Next.js, NestJS).
    *   Configuration in `.eslintrc.js` files (root and per-app/package if needed).
    *   Aim for zero ESLint errors/warnings before committing.
3.  **TypeScript Specifics:**
    *   **Type Everything:** Use `any` sparingly and only when absolutely necessary. Provide a `// TODO: type this properly` comment.
    *   Prefer `interface` for defining object shapes and `type` for unions, intersections, or simple aliases.
    *   Use `readonly` for properties that should not be reassigned after object creation.
    *   Use utility types (e.g., `Partial`, `Readonly`, `Pick`, `Omit`) where appropriate.
    *   Organize imports: grouped and sorted (can be automated by ESLint/Prettier).
4.  **React/Next.js Specifics:**
    *   **Functional Components with Hooks:** Prefer over class components.
    *   **File Structure per Component (Example):**
        ```
        components/
          UserProfile/
            UserProfile.tsx
            UserProfile.module.css (if using CSS Modules for specific styling)
            UserProfile.stories.tsx (for Storybook, if used)
            UserProfile.test.tsx
            index.ts (export default from './UserProfile';)
        ```
    *   **Props:** Clearly define prop types using TypeScript interfaces.
    *   **State Management:**
        *   Local state: `useState`, `useReducer`.
        *   Global/Shared state: Zustand, Jotai, or Redux Toolkit (choose one and stick to it). *Decision needed if complex global state arises.* For MVP, prop drilling or React Context might suffice for simple cases.
    *   **Hooks:** Custom hooks should start with `use` (e.g., `useAuth`).
5.  **NestJS Specifics:**
    *   Follow NestJS conventions for modules, controllers, services, providers.
    *   Use DTOs (Data Transfer Objects) for request/response validation and typing. Define DTOs in a shared `types` package or within the `api` app if not widely shared.
    *   Use Guards for authorization, Pipes for validation/transformation.
    *   Use dependency injection.
    *   Organize by feature module (e.g., `AuthModule`, `LoadsModule`, `BidsModule`).

### 6. Testing

1.  **Frameworks:**
    *   **Unit Tests:** Jest.
    *   **Integration Tests (Backend):** Jest with Supertest for API endpoint testing.
    *   **Component Tests (Frontend):** Jest with React Testing Library.
    *   **End-to-End (E2E) Tests:** Playwright or Cypress. (Post-MVP)
2.  **Coverage:** Aim for reasonable test coverage, especially for critical business logic and core components.
3.  **Location:** Test files should reside alongside the code they are testing (e.g., `component.test.tsx` next to `component.tsx`) or in a `__tests__` subdirectory.

### 7. Environment Variables

1.  Prefix all custom environment variables:
    *   Frontend (Next.js): `NEXT_PUBLIC_` for browser-exposed variables.
    *   Backend (NestJS): No specific prefix needed, but use clear names (e.g., `DATABASE_URL`, `CLERK_SECRET_KEY`).
2.  Use a `.env.example` file in each app/package to list required environment variables with placeholder values.
3.  Never commit `.env` files containing secrets.

### 8. API Design (RESTful - Backend)

1.  **Versioning:** `/api/v1/...`
2.  **Authentication:** Use JWTs (from Clerk) in the `Authorization: Bearer <token>` header.
3.  **Status Codes:** Use appropriate HTTP status codes (e.g., `200 OK`, `201 Created`, `204 No Content`, `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found`, `500 Internal Server Error`).
4.  **Error Responses:** Consistent JSON error response format:
    ```json
    {
      "statusCode": 400,
      "message": "Validation failed",
      "errors": [
        { "field": "email", "message": "Email is required" },
        { "field": "password", "message": "Password must be at least 8 characters" }
      ]
    }
    // Or for general errors:
    {
      "statusCode": 500,
      "message": "An unexpected error occurred"
    }
    ```
5.  **Pagination:** For list endpoints, implement pagination (e.g., `?page=1&limit=20`). Include pagination metadata in the response.
6.  **Sorting & Filtering:** Provide query parameters for sorting (e.g., `?sortBy=createdAt&order=desc`) and filtering (e.g., `?status=AVAILABLE`).

### 9. Logging

1.  Use a structured logging library (e.g., Pino for NestJS).
2.  Log important events, errors, and relevant request/response information (be mindful of PII).
3.  Log levels: `DEBUG`, `INFO`, `WARN`, `ERROR`.

### 10. Documentation

1.  **READMEs:** Each app (`web`, `api`) and significant package (`ui`, `types`) should have a `README.md` explaining its purpose, setup, and usage.
2.  **API Documentation:** Use Swagger/OpenAPI for the backend API (NestJS has built-in support).
3.  **Component Documentation (Frontend):** Storybook can be used if building a component library or for documenting complex UI components. (Post-MVP)

---