-- Final Database Schema Optimization for N8N Authentication
-- Phase 2.7-T4: Database Schema Migration
-- Applied: 2024-12-23

-- Performance Optimization: Composite indexes for authentication queries
CREATE INDEX IF NOT EXISTS "users_airtable_user_id_role_idx" ON "users" ("airtableUserId", "role");
CREATE INDEX IF NOT EXISTS "user_profiles_email_role_idx" ON "user_profiles" ("email", "role");
CREATE INDEX IF NOT EXISTS "user_profiles_mc_number_role_idx" ON "user_profiles" ("mcNumber", "role");

-- Optimize carrier profile lookups with MC Number
CREATE INDEX IF NOT EXISTS "carrier_profiles_mc_number_verified_idx" ON "carrier_profiles" ("mcNumber", "isVerifiedByAdmin");

-- Optimize load targeting queries with MC Number-based filtering
CREATE INDEX IF NOT EXISTS "loads_is_targeted_pickup_date_idx" ON "loads" ("isTargeted", "pickupDateUtc") WHERE "isPublic" = true;

-- Add constraint to ensure UserProfile data integrity
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_email_format_check" CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Add performance-optimized partial indexes for active data
CREATE INDEX IF NOT EXISTS "loads_active_status_idx" ON "loads" ("status", "createdAt") WHERE "status" IN ('AVAILABLE', 'ASSIGNED');
CREATE INDEX IF NOT EXISTS "bids_active_negotiation_idx" ON "bids" ("negotiation_status", "createdAt") WHERE "negotiation_status" = 'OPEN';

-- Add database-level constraints for N8N authentication
ALTER TABLE "users" ADD CONSTRAINT "users_airtable_user_id_not_empty" CHECK (LENGTH(TRIM("airtableUserId")) > 0);
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_airtable_user_id_not_empty" CHECK (LENGTH(TRIM("airtableUserId")) > 0);

-- Create database functions for N8N authentication performance
CREATE OR REPLACE FUNCTION get_user_by_airtable_id(airtable_id TEXT)
RETURNS TABLE(
    id TEXT,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    role TEXT,
    mc_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.email,
        u."firstName",
        u."lastName",
        u.role::TEXT,
        u."mcNumber",
        u."createdAt",
        u."updatedAt"
    FROM users u
    WHERE u."airtableUserId" = airtable_id;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function for efficient MC Number-based load targeting
CREATE OR REPLACE FUNCTION get_targeted_loads_for_mc(mc_number_param TEXT)
RETURNS TABLE(
    id TEXT,
    airtable_record_id TEXT,
    origin_city TEXT,
    origin_state TEXT,
    destination_city TEXT,
    destination_state TEXT,
    pickup_date_utc TIMESTAMP WITH TIME ZONE,
    delivery_date_utc TIMESTAMP WITH TIME ZONE,
    equipment_required TEXT,
    weight_lbs INTEGER,
    rate DOUBLE PRECISION,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id,
        l."airtableRecordId",
        l."originCity",
        l."originState",
        l."destinationCity",
        l."destinationState",
        l."pickupDateUtc",
        l."deliveryDateUtc",
        l."equipmentRequired",
        l."weightLbs",
        l.rate,
        l.status::TEXT
    FROM loads l
    WHERE (
        l."isPublic" = true 
        OR (
            l."isTargeted" = true 
            AND l."targetOrganizations" ? mc_number_param
        )
    )
    AND l.status IN ('AVAILABLE')
    ORDER BY l."pickupDateUtc" ASC;
END;
$$ LANGUAGE plpgsql STABLE;

-- Update table statistics for query optimization
ANALYZE users;
ANALYZE user_profiles;
ANALYZE carrier_profiles;
ANALYZE loads;
ANALYZE bids; 