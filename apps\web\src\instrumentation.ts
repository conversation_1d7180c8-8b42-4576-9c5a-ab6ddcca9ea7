import { registerOTel } from "@vercel/otel";

export function register() {
  registerOTel({
    // Replace with your actual service name
    serviceName: "fcp-portal-web", // Example: your-nextjs-app-name
    instrumentationConfig: {
      fetch: {
        // Ensure that internal API calls or calls to sensitive endpoints are not automatically traced
        // by adding their URLs to the propagateContextUrls array.
        // Example: if your app calls itself at /api/internal, add it here.
        // propagateContextUrls: [/\/api\/internal/],
        propagateContextUrls: [], // Start with none, add if needed
      },
    },
  });
} 