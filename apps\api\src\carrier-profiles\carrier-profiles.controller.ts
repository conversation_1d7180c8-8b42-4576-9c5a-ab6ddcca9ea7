import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch, // Using Patch for partial updates is common
  Param,
  Delete,
  UseGuards,
  Request, // To get user info from the request
  // ParseUUIDPipe, // Prisma CUIDs are not UUIDs
  UsePipes,
  ValidationPipe,
  NotFoundException, // Import exceptions
  ForbiddenException,
  ConflictException, // Import ConflictException
  Logger,
} from '@nestjs/common';
import { CarrierProfilesService } from './carrier-profiles.service';
import { CreateCarrierProfileDto, UpdateCarrierProfileDto, AdminUpdateCarrierProfileDto } from './dto';
import { AuthGuard } from '../auth/auth.guard';
import { AdminGuard } from '../auth/admin.guard';
import { AuthService } from '../auth/auth.service'; // Import AuthService
import { ApiTags, ApiBearerAuth, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger'; // For Swagger
import { CarrierProfile } from '@repo/db'; // Added import
import { AuthenticatedRequest } from '../auth/authenticated-request.interface';

// Define an interface for the expected structure of the Clerk JWT payload
// This will be attached to req.user by the ClerkGuard
// N8N JWT payload interface - moved from Clerk interface
interface N8NJwtPayload {
  id: string; // The Airtable User ID
  email?: string;
  role?: string; // User role (CARRIER, ADMIN)
  mcNumber?: string; // MC Number for targeting
  // Add other N8N JWT claims as needed
}

@ApiTags('carrier-profiles') // Group endpoints in Swagger UI
@ApiBearerAuth() // Indicate JWT Bearer auth is expected in Swagger
@UseGuards(AuthGuard) // Apply N8N authentication guard to all routes in this controller
@Controller('carrier-profiles')
@UsePipes(new ValidationPipe({ 
  whitelist: true, 
  forbidNonWhitelisted: true,
  transform: true,
  disableErrorMessages: false
})) // Apply validation pipe globally for this controller
export class CarrierProfilesController {
  private readonly logger = new Logger(CarrierProfilesController.name);

  constructor(
    private readonly carrierProfilesService: CarrierProfilesService,
    private readonly authService: AuthService, // Inject AuthService
  ) {}

  // Endpoint for a carrier to create their own profile
  @Post()
  @ApiOperation({ 
    summary: 'Create a new carrier profile',
    description: 'Create a new carrier profile for the authenticated user'
  })
  @ApiBody({ type: CreateCarrierProfileDto })
  // This will be attached to req.user by the AuthGuard
  async create(
    @Body() createCarrierProfileDto: CreateCarrierProfileDto,
    @Request() req: AuthenticatedRequest, // Use proper type
  ): Promise<CarrierProfile> {
    this.logger.log(`Creating carrier profile for user: ${req.user?.airtableUserId}`);
    
    const airtableUserId = req.user?.airtableUserId;
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new ForbiddenException('User authentication required');
    }

    try {
      const profile = await this.carrierProfilesService.create(createCarrierProfileDto, airtableUserId);
      this.logger.log(`Carrier profile created successfully for user: ${airtableUserId}, profile ID: ${profile.id}`);
      return profile;
    } catch (error) {
      this.logger.error(`Error creating carrier profile for user ${airtableUserId}:`, error);
      if (error.code === 'P2002') {
        // Prisma unique constraint error
        throw new ForbiddenException('A carrier profile already exists for this user');
      }
      throw error;
    }
  }

  // Endpoint for an admin (or maybe support) to get all profiles (needs role check later)
  @Get()
  @ApiOperation({ 
    summary: 'Get carrier profiles',
    description: 'Get all carrier profiles (admin only) or your own profile'
  })
  @UseGuards(AuthGuard) // Require authentication
  @ApiBearerAuth()
  async findAll(): Promise<CarrierProfile[]> {
    // For now, return all profiles. Later, add role-based filtering
    return this.carrierProfilesService.findAll();
  }

  // Endpoint for the authenticated user to get their own profile
  @Get('me')
  @ApiOperation({ 
    summary: 'Get my carrier profile',
    description: 'Get the carrier profile for the authenticated user'
  })
  @UseGuards(AuthGuard) // Require authentication
  @ApiBearerAuth()
  async findMyProfile(@Request() req: AuthenticatedRequest): Promise<CarrierProfile> { // Use proper type
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new ForbiddenException('User authentication required');
    }

    try {
      const profile = await this.carrierProfilesService.findMyProfileByAirtableUserId(airtableUserId);
      this.logger.log(`Retrieved carrier profile for user: ${airtableUserId}`);
      return profile;
    } catch (error) {
      this.logger.error(`Error retrieving carrier profile for user ${airtableUserId}:`, error);
      if (error instanceof NotFoundException) {
        // Auto-create missing profile as fallback using Airtable data
        this.logger.log(`Auto-creating missing CarrierProfile for user: ${airtableUserId}`);
        try {
          // Get full profile data from Airtable using injected AuthService
          const airtableProfile = await this.authService.getUserProfile(airtableUserId, true);
          
          const createProfileDto = {
            companyName: airtableProfile?.companyName || 'New Carrier',
            mcNumber: airtableProfile?.mcNumber || undefined,
            dotNumber: airtableProfile?.dotNumber || undefined,
            contact_email: airtableProfile?.email || undefined,
            // Minimal required fields - user can update later
          };
          
          this.logger.log(`Creating profile with Airtable data: ${JSON.stringify(createProfileDto)}`);
          const newProfile = await this.carrierProfilesService.create(createProfileDto, airtableUserId);
          this.logger.log(`Auto-created CarrierProfile: ${newProfile.id} for user: ${airtableUserId} with company: ${airtableProfile?.companyName}`);
          return newProfile;
        } catch (createError) {
          this.logger.error(`Failed to auto-create CarrierProfile for user ${airtableUserId}:`, createError);
          // Fallback to basic profile creation if Airtable fetch fails
          try {
            const fallbackProfileDto = {
              companyName: 'New Carrier',
            };
            const fallbackProfile = await this.carrierProfilesService.create(fallbackProfileDto, airtableUserId);
            this.logger.log(`Created fallback CarrierProfile: ${fallbackProfile.id} for user: ${airtableUserId}`);
            return fallbackProfile;
          } catch (fallbackError) {
            this.logger.error(`Failed to create fallback CarrierProfile for user ${airtableUserId}:`, fallbackError);
            throw new NotFoundException('No carrier profile found and auto-creation failed');
          }
        }
      }
      throw error;
    }
  }

  // Endpoint for getting a specific profile by ID (primarily for admin use?)
  @Get(':id')
  @ApiOperation({
      summary: 'Get a specific carrier profile by ID (Admin Only)',
      description: 'Requires Admin role. RBAC enforced.',
  })
  @ApiParam({ name: 'id', description: 'Carrier Profile ID (CUID)', type: String })
  @UseGuards(AdminGuard)
  async findOne(@Param('id') id: string): Promise<CarrierProfile> {
    const profile = await this.carrierProfilesService.findOne(id);
    if (!profile) {
        throw new NotFoundException(`Carrier profile with ID "${id}" not found`);
    }
    return profile;
  }

  // Endpoint for the authenticated user to update their own profile
  @Patch('me')
  @UseGuards(AuthGuard) // Require authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Update my carrier profile',
    description: 'Update the carrier profile for the authenticated user'
  })
  @ApiBody({ type: UpdateCarrierProfileDto })
  async updateMyProfile(
    @Body() updateCarrierProfileDto: UpdateCarrierProfileDto,
    @Request() req: AuthenticatedRequest, // Use proper type
  ): Promise<CarrierProfile> {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new ForbiddenException('User authentication required');
    }

    try {
      // Log the incoming DTO for debugging
      this.logger.debug(`Received update DTO for user ${airtableUserId}:`, JSON.stringify(updateCarrierProfileDto, null, 2));
      this.logger.debug(`DTO equipmentTypes type: ${typeof updateCarrierProfileDto.equipmentTypes}, value:`, updateCarrierProfileDto.equipmentTypes);
      
      const profile = await this.carrierProfilesService.updateMyProfileByAirtableUserId(airtableUserId, updateCarrierProfileDto);
      this.logger.log(`Updated carrier profile for user: ${airtableUserId}`);
      return profile;
    } catch (error) {
      this.logger.error(`Error updating carrier profile for user ${airtableUserId}:`, error);
      this.logger.error(`Error details:`, {
        message: error.message,
        stack: error.stack,
        statusCode: error.statusCode,
        response: error.response
      });
      if (error instanceof NotFoundException) {
        throw new NotFoundException('No carrier profile found for the authenticated user');
      }
      throw error;
    }
  }

  // Endpoint for updating a specific profile by ID (primarily for admin use?)
  @Patch(':id') // Or PUT if you require full replacement
  @ApiOperation({
      summary: 'Update a specific carrier profile by ID (Admin Only)',
      description: 'Requires Admin role. RBAC enforced.',
  })
  @ApiParam({ name: 'id', description: 'Carrier Profile ID (CUID)', type: String })
  @ApiBody({ type: UpdateCarrierProfileDto })
  @UseGuards(AdminGuard)
  update(
    @Param('id') id: string,
    @Body() updateCarrierProfileDto: AdminUpdateCarrierProfileDto,
  ): Promise<CarrierProfile> {
    return this.carrierProfilesService.update(id, updateCarrierProfileDto);
  }

  // Refresh profile from Airtable
  @Post('me/refresh-from-airtable')
  @UseGuards(AuthGuard) // Require authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Refresh my carrier profile from Airtable',
    description: 'Sync carrier profile data from Airtable UserManagement table'
  })
  async refreshMyProfileFromAirtable(@Request() req: AuthenticatedRequest): Promise<CarrierProfile> {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new ForbiddenException('User authentication required');
    }

    try {
      // Get fresh data from Airtable
      const airtableProfile = await this.authService.getUserProfile(airtableUserId, true);
      
      if (!airtableProfile) {
        throw new NotFoundException('No profile data found in Airtable for this user');
      }

      // Check if carrier profile exists
      let profile;
      try {
        profile = await this.carrierProfilesService.findMyProfileByAirtableUserId(airtableUserId);
      } catch (error) {
        if (error instanceof NotFoundException) {
          // Create new profile with Airtable data
          const createProfileDto = {
            companyName: airtableProfile.companyName || 'New Carrier',
            mcNumber: airtableProfile.mcNumber || undefined,
            dotNumber: airtableProfile.dotNumber || undefined,
            contact_email: airtableProfile.email || undefined,
          };
          
          profile = await this.carrierProfilesService.create(createProfileDto, airtableUserId);
          this.logger.log(`Created new CarrierProfile from Airtable data: ${profile.id} for user: ${airtableUserId}`);
          return profile;
        }
        throw error;
      }

      // Update existing profile with fresh Airtable data
      const updateDto = {
        companyName: airtableProfile.companyName || profile.companyName,
        mcNumber: airtableProfile.mcNumber || profile.mcNumber,
        dotNumber: airtableProfile.dotNumber || profile.dotNumber,
        contact_email: airtableProfile.email || profile.contact_email,
      };

      const updatedProfile = await this.carrierProfilesService.updateMyProfileByAirtableUserId(airtableUserId, updateDto);
      this.logger.log(`Refreshed CarrierProfile from Airtable: ${updatedProfile.id} for user: ${airtableUserId} with company: ${airtableProfile.companyName}`);
      return updatedProfile;
    } catch (error) {
      this.logger.error(`Error refreshing profile from Airtable for user ${airtableUserId}:`, error);
      throw error;
    }
  }

  // Emergency endpoint to create profile for existing user
  @Post('emergency/create')
  @UseGuards(AuthGuard) // Require authentication
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Emergency create my carrier profile',
    description: 'Emergency endpoint to create a carrier profile for authenticated user if missing'
  })
  async emergencyCreateMyProfile(@Request() req: AuthenticatedRequest): Promise<CarrierProfile> {
    const airtableUserId = req.user?.airtableUserId;
    
    if (!airtableUserId) {
      this.logger.error('No user ID found in request user data');
      throw new ForbiddenException('User authentication required');
    }

    try {
      // Check if profile already exists
      try {
        const existingProfile = await this.carrierProfilesService.findMyProfileByAirtableUserId(airtableUserId);
        this.logger.log(`Profile already exists for user: ${airtableUserId}`);
        return existingProfile;
      } catch (error) {
        // Profile doesn't exist, create it
        if (!(error instanceof NotFoundException)) {
          throw error;
        }
      }

      // Get profile data from Airtable for emergency creation too
      try {
        const airtableProfile = await this.authService.getUserProfile(airtableUserId, true);
        
        const createProfileDto = {
          companyName: airtableProfile?.companyName || 'New Carrier',
          mcNumber: airtableProfile?.mcNumber || undefined,
          dotNumber: airtableProfile?.dotNumber || undefined,
          contact_email: airtableProfile?.email || undefined,
          // Minimal required fields - user can update later
        };
        
        this.logger.log(`Emergency creating profile with Airtable data: ${JSON.stringify(createProfileDto)}`);
        const profile = await this.carrierProfilesService.create(createProfileDto, airtableUserId);
        this.logger.log(`Emergency CarrierProfile created: ${profile.id} for user: ${airtableUserId} with company: ${airtableProfile?.companyName}`);
        return profile;
      } catch (airtableError) {
        this.logger.warn(`Failed to fetch Airtable data for emergency creation: ${airtableError.message}, using defaults`);
        // Fallback to default creation if Airtable fails
        const createProfileDto = {
          companyName: 'New Carrier', // Default name, user can update later
          // Minimal required fields - user can update later
        };

        const profile = await this.carrierProfilesService.create(createProfileDto, airtableUserId);
        this.logger.log(`Emergency CarrierProfile created: ${profile.id} for user: ${airtableUserId}`);
        return profile;
      }
    } catch (error) {
      this.logger.error(`Error in emergency profile creation for user ${airtableUserId}:`, error);
      throw error;
    }
  }

  // Endpoint for deleting a profile (primarily for admin use?)
  @Delete(':id')
   @ApiOperation({
       summary: 'Delete a specific carrier profile by ID (Admin Only)',
       description: 'Requires Admin role. RBAC enforced.',
   })
  @ApiParam({ name: 'id', description: 'Carrier Profile ID (CUID)', type: String })
  @UseGuards(AdminGuard)
  remove(@Param('id') id: string): Promise<CarrierProfile> {
    return this.carrierProfilesService.remove(id);
  }
} 