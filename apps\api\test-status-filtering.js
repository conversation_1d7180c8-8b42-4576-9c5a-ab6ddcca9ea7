#!/usr/bin/env node
/**
 * Test Script: Status Filtering Validation
 * Purpose: Verify that only "Available" status loads appear on loadboard (exclude "Booking Requested")
 * 
 * Expected Results:
 * ✅ Only "Available" status loads appear on loadboard
 * ✅ "Booking Requested" loads do NOT appear
 * ✅ Other statuses do NOT appear
 */

const { PrismaClient } = require('@prisma/client');
const { ConfigService } = require('@nestjs/config');
const Airtable = require('airtable');

console.log('🧪 STATUS FILTERING TEST: Validating load visibility rules');
console.log('=====================================');

async function testStatusFiltering() {
  try {
    // Mock config service
    const configService = {
      get: (key) => {
        const config = {
          'AIRTABLE_API_KEY': process.env.AIRTABLE_API_KEY,
          'AIRTABLE_BASE_ID': process.env.AIRTABLE_BASE_ID,
          'AIRTABLE_TABLE_NAME': process.env.AIRTABLE_TABLE_NAME || 'Loads'
        };
        return config[key];
      }
    };

    const apiKey = configService.get('AIRTABLE_API_KEY');
    const baseId = configService.get('AIRTABLE_BASE_ID');
    const tableName = configService.get('AIRTABLE_TABLE_NAME');

    if (!apiKey || !baseId) {
      throw new Error('Missing Airtable configuration. Please check AIRTABLE_API_KEY and AIRTABLE_BASE_ID');
    }

    console.log(`🔧 Configuration:`);
    console.log(`   Base ID: ${baseId}`);
    console.log(`   Table: ${tableName}`);
    console.log(`   API Key: ${apiKey.substring(0, 8)}...`);
    console.log('');

    // Initialize Airtable
    const base = new Airtable({ apiKey }).base(baseId);

    console.log('📋 STEP 1: Testing Current Filter Logic (Should only show "Available")');
    console.log('=============================================================');

    // Test the NEW filter logic (should only return "Available" loads)
    const availableOnlyRecords = await base(tableName)
      .select({
        view: 'Grid view',
        fields: ['Order ID.', 'Status', 'Synced to API', 'Is Public', 'Target Organizations'],
        filterByFormula: `AND({Synced to API}, {Status} = "Available")`
      })
      .all();

    console.log(`✅ NEW FILTER RESULTS: Found ${availableOnlyRecords.length} loads with Status = "Available" AND Synced to API = true`);

    // Analyze status distribution
    const statusCounts = {};
    availableOnlyRecords.forEach(record => {
      const status = record.get('Status');
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    console.log('\n📊 Status Distribution (NEW Filter):');
    Object.entries(statusCounts).forEach(([status, count]) => {
      const icon = status === 'Available' ? '✅' : '❌';
      console.log(`   ${icon} ${status}: ${count} loads`);
    });

    console.log('\n📋 STEP 2: Testing OLD Filter Logic (Would show Available + Booking Requested)');
    console.log('=============================================================================');

    // Test the OLD filter logic for comparison
    const oldFilterRecords = await base(tableName)
      .select({
        view: 'Grid view',
        fields: ['Order ID.', 'Status', 'Synced to API', 'Is Public', 'Target Organizations'],
        filterByFormula: `AND({Synced to API}, OR({Status} = "Available", {Status} = "Booking Requested"))`
      })
      .all();

    console.log(`⚠️  OLD FILTER RESULTS: Found ${oldFilterRecords.length} loads with (Status = "Available" OR "Booking Requested") AND Synced to API = true`);

    // Analyze old status distribution
    const oldStatusCounts = {};
    oldFilterRecords.forEach(record => {
      const status = record.get('Status');
      oldStatusCounts[status] = (oldStatusCounts[status] || 0) + 1;
    });

    console.log('\n📊 Status Distribution (OLD Filter):');
    Object.entries(oldStatusCounts).forEach(([status, count]) => {
      const icon = status === 'Available' ? '✅' : status === 'Booking Requested' ? '⚠️' : '❌';
      console.log(`   ${icon} ${status}: ${count} loads`);
    });

    console.log('\n📋 STEP 3: Impact Analysis');
    console.log('========================');

    const bookingRequestedCount = (oldStatusCounts['Booking Requested'] || 0);
    const availableCount = (statusCounts['Available'] || 0);
    const totalRemovedLoads = oldFilterRecords.length - availableOnlyRecords.length;

    console.log(`🎯 FILTERING IMPACT:`);
    console.log(`   ✅ Loads still visible: ${availableCount} ("Available" status)`);
    console.log(`   🚫 Loads now hidden: ${totalRemovedLoads} (including ${bookingRequestedCount} "Booking Requested")`);
    console.log(`   📈 Security improvement: ${totalRemovedLoads > 0 ? 'SUCCESS' : 'No change needed'}`);

    console.log('\n📋 STEP 4: Sample Load Details');
    console.log('==============================');

    // Show sample loads for verification
    const sampleLoads = availableOnlyRecords.slice(0, 5);
    sampleLoads.forEach((record, index) => {
      const orderId = record.get('Order ID.');
      const status = record.get('Status');
      const isPublic = record.get('Is Public');
      const targetOrgs = record.get('Target Organizations');
      
      console.log(`   ${index + 1}. Order: ${orderId}`);
      console.log(`      Status: ${status} ✅`);
      console.log(`      Public: ${isPublic ? 'Yes' : 'No'}`);
      console.log(`      Targeting: ${targetOrgs ? Array.isArray(targetOrgs) ? targetOrgs.join(', ') : targetOrgs : 'None'}`);
      console.log('');
    });

    console.log('🎉 STATUS FILTERING TEST COMPLETED');
    console.log('=================================');
    console.log(`✅ SUCCESS: Only "Available" status loads will be visible on loadboard`);
    console.log(`🚫 BLOCKED: ${bookingRequestedCount} "Booking Requested" loads are now properly hidden`);
    console.log(`🔒 SECURITY: Load visibility properly restricted by status`);

    return {
      success: true,
      availableLoads: availableCount,
      removedLoads: totalRemovedLoads,
      bookingRequestedBlocked: bookingRequestedCount
    };

  } catch (error) {
    console.error('❌ STATUS FILTERING TEST FAILED:', error.message);
    console.error('Stack trace:', error.stack);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
testStatusFiltering()
  .then(result => {
    if (result.success) {
      console.log('\n✅ All status filtering tests passed!');
      process.exit(0);
    } else {
      console.log('\n❌ Status filtering tests failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }); 