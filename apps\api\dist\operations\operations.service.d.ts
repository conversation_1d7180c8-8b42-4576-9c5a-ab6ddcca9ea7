import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { PatternAnalysisService } from './services/pattern-analysis.service';
import { SmartSuggestionsService } from './services/smart-suggestions.service';
import { RadarDistanceService } from './services/radar-distance.service';
export interface Lane {
    id: string;
    originCity: string;
    originState: string;
    destinationCity: string;
    destinationState: string;
    estimatedMiles: number;
    estimatedDuration: string;
    frequencyRank: number;
    lastUsed?: string;
}
interface CreateOrderResult {
    id: string;
    airtableRecordId?: string;
}
export declare class OperationsService {
    private readonly prisma;
    private readonly patternAnalysis;
    private readonly smartSuggestions;
    private readonly radarDistance;
    private readonly logger;
    constructor(prisma: PrismaService, patternAnalysis: PatternAnalysisService, smartSuggestions: SmartSuggestionsService, radarDistance: RadarDistanceService);
    getLanes(): Promise<Lane[]>;
    private getCachedLanes;
    private generateBasicLanes;
    private calculateQuickDistance;
    calculateAccurateLanes(): Promise<Lane[]>;
    createOrder(createOrderDto: CreateOrderDto, clerkUserId: string): Promise<CreateOrderResult>;
    private calculateEstimatedDuration;
    getSmartSuggestions(originCity: string, originState: string, destinationCity: string, destinationState: string, userId: string, currentValues?: any): Promise<{
        suggestions: never[];
        smartDefaults: {};
        confidence: number;
        metadata: {
            error: string;
            generatedAt: Date;
            dataPoints: number;
            learningActive: boolean;
        };
    } | {
        suggestions: any[];
        smartDefaults: any;
        confidence: number;
        metadata: {
            generatedAt: Date;
            dataPoints: number;
            learningActive: boolean;
            databaseHealthy: boolean;
        };
    }>;
    validateOrderWithAI(orderData: any, context: any): Promise<{
        isValid: boolean;
        warnings: any[];
        suggestions: any[];
        criticalIssues: any[];
    }>;
    recordSuggestionFeedback(orderId: string, feedbackData: any): Promise<void>;
    getAutoCompleteSuggestions(field: string, partialValue: string, context: any): Promise<string[] | number[]>;
}
export {};
