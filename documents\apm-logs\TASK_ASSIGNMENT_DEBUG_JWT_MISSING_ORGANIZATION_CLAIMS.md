# TASK ASSIGNMENT: Debug JWT Token Missing Organization Claims

## PRIORITY: 🚨 CRITICAL - JWT TOKEN ISSUE IDENTIFIED

**Estimated Timeline**: 2-3 hours  
**Complexity**: High - JWT/Clerk Integration  
**Impact**: Critical - Complete portal access blocked

## ROOT CAUSE IDENTIFIED

### **JWT Token Analysis from Logs**
The JWT token payload is **MISSING ORGANIZATION CLAIMS**:

```javascript
// Current JWT Payload (PROBLEMATIC)
{
  sub: 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE',
  iss: 'https://clerk.fcp-portal.com', 
  exp: 1749417124,
  aud: undefined
}

// Expected JWT Payload (SHOULD INCLUDE)
{
  sub: 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE',
  iss: 'https://clerk.fcp-portal.com',
  exp: 1749417124,
  aud: undefined,
  org_id: 'org_xxxxxxxxxx',        // MISSING
  org_role: 'admin',               // MISSING  
  org_slug: 'mvt-logistics'        // MISSING
}
```

### **The Problem Chain**
1. ✅ User authentication works (JWT verified successfully)
2. ❌ **JWT contains NO organization information**
3. ❌ Backend expects organization context in JWT
4. ❌ AuthService finds 4 organizations but can't determine active one
5. ❌ Results in 401 authentication failure

## INVESTIGATION PRIORITIES

### **Task 1: Confirm Organization Selector Implementation Status**

**Immediate Check Commands:**
```bash
# Check if OrganizationSelector exists
find src/ -name "*Organization*" -type f

# Check if it's being used anywhere
grep -r "OrganizationSelector" src/ --include="*.tsx" --include="*.ts"

# Check current layout files
find src/ -name "layout.tsx" -exec grep -l "Organization" {} \;
```

**Critical Questions:**
- Was the OrganizationSelector ever implemented?
- Is it rendered in the UI?
- Is the user able to see/interact with it?

### **Task 2: Debug Clerk Organization Context State**

**Add Immediate Debug Component:**
```typescript
// src/components/JwtDebugger.tsx
'use client';

import { useAuth, useOrganization, useOrganizationList } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

export function JwtDebugger() {
  const { getToken } = useAuth();
  const { organization } = useOrganization();
  const { organizationList } = useOrganizationList();
  const [tokenPayload, setTokenPayload] = useState<any>(null);

  useEffect(() => {
    const debugToken = async () => {
      try {
        const token = await getToken();
        if (token) {
          // Decode JWT payload
          const payload = JSON.parse(atob(token.split('.')[1]));
          setTokenPayload(payload);
          
          console.log('🔍 JWT DEBUG ANALYSIS:');
          console.log('Current Organization:', organization);
          console.log('Available Organizations:', organizationList?.length);
          console.log('JWT Token Payload:', payload);
          console.log('Organization in JWT:', payload.org_id || 'MISSING ❌');
          console.log('Organization Role in JWT:', payload.org_role || 'MISSING ❌');
          console.log('Organization Slug in JWT:', payload.org_slug || 'MISSING ❌');
        }
      } catch (error) {
        console.error('JWT Debug Error:', error);
      }
    };

    debugToken();
  }, [organization, organizationList, getToken]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div className="fixed top-4 right-4 bg-yellow-100 p-4 rounded-lg shadow-lg max-w-md z-50 text-xs">
      <h4 className="font-bold text-yellow-800 mb-2">JWT Organization Debug</h4>
      <div className="space-y-1 text-yellow-700">
        <div>Current Org: {organization?.id || '❌ NONE'}</div>
        <div>Available Orgs: {organizationList?.length || 0}</div>
        <div>JWT org_id: {tokenPayload?.org_id || '❌ MISSING'}</div>
        <div>JWT org_role: {tokenPayload?.org_role || '❌ MISSING'}</div>
        <div>JWT org_slug: {tokenPayload?.org_slug || '❌ MISSING'}</div>
      </div>
    </div>
  );
}
```

### **Task 3: Test Organization Selection Process**

**Manual Testing Protocol:**
1. **Navigate to the web app**
2. **Open browser developer console**
3. **Look for organization selector in UI**
4. **If selector exists, attempt to select an organization**
5. **Monitor console logs for setActive() calls**
6. **Check if JWT token updates after selection**

**Key Questions to Answer:**
- Is there a visible organization selector dropdown?
- Does selecting an organization call `setActive({ organization: orgId })`?
- Does the JWT token get refreshed after organization selection?
- Are there any JavaScript errors preventing organization switching?

### **Task 4: Debug setActive() Function Calls**

**Add Organization Selection Debugging:**
```typescript
// Add this to OrganizationSelector component (if it exists)
const handleOrgSwitch = async (orgId: string) => {
  console.log('🔄 Attempting to switch organization:', orgId);
  
  try {
    await setActive({ organization: orgId });
    console.log('✅ setActive() completed successfully');
    
    // Test if JWT token now includes organization
    setTimeout(async () => {
      const newToken = await getToken();
      if (newToken) {
        const newPayload = JSON.parse(atob(newToken.split('.')[1]));
        console.log('🔍 JWT after setActive():', newPayload);
        console.log('Organization in new JWT:', newPayload.org_id || 'STILL MISSING ❌');
      }
    }, 1000);
    
  } catch (error) {
    console.error('❌ setActive() failed:', error);
  }
};
```

### **Task 5: Investigate Clerk Configuration Issues**

**Potential Clerk Configuration Problems:**
1. **JWT Template Configuration** - Organization claims not included in JWT template
2. **Organization Context** - Clerk organization context not properly configured
3. **SDK Version Issues** - Clerk SDK version compatibility problems

**Investigation Steps:**
```bash
# Check Clerk SDK version
grep "@clerk" package.json

# Check environment variables
grep "CLERK" .env.local

# Look for Clerk configuration files
find . -name "*clerk*" -type f | grep -v node_modules
```

### **Task 6: Backend Integration Analysis**

**Check what the backend expects:**
```bash
# Find AuthService implementation
find . -name "*auth*" -path "*/apps/api/*" -type f

# Search for organization parsing logic
grep -r "org_id\|org_role\|org_slug" apps/api/ --include="*.ts" --include="*.js"
```

**Key Questions:**
- What JWT claims does the backend AuthService expect?
- How does it parse organization information from JWT?
- Is there a mismatch between frontend JWT generation and backend parsing?

## IMPLEMENTATION FIXES

### **Fix Scenario A: Organization Selector Not Implemented**
```typescript
// Implement basic organization selector immediately
'use client';

import { useOrganization, useOrganizationList, useClerk } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';

export function EmergencyOrgSelector() {
  const { organization } = useOrganization();
  const { organizationList } = useOrganizationList();
  const { setActive } = useClerk();

  if (!organizationList || organizationList.length <= 1) return null;

  const handleSelect = async (orgId: string) => {
    console.log('Emergency org selection:', orgId);
    try {
      await setActive({ organization: orgId });
      window.location.reload(); // Force refresh
    } catch (error) {
      console.error('Emergency org selection failed:', error);
    }
  };

  return (
    <div className="fixed top-16 left-4 bg-red-100 p-4 rounded-lg shadow-lg z-50">
      <h3 className="font-bold text-red-800 mb-2">Emergency: Select Organization</h3>
      <div className="space-y-2">
        {organizationList.map((org) => (
          <Button
            key={org.organization.id}
            onClick={() => handleSelect(org.organization.id)}
            className="w-full text-left justify-start"
            variant={organization?.id === org.organization.id ? "default" : "outline"}
          >
            {org.organization.name}
            {organization?.id === org.organization.id && " (Current)"}
          </Button>
        ))}
      </div>
    </div>
  );
}
```

### **Fix Scenario B: JWT Template Configuration**
If JWT tokens don't include organization claims, may need to:
1. **Check Clerk Dashboard JWT Templates**
2. **Add organization claims to JWT template**
3. **Configure proper token generation**

### **Fix Scenario C: Clerk SDK Issues**
```bash
# Update Clerk SDK to latest version
pnpm update @clerk/nextjs @clerk/themes

# Check for breaking changes
pnpm list @clerk/nextjs
```

## IMMEDIATE ACTION PLAN

### **Step 1: Quick Assessment (10 minutes)**
```bash
# Run these commands immediately
find src/ -name "*Organization*" -type f
grep -r "setActive" src/ | head -5
grep "@clerk/nextjs" package.json
```

### **Step 2: Add Debug Components (20 minutes)**
1. Add `JwtDebugger` component to main layout
2. Add `EmergencyOrgSelector` if no selector exists
3. Load page and check console logs

### **Step 3: Test Organization Selection (30 minutes)**
1. Attempt to select organization using any available selector
2. Monitor JWT token changes
3. Check if organization claims appear in token

### **Step 4: Implement Fix (1-2 hours)**
Based on findings, implement appropriate solution

## SUCCESS CRITERIA

### **Immediate Goals**
- [ ] JWT token includes organization claims (`org_id`, `org_role`, `org_slug`)
- [ ] Backend AuthService can identify user's active organization
- [ ] API calls return 200 instead of 401
- [ ] User can access portal features

### **JWT Token Validation**
After fix, JWT payload should include:
```javascript
{
  sub: 'user_2xZWyA8oVI2bhQOrZ5vgJNRonLE',
  iss: 'https://clerk.fcp-portal.com',
  exp: 1749417124,
  aud: undefined,
  org_id: 'org_xxxxxxxxxx',        // ✅ REQUIRED
  org_role: 'admin',               // ✅ REQUIRED
  org_slug: 'mvt-logistics'        // ✅ REQUIRED
}
```

## ESCALATION TRIGGERS

**Escalate Immediately if:**
- Clerk Dashboard access is needed to modify JWT templates
- Organization selector implementation requires > 2 hours
- Backend AuthService parsing logic needs modification
- Clerk SDK version conflicts require architecture changes

**Contact User if:**
- Need Clerk Dashboard admin access
- Require specific organization IDs for testing
- Need clarification on expected organization behavior 