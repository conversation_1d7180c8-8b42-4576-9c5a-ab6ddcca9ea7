'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from './dialog';
import { Button } from './button';
import { Input } from './input';
import { Label } from './label';
import { Textarea } from './textarea';
import { Checkbox } from './checkbox';
import { Badge } from './badge';
import { CalendarIcon, MapPinIcon, TruckIcon, DollarSignIcon, MessageSquareIcon } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';

interface Load {
  id: string;
  proNumber?: string;
  origin?: string;
  destination?: string;
  pickupDateTime?: string;
  deliveryDateTime?: string;
  equipment?: string;
  weight?: number;
  rate?: number;
  status?: string;
  miles?: number;
  temp?: string;
  commodity?: string;
}

interface BookingDetails {
  isTruckEmpty: boolean;
  truckEmptyLocation?: string;
  etaToShipper: string;
  notes?: string;
}

interface BiddingDetails {
  bidAmount: number;
  carrierNotes?: string;
  isTruckEmpty?: boolean;
  truckEmptyLocation?: string;
  etaToShipper?: string;
}

interface BookLoadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  load: Load | null;
  onSubmit?: (loadId: string, bookingDetails: BookingDetails) => Promise<void>;
  onBidSubmit?: (loadId: string, biddingDetails: BiddingDetails) => Promise<void>;
  isSubmitting?: boolean;
  mode?: 'book' | 'bid'; // New prop to determine dialog mode
}

export function BookLoadDialog({ 
  open, 
  onOpenChange, 
  load, 
  onSubmit, 
  onBidSubmit,
  isSubmitting = false,
  mode = 'book' // Default to booking mode for backward compatibility
}: BookLoadDialogProps) {
  const { user, getToken } = useAuth();
  
  const [bookingDetails, setBookingDetails] = useState<BookingDetails>({
    isTruckEmpty: false,
    truckEmptyLocation: '',
    etaToShipper: '',
    notes: '',
  });

  const [biddingDetails, setBiddingDetails] = useState<BiddingDetails>({
    bidAmount: 0,
    carrierNotes: '',
    isTruckEmpty: false,
    truckEmptyLocation: '',
    etaToShipper: '',
  });

  const [bidSubmitted, setBidSubmitted] = useState(false);
  const [submittedBidId, setSubmittedBidId] = useState<string | null>(null);
  const [bidExpiresAt, setBidExpiresAt] = useState<string | null>(null);
  const [isSubmittingBid, setIsSubmittingBid] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);
  const [loadingProfile, setLoadingProfile] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch profile data when dialog opens for bidding
  const fetchProfileData = async () => {
    if (!user || mode !== 'bid' || profileData) return;
    
    setLoadingProfile(true);
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch('/api/v1/carrier-profiles/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Fetched profile data for bidding:', data);
        setProfileData(data);
      } else {
        console.error('Failed to fetch profile data');
      }
    } catch (error) {
      console.error('Error fetching profile data:', error);
    } finally {
      setLoadingProfile(false);
    }
  };

  // Fetch profile data when dialog opens for bidding
  React.useEffect(() => {
    if (open && mode === 'bid') {
      fetchProfileData();
    }
  }, [open, mode]);

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!load || !onSubmit) return;

    // Validation for booking
    const newErrors: Record<string, string> = {};
    
    if (!bookingDetails.etaToShipper.trim()) {
      newErrors.etaToShipper = 'ETA to shipper is required';
    }
    
    if (bookingDetails.isTruckEmpty && !bookingDetails.truckEmptyLocation?.trim()) {
      newErrors.truckEmptyLocation = 'Truck empty location is required when truck is empty';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});

    try {
      await onSubmit(load.id, {
        isTruckEmpty: bookingDetails.isTruckEmpty,
        truckEmptyLocation: bookingDetails.isTruckEmpty ? bookingDetails.truckEmptyLocation : undefined,
        etaToShipper: bookingDetails.etaToShipper,
        notes: bookingDetails.notes || undefined,
      });
      
      // Reset form on success
      setBookingDetails({
        isTruckEmpty: false,
        truckEmptyLocation: '',
        etaToShipper: '',
        notes: '',
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Booking submission failed:', error);
    }
  };

  const handleBiddingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!load || !user) return;

    // Validation for bidding - only bid amount is required
    const newErrors: Record<string, string> = {};
    
    if (!biddingDetails.bidAmount || biddingDetails.bidAmount <= 0) {
      newErrors.bidAmount = 'Bid amount must be greater than $0';
    }
    
    if (biddingDetails.bidAmount > (load.rate || 0) * 2) {
      newErrors.bidAmount = 'Bid amount seems unusually high. Please verify.';
    }

    // Note: isTruckEmpty and etaToShipper are now optional for bids

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});
    setIsSubmittingBid(true);

    try {
      // Get email from JWT token since user object might be empty
      let userEmail = user.email;
      if (!userEmail) {
        try {
          const token = getToken();
          if (token) {
            const parts = token.split('.');
            if (parts.length === 3 && parts[1]) {
              const payload = JSON.parse(atob(parts[1]));
              userEmail = payload.email;
            }
          }
        } catch (e) {
          console.error('Error parsing JWT token:', e);
        }
      }

      const bidData = {
        loadId: load.id,
        carrierUserId: profileData?.userId || user.id || 'unknown',
        bidAmount: biddingDetails.bidAmount,
        carrierNotes: biddingDetails.carrierNotes || '',
        carrierCompanyName: profileData?.companyName || user.companyName || 'Unknown Company',
        carrierMcNumber: profileData?.mcNumber || user.mcNumber || 'Unknown',
        carrierEmail: userEmail || '<EMAIL>',
        // Optional fields for bidding
        isTruckEmpty: biddingDetails.isTruckEmpty || false,
        truckEmptyLocation: biddingDetails.truckEmptyLocation || '',
        etaToShipper: biddingDetails.etaToShipper || '',
        loadDetails: {
          origin: load.origin,
          destination: load.destination,
          pickupDate: load.pickupDateTime,
          deliveryDate: load.deliveryDateTime,
          miles: load.miles?.toString(),
          equipment: load.equipment,
          weight: load.weight?.toString()
        }
      };

      console.log('Submitting bid data:', bidData);

      // Submit bid via our API endpoint which forwards to N8N
      const token = getToken();
      const response = await fetch('/api/v1/bids/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(bidData),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Bid submission failed:', errorData);
        throw new Error(`Bid submission failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Bid submission successful:', result);

      // Show success state
      setBidSubmitted(true);
      setSubmittedBidId(result.bidId || 'Unknown');
      // Preserve the submitted bid amount from the response
      if (result.bidAmount) {
        setBiddingDetails(prev => ({
          ...prev,
          bidAmount: result.bidAmount
        }));
      }
      // Set expiration to 24 hours from now
      setBidExpiresAt(new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString());
      
      toast.success('Bid submitted successfully! You will receive email notifications when the admin responds.');

      // Reset form
      setBiddingDetails({
        bidAmount: load?.rate || 0,
        carrierNotes: '',
        isTruckEmpty: false,
        truckEmptyLocation: '',
        etaToShipper: '',
      });

    } catch (error) {
      console.error('Bid submission error:', error);
      toast.error(`Failed to submit bid: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmittingBid(false);
    }
  };

  const handleInputChange = (field: keyof BookingDetails | keyof BiddingDetails, value: string | boolean | number) => {
    if (mode === 'bid') {
      setBiddingDetails(prev => ({
        ...prev,
        [field]: value,
      }));
    } else {
      setBookingDetails(prev => ({
        ...prev,
        [field]: value,
      }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const resetDialog = () => {
    setBookingDetails({
      isTruckEmpty: false,
      truckEmptyLocation: '',
      etaToShipper: '',
      notes: '',
    });
    setBiddingDetails({
      bidAmount: load?.rate || 0,
      carrierNotes: '',
      isTruckEmpty: false,
      truckEmptyLocation: '',
      etaToShipper: '',
    });
    setErrors({});
    setBidSubmitted(false);
    setSubmittedBidId(null);
    setBidExpiresAt(null);
    setProfileData(null);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetDialog();
    }
    onOpenChange(newOpen);
  };

  if (!load) return null;

  // Success state for completed bid
  if (bidSubmitted && mode === 'bid') {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-green-600">
              <DollarSignIcon className="h-5 w-5" />
              Bid Submitted Successfully!
            </DialogTitle>
            <DialogDescription>
              Your bid has been submitted and the admin has been notified.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Bid ID:</span>
                <span className="font-mono">{submittedBidId}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Amount:</span>
                <span className="font-semibold">${biddingDetails.bidAmount?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Expires:</span>
                <span>{bidExpiresAt ? new Date(bidExpiresAt).toLocaleDateString() : '24 hours'}</span>
              </div>
            </div>

            <div className="text-sm text-muted-foreground space-y-1">
              <p>• Admin will review your bid within 24 hours</p>
              <p>• You'll receive email notifications for any updates</p>
              <p>• Bid expires automatically if not responded to</p>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => handleOpenChange(false)} className="w-full">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl max-h-[95vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            {mode === 'bid' ? (
              <>
                <DollarSignIcon className="h-5 w-5" />
                Place Bid - {load.proNumber || 'TBD'}
              </>
            ) : (
              <>
                <TruckIcon className="h-5 w-5" />
                Book Load - {load.proNumber || 'TBD'}
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {mode === 'bid' 
              ? 'Submit your bid for this load. You will receive email notifications when the admin responds.'
              : 'Complete the booking details below to request this load.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4">
          {/* Load Summary */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <h3 className="font-semibold text-sm">Load Details</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <MapPinIcon className="h-4 w-4 text-green-600" />
                <div>
                  <span className="text-muted-foreground">Origin:</span>
                  <div className="font-medium">{load.origin || 'TBD'}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <MapPinIcon className="h-4 w-4 text-red-600" />
                <div>
                  <span className="text-muted-foreground">Destination:</span>
                  <div className="font-medium">{load.destination || 'TBD'}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4 text-blue-600" />
                <div>
                  <span className="text-muted-foreground">Pickup:</span>
                  <div className="font-medium">
                    {load.pickupDateTime 
                      ? new Date(load.pickupDateTime).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true
                        })
                      : 'TBD'
                    }
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4 text-orange-600" />
                <div>
                  <span className="text-muted-foreground">Delivery:</span>
                  <div className="font-medium">
                    {load.deliveryDateTime 
                      ? new Date(load.deliveryDateTime).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true
                        })
                      : 'TBD'
                    }
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TruckIcon className="h-4 w-4 text-gray-600" />
                <div>
                  <span className="text-muted-foreground">Equipment:</span>
                  <div className="font-medium">{load.equipment || 'TBD'}</div>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="flex flex-col gap-1">
                <Badge variant="outline" className="bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 w-fit">
                  {mode === 'bid' ? 'Starting Rate' : 'Rate'}: ${load.rate?.toLocaleString() || '0'}
                </Badge>
                {load.miles && load.rate && (
                  <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 w-fit text-xs">
                    ${(load.rate / load.miles).toFixed(2)}/mile
                  </Badge>
                )}
              </div>
              <div className="flex flex-col gap-1 text-right">
                {load.weight && (
                  <Badge variant="outline" className="bg-gray-50 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300 w-fit">
                    {load.weight.toLocaleString()} lbs
                  </Badge>
                )}
                {load.miles && (
                  <Badge variant="outline" className="bg-gray-50 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300 w-fit text-xs">
                    {load.miles.toLocaleString()} miles
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Form Content */}
          <form onSubmit={mode === 'bid' ? handleBiddingSubmit : handleBookingSubmit} className="space-y-4">
            {mode === 'bid' ? (
              // Enhanced Bidding Form
              <>
                <div className="space-y-2">
                  <Label htmlFor="bidAmount" className="text-sm font-medium">
                    Your Bid Amount <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <DollarSignIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="bidAmount"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter your bid amount"
                      value={biddingDetails.bidAmount === 0 ? '' : biddingDetails.bidAmount}
                      onChange={(e) => handleInputChange('bidAmount', parseFloat(e.target.value) || 0)}
                      className={`pl-10 ${errors.bidAmount ? 'border-red-500' : 'border-border bg-background'}`}
                      required
                    />
                  </div>
                  {biddingDetails.bidAmount > 0 && load.miles && (
                    <p className="text-sm text-muted-foreground">
                      Rate per mile: ${(biddingDetails.bidAmount / load.miles).toFixed(2)}/mile
                    </p>
                  )}
                  {errors.bidAmount && (
                    <p className="text-sm text-red-500">{errors.bidAmount}</p>
                  )}
                </div>

                {/* Truck Status Section */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="biddingIsTruckEmpty"
                      checked={biddingDetails.isTruckEmpty || false}
                      onCheckedChange={(checked) => handleInputChange('isTruckEmpty', checked as boolean)}
                      className="border-border bg-background focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    />
                    <Label
                      htmlFor="biddingIsTruckEmpty"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground cursor-pointer"
                    >
                      Is your truck currently empty? (Optional)
                    </Label>
                  </div>

                  {biddingDetails.isTruckEmpty && (
                    <div className="space-y-2 ml-6">
                      <Label htmlFor="biddingTruckEmptyLocation" className="text-sm font-medium">
                        Current truck location (Optional)
                      </Label>
                      <Input
                        id="biddingTruckEmptyLocation"
                        placeholder="City, State (e.g., Chicago, IL)"
                        value={biddingDetails.truckEmptyLocation || ''}
                        onChange={(e) => handleInputChange('truckEmptyLocation', e.target.value)}
                        className="border-border bg-background"
                      />
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="biddingEtaToShipper" className="text-sm font-medium">
                      ETA to shipper (Optional)
                    </Label>
                    <Input
                      id="biddingEtaToShipper"
                      type="datetime-local"
                      value={biddingDetails.etaToShipper || ''}
                      onChange={(e) => handleInputChange('etaToShipper', e.target.value)}
                      className="border-border bg-background"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="carrierNotes" className="text-sm font-medium">
                    Additional Notes (Optional)
                  </Label>
                  <Textarea
                    id="carrierNotes"
                    placeholder="Any additional information about your capabilities, timing, etc."
                    value={biddingDetails.carrierNotes || ''}
                    onChange={(e) => handleInputChange('carrierNotes', e.target.value)}
                    className="border-border bg-background"
                    rows={2}
                  />
                </div>

                {/* Compact Bidding Instructions */}
                <div className="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-3">
                  <div className="flex items-start gap-3">
                    <MessageSquareIcon className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Bidding Process
                      </p>
                      <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-0.5">
                        <li>• Reviewed by admin within 24 hours</li>
                        <li>• Email notifications for updates</li>
                        <li>• Admin may accept, decline, or counter</li>
                        <li>• Bids expire after 24 hours if not responded to</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              // Booking Form (Original)
              <>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isTruckEmpty"
                      checked={bookingDetails.isTruckEmpty}
                      onCheckedChange={(checked) => handleInputChange('isTruckEmpty', checked as boolean)}
                      className="border-border bg-background focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    />
                    <Label
                      htmlFor="isTruckEmpty"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground cursor-pointer"
                    >
                      Is your truck currently empty?
                    </Label>
                  </div>

                  {bookingDetails.isTruckEmpty && (
                    <div className="space-y-2">
                      <Label htmlFor="truckEmptyLocation" className="text-sm font-medium">
                        Current truck location <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="truckEmptyLocation"
                        placeholder="City, State (e.g., Chicago, IL)"
                        value={bookingDetails.truckEmptyLocation || ''}
                        onChange={(e) => handleInputChange('truckEmptyLocation', e.target.value)}
                        className={errors.truckEmptyLocation ? 'border-red-500' : 'border-border bg-background'}
                      />
                      {errors.truckEmptyLocation && (
                        <p className="text-sm text-red-500">{errors.truckEmptyLocation}</p>
                      )}
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="etaToShipper" className="text-sm font-medium">
                      ETA to shipper <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="etaToShipper"
                      type="datetime-local"
                      value={bookingDetails.etaToShipper}
                      onChange={(e) => handleInputChange('etaToShipper', e.target.value)}
                      className={errors.etaToShipper ? 'border-red-500' : 'border-border bg-background'}
                    />
                    {errors.etaToShipper && (
                      <p className="text-sm text-red-500">{errors.etaToShipper}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes" className="text-sm font-medium">
                      Additional notes (optional)
                    </Label>
                    <Textarea
                      id="notes"
                      placeholder="Any additional information about your equipment, experience, etc."
                      value={bookingDetails.notes || ''}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      className="border-border bg-background"
                      rows={3}
                    />
                  </div>
                </div>
              </>
            )}
          </form>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isSubmitting || isSubmittingBid}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={mode === 'bid' ? handleBiddingSubmit : handleBookingSubmit}
            disabled={isSubmitting || isSubmittingBid || (mode === 'bid' && loadingProfile)}
            className="bg-primary hover:bg-primary/90"
          >
            {isSubmitting || isSubmittingBid || (mode === 'bid' && loadingProfile) ? (
              <>
                <span className="loading loading-spinner loading-sm mr-2"></span>
                {loadingProfile ? 'Loading Profile...' : (mode === 'bid' ? 'Submitting Bid...' : 'Submitting...')}
              </>
            ) : (
              mode === 'bid' ? 'Submit Bid' : 'Submit Booking Request'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 