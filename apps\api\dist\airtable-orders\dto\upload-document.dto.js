"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadDocumentDto = exports.DocumentType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var DocumentType;
(function (DocumentType) {
    DocumentType["BOL"] = "bol";
    DocumentType["POD"] = "pod";
    DocumentType["INVOICE"] = "invoice";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
class UploadDocumentDto {
    documentType;
    fileUrl;
}
exports.UploadDocumentDto = UploadDocumentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of document being uploaded',
        enum: DocumentType,
        example: DocumentType.BOL
    }),
    (0, class_validator_1.IsEnum)(DocumentType, {
        message: 'documentType must be one of: bol, pod, invoice'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UploadDocumentDto.prototype, "documentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Secure URL of the uploaded file',
        example: 'https://secure-bucket.s3.amazonaws.com/documents/abc123.pdf'
    }),
    (0, class_validator_1.IsUrl)({
        protocols: ['https'],
        require_protocol: true
    }, {
        message: 'fileUrl must be a valid HTTPS URL'
    }),
    (0, class_validator_1.Matches)(/\.(pdf|jpg|jpeg|png|doc|docx)$/i, {
        message: 'File must be a valid document type (pdf, jpg, jpeg, png, doc, docx)'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UploadDocumentDto.prototype, "fileUrl", void 0);
//# sourceMappingURL=upload-document.dto.js.map