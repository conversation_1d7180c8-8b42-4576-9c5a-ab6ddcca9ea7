import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma/prisma.service';

@Injectable()
export class AppService {
  constructor(private prisma: PrismaService) {}

  getHello(): string {
    return 'Hello World!';
  }

  /**
   * @deprecated This method was part of the legacy Clerk system.
   * Use findUserByAirtableId instead.
   */
  async getUserWithOrganization(clerkUserId: string) {
    console.warn('getUserWithOrganization is deprecated - use findUserByAirtableId instead');
    return null;
  }

  /**
   * Find user by Airtable user ID for N8N authentication
   * @param airtableUserId - The Airtable user ID from N8N JWT
   * @returns User data from the database
   */
  async findUserByAirtableId(airtableUserId: string) {
    try {
      // Check if we have a UserProfile table for caching
      const userProfile = await (this.prisma as any).userProfile?.findUnique({
        where: { airtableUserId }
      });

      if (userProfile) {
        return {
          airtableUserId: userProfile.airtableUserId,
          email: userProfile.email,
          firstName: userProfile.firstName,
          lastName: userProfile.lastName,
          companyName: userProfile.companyName,
          mcNumber: userProfile.mcNumber,
          role: userProfile.role
        };
      }

      // Fallback: check legacy User table if UserProfile doesn't exist
      const legacyUser = await this.prisma.user.findFirst({
        where: { email: airtableUserId } // This is a fallback, not ideal
      });

      return legacyUser;
    } catch (error) {
      console.error(`Error finding user by Airtable ID ${airtableUserId}:`, error);
      return null;
    }
  }
}
