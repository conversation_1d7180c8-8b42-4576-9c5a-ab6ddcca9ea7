import { Controller, Get, UseGuards, Request, Logger } from '@nestjs/common';
import { AppService } from './app.service';
import { AuthGuard } from './auth/auth.guard';
import { AuthenticatedRequest } from './auth/authenticated-request.interface';
import { PrismaService } from './prisma/prisma.service';
import { CircuitBreakerService } from './common/services/circuit-breaker.service';

@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(
    private readonly appService: AppService,
    private readonly prismaService: PrismaService,
    private readonly circuitBreakerService: CircuitBreakerService
  ) {}

  @Get('test-simple')
  getSimpleTest() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      message: 'Simple test endpoint working'
    };
  }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  async getHealth() {
    const startTime = Date.now();
    
    try {
      // Simple health check without database for faster response
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: `${responseTime}ms`,
        version: process.env.npm_package_version || 'unknown',
        environment: process.env.NODE_ENV || 'unknown',
        message: 'API server is running'
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: `${Date.now() - startTime}ms`,
        error: error.message
      };
    }
  }

  @Get('health/detailed')
  async getDetailedHealth() {
    const startTime = Date.now();
    
    try {
      // Comprehensive health check with all services
      const dbHealth = await this.prismaService.getDetailedHealthCheck();
      const circuitBreakerHealth = this.circuitBreakerService.getHealthCheck();
      
      const responseTime = Date.now() - startTime;
      const overallStatus = this.determineOverallStatus([dbHealth.status, circuitBreakerHealth.status]);
      
      return {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: `${responseTime}ms`,
        version: process.env.npm_package_version || 'unknown',
        environment: process.env.NODE_ENV || 'unknown',
        services: {
          database: dbHealth,
          circuitBreakers: circuitBreakerHealth
        }
      };
    } catch (error) {
      this.logger.error('Detailed health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: `${Date.now() - startTime}ms`,
        error: error.message
      };
    }
  }

  @Get('health/ready')
  async getReadiness() {
    try {
      // Check if all critical services are ready
      const dbHealth = await this.prismaService.healthCheck();
      
      if (!dbHealth) {
        return {
          status: 'not ready',
          reason: 'Database connection failed'
        };
      }
      
      return {
        status: 'ready',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'not ready',
        reason: error.message
      };
    }
  }

  @Get('health/live')
  getLiveness() {
    // Simple liveness check - if this endpoint responds, the app is alive
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  }

  @Get('health/db')
  async getDatabaseHealth() {
    const dbStatus = await this.prismaService.verifyConnection();
    const isHealthy = await this.prismaService.healthCheck();
    
    return {
      connected: dbStatus.connected,
      healthy: isHealthy,
      error: dbStatus.error,
      timestamp: new Date().toISOString()
    };
  }

  @Get('debug')
  getDebug() {
    // Only show debug info in development or if specifically enabled
    // Temporarily allow in production to debug the issue
    // if (process.env.NODE_ENV === 'production' && !process.env.ENABLE_DEBUG) {
    //   return { error: 'Debug endpoint disabled in production' };
    // }

    const envVars = [
      'NODE_ENV',
      'DATABASE_URL',
      'N8N_JWT_SECRET',
      'N8N_JWT_ISSUER',
      'N8N_BASE_URL',
      'AIRTABLE_API_KEY',
      'AIRTABLE_BASE_ID',
      'AIRTABLE_TABLE_NAME'
    ];

    const envStatus: Record<string, string> = {};
    envVars.forEach(varName => {
      const value = process.env[varName];
      if (value) {
        // Mask sensitive values
        if (varName.includes('SECRET') || varName.includes('KEY') || varName.includes('URL')) {
          envStatus[varName] = `${value.substring(0, 8)}...${value.substring(value.length - 4)}`;
        } else {
          envStatus[varName] = value;
        }
      } else {
        envStatus[varName] = 'NOT_SET';
      }
    });

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      environmentVariables: envStatus,
      allEnvKeys: Object.keys(process.env).filter(key => 
        key.startsWith('N8N_') || 
        key.startsWith('DATABASE_') || 
        key.startsWith('AIRTABLE_') ||
        key.startsWith('VERCEL_') ||
        key === 'NODE_ENV'
      )
    };
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  getProfile(@Request() req: AuthenticatedRequest) {
    return req.user;
  }

  @UseGuards(AuthGuard)
  @Get('debug/user')
  async getDebugUser(@Request() req: AuthenticatedRequest) {
    // Debug endpoint to check user profile sync
    // Temporarily allow in production to debug the issue
    // if (process.env.NODE_ENV === 'production' && !process.env.ENABLE_DEBUG) {
    //   return { error: 'Debug endpoint disabled in production' };
    // }

    const userId = req.user?.airtableUserId;
    if (!userId) {
      return { error: 'No user ID in auth payload' };
    }

    try {
      // Get user from database using N8N auth
      const user = await this.appService.findUserByAirtableId(userId);
      
      return {
        n8nPayload: req.auth,
        userContext: req.user,
        databaseUser: user,
        authSync: {
          hasUserId: !!user?.airtableUserId,
          hasEmail: !!user?.email,
          hasMcNumber: !!user?.mcNumber,
          hasRole: !!user?.role,
        }
      };
    } catch (error) {
      return {
        error: error.message,
        n8nPayload: req.auth,
        userContext: req.user,
        stack: error.stack
      };
    }
  }

  private determineOverallStatus(statuses: string[]): 'healthy' | 'degraded' | 'unhealthy' {
    if (statuses.some(status => status === 'unhealthy')) {
      return 'unhealthy';
    }
    if (statuses.some(status => status === 'degraded')) {
      return 'degraded';
    }
    return 'healthy';
  }
}
