// Simple test function to verify Vercel setup
module.exports = async (req, res) => {
  console.log('[TEST] Simple function invoked');
  console.log('[TEST] Request method:', req.method);
  console.log('[TEST] Request URL:', req.url);
  console.log('[TEST] Node version:', process.version);
  console.log('[TEST] Working directory:', process.cwd());
  console.log('[TEST] Environment:', process.env.NODE_ENV);
  
  return res.status(200).json({ 
    message: 'Test function working!',
    method: req.method,
    url: req.url,
    nodeVersion: process.version,
    cwd: process.cwd(),
    timestamp: new Date().toISOString()
  });
}; 