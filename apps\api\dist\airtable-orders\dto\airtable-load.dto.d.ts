export declare class AirtableLoadWebhookDto {
    airtable_record_id: string;
    status?: string;
    payout?: string;
    origin_city?: string;
    origin_state?: string;
    destination_city?: string;
    destination_state?: string;
    distance?: string;
    pro_number?: string;
    pickup_date_utc?: string;
    delivery_date_utc?: string;
    equipment_required?: string;
    weight_lbs?: number;
    rate?: number;
    temperature?: string;
    target_organizations?: string | string[];
    is_public?: boolean;
    raw_airtable_data?: Record<string, any>;
}
export declare class AirtableLoadDataDto {
    'Record ID': string;
    'Status'?: string;
    'Payout'?: number;
    'Origin City'?: string;
    'Origin State'?: string;
    'Destination City'?: string;
    'Destination State'?: string;
    'Distance (Miles)'?: number;
    'Pro Number'?: string;
}
export declare class AirtableWebhookPayloadDto {
    airtableRecordId: string;
    fields: AirtableLoadDataDto;
}
export declare class AirtableWebhookEventDto {
    payload: AirtableWebhookPayloadDto;
}
