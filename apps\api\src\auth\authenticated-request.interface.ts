import { Request } from 'express';

// N8N JWT payload structure (minimal as per architecture decisions)
export interface N8NJwtPayload {
  id: string;      // Airtable user ID
  email: string;
  role: 'CARRIER' | 'ADMIN';
  mcNumber?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  iat: number;     // Issued at
  exp: number;     // Expiration
}

// Authenticated request interface for controllers
export interface AuthenticatedRequest extends Request {
  user?: {
    airtableUserId: string;
    email: string;
    mcNumber?: string;
    companyName?: string;
    role: 'CARRIER' | 'ADMIN';
    isAdmin: boolean;
  };
  auth?: N8NJwtPayload; // Raw JWT payload for debugging
} 