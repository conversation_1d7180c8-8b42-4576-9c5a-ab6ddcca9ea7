# 🔍 **DEBUG GUIDE: Airtable Load Sync Issues**

**Issue:** Loads pushed from Airtable don't show up on loadboard (even with "Is Public" = true)  
**Priority:** 🔴 **CRITICAL** - Core functionality broken  
**Created:** 2025-01-27  

---

## **🎯 DEBUGGING CHECKLIST**

### **Phase 1: Webhook Reception & Processing**

#### **1.1 Verify Webhook Endpoint is Working**
```bash
# Test webhook endpoint manually
curl -X POST https://api.fcp-portal.com/api/v1/airtable-orders/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "airtable_record_id": "test123",
    "pro_number": "TEST001",
    "status": "Available",
    "is_public": true
  }'
```

**Expected Result:** HTTP 200/201 response  
**If Failed:** Check API deployment, route configuration, authentication

#### **1.2 Check Webhook Logs**
```bash
# Monitor API logs when pushing load from Airtable
# Look for these log patterns:
grep "processLoadWebhook" /var/log/api.log
grep "Webhook received" /var/log/api.log
grep "SERVICE:" /var/log/api.log | grep -i load
```

**Key Questions:**
- [ ] Is the webhook being triggered when you push from Airtable?
- [ ] Are there any error messages in the webhook processing?
- [ ] Is the `processLoadWebhook()` method being called?

#### **1.3 Validate Airtable Webhook Configuration**
**Check in Airtable Automation:**
- [ ] Webhook URL points to: `https://api.fcp-portal.com/api/v1/airtable-orders/webhook`
- [ ] Webhook is triggered on record creation/update
- [ ] All required fields are included in webhook payload
- [ ] "When record matches conditions" includes your test load

---

### **Phase 2: Database Sync Investigation**

#### **2.1 Check Database for Load Record**
```sql
-- Connect to PostgreSQL database and check if load was created
SELECT 
  id,
  airtable_record_id,
  pro_number,
  status,
  is_public,
  is_targeted,
  target_organizations,
  created_at,
  updated_at
FROM loads 
WHERE airtable_record_id = 'YOUR_AIRTABLE_RECORD_ID'
ORDER BY created_at DESC;
```

**Key Questions:**
- [ ] Is the load record created in the database?
- [ ] Are all fields populated correctly?
- [ ] Is `status` set to "Available" or "Booking Requested"?
- [ ] Is `is_public` set to `true`?

#### **2.2 Check Load Filtering Logic**
```typescript
// Test the exact filter used in getAvailableLoads()
// Check this query in database or API logs:

// Expected Airtable filter:
// AND({Synced to API}, OR({Status} = "Available", {Status} = "Booking Requested"))

// Expected database filter:
// WHERE status IN ('Available', 'Booking Requested') 
// AND (is_public = true OR target_organizations IS NOT NULL)
```

**Key Questions:**
- [ ] Does your load meet the filtering criteria?
- [ ] Is "Synced to API" field checked in Airtable?
- [ ] Is Status exactly "Available" (case sensitive)?

---

### **Phase 3: API Response Investigation**

#### **3.1 Test Load Board API Directly**
```bash
# Get JWT token from browser developer tools (Application > Local Storage)
export JWT_TOKEN="your_jwt_token_here"

# Test available loads endpoint
curl -X GET "https://api.fcp-portal.com/api/v1/airtable-orders/available" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Result:** JSON array with load objects  
**Check For:**
- [ ] Is your load in the response?
- [ ] Are organization filtering rules working correctly?
- [ ] Are there any error messages?

#### **3.2 Debug Organization Filtering**
```bash
# Check if organization filtering is affecting results
# Test with admin user vs carrier user

# Admin users should see ALL loads
# Carrier users should see public + targeted loads for their org
```

**Key Questions:**
- [ ] What organization is your test user assigned to?
- [ ] Are you testing with admin or carrier account?
- [ ] Does organization name in Clerk match exactly with Airtable targeting?

---

### **Phase 4: Frontend Display Investigation**

#### **4.1 Check Frontend Load Fetching**
```javascript
// Open browser dev tools on loadboard page
// Check Network tab for API calls

// Look for:
// GET /api/v1/airtable-orders/available
// Response status and data

// Check Console for errors:
console.log("Loads fetched:", loads);
```

#### **4.2 Verify Frontend Filtering**
```javascript
// Check if frontend has additional filtering logic
// Look in: apps/web/src/app/org/[orgId]/loadboard/page.tsx

// Search for:
// - Additional filter conditions
// - Organization-based filtering
// - Status-based filtering
// - Date-based filtering
```

---

### **Phase 5: Step-by-Step Debugging Process**

#### **Step 1: Create Test Load in Airtable**
```
1. Go to your Airtable base
2. Create new record with:
   - Order ID: "DEBUG001"
   - Status: "Available"
   - Is Public: ✅ (checked)
   - Synced to API: ✅ (checked)
   - Rate to Carrier: 2500
   - Pickup City Lookup: "Los Angeles"
   - Delivery City Lookup: "Phoenix"
   - All other required fields filled
```

#### **Step 2: Monitor Webhook Processing**
```bash
# Watch API logs in real-time
tail -f /var/log/api.log | grep -i "debug001\|webhook\|processLoad"

# Or check Vercel function logs if using Vercel
```

#### **Step 3: Verify Database Creation**
```sql
-- Check if record was created
SELECT * FROM loads WHERE pro_number = 'DEBUG001';

-- If not found, check for any loads created recently
SELECT * FROM loads ORDER BY created_at DESC LIMIT 5;
```

#### **Step 4: Test API Response**
```bash
# Test with your user token
curl -X GET "https://api.fcp-portal.com/api/v1/airtable-orders/available" \
  -H "Authorization: Bearer $YOUR_TOKEN" | jq '.[] | select(.proNumber == "DEBUG001")'
```

#### **Step 5: Check Frontend**
```
1. Refresh loadboard page
2. Open Developer Tools > Network tab
3. Look for the API call and response
4. Check if DEBUG001 load appears in the data
5. Verify it's not filtered out by frontend logic
```

---

### **Phase 6: Common Issues & Solutions**

#### **6.1 Webhook Not Triggered**
**Symptoms:** No logs, no database record  
**Solutions:**
- Check Airtable automation is active
- Verify webhook URL is correct
- Test webhook endpoint manually
- Check if load meets trigger conditions

#### **6.2 Webhook Fails Processing**
**Symptoms:** Logs show errors, no database record  
**Solutions:**
- Check required fields are present in payload
- Verify DTO validation rules
- Check for authentication issues
- Look for TypeScript compilation errors

#### **6.3 Database Record Created But Not Visible**
**Symptoms:** Database has record, API doesn't return it  
**Solutions:**
- Check status filtering (`Available` vs `available`)
- Verify `is_public` field is `true`
- Check organization filtering logic
- Verify `synced_to_api` equivalent in database

#### **6.4 API Returns Load But Frontend Doesn't Show**
**Symptoms:** API response includes load, loadboard empty  
**Solutions:**
- Check frontend filtering logic
- Verify React state updates
- Check for JavaScript errors in console
- Look for CSS hiding issues

#### **6.5 Organization Targeting Issues**
**Symptoms:** Load visible to some users but not others  
**Solutions:**
- Check user organization assignment in Clerk
- Verify organization name matching (case sensitive)
- Test with admin user (should see all loads)
- Check `target_organizations` field in database

---

### **Phase 7: Debug Commands & Endpoints**

#### **7.1 API Debug Endpoints**
```bash
# Health check
GET /api/v1/health

# Available loads (with auth)
GET /api/v1/airtable-orders/available
Authorization: Bearer {token}

# Specific load by Airtable ID (if endpoint exists)
GET /api/v1/airtable-orders/{airtableRecordId}

# User profile (to check organization)
GET /api/v1/carrier-profiles/me
```

#### **7.2 Database Debug Queries**
```sql
-- Check recent loads
SELECT id, airtable_record_id, pro_number, status, is_public, created_at 
FROM loads 
ORDER BY created_at DESC 
LIMIT 10;

-- Check load filtering criteria
SELECT COUNT(*) as total_loads FROM loads;
SELECT COUNT(*) as available_loads FROM loads WHERE status IN ('Available', 'Booking Requested');
SELECT COUNT(*) as public_loads FROM loads WHERE is_public = true;

-- Check user organization data
SELECT clerk_user_id, org_name, role FROM users WHERE org_name IS NOT NULL;
```

#### **7.3 Log Monitoring Commands**
```bash
# Monitor webhook processing
tail -f /var/log/api.log | grep "processLoadWebhook\|SERVICE:"

# Monitor Airtable integration
tail -f /var/log/api.log | grep "airtable\|webhook"

# Monitor load filtering
tail -f /var/log/api.log | grep "getAvailableLoads\|Load.*filtering"
```

---

### **Phase 8: Expected Data Flow**

#### **8.1 Normal Load Sync Flow**
```
1. Load created/updated in Airtable
   ↓
2. Airtable automation triggers webhook
   ↓
3. POST /api/v1/airtable-orders/webhook
   ↓
4. processLoadWebhook() method called
   ↓
5. Data mapped and validated via DTO
   ↓
6. Load record created/updated in database
   ↓
7. Frontend calls GET /api/v1/airtable-orders/available
   ↓
8. getAvailableLoads() filters and returns loads
   ↓
9. Frontend displays loads on loadboard
```

#### **8.2 Failure Points to Check**
- [ ] **Step 1-2:** Airtable automation configuration
- [ ] **Step 3:** API endpoint routing and deployment
- [ ] **Step 4:** Webhook handler method execution
- [ ] **Step 5:** DTO validation and field mapping
- [ ] **Step 6:** Database connection and schema
- [ ] **Step 7:** Authentication and API routing
- [ ] **Step 8:** Load filtering logic
- [ ] **Step 9:** Frontend data processing and display

---

### **Phase 9: Quick Diagnostic Script**

```bash
#!/bin/bash
# Quick diagnostic for Airtable load sync issues

echo "🔍 Airtable Load Sync Diagnostic"
echo "==============================="

# 1. Test API health
echo "1. Testing API health..."
curl -s https://api.fcp-portal.com/api/v1/health | jq .

# 2. Test webhook endpoint
echo "2. Testing webhook endpoint..."
curl -s -X POST https://api.fcp-portal.com/api/v1/airtable-orders/webhook \
  -H "Content-Type: application/json" \
  -d '{"test": true}' | head -n 1

# 3. Check if loads exist in database (requires database access)
echo "3. Recent loads in database:"
echo "   (Check manually with SQL query)"

# 4. Test available loads API (requires token)
echo "4. Testing available loads API:"
echo "   curl -H 'Authorization: Bearer YOUR_TOKEN' https://api.fcp-portal.com/api/v1/airtable-orders/available"

echo ""
echo "📋 Next Steps:"
echo "   1. Replace YOUR_TOKEN with actual JWT token"
echo "   2. Check webhook logs for your specific load"
echo "   3. Verify Airtable automation is active"
echo "   4. Test with a simple load record"
```

---

### **🎯 SUCCESS CRITERIA**

**✅ Load Sync Working When:**
- [ ] Webhook receives Airtable data successfully
- [ ] Database record is created with correct fields
- [ ] API returns load in available loads response
- [ ] Frontend displays load on loadboard
- [ ] Load appears for correct users based on organization rules

**🔧 Key Files to Check:**
- `apps/api/src/airtable-orders/airtable-orders.service.ts` (webhook processing)
- `apps/api/src/airtable-orders/dto/airtable-load-webhook.dto.ts` (data validation)
- `apps/web/src/app/org/[orgId]/loadboard/page.tsx` (frontend display)
- Database `loads` table (data persistence)
- Airtable automation configuration (webhook trigger)

---

**💡 Pro Tip:** Start with the simplest possible test load and work through each step of the pipeline to isolate where the failure occurs. 