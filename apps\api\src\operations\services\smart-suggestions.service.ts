import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { PatternAnalysisService } from './pattern-analysis.service';

interface SuggestionFeedback {
  userId: string;
  suggestionType: string;
  suggestedValue: any;
  actualValue: any;
  accepted: boolean;
  timestamp: Date;
  orderContext: {
    laneId: string;
    equipment: string;
    weight?: number;
  };
}

interface LearnedInsight {
  type: 'rate_adjustment' | 'equipment_preference' | 'timing_preference' | 'validation_rule';
  userId?: string; // null for global insights
  pattern: string;
  confidence: number;
  usageCount: number;
  lastUpdated: Date;
}

@Injectable()
export class SmartSuggestionsService {
  private readonly logger = new Logger(SmartSuggestionsService.name);
  private healthCheckCache: { isHealthy: boolean; lastCheck: number } = { isHealthy: true, lastCheck: 0 };
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private readonly DB_TIMEOUT = 5000; // 5 second database timeout

  constructor(
    private readonly prisma: PrismaService,
    private readonly patternAnalysis: PatternAnalysisService
  ) {}

  // Database health check with caching
  private async checkDatabaseHealth(): Promise<boolean> {
    const now = Date.now();
    
    // Return cached result if recent
    if (now - this.healthCheckCache.lastCheck < this.HEALTH_CHECK_INTERVAL) {
      return this.healthCheckCache.isHealthy;
    }

    try {
      // Simple health check query with timeout
      const startTime = Date.now();
      await Promise.race([
        this.prisma.$queryRaw`SELECT 1`,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Database health check timeout')), this.DB_TIMEOUT)
        )
      ]);
      
      const responseTime = Date.now() - startTime;
      this.healthCheckCache = { isHealthy: true, lastCheck: now };
      this.logger.log(`Database health check passed in ${responseTime}ms`);
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error.message);
      this.healthCheckCache = { isHealthy: false, lastCheck: now };
      return false;
    }
  }

  // Enhanced database operation with health check and timeout
  private async safeDbOperation<T>(operation: () => Promise<T>, fallback: T, operationName: string): Promise<T> {
    try {
      // Check database health first
      const isHealthy = await this.checkDatabaseHealth();
      if (!isHealthy) {
        this.logger.warn(`Database unhealthy, using fallback for ${operationName}`);
        return fallback;
      }

      // Execute operation with timeout
      const result = await Promise.race([
        operation(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error(`${operationName} operation timeout`)), this.DB_TIMEOUT)
        )
      ]);

      return result;
    } catch (error) {
      this.logger.error(`Database operation failed for ${operationName}:`, error.message);
      return fallback;
    }
  }

  /**
   * Get comprehensive smart suggestions for order creation
   */
  async getOrderSuggestions(orderContext: {
    originCity: string;
    originState: string;
    destinationCity: string;
    destinationState: string;
    userId: string;
    currentValues?: any;
  }) {
    this.logger.log(`[DEBUG] Generating smart suggestions for user ${orderContext.userId}`);
    this.logger.log(`[DEBUG] Lane: ${orderContext.originCity}, ${orderContext.originState} → ${orderContext.destinationCity}, ${orderContext.destinationState}`);
    this.logger.log(`[DEBUG] Current values: ${JSON.stringify(orderContext.currentValues)}`);

    const fallbackResult = {
      suggestions: [],
      smartDefaults: {},
      confidence: 0,
      metadata: { 
        error: 'Service temporarily unavailable', 
        generatedAt: new Date(),
        dataPoints: 0,
        learningActive: false
      }
    };

    try {
      // Get base suggestions from pattern analysis with protection
      this.logger.log('[DEBUG] Step 1: Getting base suggestions from pattern analysis...');
      const baseSuggestions = await this.safeDbOperation(
        () => this.patternAnalysis.generateSmartSuggestions(
          orderContext.originCity,
          orderContext.originState,
          orderContext.destinationCity,
          orderContext.destinationState,
          orderContext.userId,
          orderContext.currentValues
        ),
        [],
        'pattern analysis'
      );
      this.logger.log(`[DEBUG] Step 1 Complete: Got ${baseSuggestions.length} base suggestions`);

      // Enhanced suggestions with learned insights (in-memory operation, faster)
      this.logger.log('[DEBUG] Step 2: Enhancing with learned insights...');
      const enhancedSuggestions = await this.enhanceWithLearnings(baseSuggestions, orderContext);
      this.logger.log(`[DEBUG] Step 2 Complete: Enhanced ${enhancedSuggestions.length} suggestions`);

      // Apply real-time market adjustments (in-memory operation)
      this.logger.log('[DEBUG] Step 3: Applying market adjustments...');
      const marketAdjustedSuggestions = await this.applyMarketAdjustments(enhancedSuggestions);
      this.logger.log(`[DEBUG] Step 3 Complete: Market adjusted ${marketAdjustedSuggestions.length} suggestions`);

      // Generate smart defaults with database protection
      this.logger.log('[DEBUG] Step 4: Generating smart defaults...');
      const smartDefaults = await this.generateSmartDefaults(orderContext);
      this.logger.log(`[DEBUG] Step 4 Complete: Generated ${Object.keys(smartDefaults).length} smart defaults`);

      const confidence = this.calculateOverallConfidence(marketAdjustedSuggestions);
      
      const result = {
        suggestions: marketAdjustedSuggestions,
        smartDefaults,
        confidence,
        metadata: {
          generatedAt: new Date(),
          dataPoints: baseSuggestions.length,
          learningActive: true,
          databaseHealthy: this.healthCheckCache.isHealthy
        }
      };

      this.logger.log(`[DEBUG] SUCCESS: Generated ${result.suggestions.length} suggestions with ${(confidence * 100).toFixed(1)}% confidence`);
      return result;

    } catch (error) {
      this.logger.error('[ERROR] Error generating order suggestions:', error);
      this.logger.error('[ERROR] Stack trace:', error.stack);
      
      // Return graceful fallback instead of throwing
      this.logger.log('[DEBUG] Returning fallback result due to error');
      return fallbackResult;
    }
  }

  /**
   * Record user feedback on suggestions to improve future recommendations
   */
  async recordSuggestionFeedback(feedback: {
    userId: string;
    orderId: string;
    suggestions: any[];
    actualValues: any;
    orderContext: any;
  }) {
    this.logger.log(`Recording suggestion feedback for user ${feedback.userId}`);

    try {
      const feedbackRecords: SuggestionFeedback[] = [];

      // Compare each suggestion with actual values
      feedback.suggestions.forEach(suggestion => {
        let actualValue: any = null;
        let accepted = false;

        switch (suggestion.type) {
          case 'rate':
            actualValue = feedback.actualValues.rate;
            accepted = actualValue && Math.abs(actualValue - suggestion.suggestion.rate) < (suggestion.suggestion.rate * 0.1);
            break;
          case 'equipment':
            actualValue = feedback.actualValues.equipmentRequired;
            accepted = actualValue === suggestion.suggestion.equipment;
            break;
          case 'weight':
            actualValue = feedback.actualValues.weightLbs;
            accepted = actualValue && Math.abs(actualValue - suggestion.suggestion.weight) < (suggestion.suggestion.weight * 0.2);
            break;
          case 'timing':
            // Compare pickup to delivery date difference
            if (feedback.actualValues.pickupDate && feedback.actualValues.deliveryDate) {
              const actualDays = this.calculateDaysDifference(
                feedback.actualValues.pickupDate,
                feedback.actualValues.deliveryDate
              );
              accepted = Math.abs(actualDays - suggestion.suggestion.transitDays) <= 1;
              actualValue = actualDays;
            }
            break;
        }

        feedbackRecords.push({
          userId: feedback.userId,
          suggestionType: suggestion.type,
          suggestedValue: suggestion.suggestion,
          actualValue,
          accepted,
          timestamp: new Date(),
          orderContext: {
            laneId: `${feedback.orderContext.originState}_${feedback.orderContext.destinationState}`,
            equipment: feedback.actualValues.equipmentRequired,
            weight: feedback.actualValues.weightLbs
          }
        });
      });

      // Store feedback in database (using rawAirtableData field for now)
      await this.storeFeedbackData(feedback.orderId, feedbackRecords);

      // Update learned insights based on feedback
      await this.updateLearnedInsights(feedbackRecords);

      this.logger.log(`Recorded ${feedbackRecords.length} feedback items`);

    } catch (error) {
      this.logger.error('Error recording suggestion feedback:', error);
    }
  }

  /**
   * Get AI-powered auto-completion suggestions as user types
   */
  async getAutoCompleteSuggestions(field: string, partialValue: string, context: any) {
    this.logger.log(`Getting auto-complete suggestions for ${field}: "${partialValue}"`);

    try {
      switch (field) {
        case 'poNumber':
          return this.suggestPONumbers(partialValue, context.userId);
        case 'rate':
          return this.suggestRates(partialValue, context);
        case 'weight':
          return this.suggestWeights(partialValue, context);
        case 'notes':
          return this.suggestNotes(partialValue, context);
        default:
          return [];
      }
    } catch (error) {
      this.logger.error(`Error getting auto-complete for ${field}:`, error);
      return [];
    }
  }

  /**
   * Validate order data and provide intelligent warnings
   */
  async validateOrderData(orderData: any, context: any) {
    this.logger.log('[VALIDATION-DEBUG] Starting order data validation');
    this.logger.log(`[VALIDATION-DEBUG] Order data: ${JSON.stringify(orderData)}`);
    this.logger.log(`[VALIDATION-DEBUG] Context: ${JSON.stringify(context)}`);

    const validationResults = {
      isValid: true,
      warnings: [] as any[],
      suggestions: [] as any[],
      criticalIssues: [] as any[]
    };

    try {
      // Business rule validations (no database needed)
      this.logger.log('[VALIDATION-DEBUG] Step 1: Running business rule validations...');
      await this.validateBusinessRules(orderData, validationResults);
      this.logger.log(`[VALIDATION-DEBUG] Step 1 Complete: ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);

      // Historical pattern validations (database protected)
      this.logger.log('[VALIDATION-DEBUG] Step 2: Running historical pattern validations...');
      await this.safeDbOperation(
        () => this.validateAgainstPatterns(orderData, context, validationResults),
        undefined,
        'pattern validation'
      );
      this.logger.log(`[VALIDATION-DEBUG] Step 2 Complete: ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);

      // Market condition validations (no database needed)
      this.logger.log('[VALIDATION-DEBUG] Step 3: Running market condition validations...');
      await this.validateMarketConditions(orderData, validationResults);
      this.logger.log(`[VALIDATION-DEBUG] Step 3 Complete: ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);

      this.logger.log(`[VALIDATION-DEBUG] SUCCESS: Validation complete - ${validationResults.warnings.length} warnings, ${validationResults.criticalIssues.length} critical issues`);

    } catch (error) {
      this.logger.error('[VALIDATION-ERROR] Error in smart validation:', error);
      this.logger.error('[VALIDATION-ERROR] Stack trace:', error.stack);
      validationResults.criticalIssues.push({
        type: 'system_error',
        message: 'Validation system temporarily unavailable',
        details: error.message
      });
    }

    // Set isValid based on critical issues
    validationResults.isValid = validationResults.criticalIssues.length === 0;

    return validationResults;
  }

  // Private helper methods

  private async enhanceWithLearnings(baseSuggestions: any[], context: any) {
    // Get user-specific learned insights
    const userInsights = await this.getUserLearnedInsights(context.userId);
    
    return baseSuggestions.map(suggestion => {
      const relevantInsight = userInsights.find(insight => 
        insight.type.includes(suggestion.type) && insight.confidence > 0.6
      );

      if (relevantInsight) {
        // Adjust suggestion based on learned insight
        return {
          ...suggestion,
          confidence: Math.min(suggestion.confidence * 1.2, 1.0), // Boost confidence
          suggestion: this.adjustSuggestionWithInsight(suggestion.suggestion, relevantInsight),
          reasoning: `${suggestion.reasoning} (Enhanced with your preferences)`
        };
      }

      return suggestion;
    });
  }

  private async applyMarketAdjustments(suggestions: any[]) {
    // Apply market-based adjustments (fuel prices, seasonal demand, etc.)
    // For Phase 1+, we'll implement basic seasonal adjustments
    
    const currentMonth = new Date().getMonth() + 1;
    const seasonalFactors = {
      // Winter months typically have higher rates
      12: 1.1, 1: 1.1, 2: 1.05,
      // Spring/Summer normal rates
      3: 1.0, 4: 1.0, 5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
      // Fall peak season
      9: 1.05, 10: 1.1, 11: 1.15
    };

    const seasonalFactor = seasonalFactors[currentMonth] || 1.0;

    return suggestions.map(suggestion => {
      if (suggestion.type === 'rate' && seasonalFactor !== 1.0) {
        return {
          ...suggestion,
          suggestion: {
            ...suggestion.suggestion,
            rate: Math.round(suggestion.suggestion.rate * seasonalFactor),
            seasonalAdjustment: seasonalFactor
          },
          reasoning: `${suggestion.reasoning} (Seasonal adjustment: ${((seasonalFactor - 1) * 100).toFixed(0)}%)`
        };
      }
      return suggestion;
    });
  }

  private async generateSmartDefaults(context: any) {
    // Generate smart default values for form fields
    const defaults: any = {};

    try {
      // Get user preferences
      const userPrefs = await this.patternAnalysis.analyzeUserPreferences(context.userId);
      
      if (userPrefs && userPrefs.confidenceScore > 0.5) {
        // Equipment default
        const preferredEquipment = Object.entries(userPrefs.preferredEquipment)
          .sort(([,a], [,b]) => (b as number) - (a as number))[0];
        if (preferredEquipment) {
          defaults.equipmentRequired = preferredEquipment[0];
        }

        // Weight default based on equipment
        if (defaults.equipmentRequired && userPrefs.preferredWeightRanges[defaults.equipmentRequired]) {
          const weightRange = userPrefs.preferredWeightRanges[defaults.equipmentRequired];
          defaults.weightLbs = Math.round((weightRange.min + weightRange.max) / 2);
        }
      }

      // Auto-generate PO number with smart incrementing
      defaults.poNumber = await this.generateSmartPONumber(context.userId);

    } catch (error) {
      this.logger.error('Error generating smart defaults:', error);
    }

    return defaults;
  }

  private calculateOverallConfidence(suggestions: any[]): number {
    if (suggestions.length === 0) return 0;
    
    const avgConfidence = suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length;
    return Math.round(avgConfidence * 100) / 100;
  }

  private calculateDaysDifference(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  }

  private async storeFeedbackData(orderId: string, feedbackRecords: SuggestionFeedback[]) {
    // Store feedback in the order's rawAirtableData for now
    // In production, this would be a separate feedback table
    
    await this.prisma.load.update({
      where: { id: orderId },
      data: {
        rawAirtableData: {
          suggestionFeedback: feedbackRecords.map(record => ({
            userId: record.userId,
            suggestionType: record.suggestionType,
            suggestedValue: record.suggestedValue,
            actualValue: record.actualValue,
            accepted: record.accepted,
            timestamp: record.timestamp.toISOString(),
            orderContext: record.orderContext
          }))
        }
      }
    });
  }

  private async updateLearnedInsights(feedbackRecords: SuggestionFeedback[]) {
    // Update learned insights based on feedback patterns
    // This would typically involve ML model updates or statistical analysis
    
    feedbackRecords.forEach(record => {
      if (record.accepted) {
        // Positive feedback - reinforce this pattern
        this.logger.log(`Positive feedback: ${record.suggestionType} suggestion accepted`);
      } else {
        // Negative feedback - learn from the deviation
        this.logger.log(`Negative feedback: ${record.suggestionType} suggestion rejected, actual: ${JSON.stringify(record.actualValue)}`);
      }
    });
  }

  private async getUserLearnedInsights(userId: string): Promise<LearnedInsight[]> {
    // Get user-specific learned insights
    // For Phase 1+, return mock insights based on stored patterns
    
    return [
      {
        type: 'rate_adjustment',
        userId,
        pattern: 'typically_adjusts_rates_up_by_5_percent',
        confidence: 0.8,
        usageCount: 15,
        lastUpdated: new Date()
      }
    ];
  }

  private adjustSuggestionWithInsight(suggestion: any, insight: LearnedInsight) {
    // Adjust suggestion based on learned insight
    
    if (insight.type === 'rate_adjustment' && suggestion.rate) {
      // Apply learned rate adjustment pattern
      return {
        ...suggestion,
        rate: Math.round(suggestion.rate * 1.05) // 5% adjustment example
      };
    }

    return suggestion;
  }

  private async suggestPONumbers(partial: string, userId: string): Promise<string[]> {
    // Suggest PO numbers based on user's patterns and format
    const suggestions: string[] = [];
    
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    if (partial.startsWith('PO-')) {
      for (let i = 1; i <= 5; i++) {
        suggestions.push(`PO-${year}${month}${day}-${String(i).padStart(3, '0')}`);
      }
    }
    
    return suggestions.filter(s => s.toLowerCase().includes(partial.toLowerCase()));
  }

  private async suggestRates(partial: string, context: any): Promise<number[]> {
    // Suggest rates based on lane patterns
    const lanePattern = await this.patternAnalysis.analyzeLanePatterns(
      context.originCity, context.originState,
      context.destinationCity, context.destinationState
    );

    if (!lanePattern) return [];

    const baseRate = lanePattern.medianRate;
    return [
      Math.round(baseRate * 0.95),
      baseRate,
      Math.round(baseRate * 1.05),
      Math.round(baseRate * 1.1)
    ].filter(rate => rate.toString().includes(partial));
  }

  private async suggestWeights(partial: string, context: any): Promise<number[]> {
    // Suggest common weights for the equipment type
    const commonWeights = {
      'Dry Van': [25000, 35000, 45000],
      'Reefer': [20000, 30000, 40000],
      'Flatbed': [35000, 45000, 48000]
    };

    const equipment = context.equipmentRequired || 'Dry Van';
    const weights = commonWeights[equipment] || commonWeights['Dry Van'];
    
    return weights.filter(weight => weight.toString().includes(partial));
  }

  private async suggestNotes(partial: string, context: any): Promise<string[]> {
    // Suggest common notes based on equipment and patterns
    const commonNotes = [
      'Handle with care - fragile cargo',
      'Temperature sensitive - maintain cold chain',
      'Call before delivery',
      'Delivery appointment required',
      'Dock high loading/unloading',
      'Inside delivery required'
    ];

    return commonNotes.filter(note => 
      note.toLowerCase().includes(partial.toLowerCase())
    );
  }

  private async generateSmartPONumber(userId: string): Promise<string> {
    // Generate smart PO number based on user patterns and ensure uniqueness
    
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    // Find the next available number for today
    let counter = 1;
    let poNumber: string;
    
    do {
      poNumber = `PO-${year}${month}${day}-${String(counter).padStart(3, '0')}`;
      counter++;
      
      // Check if PO number already exists
      const existing = await this.prisma.load.findFirst({
        where: { proNumber: poNumber }
      });
      
      if (!existing) break;
      
    } while (counter <= 999);
    
    return poNumber;
  }

  private async validateBusinessRules(orderData: any, results: any) {
    // Validate against business rules
    
    // Rate validation
    if (orderData.rate && orderData.rate < 100) {
      results.warnings.push({
        type: 'low_rate',
        message: 'Rate appears unusually low',
        suggestion: 'Double-check rate calculation'
      });
    }

    // Weight validation
    if (orderData.weightLbs && orderData.equipmentRequired) {
      const maxWeights = {
        'Dry Van': 48000,
        'Reefer': 45000,
        'Flatbed': 48000
      };
      
      const maxWeight = maxWeights[orderData.equipmentRequired];
      if (maxWeight && orderData.weightLbs > maxWeight) {
        results.criticalIssues.push({
          type: 'overweight',
          message: `Weight exceeds capacity for ${orderData.equipmentRequired}`,
          maxAllowed: maxWeight
        });
        results.isValid = false;
      }
    }
  }

  private async validateAgainstPatterns(orderData: any, context: any, results: any) {
    // Validate against historical patterns
    
    const lanePattern = await this.patternAnalysis.analyzeLanePatterns(
      context.originCity, context.originState,
      context.destinationCity, context.destinationState
    );

    if (lanePattern && orderData.rate) {
      if (orderData.rate < lanePattern.rateRange.min * 0.7) {
        results.warnings.push({
          type: 'rate_below_market',
          message: 'Rate is significantly below market average',
          marketRange: lanePattern.rateRange
        });
      }
    }
  }

  private async validateMarketConditions(orderData: any, results: any) {
    // Validate against current market conditions
    // For Phase 1+, implement basic seasonal validations
    
    const currentMonth = new Date().getMonth() + 1;
    
    // Peak season warning (Nov-Dec)
    if ([11, 12].includes(currentMonth)) {
      results.suggestions.push({
        type: 'peak_season',
        message: 'Peak shipping season - consider rate premiums and extended transit times',
        impact: 'Expect 10-15% higher rates and potential delays'
      });
    }
  }
} 