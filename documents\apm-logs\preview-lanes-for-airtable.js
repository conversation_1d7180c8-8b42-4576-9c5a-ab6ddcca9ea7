// Preview Lanes for Airtable - Shows what will be synced
// This script generates the lane data and shows you exactly what to create in Airtable

const fs = require('fs');
const path = require('path');

// State-to-state distance estimation (for quick calculations)
function calculateQuickDistance(originState, destinationState) {
  // Simplified state center coordinates for distance calculation
  const stateCoords = {
    'AL': { lat: 32.377716, lng: -86.300568 },
    'AK': { lat: 64.068, lng: -152.2782 },
    'AZ': { lat: 34.048927, lng: -111.093735 },
    'AR': { lat: 34.736009, lng: -92.331122 },
    'CA': { lat: 36.778261, lng: -119.417932 },
    'CO': { lat: 39.550051, lng: -105.782067 },
    'CT': { lat: 41.767, lng: -72.677 },
    'DE': { lat: 39.161921, lng: -75.526755 },
    'FL': { lat: 27.766279, lng: -81.686783 },
    'GA': { lat: 33.76, lng: -84.39 },
    'HI': { lat: 21.30895, lng: -157.826182 },
    'ID': { lat: 44.2394, lng: -114.5103 },
    'IL': { lat: 40.349457, lng: -88.986137 },
    'IN': { lat: 39.790942, lng: -86.147685 },
    'IA': { lat: 42.032974, lng: -93.581543 },
    'KS': { lat: 38.572954, lng: -98.580696 },
    'KY': { lat: 37.608, lng: -84.86311 },
    'LA': { lat: 30.45809, lng: -91.140229 },
    'ME': { lat: 45.367584, lng: -68.972168 },
    'MD': { lat: 39.045923, lng: -76.641271 },
    'MA': { lat: 42.2352, lng: -71.0275 },
    'MI': { lat: 44.182205, lng: -84.506836 },
    'MN': { lat: 46.39241, lng: -94.63623 },
    'MS': { lat: 32.354668, lng: -89.398528 },
    'MO': { lat: 38.572954, lng: -92.60376 },
    'MT': { lat: 47.042418, lng: -109.633835 },
    'NE': { lat: 41.492537, lng: -99.901813 },
    'NV': { lat: 38.544907, lng: -117.022027 },
    'NH': { lat: 43.220093, lng: -71.549896 },
    'NJ': { lat: 40.221741, lng: -74.756138 },
    'NM': { lat: 34.307144, lng: -106.018066 },
    'NY': { lat: 42.659829, lng: -75.615 },
    'NC': { lat: 35.771, lng: -78.638 },
    'ND': { lat: 47.650589, lng: -100.437012 },
    'OH': { lat: 40.367474, lng: -82.996216 },
    'OK': { lat: 35.482309, lng: -97.534994 },
    'OR': { lat: 44.931109, lng: -123.029159 },
    'PA': { lat: 40.269789, lng: -76.875613 },
    'RI': { lat: 41.82355, lng: -71.422132 },
    'SC': { lat: 33.836082, lng: -81.163727 },
    'SD': { lat: 44.367966, lng: -100.336378 },
    'TN': { lat: 35.771, lng: -86.25 },
    'TX': { lat: 31.106, lng: -97.6475 },
    'UT': { lat: 39.161921, lng: -111.313726 },
    'VT': { lat: 44.26639, lng: -72.580536 },
    'VA': { lat: 37.54, lng: -78.83 },
    'WA': { lat: 47.042418, lng: -122.893077 },
    'WV': { lat: 38.349497, lng: -81.633294 },
    'WI': { lat: 44.78444, lng: -89.814278 },
    'WY': { lat: 43.07424, lng: -107.290284 }
  };

  const origin = stateCoords[originState];
  const destination = stateCoords[destinationState];

  if (!origin || !destination) {
    return 500; // Default fallback distance
  }

  // Calculate distance using Haversine formula
  const R = 3959; // Earth's radius in miles
  const dLat = (destination.lat - origin.lat) * Math.PI / 180;
  const dLng = (destination.lng - origin.lng) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(origin.lat * Math.PI / 180) * Math.cos(destination.lat * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return Math.round(distance);
}

// Calculate estimated duration based on miles
function calculateEstimatedDuration(miles) {
  const hoursPerDay = 11; // DOT driving hours limit
  const avgSpeedMph = 55; // Average highway speed including stops
  
  const totalHours = miles / avgSpeedMph;
  const days = Math.ceil(totalHours / hoursPerDay);
  
  if (days === 1) {
    return `${Math.ceil(totalHours)} hours`;
  } else {
    return `${days} days`;
  }
}

// Sample lanes (from app's screenshots and common freight corridors)
function generateSampleLanes() {
  console.log('🎯 Generating sample lanes (matching your app display)...');
  
  const sampleLaneData = [
    // From the screenshots you shared
    { originCity: 'Chicopee', originState: 'MA', destinationCity: 'Londonderry', destinationState: 'NH', loadCount: 12 },
    { originCity: 'Chicopee', originState: 'MA', destinationCity: 'Montgomery', destinationState: 'NY', loadCount: 8 },
    { originCity: 'Edwardsville', originState: 'KS', destinationCity: 'Rialto', destinationState: 'CA', loadCount: 15 },
    { originCity: 'Edwardsville', originState: 'KS', destinationCity: 'Cedar Falls', destinationState: 'IA', loadCount: 6 },
    
    // Additional common freight lanes
    { originCity: 'Atlanta', originState: 'GA', destinationCity: 'Miami', destinationState: 'FL', loadCount: 25 },
    { originCity: 'Chicago', originState: 'IL', destinationCity: 'Dallas', destinationState: 'TX', loadCount: 30 },
    { originCity: 'Los Angeles', originState: 'CA', destinationCity: 'Phoenix', destinationState: 'AZ', loadCount: 22 },
    { originCity: 'New York', originState: 'NY', destinationCity: 'Boston', destinationState: 'MA', loadCount: 18 },
    { originCity: 'Denver', originState: 'CO', destinationCity: 'Salt Lake City', destinationState: 'UT', loadCount: 10 },
    { originCity: 'Houston', originState: 'TX', destinationCity: 'New Orleans', destinationState: 'LA', loadCount: 14 },
    { originCity: 'Seattle', originState: 'WA', destinationCity: 'Portland', destinationState: 'OR', loadCount: 16 },
    { originCity: 'Detroit', originState: 'MI', destinationCity: 'Cleveland', destinationState: 'OH', loadCount: 11 },
    { originCity: 'Memphis', originState: 'TN', destinationCity: 'Nashville', destinationState: 'TN', loadCount: 13 },
    { originCity: 'Kansas City', originState: 'MO', destinationCity: 'Oklahoma City', destinationState: 'OK', loadCount: 9 },
    { originCity: 'Las Vegas', originState: 'NV', destinationCity: 'San Diego', destinationState: 'CA', loadCount: 17 },
    
    // Cross-country high-volume lanes
    { originCity: 'Los Angeles', originState: 'CA', destinationCity: 'Atlanta', destinationState: 'GA', loadCount: 35 },
    { originCity: 'New York', originState: 'NY', destinationCity: 'Los Angeles', destinationState: 'CA', loadCount: 32 },
    { originCity: 'Chicago', originState: 'IL', destinationCity: 'Los Angeles', destinationState: 'CA', loadCount: 28 },
    { originCity: 'Miami', originState: 'FL', destinationCity: 'New York', destinationState: 'NY', loadCount: 24 },
    { originCity: 'Seattle', originState: 'WA', destinationCity: 'Los Angeles', destinationState: 'CA', loadCount: 20 },
    
    // Regional short-haul lanes
    { originCity: 'Philadelphia', originState: 'PA', destinationCity: 'Baltimore', destinationState: 'MD', loadCount: 19 },
    { originCity: 'Dallas', originState: 'TX', destinationCity: 'Houston', destinationState: 'TX', loadCount: 26 },
    { originCity: 'San Francisco', originState: 'CA', destinationCity: 'San Jose', destinationState: 'CA', loadCount: 21 },
    { originCity: 'Charlotte', originState: 'NC', destinationCity: 'Raleigh', destinationState: 'NC', loadCount: 12 },
    { originCity: 'Jacksonville', originState: 'FL', destinationCity: 'Tampa', destinationState: 'FL', loadCount: 15 }
  ];

  const lanes = sampleLaneData.map((rawLane, index) => {
    const frequency = rawLane.loadCount;
    const lastUsed = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Random date within last 30 days
    const laneId = `lane_${index + 1}_${rawLane.originState}_${rawLane.destinationState}`;
    
    // Use quick state-to-state distance calculation
    const estimatedMiles = calculateQuickDistance(rawLane.originState, rawLane.destinationState);
    const estimatedDuration = calculateEstimatedDuration(estimatedMiles);
    const frequencyRank = Math.min(Math.max(Math.ceil(frequency / 5), 1), 10); // Scale to 1-10

    return {
      id: laneId,
      originCity: rawLane.originCity,
      originState: rawLane.originState,
      destinationCity: rawLane.destinationCity,
      destinationState: rawLane.destinationState,
      estimatedMiles,
      estimatedDuration,
      frequencyRank,
      lastUsed,
      loadCount: frequency,
      route: `${rawLane.originCity}, ${rawLane.originState} → ${rawLane.destinationCity}, ${rawLane.destinationState}`
    };
  });

  // Sort by frequency rank (highest first), then alphabetically
  return lanes.sort((a, b) => {
    if (b.frequencyRank !== a.frequencyRank) {
      return b.frequencyRank - a.frequencyRank;
    }
    return a.originCity.localeCompare(b.originCity);
  });
}

// Generate CSV content for easy import into Airtable
function generateCSVContent(lanes) {
  const headers = [
    'Lane ID',
    'Origin City', 
    'Origin State',
    'Destination City',
    'Destination State', 
    'Estimated Miles',
    'Estimated Duration',
    'Frequency Rank',
    'Load Count',
    'Last Used',
    'Lane Route'
  ];
  
  const rows = lanes.map(lane => [
    lane.id,
    lane.originCity,
    lane.originState,
    lane.destinationCity,
    lane.destinationState,
    lane.estimatedMiles,
    lane.estimatedDuration,
    lane.frequencyRank,
    lane.loadCount,
    lane.lastUsed,
    lane.route
  ]);
  
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n');
  
  return csvContent;
}

// Main function
function previewLanesForAirtable() {
  console.log('🚀 LANES PREVIEW FOR AIRTABLE');
  console.log('============================\n');
  
  // Generate the lanes
  const lanes = generateSampleLanes();
  
  console.log(`\n✅ Generated ${lanes.length} lanes total\n`);
  
  // Display all lanes in a nice format
  console.log('📋 COMPLETE LANE LIBRARY DATA:');
  console.log('===============================');
  
  lanes.forEach((lane, index) => {
    console.log(`${index + 1}. ${lane.route}`);
    console.log(`   - Lane ID: ${lane.id}`);
    console.log(`   - Distance: ${lane.estimatedMiles} miles`);
    console.log(`   - Duration: ${lane.estimatedDuration}`);
    console.log(`   - Frequency Rank: ${lane.frequencyRank}/10`);
    console.log(`   - Load Count: ${lane.loadCount}`);
    console.log(`   - Last Used: ${lane.lastUsed}`);
    console.log('');
  });
  
  // Generate CSV file
  const csvContent = generateCSVContent(lanes);
  const csvFilename = 'lane-library-data.csv';
  
  try {
    fs.writeFileSync(csvFilename, csvContent);
    console.log(`📄 CSV file created: ${csvFilename}`);
  } catch (error) {
    console.log(`❌ Error creating CSV: ${error.message}`);
  }
  
  console.log('\n📋 AIRTABLE SETUP INSTRUCTIONS:');
  console.log('================================');
  console.log('');
  console.log('OPTION 1: Manual Table Creation');
  console.log('-------------------------------');
  console.log('1. Go to your Airtable base in the browser');
  console.log('2. Create a new table called "Lane Library"');
  console.log('3. Add the following fields:');
  console.log('   - Lane ID (Single line text, Primary field)');
  console.log('   - Origin City (Single line text)');
  console.log('   - Origin State (Single select with all US state abbreviations)');
  console.log('   - Destination City (Single line text)');
  console.log('   - Destination State (Single select with all US state abbreviations)');
  console.log('   - Estimated Miles (Number)');
  console.log('   - Estimated Duration (Single line text)');
  console.log('   - Frequency Rank (Number, 1-10 scale)');
  console.log('   - Load Count (Number)');
  console.log('   - Last Used (Date)');
  console.log('   - Lane Route (Formula: {Origin City} & ", " & {Origin State} & " → " & {Destination City} & ", " & {Destination State})');
  console.log('   - Created At (Created time)');
  console.log('   - Updated At (Last modified time)');
  console.log('');
  console.log('OPTION 2: CSV Import (Recommended)');
  console.log('----------------------------------');
  console.log('1. Go to your Airtable base in the browser');
  console.log('2. Click "+ Add or import" next to your tables');
  console.log('3. Choose "CSV file"');
  console.log(`4. Upload the generated file: ${csvFilename}`);
  console.log('5. Name the table "Lane Library"');
  console.log('6. Review the field types and adjust if needed');
  console.log('7. Click "Import table"');
  console.log('');
  console.log('OPTION 3: Copy-Paste Data');
  console.log('-------------------------');
  console.log('1. Create the "Lane Library" table with the fields listed above');
  console.log('2. Copy the data from the output above');
  console.log('3. Paste it into the Airtable table');
  console.log('');
  console.log('🎯 BENEFITS OF HAVING LANES IN AIRTABLE:');
  console.log('=========================================');
  console.log('✅ Easy filtering and sorting of lane data');
  console.log('✅ Visual analysis with Airtable views');
  console.log('✅ Integration with other Airtable data');
  console.log('✅ Sharing lane data with your team');
  console.log('✅ Creating reports and dashboards');
  console.log('✅ Automations based on lane performance');
  console.log('');
  console.log('📊 SUMMARY STATISTICS:');
  console.log('======================');
  
  const maxFrequency = Math.max(...lanes.map(l => l.frequencyRank));
  const minFrequency = Math.min(...lanes.map(l => l.frequencyRank));
  const avgMiles = Math.round(lanes.reduce((sum, l) => sum + l.estimatedMiles, 0) / lanes.length);
  const states = new Set([...lanes.map(l => l.originState), ...lanes.map(l => l.destinationState)]);
  
  console.log(`📦 Total lanes: ${lanes.length}`);
  console.log(`🗺️  States covered: ${states.size}`);
  console.log(`📏 Average distance: ${avgMiles} miles`);
  console.log(`📊 Frequency range: ${minFrequency}-${maxFrequency} (1-10 scale)`);
  console.log(`🔝 Highest frequency lanes:`);
  
  const topLanes = lanes.filter(l => l.frequencyRank === maxFrequency).slice(0, 3);
  topLanes.forEach((lane, index) => {
    console.log(`   ${index + 1}. ${lane.route} (${lane.loadCount} loads)`);
  });
  
  console.log('\n🎉 Data is ready to import into Airtable!');
  
  return { lanes, csvFilename };
}

// Run the preview
if (require.main === module) {
  previewLanesForAirtable();
}

module.exports = { previewLanesForAirtable, generateSampleLanes }; 