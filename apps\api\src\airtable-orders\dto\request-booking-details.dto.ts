import { IsBoolean, IsNotEmpty, IsOptional, IsString, <PERSON>Length, ValidateIf } from 'class-validator';

export class RequestBookingDetailsDto {
  @IsBoolean()
  isTruckEmpty: boolean;

  @ValidateIf(o => o.isTruckEmpty === true)
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  truckEmptyLocation?: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  etaToShipper: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
} 