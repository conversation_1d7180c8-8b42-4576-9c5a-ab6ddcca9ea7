# APM Task Assignment: Debug Specific API & CSP Errors

## 1. Agent Role & APM Context

**Introduction:** You are activated as a **Debug Agent** within the Agentic Project Management (APM) framework for the Carrier Portal Enhancement Project.

**Your Role:** As a Debug Agent, you are responsible for fixing specific API endpoint failures, Content Security Policy violations, and resource loading issues identified in the browser network tab and console.

**Workflow:** You will work directly with the Manager Agent (via the User) to resolve targeted issues causing dashboard functionality failures and security policy violations.

## 2. Context from Prior Work

**Specific Issues from Network Analysis:**
- ❌ **API Endpoint: `/api/v1/airtable-orders/assigned`** - HTTP 500 (1523ms)
- ❌ **API Endpoint: `/api/v1/airtable-orders/bids`** - HTTP 500 (911ms) 
- ❌ **API Endpoint: `/api/v1/auth/me`** - HTTP 500 (1149ms)
- ❌ **CSP Violations:** 24 warnings for inline script execution blocked
- ❌ **Font Loading:** WOFF files preloaded but not used within timeout
- ❌ **Repeated Errors:** "Error fetching dashboard data" occurring multiple times
- 🎯 **Goal:** Fix these specific endpoints and violations

**Evidence from Network Tab:**
```
GET https://www.fcp-portal.com/api/v1/airtable-orders/assigned [HTTP/2 500 1523ms]
GET https://www.fcp-portal.com/api/v1/airtable-orders/bids [HTTP/2 500 911ms]  
GET https://www.fcp-portal.com/api/v1/auth/me [HTTP/2 500 1149ms]

CSP Error: script-src-elem 'self' 'nonce-YWU3MGI5MjUtNDFiYS00YmEzLTllYTYtZjFlMDdmM2YyMzk4'
Error fetching dashboard data: Error: Failed to fetch assigned loads
```

## 3. Task Assignment

**Reference Implementation Plan:** Targeted debugging of specific failing endpoints and CSP violations

**Objective:** Fix the three failing API endpoints, resolve CSP violations with proper nonce implementation, and optimize font loading to eliminate console errors.

### Critical Debug Requirements:

#### Issue #1: Airtable Orders Assigned Endpoint (500 Error)
**Problem:** `/api/v1/airtable-orders/assigned` returning 500 after 1523ms
- Dashboard cannot load assigned loads data
- Critical for dashboard functionality
- Slow response time indicating backend issues

**Debug Strategy Required:**
- **Analyze:** Assigned orders API implementation
- **Check:** Database queries and Airtable connectivity
- **Fix:** Endpoint logic and error handling
- **Optimize:** Response time and data fetching

#### Issue #2: Airtable Orders Bids Endpoint (500 Error)
**Problem:** `/api/v1/airtable-orders/bids` returning 500 after 911ms
- Bid data not loading for dashboard
- Essential for carrier bid management
- Potential database or Airtable integration issues

**Solution Required:**
- **Debug:** Bids API endpoint implementation
- **Verify:** Airtable bids table connectivity
- **Fix:** Query logic and data transformation
- **Test:** Response consistency and performance

#### Issue #3: Auth Me Endpoint (500 Error)
**Problem:** `/api/v1/auth/me` returning 500 after 1149ms
- User authentication data not loading
- Critical for user session management
- Blocking dashboard personalization

**Fix Required:**
- **Analyze:** Authentication endpoint logic
- **Check:** Clerk integration and user data fetching
- **Resolve:** Database user queries
- **Implement:** Proper error handling and fallbacks

#### Issue #4: CSP Inline Script Violations
**Problem:** 24 CSP warnings for blocked inline scripts
- `script-src-elem` directive violations
- Nonce not properly implemented for inline scripts
- Potential functionality breaking due to blocked scripts

**Solution Required:**
- **Identify:** All inline scripts causing violations
- **Implement:** Proper nonce usage for allowed scripts
- **Move:** Inline scripts to external files where possible
- **Update:** CSP policy for necessary inline scripts

#### Issue #5: Font Preloading Optimization
**Problem:** WOFF fonts preloaded but not used within timeout
- Resource waste and console warnings
- Potential performance impact
- Preload strategy not optimized

**Fix Required:**
- **Analyze:** Font usage timing and necessity
- **Optimize:** Preload strategy and font loading
- **Remove:** Unnecessary preloads
- **Implement:** Proper font loading best practices

### Detailed Debug Steps:

#### A. Fix Airtable Orders Assigned Endpoint
1. **Debug Assigned Orders API:**
   ```typescript
   // apps/api/src/airtable-orders/airtable-orders.controller.ts
   
   @Get('assigned')
   async getAssignedOrders(@Req() request: Request) {
     try {
       console.log('Assigned orders endpoint called:', new Date().toISOString());
       
       // Verify authentication
       const user = request.user;
       if (!user) {
         throw new UnauthorizedException('User not authenticated');
       }
       
       console.log('User ID:', user.id);
       
       // Check database connection
       try {
         await this.prisma.$queryRaw`SELECT 1`;
         console.log('Database connection verified');
       } catch (dbError) {
         console.error('Database connection failed:', dbError);
         throw new InternalServerErrorException('Database connection failed');
       }
       
       // Fetch assigned orders with timeout
       const assignedOrders = await Promise.race([
         this.airtableOrdersService.getAssignedOrders(user.id),
         new Promise((_, reject) => 
           setTimeout(() => reject(new Error('Request timeout')), 10000)
         )
       ]);
       
       console.log('Assigned orders fetched:', assignedOrders.length);
       return assignedOrders;
       
     } catch (error) {
       console.error('Assigned orders error:', error);
       
       if (error instanceof UnauthorizedException) {
         throw error;
       }
       
       throw new InternalServerErrorException({
         error: 'Failed to fetch assigned orders',
         message: error.message,
         timestamp: new Date().toISOString()
       });
     }
   }
   ```

2. **Fix Service Implementation:**
   ```typescript
   // apps/api/src/airtable-orders/airtable-orders.service.ts
   
   async getAssignedOrders(userId: string) {
     try {
       console.log('Fetching assigned orders for user:', userId);
       
       // Check environment variables
       if (!process.env.AIRTABLE_API_KEY || !process.env.AIRTABLE_BASE_ID) {
         throw new Error('Missing Airtable configuration');
       }
       
       // Fetch from database first (faster)
       const dbOrders = await this.prisma.order.findMany({
         where: {
           carrierId: userId,
           status: 'ASSIGNED'
         },
         include: {
           pickupLocation: true,
           deliveryLocation: true
         }
       });
       
       console.log('DB assigned orders found:', dbOrders.length);
       
       // If no DB orders, fetch from Airtable with retry logic
       if (dbOrders.length === 0) {
         let retries = 3;
         while (retries > 0) {
           try {
             const airtableOrders = await this.fetchAssignedFromAirtable(userId);
             console.log('Airtable assigned orders found:', airtableOrders.length);
             return airtableOrders;
           } catch (airtableError) {
             retries--;
             if (retries === 0) throw airtableError;
             await new Promise(resolve => setTimeout(resolve, 1000));
           }
         }
       }
       
       return dbOrders;
       
     } catch (error) {
       console.error('Service error fetching assigned orders:', error);
       throw error;
     }
   }
   ```

#### B. Fix Airtable Orders Bids Endpoint
1. **Debug Bids API:**
   ```typescript
   // apps/api/src/airtable-orders/airtable-orders.controller.ts
   
   @Get('bids')
   async getBids(@Req() request: Request) {
     try {
       console.log('Bids endpoint called:', new Date().toISOString());
       
       const user = request.user;
       if (!user) {
         throw new UnauthorizedException('User not authenticated');
       }
       
       // Fetch bids with proper error handling
       const bids = await this.airtableOrdersService.getUserBids(user.id);
       
       console.log('Bids fetched successfully:', bids.length);
       return bids;
       
     } catch (error) {
       console.error('Bids endpoint error:', error);
       
       // Return empty array instead of error for better UX
       if (error.message.includes('No bids found')) {
         return [];
       }
       
       throw new InternalServerErrorException({
         error: 'Failed to fetch bids',
         message: error.message,
         timestamp: new Date().toISOString()
       });
     }
   }
   ```

2. **Fix Bids Service:**
   ```typescript
   async getUserBids(userId: string) {
     try {
       console.log('Fetching bids for user:', userId);
       
       // Fetch from bids table
       const bids = await this.prisma.bid.findMany({
         where: {
           carrierId: userId
         },
         include: {
           order: {
             include: {
               pickupLocation: true,
               deliveryLocation: true
             }
           }
         },
         orderBy: {
           createdAt: 'desc'
         }
       });
       
       console.log('User bids found:', bids.length);
       return bids;
       
     } catch (error) {
       console.error('Error fetching user bids:', error);
       throw new Error(`Failed to fetch bids: ${error.message}`);
     }
   }
   ```

#### C. Fix Auth Me Endpoint
1. **Debug Authentication API:**
   ```typescript
   // apps/api/src/auth/auth.controller.ts
   
   @Get('me')
   async getMe(@Req() request: Request) {
     try {
       console.log('Auth me endpoint called:', new Date().toISOString());
       
       // Get authorization header
       const authHeader = request.headers.authorization;
       if (!authHeader || !authHeader.startsWith('Bearer ')) {
         throw new UnauthorizedException('No valid authorization header');
       }
       
       const token = authHeader.substring(7);
       console.log('Token received, length:', token.length);
       
       // Verify token with Clerk
       const clerkUser = await this.authService.verifyToken(token);
       console.log('Clerk user verified:', clerkUser.id);
       
       // Fetch user from database
       const dbUser = await this.authService.getUserFromDatabase(clerkUser.id);
       console.log('DB user fetched:', dbUser?.id);
       
       return {
         id: clerkUser.id,
         email: clerkUser.primaryEmailAddress?.emailAddress,
         firstName: clerkUser.firstName,
         lastName: clerkUser.lastName,
         profile: dbUser
       };
       
     } catch (error) {
       console.error('Auth me error:', error);
       
       if (error instanceof UnauthorizedException) {
         throw error;
       }
       
       throw new InternalServerErrorException({
         error: 'Authentication failed',
         message: error.message,
         timestamp: new Date().toISOString()
       });
     }
   }
   ```

#### D. Fix CSP Violations
1. **Update CSP Policy:**
   ```typescript
   // apps/web/next.config.js
   
   const nextConfig = {
     async headers() {
       const nonce = Buffer.from(crypto.randomUUID()).toString('base64');
       
       return [
         {
           source: '/(.*)',
           headers: [
             {
               key: 'Content-Security-Policy',
               value: `
                 default-src 'self';
                 script-src 'self' 'nonce-${nonce}' 'strict-dynamic' 
                   https://clerk.fcp-portal.com 
                   https://*.clerk.com 
                   https://*.segment.com 
                   https://js.sentry-cdn.com 
                   https://browser.sentry-cdn.com 
                   https://*.sentry.io 
                   https://js.stripe.com 
                   https://*.js.stripe.com 
                   https://maps.googleapis.com 
                   https://*.googletagmanager.com 
                   https://*.google-analytics.com 
                   https://*.vercel.live 
                   https://vercel.live 
                   https://*.hotjar.com 
                   https://static.hotjar.com 
                   https://*.intercom.io 
                   https://widget.intercom.io;
                 script-src-elem 'self' 'nonce-${nonce}' 
                   https://clerk.fcp-portal.com 
                   https://*.clerk.com 
                   https://*.segment.com 
                   https://js.sentry-cdn.com;
                 style-src 'self' 'unsafe-inline';
                 img-src 'self' data: https: blob:;
                 font-src 'self' https://fonts.gstatic.com;
                 connect-src 'self' 
                   https://clerk.fcp-portal.com 
                   https://*.clerk.com 
                   https://*.sentry.io;
               `.replace(/\s+/g, ' ').trim()
             },
             {
               key: 'X-Nonce',
               value: nonce
             }
           ]
         }
       ];
     }
   };
   ```

2. **Fix Inline Scripts:**
   ```tsx
   // apps/web/src/app/layout.tsx
   
   import { headers } from 'next/headers';
   
   export default function RootLayout({ children }) {
     const nonce = headers().get('x-nonce');
     
     return (
       <html>
         <head>
           {/* Move inline scripts to external files or add nonce */}
           <script 
             nonce={nonce}
             dangerouslySetInnerHTML={{
               __html: `
                 // Inline script content with nonce
               `
             }}
           />
         </head>
         <body>{children}</body>
       </html>
     );
   }
   ```

#### E. Optimize Font Loading
1. **Fix Font Preloading:**
   ```tsx
   // apps/web/src/app/layout.tsx
   
   export default function RootLayout({ children }) {
     return (
       <html>
         <head>
           {/* Only preload fonts that are used immediately */}
           <link
             rel="preload"
             href="/_next/static/media/4473ecc91f70f139-s.p.woff"
             as="font"
             type="font/woff"
             crossOrigin="anonymous"
           />
           {/* Remove unused font preloads */}
         </head>
         <body>{children}</body>
       </html>
     );
   }
   ```

2. **Optimize Font CSS:**
   ```css
   /* apps/web/src/app/globals.css */
   
   @font-face {
     font-family: 'CustomFont';
     src: url('/_next/static/media/4473ecc91f70f139-s.p.woff') format('woff');
     font-display: swap; /* Improve loading performance */
   }
   
   /* Remove unused font declarations */
   ```

#### F. Improve Error Handling
1. **Add Dashboard Error Boundary:**
   ```tsx
   // apps/web/src/app/org/[orgId]/dashboard/error.tsx
   
   'use client';
   
   export default function DashboardError({
     error,
     reset,
   }: {
     error: Error & { digest?: string };
     reset: () => void;
   }) {
     return (
       <div className="flex items-center justify-center min-h-96">
         <div className="text-center">
           <h2 className="text-xl font-semibold mb-4">Dashboard Error</h2>
           <p className="text-gray-600 mb-4">
             Failed to load dashboard data. Please try again.
           </p>
           <button
             onClick={reset}
             className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
           >
             Try Again
           </button>
         </div>
       </div>
     );
   }
   ```

## 4. Technical Implementation Guidelines

**Files to Debug/Fix:**
- `apps/api/src/airtable-orders/airtable-orders.controller.ts` - Fix assigned/bids endpoints
- `apps/api/src/airtable-orders/airtable-orders.service.ts` - Fix service implementations
- `apps/api/src/auth/auth.controller.ts` - Fix auth/me endpoint
- `apps/web/next.config.js` - Update CSP policy with proper nonce
- `apps/web/src/app/layout.tsx` - Fix font preloading and inline scripts
- `apps/web/src/app/globals.css` - Optimize font declarations

**Debug Process:**
1. **API Testing:** Test each endpoint individually with Postman
2. **Database Verification:** Check database connectivity and query performance
3. **CSP Validation:** Verify nonce implementation resolves violations
4. **Font Analysis:** Audit font usage and optimize preloading

## 5. Expected Output & Deliverables

**Define Success:** Successful completion means:
- ✅ **`/api/v1/airtable-orders/assigned` returns 200**
- ✅ **`/api/v1/airtable-orders/bids` returns 200**
- ✅ **`/api/v1/auth/me` returns 200**
- ✅ **No CSP violations in browser console**
- ✅ **Font preloading warnings eliminated**
- ✅ **Dashboard loads without repeated error messages**
- ✅ **Response times under 1 second**

**Critical Success Criteria:**
- **API Stability:** All three endpoints responding without 500 errors
- **Performance:** Response times significantly improved
- **Security:** CSP compliance without functionality loss
- **User Experience:** Clean console without errors or warnings

## 6. Memory Bank Logging Instructions

Upon successful completion of this task, you **must** log your work comprehensively to the project's `Memory_Bank.md` file.

**Format Adherence:** Ensure your log includes:
- Reference to **Specific API & CSP Error Fixes**
- **Endpoint Fixes:** Solutions for assigned/bids/auth endpoints
- **CSP Resolution:** Nonce implementation and policy updates
- **Performance:** Response time improvements achieved
- **Resource Optimization:** Font loading improvements

## 7. Immediate Action Required

**Priority Instructions:**
1. **Fix the three failing API endpoints** with proper error handling
2. **Implement CSP nonce system** to resolve 24 violations
3. **Optimize font preloading** to eliminate resource warnings
4. **Add proper error boundaries** to prevent repeated error messages
5. **Test all endpoints** to ensure 200 responses and proper data

---

**Priority:** 🔴 **CRITICAL** - Multiple API failures blocking dashboard functionality

**Estimated Duration:** 2-3 hours

**Success Metric:** All API endpoints returning 200, zero CSP violations, clean console

**Dependencies:** Backend API access, CSP configuration, font resources

**Impact:** Restores complete dashboard functionality with proper security compliance 