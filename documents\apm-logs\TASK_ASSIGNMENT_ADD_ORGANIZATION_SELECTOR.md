# TASK ASSIGNMENT: Add Organization Selector to Top Navigation
**Priority**: P2 (Critical for Admin Access)  
**Agent Type**: Implementation Agent  
**Complexity**: Medium  
**Estimated Time**: 1-2 days  
**Tags**: #organization-selector #admin-access #navigation #clerk-auth

## OVERVIEW
Add an organization selector dropdown in the top navigation (where the "Help" button currently is) to allow admin users with access to multiple organizations to easily switch between them. This is needed for admin accounts that have access to all organizations.

## PROBLEM STATEMENT
- Admin user has access to multiple organizations via Clerk
- Currently cannot properly authenticate/access due to unclear organization context
- Need a way to select which organization to operate within
- Should be prominently placed in the top navigation for easy access

## REQUIREMENTS

### **Functional Requirements**
1. **Organization Dropdown**
   - Display all organizations the current user has access to
   - Show organization name and logo (if available)
   - Highlight currently selected organization
   - Allow switching between organizations
   - Persist selection across page reloads

2. **Authentication Integration**
   - Integrate with existing Clerk organization system
   - Update organization context when selection changes
   - Redirect appropriately after organization switch
   - Handle organization permissions properly

3. **UI/UX Requirements**
   - Replace or position near the current "Help" button
   - Match existing design system and styling
   - Mobile-responsive design
   - Loading states during organization switch
   - Clear visual indication of active organization

4. **Admin Access**
   - Only show for users with multi-organization access
   - Hide for regular users with single organization access
   - Show appropriate permissions/roles for each organization

## IMPLEMENTATION TASKS

### **Task 1: Analyze Current Navigation Structure**
First, examine the existing top navigation to understand the current layout and where to place the organization selector.

**Files to examine:**
- Navigation components in `src/components/layout/`
- Check where "Help" button is currently located
- Understand the current Clerk organization integration

**Research Questions:**
- How is the current navigation structured?
- Where exactly is the Help button located?
- How is Clerk organization context currently handled?
- What organization data is available from Clerk?

### **Task 2: Create Organization Selector Component**
Create a reusable organization selector dropdown component.

**File: `src/components/OrganizationSelector.tsx`**

```typescript
'use client';

import { useState, useEffect } from 'react';
import { useOrganization, useOrganizationList, useClerk, useAuth } from '@clerk/nextjs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Check, ChevronDown, Building2, Loader2, AlertTriangle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';

interface OrganizationSelectorProps {
  className?: string;
  onOrganizationChange?: (orgId: string) => void;
}

export function OrganizationSelector({ className, onOrganizationChange }: OrganizationSelectorProps) {
  const { organization: currentOrg, isLoaded: orgLoaded } = useOrganization();
  const { organizationList, isLoaded: listLoaded } = useOrganizationList();
  const { setActive } = useClerk();
  const { isSignedIn } = useAuth();
  const router = useRouter();
  const [switching, setSwitching] = useState<string | null>(null);
  const [hasMultiOrgError, setHasMultiOrgError] = useState(false);

  // Check if user has multiple organizations but no active one
  const hasMultipleOrgs = listLoaded && organizationList && organizationList.length > 1;
  const hasNoActiveOrg = orgLoaded && !currentOrg && hasMultipleOrgs;

  // Detect the multi-org authentication error
  useEffect(() => {
    if (hasNoActiveOrg) {
      setHasMultiOrgError(true);
      // Show toast notification about the issue
      toast({
        title: "Organization Selection Required",
        description: "Please select an organization to continue using the portal.",
        variant: "destructive",
      });
    } else {
      setHasMultiOrgError(false);
    }
  }, [hasNoActiveOrg]);

  // Don't render anything if user is not signed in or data not loaded
  if (!isSignedIn || !listLoaded || !orgLoaded) {
    return (
      <div className="h-9 w-32 bg-gray-100 animate-pulse rounded-md"></div>
    );
  }

  // For single organization users, don't show the selector
  if (!hasMultipleOrgs) {
    return null;
  }

  const handleOrgSwitch = async (orgId: string) => {
    if (orgId === currentOrg?.id || switching) return;

    setSwitching(orgId);
    try {
      // CRITICAL: Set the active organization in Clerk
      await setActive({ organization: orgId });
      
      // Clear the multi-org error state
      setHasMultiOrgError(false);
      
      // Notify parent component
      onOrganizationChange?.(orgId);
      
      // Redirect to dashboard of selected organization
      router.push(`/org/${orgId}/dashboard`);
      
      // Force a page refresh to update API context
      router.refresh();
      
      toast({
        title: "Organization switched",
        description: `Now managing ${organizationList?.find(org => org.organization.id === orgId)?.organization.name}`,
      });
      
    } catch (error) {
      console.error('Failed to switch organization:', error);
      toast({
        title: "Failed to switch organization",
        description: "Please try again or contact support if the issue persists.",
        variant: "destructive",
      });
    } finally {
      setSwitching(null);
    }
  };

  const getCurrentOrgName = () => {
    if (hasNoActiveOrg) {
      return 'Select Organization';
    }
    return currentOrg?.name || 'Select Organization';
  };

  const isCurrentOrg = (orgId: string) => {
    return currentOrg?.id === orgId;
  };

  const isSwitching = (orgId: string) => {
    return switching === orgId;
  };

  return (
    <div className="flex flex-col">
      {/* Show alert for multi-org error */}
      {hasMultiOrgError && (
        <Alert className="mb-2 border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800 text-sm">
            Multiple organizations detected. Please select one to continue.
          </AlertDescription>
        </Alert>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={hasNoActiveOrg ? "destructive" : "outline"}
            className={`h-9 px-3 ${className} ${hasNoActiveOrg ? 'animate-pulse' : ''}`}
            disabled={!!switching}
          >
            <Building2 className="h-4 w-4 mr-2" />
            <span className="max-w-[150px] truncate">
              {getCurrentOrgName()}
            </span>
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-[300px]">
          <div className="px-2 py-1.5">
            <p className="text-sm font-medium text-gray-900">
              Switch Organization
            </p>
            <p className="text-xs text-gray-500">
              {hasNoActiveOrg 
                ? "Required: Select an organization to access the portal"
                : "Select which organization to manage"
              }
            </p>
          </div>
          
          <DropdownMenuSeparator />
          
          {organizationList?.map((org) => (
            <DropdownMenuItem
              key={org.organization.id}
              className="flex items-center justify-between p-2 cursor-pointer"
              onClick={() => handleOrgSwitch(org.organization.id)}
              disabled={isSwitching(org.organization.id)}
            >
              <div className="flex items-center space-x-3">
                {org.organization.imageUrl ? (
                  <img
                    src={org.organization.imageUrl}
                    alt={org.organization.name}
                    className="h-6 w-6 rounded-full"
                  />
                ) : (
                  <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center">
                    <Building2 className="h-3 w-3 text-gray-500" />
                  </div>
                )}
                
                <div className="flex flex-col">
                  <span className="text-sm font-medium">
                    {org.organization.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    {org.membership.role}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {isCurrentOrg(org.organization.id) && (
                  <Badge variant="secondary" className="text-xs">
                    Active
                  </Badge>
                )}
                
                {isSwitching(org.organization.id) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : isCurrentOrg(org.organization.id) ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : null}
              </div>
            </DropdownMenuItem>
          ))}
          
          {hasNoActiveOrg && (
            <>
              <DropdownMenuSeparator />
              <div className="px-2 py-1.5 text-xs text-orange-600 bg-orange-50 rounded-sm mx-1">
                <AlertTriangle className="h-3 w-3 inline mr-1" />
                API access blocked until organization is selected
              </div>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
```

### **Task 3: Integrate with Top Navigation**
Add the organization selector to the top navigation layout.

**Find and update the main navigation component** (likely in one of these locations):
- `src/components/layout/Header.tsx`
- `src/components/layout/Navigation.tsx` 
- `src/components/layout/TopNav.tsx`
- `src/app/layout.tsx` or similar

**Integration example:**
```typescript
import { OrganizationSelector } from '@/components/OrganizationSelector';

// In your navigation component, replace or position near the Help button
export function TopNavigation() {
  return (
    <header className="border-b">
      <div className="flex h-16 items-center px-4">
        {/* Existing navigation items */}
        <div className="ml-auto flex items-center space-x-4">
          {/* Add organization selector */}
          <OrganizationSelector />
          
          {/* Keep existing items like user menu, notifications, etc. */}
          <UserMenu />
        </div>
      </div>
    </header>
  );
}
```

### **Task 4: Update Organization Context Handling**
Ensure proper organization context is maintained throughout the app.

**File: `src/lib/organization.ts` (create if doesn't exist)**

```typescript
import { useOrganization } from '@clerk/nextjs';
import { useParams, useRouter } from 'next/navigation';

export function useCurrentOrganization() {
  const { organization, isLoaded } = useOrganization();
  const params = useParams();
  const router = useRouter();

  // Get organization ID from URL params
  const orgIdFromUrl = params.orgId as string;

  // Redirect if organization context doesn't match URL
  if (isLoaded && organization && orgIdFromUrl && organization.id !== orgIdFromUrl) {
    router.push(`/org/${organization.id}/dashboard`);
  }

  return {
    organization,
    isLoaded,
    orgId: organization?.id || orgIdFromUrl,
  };
}

export function getOrganizationPath(orgId: string, path: string = '/dashboard') {
  return `/org/${orgId}${path}`;
}
```

### **Task 5: Add Loading and Error States**
Handle edge cases and provide proper feedback.

**Loading States:**
- Show skeleton loader while organization list is loading
- Show spinner when switching organizations
- Disable interactions during organization switch

**Error Handling:**
- Handle failed organization switches
- Show error messages using existing toast system
- Provide fallback if organization access is revoked

**Edge Cases:**
- User loses access to current organization
- Network errors during organization switch
- Invalid organization IDs in URLs

### **Task 6: Styling and Mobile Responsiveness**
Ensure the component looks good across all screen sizes.

**Desktop:**
- Proper spacing in top navigation
- Clear visual hierarchy
- Consistent with existing design system

**Mobile:**
- Responsive dropdown sizing
- Touch-friendly interaction targets
- Proper overflow handling for long organization names

**Accessibility:**
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility

### **Task 7: Testing Organization Switching**
Test the functionality thoroughly with multiple organizations.

**Test Cases:**
- [ ] Switching between organizations updates context correctly
- [ ] URL updates to reflect current organization
- [ ] Page content updates after organization switch
- [ ] Permissions are properly applied for each organization
- [ ] Component only shows for multi-org users
- [ ] Loading states work correctly
- [ ] Error handling works for failed switches
- [ ] Mobile responsiveness is maintained

## INTEGRATION NOTES

### **Current Navigation Structure**
Based on the project structure, look for:
1. Main layout components in `src/components/layout/`
2. How the Help button is currently implemented
3. Existing Clerk organization integration patterns
4. Current navigation styling and components

### **Organization URL Structure**
The app appears to use `/org/[orgId]/...` URL structure:
- Ensure organization selector updates URLs correctly
- Maintain deep linking to specific organization pages
- Handle organization context in all protected routes

### **Clerk Integration**
Leverage existing Clerk setup:
- Use `useOrganizationList` to get available organizations
- Use `useOrganization` for current organization context
- Use `setActive` to switch organization context
- Respect existing organization permissions and roles

## ACCEPTANCE CRITERIA

### **Functional Acceptance**
- [ ] Organization selector appears in top navigation for multi-org users
- [ ] Shows all organizations the user has access to
- [ ] Allows switching between organizations seamlessly
- [ ] Updates URL and page content after organization switch
- [ ] Maintains organization context across page reloads
- [ ] Hides for users with single organization access

### **UI/UX Acceptance**
- [ ] Matches existing design system and styling
- [ ] Works responsively on all screen sizes
- [ ] Provides clear visual feedback during loading states
- [ ] Shows appropriate error messages for failures
- [ ] Maintains accessibility standards

### **Technical Acceptance**
- [ ] Integrates properly with existing Clerk authentication
- [ ] Follows project TypeScript and ESLint standards
- [ ] Uses existing UI components and patterns
- [ ] Handles edge cases and error scenarios
- [ ] Performs efficiently without unnecessary re-renders

## DEPLOYMENT CHECKLIST

### **Pre-deployment**
- [ ] Test with multiple organizations in development
- [ ] Verify organization permissions work correctly
- [ ] Test mobile responsiveness
- [ ] Check accessibility compliance
- [ ] Verify error handling scenarios

### **Post-deployment**
- [ ] Monitor for organization switching errors
- [ ] Verify performance impact is minimal
- [ ] Test with real admin accounts
- [ ] Collect feedback on UX improvements

## FUTURE ENHANCEMENTS (NOT IN SCOPE)

- Organization creation/management capabilities
- Recent organizations quick access
- Organization search/filtering for large lists
- Organization-specific settings and preferences
- Bulk actions across multiple organizations

## GUIDING NOTES

1. **Seamless UX** - Organization switching should feel instant and natural
2. **Admin Focus** - Design for admin users who frequently switch organizations
3. **Existing Patterns** - Follow current navigation and authentication patterns
4. **Error Recovery** - Always provide a way to recover from failed organization switches
5. **Performance** - Don't impact page load times with heavy organization data fetching
6. **Security** - Respect organization permissions and access controls
7. **Accessibility** - Ensure all users can navigate organizations effectively

## CONTACT & ESCALATION

- **Clerk Integration Issues**: Check Clerk documentation for organization management
- **Navigation Layout Questions**: Review existing navigation components and patterns
- **Permission/Access Issues**: Understand current organization role system
- **UI/UX Decisions**: Follow existing design system components and styling 

## CRITICAL CLERK ERROR TO HANDLE

### **Specific Error from Backend:**
```
ERROR [AuthService] Failed to fetch user details from Clerk for user ID user_2xZWyA8oVI2bhQOrZ5vgJNRonLE: 
Multiple organizations found (MVT Logistics, MNM Transport, US Freight Lines, First Cut Produce). 
Please set your active organization in Clerk profile settings.
```

### **Root Cause:**
- Admin user has access to multiple organizations
- No active organization is set in Clerk context
- Backend API expects single organization context
- Results in 401 authentication failure

### **Solution Requirements:**
1. **Organization selector MUST set active organization** in Clerk
2. **Handle the 401 error gracefully** until organization is selected
3. **Provide clear UX** for organization selection
4. **Ensure API calls work** after organization selection

## IMPLEMENTATION UPDATES

### **Task 8 NEW: Error Boundary and Recovery**

**File: `src/components/MultiOrgErrorBoundary.tsx`**

```typescript
'use client';

import { useEffect, useState } from 'react';
import { useOrganization, useOrganizationList } from '@clerk/nextjs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { OrganizationSelector } from './OrganizationSelector';

interface MultiOrgErrorBoundaryProps {
  children: React.ReactNode;
}

export function MultiOrgErrorBoundary({ children }: MultiOrgErrorBoundaryProps) {
  const { organization, isLoaded: orgLoaded } = useOrganization();
  const { organizationList, isLoaded: listLoaded } = useOrganizationList();
  const [hasApiError, setHasApiError] = useState(false);

  const hasMultipleOrgs = listLoaded && organizationList && organizationList.length > 1;
  const hasNoActiveOrg = orgLoaded && !organization && hasMultipleOrgs;

  // Listen for API errors that might be caused by multi-org issues
  useEffect(() => {
    const handleApiError = (event: any) => {
      if (event.detail?.status === 401 && hasNoActiveOrg) {
        setHasApiError(true);
      }
    };

    window.addEventListener('api-error', handleApiError);
    return () => window.removeEventListener('api-error', handleApiError);
  }, [hasNoActiveOrg]);

  // Reset error state when organization is selected
  useEffect(() => {
    if (organization) {
      setHasApiError(false);
    }
  }, [organization]);

  // Show blocking UI if user needs to select organization
  if (hasNoActiveOrg || hasApiError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-6">
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertTitle className="text-orange-800">Organization Selection Required</AlertTitle>
            <AlertDescription className="text-orange-700">
              You have access to multiple organizations. Please select which organization 
              you want to manage to continue using the carrier portal.
            </AlertDescription>
          </Alert>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4">Select Your Organization</h2>
            <OrganizationSelector 
              className="w-full justify-start"
              onOrganizationChange={() => {
                // Refresh the page after organization selection
                window.location.reload();
              }}
            />
          </div>

          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="w-full"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Page
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
```

### **Task 9 NEW: API Error Handling**

**File: `src/lib/api-client.ts` (update existing or create)**

```typescript
// Enhanced API client to handle multi-org authentication errors

export class ApiClient {
  private static instance: ApiClient;
  
  static getInstance() {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        // Check for the specific multi-org error
        if (response.status === 401) {
          const errorText = await response.text();
          
          if (errorText.includes('Multiple organizations found')) {
            // Dispatch custom event for the error boundary to catch
            window.dispatchEvent(new CustomEvent('api-error', {
              detail: {
                status: 401,
                message: 'Multiple organizations detected',
                type: 'multi-org-error'
              }
            }));
            
            throw new Error('Please select an organization to continue');
          }
        }
        
        throw new Error(`API Error: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
}
```

### **Task 10 NEW: Layout Integration with Error Boundary**

**Update main layout to wrap with error boundary:**

```typescript
// In your main layout file (layout.tsx or similar)
import { MultiOrgErrorBoundary } from '@/components/MultiOrgErrorBoundary';
import { OrganizationSelector } from '@/components/OrganizationSelector';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <MultiOrgErrorBoundary>
            {/* Your existing layout structure */}
            <header className="border-b">
              <div className="flex h-16 items-center px-4">
                {/* Existing navigation items */}
                <div className="ml-auto flex items-center space-x-4">
                  <OrganizationSelector />
                  <UserMenu />
                </div>
              </div>
            </header>
            
            <main>{children}</main>
          </MultiOrgErrorBoundary>
        </body>
      </html>
    </ClerkProvider>
  );
}
```

## ENHANCED ACCEPTANCE CRITERIA

### **Critical Error Handling**
- [ ] **Handles the specific Clerk multi-org error gracefully**
- [ ] **Blocks UI access until organization is selected**
- [ ] **Shows clear messaging about organization requirement**
- [ ] **Automatically sets active organization in Clerk context**
- [ ] **Refreshes API context after organization selection**

### **User Experience for Multi-Org Error**
- [ ] **Shows prominent organization selector when error occurs**
- [ ] **Provides clear explanation of why selection is required**
- [ ] **Prevents user from accessing features until org is selected**
- [ ] **Shows loading states during organization switching**
- [ ] **Confirms successful organization switch**

### **API Integration**
- [ ] **API calls work properly after organization selection**
- [ ] **Handles 401 errors caused by missing organization context**
- [ ] **Maintains organization context across page reloads**
- [ ] **Updates authentication headers with organization info**

## TESTING REQUIREMENTS FOR MULTI-ORG ERROR

### **Reproduction Steps:**
1. **Login with admin account** that has access to multiple organizations
2. **Ensure no active organization** is set in Clerk
3. **Navigate to any protected page** (e.g., `/org/[orgId]/dashboard`)
4. **Verify the 401 error occurs** with the specific multi-org message
5. **Test organization selector** resolves the error
6. **Verify API calls work** after organization selection

### **Test Cases:**
- [ ] Error boundary shows when multi-org error occurs
- [ ] Organization selector is prominently displayed
- [ ] Selecting organization resolves authentication issues
- [ ] Page refreshes and loads correctly after selection
- [ ] Subsequent API calls include proper organization context
- [ ] Error state clears when organization is selected
- [ ] Works correctly on mobile devices 