# APM Handover Protocol Guide

## 1. Purpose and Scope

This document outlines the **Agentic Project Management (APM) Handover Protocol**. Its primary purpose is to ensure seamless project continuity when context transfer is required between AI agent instances. This is most commonly triggered when an active agent (typically the Manager Agent, but potentially a specialized agent like a <PERSON>bugger or Implementer) approaches its operational context window limitations, threatening its ability to maintain a coherent understanding of the project's state and history.

The protocol facilitates the transfer of essential project knowledge from the outgoing ("incumbent") agent to a new, incoming agent instance, minimizing disruption and preserving the integrity of the project workflow.

This guide provides the procedural steps and content requirements for executing a successful handover. It is primarily intended for the Manager Agent overseeing the handover process but is also crucial for the User's understanding.

## 2. Trigger Conditions

The Handover Protocol should be initiated under the following circumstances:

*   **Context Window Limitation:** The incumbent agent (Manager or specialized) indicates, or the User observes, that its context window is nearing capacity, leading to potential loss of recall regarding earlier instructions, decisions, or project details.
*   **Strategic Agent Replacement:** The User decides to replace the current agent instance with a new one for strategic reasons (e.g., upgrading to a different model, re-scoping agent responsibilities).
*   **Extended Project Duration:** For projects anticipated to run significantly longer than a single agent's context lifespan, planned handovers may be scheduled proactively.

**Initiation:** The User typically initiates the handover process. However, the Manager Agent is responsible for monitoring its own context and advising the User when a handover becomes necessary due to context limitations.

## 3. Handover Components

The protocol comprises two critical artifacts generated by the incumbent Manager Agent (or the agent initiating the handover if specialized):

### 3.1. The `Handover_File.md` (Context Dump)

*   **Purpose:** To serve as a comprehensive, structured dump of all pertinent project context accumulated by the outgoing agent. This file acts as the primary knowledge base for the incoming agent.
*   **Content Requirements:** The file must encapsulate the current project state. While the specific format details are defined in `prompts/02_Utility_Prompts_And_Format_Definitions/Handover_Artifact_Formats.md`, the `Handover_File.md` must generally include:
    *   **Project Summary:** High-level goals, current status, and objectives.
    *   **Implementation Plan Status:** Link to or embed the current `Implementation_Plan.md`, highlighting completed tasks, tasks in progress, and upcoming tasks. Note any deviations or approved changes from the original plan.
    *   **Key Decisions & Rationale:** A log of significant decisions made, justifications, and User approvals.
    *   **Agent Roster & Roles:** List of active Implementation or specialized agents, their assignments, and current status (if known).
    *   **Recent Memory Bank Entries:** Summaries or verbatim copies of the most recent/relevant logs from the `Memory_Bank.md` providing immediate context on ongoing work.
    *   **Critical Code Snippets/Outputs:** Essential code, configurations, or outputs generated recently or frequently referenced.
    *   **Obstacles & Challenges:** Any known blockers, risks, or unresolved issues.
    *   **User Directives:** Record of recent or outstanding instructions from the User.
    *   **File Manifest (Optional but Recommended):** A list of key project files and their purpose.
*   **Format:** Must adhere to the structure defined in `prompts/02_Utility_Prompts_And_Format_Definitions/Handover_Artifact_Formats.md` to ensure parsability by the incoming agent.

### 3.2. The `Handover_Prompt.md` (New Agent Initialization)

*   **Purpose:** To initialize the *new* agent instance, providing it with both the standard APM framework orientation and the specific context necessary to take over the project seamlessly.
*   **Content Requirements:** This prompt is crucial and must contain:
    *   **APM Framework Introduction:** Incorporate essential sections from the standard `prompts/00_Initial_Manager_Setup/01_Initiation_Prompt.md`. This includes the APM Workflow Overview, the agent's Core Responsibilities (adapted for the incoming role, e.g., "You are taking over as Manager Agent..."), and the importance of APM assets.
    *   **Handover Context Introduction:** Clearly state that this is a handover situation.
    *   **`Handover_File.md` Summary:** Provide a concise overview of the structure and key contents of the accompanying `Handover_File.md`.
    *   **Instructions for Processing:** Explicit instructions directing the new agent to thoroughly read, parse, and internalize the contents of the `Handover_File.md`.
    *   **Immediate Objectives:** Clearly state the immediate next steps or priorities for the new agent based on the handover context (e.g., "Review Task X status", "Prepare prompt for Agent B", "Address User query regarding Y").
    *   **Verification Step:** Instruct the new agent to confirm its understanding of the handover context and its readiness to proceed by summarizing the project status and immediate objectives back to the User.
*   **Format:** Should follow the structure and principles defined for handover prompts within `prompts/02_Utility_Prompts_And_Format_Definitions/Handover_Artifact_Formats.md`, ensuring clarity and actionable instructions.

## 4. Handover Procedure (Manager Agent Focus)

The incumbent Manager Agent executes the handover as follows (under User supervision):

1.  **Confirmation:** User confirms the need for handover.
2.  **`Handover_File.md` Generation:**
    *   Consult the `Handover_File_Content.md` guide for formatting.
    *   Gather all necessary context (as detailed in section 3.1).
    *   Structure and write the content into a new file named `Handover_File.md` (or a User-specified name).
    *   Present the generated file to the User for review and optional modification.
3.  **`Handover_Prompt.md` Generation:**
    *   Draft the prompt content (as detailed in section 3.2).
    *   Crucially, integrate core sections from `01_Initiation_Prompt.md`.
    *   Reference the generated `Handover_File.md`.
    *   Specify immediate next steps for the incoming agent.
    *   Present the generated prompt to the User for review and approval.
4.  **Execution:** The User takes the approved `Handover_Prompt.md` and the `Handover_File.md` and uses them to initialize the new Manager Agent instance in a fresh session.
5.  **Verification:** The new Manager Agent processes the prompt and file, then confirms its readiness and understanding to the User.

## 5. Handover for Specialized Agents

While the primary focus is on the Manager Agent, the protocol can be adapted for specialized agents (Implementer, Debugger, etc.) reaching their context limits.

*   **Initiation:** Typically triggered by the User or the Manager Agent observing context issues with the specialized agent.
*   **Responsibility:** The Manager Agent usually oversees this process.
*   **`Handover_File.md` (Simplified):** Contains context relevant *only* to the specialized agent's current task or area of responsibility (e.g., specific function being debugged, relevant code files, recent error messages, task requirements).
*   **`Handover_Prompt.md` (Simplified):** Initializes the new specialized agent instance, explains the handover, points to the simplified Handover File, and restates the specific task objectives. It does *not* typically need the full APM introduction from the Manager's initiation prompt.

## 6. Final Considerations

*   **User Oversight:** The User plays a critical role in confirming the need for handover, reviewing the generated artifacts (`Handover_File.md`, `Handover_Prompt.md`), and initiating the new agent instance.
*   **Clarity and Accuracy:** The success of the handover depends entirely on the clarity, accuracy, and completeness of the information provided in the Handover File and Prompt. The outgoing agent must be diligent in its generation.
*   **Iterative Process:** The User may request revisions to the Handover File or Prompt before finalizing them.

This protocol provides the standardized mechanism for maintaining project momentum and knowledge continuity within the APM framework.

### Step X: Incorporate Recent Conversational Context (Outgoing MA)

**Objective:** To ensure the handover captures not only the formally documented project state but also the most recent, potentially unlogged, user intent and directives.

**Actions:**

1.  **Review Recent Interactions:** Before finalizing the `Handover_File.md` and the `Handover_Prompt.md`, the Outgoing Manager Agent (OMA) MUST explicitly review the transcript of the last N (e.g., 5-10, or a reasonable span covering the latest significant interactions) conversational turns with the User.

2.  **Identify Key Unlogged Information:** From this review, identify:
    *   Any critical user directives or instructions.
    *   Subtle shifts in project priority or focus.
    *   New ideas or requirements expressed by the User.
    *   Contextual clarifications that significantly impact ongoing or upcoming tasks.
    *   Any information that is vital for the Incoming Manager Agent (IMA) to know but might not have been formally logged in the Memory Bank or updated in the `Implementation_Plan.md` with the same immediacy.

3.  **Summarize Findings:** Prepare a concise, bullet-point summary of this "freshest layer of user intent." Focus on actionable information or critical context.

4.  **Update Handover Artifacts:**
    *   This summary MUST be included in the dedicated section (e.g., "Section 7: Recent Conversational Context & Key User Directives") within the `Handover_File.md`. Refer to the `Handover_Artifact_Format.md` for the precise structure.
    *   The insights from this summary should also be used to inform and refine the `Handover_Prompt.md`, ensuring the IMA is explicitly briefed on these recent nuances.

**Rationale:** This step is crucial for bridging any potential gap between the formal, logged project state and the immediate, evolving conversational context. It provides the IMA with the most current and complete understanding of the User's expectations and the project's micro-dynamics, leading to a smoother and more effective transition.